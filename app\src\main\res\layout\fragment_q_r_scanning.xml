<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.fragments.qr.QRScanningFragment">

    <SurfaceView
        android:id="@+id/cameraSurfaceView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/textView20"
        style="@style/h2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_20sdp"
        android:text="@string/qr_scanning"
        android:textColor="@color/white"
        android:textSize="26sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/textView21"
        style="@style/subtitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:maxLines="3"
        android:text="@string/find_other_users_nopen_luv_crates_nopen_luv_chests"
        android:textColor="@color/semigray"
        app:layout_constraintStart_toStartOf="@+id/textView20"
        app:layout_constraintTop_toBottomOf="@+id/textView20" />

    <ImageView
        android:layout_width="match_parent"
        android:layout_height="@dimen/_200sdp"
        android:src="@drawable/ic_qr_frame"
        app:layout_constraintBottom_toTopOf="@+id/photoLib"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textView21" />


    <LinearLayout
        android:id="@+id/photoLib"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/_30sdp"
        android:gravity="center"
        android:orientation="vertical"
        android:elevation="5dp"
        app:layout_constraintBottom_toTopOf="@+id/textView"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <ImageView
            android:id="@+id/imageView5"
            android:layout_width="@dimen/_30sdp"
            android:layout_height="@dimen/_30sdp"
            android:layout_marginBottom="@dimen/_5sdp"
            android:alpha="0.7"
            android:src="@drawable/ic_scan_btn" />

        <TextView
            style="@style/small"
            android:gravity="center"
            android:text="@string/photo_library"
            android:textColor="@color/semigray" />

    </LinearLayout>

    <TextView
        android:id="@+id/textView"
        style="@style/subtitle"
        android:layout_marginBottom="@dimen/_30sdp"
        android:gravity="center"
        android:text="@string/place_the_qr_code_in_front_nof_the_square"
        android:textColor="@color/semigray"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>