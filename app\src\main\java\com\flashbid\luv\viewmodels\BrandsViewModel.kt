package com.flashbid.luv.viewmodels

import androidx.lifecycle.LiveData
import androidx.lifecycle.ViewModel
import com.flashbid.luv.models.remote.AllBrandsResponse
import com.flashbid.luv.models.remote.BrandCategoryResponse
import com.flashbid.luv.models.remote.CategoryBrandsResponse
import com.flashbid.luv.network.Resource
import com.flashbid.luv.repositories.BrandsRepository

class BrandsViewModel(
    private val brandsRepository: BrandsRepository
) : ViewModel() {

    fun getTopBrands(period: String, userType: String): LiveData<Resource<BrandCategoryResponse>> {
        return brandsRepository.getTopBrands(period, userType)
    }

    fun getAllBrands(period: String, category: Int): LiveData<Resource<CategoryBrandsResponse>> {
        return brandsRepository.getAllBrands(period, category)
    }

    fun getAllBrandsByCategory(): LiveData<Resource<AllBrandsResponse>> {
        return brandsRepository.getAllBrandsByCategory()
    }

    fun getAllBrands(): LiveData<Resource<AllBrandsResponse>> {
        return brandsRepository.getAllBrands()
    }

}