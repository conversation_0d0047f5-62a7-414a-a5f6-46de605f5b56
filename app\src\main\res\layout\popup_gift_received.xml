<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_marginHorizontal="@dimen/_50sdp"
    app:cardCornerRadius="@dimen/_24sdp"
    android:layout_gravity="center"
    app:cardBackgroundColor="#90000000"
    app:strokeWidth="0dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:layout_gravity="center"
        android:elevation="4dp"
        android:gravity="center"
        android:paddingHorizontal="@dimen/_35sdp"
        android:orientation="vertical"
        android:paddingVertical="@dimen/_24sdp">

        <ImageView
            android:id="@+id/giftIcon"
            android:layout_width="@dimen/_150sdp"
            android:layout_height="@dimen/_150sdp"
            android:layout_gravity="center"
            android:padding="@dimen/_10sdp"
            android:src="@drawable/ic_gift_box"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/giftInfo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textAlignment="center"
            android:textColor="@color/white"
            android:textSize="16sp"
            android:layout_marginTop="@dimen/_8sdp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/giftIcon"
            tools:text="FloydMiles sent\nGift Name!" />

        <ImageView
            android:id="@+id/ivHeart"
            android:layout_width="@dimen/_18sdp"
            android:layout_height="@dimen/_18sdp"
            android:layout_gravity="center"
            android:src="@drawable/heart_filled"
            android:padding="@dimen/_2sdp"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintBottom_toBottomOf="@+id/giftValue"
            app:layout_constraintEnd_toStartOf="@+id/giftValue"
            app:layout_constraintStart_toStartOf="@+id/giftInfo"
            app:layout_constraintTop_toTopOf="@+id/giftValue" />

        <TextView
            android:id="@+id/giftValue"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="@dimen/_8sdp"
            android:text="5000"
            android:textColor="@color/redgradstart"
            android:textSize="14sp"
            app:layout_constraintEnd_toEndOf="@+id/giftInfo"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toEndOf="@+id/ivHeart"
            app:layout_constraintTop_toBottomOf="@+id/giftInfo" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/counterButton"
            style="@style/button"
            android:layout_width="match_parent"
            android:layout_marginTop="@dimen/_14sdp"
            android:text="@string/tap_to_counter"
            android:textColor="@color/white"
            android:textStyle="bold"
            app:backgroundTint="@color/redgradstart"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/giftValue" />


    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>