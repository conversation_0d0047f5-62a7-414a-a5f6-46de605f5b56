<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.fragments.story.UserStoryFragment">

    <androidx.media3.ui.PlayerView
        android:id="@+id/playerView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/black"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:use_controller="false" />

    <LinearLayout
        android:id="@+id/progressBarContainer"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_5sdp"
        android:orientation="horizontal"
        android:paddingTop="8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivUser"
        android:layout_width="@dimen/_35sdp"
        android:layout_height="@dimen/_35sdp"
        android:layout_marginStart="@dimen/_10sdp"
        android:layout_marginTop="@dimen/_15sdp"
        android:scaleType="centerCrop"
        android:src="@drawable/image"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/progressBarContainer"
        app:shapeAppearance="@style/ShapeAppearance.Material3.Corner.Full" />

    <androidx.cardview.widget.CardView
        android:id="@+id/cdvUserName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_6sdp"
        app:cardBackgroundColor="@color/white"
        app:cardCornerRadius="@dimen/_20sdp"
        app:cardElevation="0dp"
        app:contentPaddingLeft="@dimen/_10sdp"
        app:contentPaddingRight="@dimen/_10sdp"
        app:layout_constraintBottom_toTopOf="@+id/cdvReceiver"
        app:layout_constraintStart_toEndOf="@+id/ivUser"
        app:layout_constraintTop_toTopOf="@+id/ivUser">

        <TextView
            android:id="@+id/tvUsername"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:text="victordetroy"
            android:textColor="@color/black"
            android:textSize="@dimen/_10sdp" />

    </androidx.cardview.widget.CardView>

    <androidx.cardview.widget.CardView
        android:id="@+id/cdvReceiver"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_6sdp"
        android:layout_marginTop="@dimen/_4sdp"
        app:cardBackgroundColor="@color/lightBlack"
        app:cardCornerRadius="@dimen/_20sdp"
        app:cardElevation="0dp"
        app:contentPaddingLeft="@dimen/_10sdp"
        app:contentPaddingRight="@dimen/_10sdp"
        app:layout_constraintBottom_toBottomOf="@+id/ivUser"
        app:layout_constraintStart_toEndOf="@+id/ivUser"
        app:layout_constraintTop_toBottomOf="@+id/cdvUserName">

        <TextView
            android:id="@+id/tvReceiverUsername"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="victordetroy"
            android:textColor="@color/white"
            android:textSize="@dimen/_10sdp" />

    </androidx.cardview.widget.CardView>

    <androidx.cardview.widget.CardView
        android:id="@+id/cdvFollow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_5sdp"
        android:visibility="gone"
        app:cardBackgroundColor="@color/redgradstart"
        app:cardCornerRadius="@dimen/_20sdp"
        app:cardElevation="0dp"
        app:contentPaddingLeft="@dimen/_10sdp"
        app:contentPaddingRight="@dimen/_10sdp"
        app:layout_constraintBottom_toBottomOf="@+id/cdvUserName"
        app:layout_constraintStart_toEndOf="@+id/cdvUserName"
        app:layout_constraintTop_toTopOf="@+id/cdvUserName">

        <TextView
            android:id="@+id/tvFollow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/follow"
            android:textColor="@color/white"
            android:textSize="@dimen/_10sdp" />

    </androidx.cardview.widget.CardView>

    <androidx.cardview.widget.CardView
        android:id="@+id/cdvFollowing"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_5sdp"
        android:visibility="gone"
        app:cardBackgroundColor="#C3B3A4"
        app:cardCornerRadius="@dimen/_20sdp"
        app:cardElevation="0dp"
        app:contentPaddingLeft="@dimen/_10sdp"
        app:contentPaddingRight="@dimen/_10sdp"
        app:layout_constraintBottom_toBottomOf="@+id/cdvUserName"
        app:layout_constraintStart_toEndOf="@+id/cdvFollow"
        app:layout_constraintTop_toTopOf="@+id/cdvUserName">

        <TextView
            android:id="@+id/tvFollowing"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/following"
            android:textColor="#4C4640"
            android:textSize="@dimen/_10sdp" />

    </androidx.cardview.widget.CardView>

    <androidx.cardview.widget.CardView
        android:id="@+id/cvClose"
        android:layout_width="@dimen/_30sdp"
        android:layout_height="@dimen/_30sdp"
        android:layout_marginEnd="@dimen/_10sdp"
        app:cardBackgroundColor="@color/lightBlack"
        app:cardCornerRadius="@dimen/_20sdp"
        app:cardElevation="0dp"
        app:layout_constraintBottom_toBottomOf="@+id/cdvFollow"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/cdvFollow">

        <ImageView
            android:id="@+id/ivClose"
            android:layout_width="@dimen/_20sdp"
            android:layout_height="@dimen/_20sdp"
            android:layout_gravity="center"
            android:src="@drawable/ic_close" />

    </androidx.cardview.widget.CardView>

    <androidx.cardview.widget.CardView
        android:id="@+id/cvOptions"
        android:layout_width="@dimen/_30sdp"
        android:layout_height="@dimen/_30sdp"
        android:layout_marginEnd="@dimen/_5sdp"
        app:cardBackgroundColor="@color/lightBlack"
        app:cardCornerRadius="@dimen/_20sdp"
        app:cardElevation="0dp"
        app:layout_constraintBottom_toBottomOf="@+id/cdvFollow"
        app:layout_constraintEnd_toStartOf="@+id/cvClose"
        app:layout_constraintTop_toTopOf="@+id/cdvFollow">

        <ImageView
            android:id="@+id/ivOptions"
            android:layout_width="@dimen/_20sdp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:src="@drawable/ic_options" />

    </androidx.cardview.widget.CardView>

    <androidx.cardview.widget.CardView
        android:id="@+id/cvReport"
        android:layout_width="@dimen/_150sdp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_10sdp"
        app:cardBackgroundColor="#80000000"
        app:cardCornerRadius="@dimen/_12sdp"
        app:cardElevation="0dp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@+id/cvClose"
        app:layout_constraintTop_toBottomOf="@+id/cvOptions">

        <TextView
            android:id="@+id/btnReport"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="start"
            android:gravity="start"
            android:paddingHorizontal="@dimen/_20sdp"
            android:paddingVertical="@dimen/_8sdp"
            android:text="@string/report"
            android:textColor="@color/white"
            android:textSize="@dimen/_12sdp" />

    </androidx.cardview.widget.CardView>

    <ProgressBar
        android:id="@+id/pgBar"
        android:layout_width="@dimen/_40sdp"
        android:layout_height="@dimen/_40sdp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.5" />

</androidx.constraintlayout.widget.ConstraintLayout>