<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.fragments.operations.withdraw.WithdrawAmountSelectFragment">

    <ImageView
        android:id="@+id/imageView"
        android:layout_width="@dimen/_30sdp"
        android:layout_height="@dimen/_30sdp"
        android:layout_margin="@dimen/_15sdp"
        android:src="@drawable/ic_back_btn"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/textView"
        style="@style/h3"
        android:text="@string/withdraw"
        app:layout_constraintBottom_toTopOf="@+id/tvInfo"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/imageView" />

    <TextView
        android:id="@+id/tvInfo"
        style="@style/text"
        android:layout_width="wrap_content"
        android:gravity="center"
        android:paddingStart="0dp"
        android:paddingEnd="@dimen/_5sdp"
        android:text="@string/balance"
        android:textColor="@color/gray"
        app:layout_constraintBottom_toBottomOf="@+id/imageView"
        app:layout_constraintEnd_toStartOf="@+id/tvBalance"
        app:layout_constraintStart_toStartOf="@+id/textView"
        app:layout_constraintTop_toBottomOf="@+id/textView" />

    <TextView
        android:id="@+id/tvBalance"
        style="@style/text"
        android:layout_width="wrap_content"
        android:drawablePadding="@dimen/_3sdp"
        android:gravity="center"
        android:textColor="@color/blue"
        app:drawableEndCompat="@drawable/diamond_small"
        app:layout_constraintBottom_toBottomOf="@+id/tvInfo"
        app:layout_constraintEnd_toEndOf="@+id/textView"
        app:layout_constraintStart_toEndOf="@+id/tvInfo"
        app:layout_constraintTop_toTopOf="@+id/tvInfo"
        tools:text="500" />

    <View
        android:id="@+id/view"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginTop="@dimen/_10sdp"
        android:background="@color/semigray"
        app:layout_constraintEnd_toEndOf="@+id/textView9"
        app:layout_constraintStart_toStartOf="@+id/imageView"
        app:layout_constraintTop_toBottomOf="@+id/tvInfo" />

    <TextView
        style="@style/text"
        android:layout_width="match_parent"
        android:layout_marginTop="@dimen/_20sdp"
        android:background="@color/semired"
        android:gravity="center"
        android:padding="@dimen/_5sdp"
        android:text="@string/you_don_t_have_enough_diamonds"
        android:textColor="@color/redgradstart"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@+id/textView9"
        app:layout_constraintStart_toStartOf="@+id/imageView"
        app:layout_constraintTop_toBottomOf="@+id/tvInfo" />

    <TextView
        android:id="@+id/textView9"
        style="@style/text"
        android:layout_width="wrap_content"
        android:layout_marginEnd="@dimen/_20sdp"
        android:gravity="center"
        android:text="@string/_2_2"
        android:textColor="@color/gray"
        app:layout_constraintBottom_toBottomOf="@+id/tvInfo"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/textView" />

    <TextView
        android:id="@+id/tvError"
        style="@style/text"
        android:layout_width="match_parent"
        android:gravity="center"
        android:padding="@dimen/_5sdp"
        android:textColor="@color/gray"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/view" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_10sdp"
        android:gravity="center"
        android:orientation="vertical"
        app:layout_constraintBottom_toTopOf="@+id/btnNext"
        app:layout_constraintEnd_toEndOf="@+id/view"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="@+id/view"
        app:layout_constraintTop_toBottomOf="@+id/tvError"
        app:layout_constraintVertical_bias="0.181">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginVertical="@dimen/_5sdp"
            android:orientation="horizontal">

            <TextView
                style="@style/text"
                android:layout_width="wrap_content"
                android:paddingEnd="@dimen/_5sdp"
                android:text="@string/weekly_limit_used"
                android:textColor="@color/gray" />

            <TextView
                android:id="@+id/tvUsedLimit"
                style="@style/text"
                android:layout_width="wrap_content"
                android:gravity="center" />

            <TextView
                android:id="@+id/tvLimit"
                style="@style/text"
                android:layout_width="wrap_content"
                android:gravity="center"
                android:text="/1000" />

        </LinearLayout>

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/edtAmount"
            style="@style/small"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_35sdp"
            android:background="@drawable/bg_edit"
            android:digits="1234567890"
            android:gravity="center"
            android:enabled="false"
            android:hint="@string/diamonds"
            android:inputType="number"
            android:maxLines="1"
            android:minHeight="@dimen/_70sdp"
            android:padding="@dimen/_10sdp"
            android:textSize="24sp" />

        <TextView
            android:id="@+id/tvUsd"
            style="@style/text"
            android:layout_width="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="@dimen/_10sdp"
            android:drawablePadding="@dimen/_5sdp"
            android:gravity="center"
            android:textColor="@color/green"
            app:drawableEndCompat="@drawable/ic_dollar_small" />

        <TextView
            style="@style/text"
            android:layout_width="match_parent"
            android:layout_gravity="center"
            android:layout_marginTop="@dimen/_30sdp"
            android:gravity="center"
            android:text="@string/once_submitted_your_request_will_be_processed_nvia_paypal_paypal_fees_may_result"
            android:textColor="@color/gray" />

        <TextView
            style="@style/text"
            android:layout_width="match_parent"
            android:layout_gravity="center"
            android:layout_marginTop="@dimen/_30sdp"
            android:gravity="center"
            android:text="Minimum withdrawal amount is $50"
            android:textColor="@color/gray"
            android:textSize="12sp"
            android:textStyle="bold" />

    </LinearLayout>

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btnNext"
        style="@style/button"
        android:layout_marginBottom="@dimen/_10sdp"
        android:text="@string/submit"
        android:enabled="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>