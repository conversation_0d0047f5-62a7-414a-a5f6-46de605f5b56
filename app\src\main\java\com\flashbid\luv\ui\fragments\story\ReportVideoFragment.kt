package com.flashbid.luv.ui.fragments.story

import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.View
import androidx.navigation.fragment.findNavController
import com.flashbid.luv.MainActivity
import com.flashbid.luv.R
import com.flashbid.luv.databinding.FragmentReportVideoBinding
import com.flashbid.luv.extensions.TextState
import com.flashbid.luv.extensions.hide
import com.flashbid.luv.extensions.show
import com.flashbid.luv.extensions.snackBar
import com.flashbid.luv.extensions.viewBinding
import com.flashbid.luv.models.remote.ReportCategoryResponse
import com.flashbid.luv.network.Status
import com.flashbid.luv.util.HistoryMapping
import com.flashbid.luv.util.setArrayAdapter
import com.flashbid.luv.viewmodels.StoryViewModel
import com.flashbid.luv.viewmodels.UserViewModel
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.util.ArrayList

class ReportVideoFragment : Fragment(R.layout.fragment_report_video) {

    private val binding by viewBinding(FragmentReportVideoBinding::bind)
    private val storyViewModel: StoryViewModel by viewModel()
    private var reporting: ReportCategoryResponse.Message? = null

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        getReports()

        binding.btnSubmit.setOnClickListener {
            if (reporting == null) {
                snackBar("Select report type..")
            } else {
                createReport(
                    type = "Value",
                    type_id = reporting?.id.toString(),
                    comment = reporting?.category_name.toString(),
                    category = reporting?.id ?: -1
                )
            }
        }

        binding.imageView.setOnClickListener {
            findNavController().popBackStack()
        }
    }

    private fun createReport(
        type: String, type_id: String, comment: String, category: Int
    ) {
        storyViewModel.createReport(type, type_id, comment, category).observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(
                    it.message ?: getString(
                        R.string.something_went_wrong, getString(R.string.try_again)
                    )
                )

                Status.LOADING -> {}
                Status.SUCCESS -> {
                    findNavController().popBackStack(R.id.homeFragment, false)
                    (requireActivity() as MainActivity).showSnackBar(getString(R.string.the_report_will_be_reviewed_promptly),
                        TextState.SUCCESS)
                }
            }
        }
    }

    private fun getReports() {
        storyViewModel.getReports().observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(
                    it.message ?: getString(
                        R.string.something_went_wrong, getString(R.string.try_again)
                    )
                )

                Status.LOADING -> {}
                Status.SUCCESS -> {
                    val reportingList = it.data?.message
                    binding.edtReporting.setArrayAdapter(reportingList?.map { it.category_name } as ArrayList<String>)
                    binding.edtReporting.setOnItemClickListener { _, _, position, _ ->
                        reporting = reportingList[position]
                    }
                }
            }
        }
    }

}