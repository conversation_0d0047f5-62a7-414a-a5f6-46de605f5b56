<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:context=".ui.fragments.transactions.ReferralAwardFragment">

    <ImageView
        android:id="@+id/imageView"
        android:layout_width="@dimen/_30sdp"
        android:layout_height="@dimen/_30sdp"
        android:layout_margin="@dimen/_20sdp"
        android:src="@drawable/ic_back_btn"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/textView"
        style="@style/h3"
        android:text="@string/quest_reward_referral"
        app:layout_constraintBottom_toTopOf="@+id/tvInfo"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/imageView" />

    <TextView
        android:id="@+id/tvInfo"
        style="@style/small"
        android:layout_width="wrap_content"
        android:gravity="center"
        android:textColor="@color/gray"
        app:layout_constraintBottom_toBottomOf="@+id/imageView"
        app:layout_constraintEnd_toEndOf="@+id/textView"
        app:layout_constraintStart_toStartOf="@+id/textView"
        app:layout_constraintTop_toBottomOf="@+id/textView"
        tools:text="5 days ago" />

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/materialCardView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_20sdp"
        app:cardBackgroundColor="#80FFFFFF"
        app:cardCornerRadius="32dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvInfo"
        app:strokeColor="@color/white"
        app:strokeWidth="4dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginHorizontal="@dimen/_5sdp"
            android:layout_marginVertical="@dimen/_15sdp"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/ivDollar"
                    android:layout_width="@dimen/_20sdp"
                    android:layout_height="@dimen/_20sdp"
                    android:src="@drawable/dollar_square_small"/>

                <TextView
                    android:id="@+id/tvAmount"
                    style="@style/h2"
                    android:layout_width="wrap_content"
                    android:gravity="center"
                    android:textColor="@color/gray"
                    tools:text="0" />

                <TextView
                    style="@style/small"
                    android:layout_width="wrap_content"
                    android:gravity="center"
                    android:text="@string/usd"
                    android:textColor="@color/gray" />

            </LinearLayout>

            <View
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:background="@color/semigray" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvHearts"
                    style="@style/h2"
                    android:layout_width="wrap_content"
                    android:drawablePadding="@dimen/_5sdp"
                    android:gravity="center"
                    android:textColor="@color/redgradstart"
                    app:drawableTopCompat="@drawable/heart_square_small"
                    tools:text="+ 1200" />

                <TextView
                    style="@style/small"
                    android:layout_width="wrap_content"
                    android:layout_marginStart="@dimen/_5sdp"
                    android:gravity="center"
                    android:text="@string/luvs"
                    android:textColor="@color/gray" />

            </LinearLayout>

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_5sdp"
        android:layout_marginVertical="@dimen/_15sdp"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="@+id/materialCardView"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="@+id/materialCardView"
        app:layout_constraintTop_toBottomOf="@+id/materialCardView">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingVertical="@dimen/_10sdp">

            <TextView
                style="@style/text"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:drawablePadding="@dimen/_5sdp"
                android:text="@string/status"
                android:textColor="@color/gray" />

            <TextView
                android:id="@+id/tvStatus"
                style="@style/text"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:gravity="start"
                tools:text="Completed" />

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/semigray" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingVertical="@dimen/_10sdp">

            <TextView
                style="@style/text"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:drawablePadding="@dimen/_5sdp"
                android:text="@string/date_and_time"
                android:textColor="@color/gray" />

            <TextView
                android:id="@+id/tvDate"
                style="@style/text"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:gravity="start"
                tools:text="01 / 02 / 2022" />

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/semigray" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingVertical="@dimen/_10sdp">

            <TextView
                style="@style/text"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:drawablePadding="@dimen/_5sdp"
                android:text="@string/transaction_id"
                android:textColor="@color/gray" />

            <TextView
                android:id="@+id/tvTransaction"
                style="@style/text"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:gravity="start"
                tools:text="20002214574545" />

        </LinearLayout>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>