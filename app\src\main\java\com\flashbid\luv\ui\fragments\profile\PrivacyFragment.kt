package com.flashbid.luv.ui.fragments.profile

import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import com.flashbid.luv.R
import com.flashbid.luv.data.local.AppPreferences
import com.flashbid.luv.databinding.FragmentPrivacyBinding
import com.flashbid.luv.extensions.setOnClickWithDebounce
import com.flashbid.luv.extensions.snackBar
import com.flashbid.luv.extensions.viewBinding
import com.flashbid.luv.models.remote.UpdateProfileRequest
import com.flashbid.luv.network.Status
import com.flashbid.luv.util.HistoryMapping
import com.flashbid.luv.util.setArrayAdapter
import com.flashbid.luv.viewmodels.UserViewModel
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel

class PrivacyFragment : Fragment(R.layout.fragment_privacy) {

    private val binding by viewBinding(FragmentPrivacyBinding::bind)
    private var followers: Int = -1
    private var followings: Int = -1
    private val userViewModel: UserViewModel by viewModel()
    private val pref: AppPreferences by inject()

    private val peopleList by lazy {
        arrayListOf<Pair<String, Int>>().apply {
            add(Pair(getString(R.string.onlyme), HistoryMapping.PRIVACY.ONLY_ME))
            add(Pair(getString(R.string.peopleifollow), HistoryMapping.PRIVACY.PEOPLE_FOLLOW))
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setFollowersList()

        setFollowList()

        binding.edtFollowers.setText(
            peopleList.find { it.second == pref.follower }?.first ?: "",
            false
        )
        binding.edtFollow.setText(
            peopleList.find { it.second == pref.following }?.first ?: "",
            false
        )

        binding.imageView.setOnClickWithDebounce {
            findNavController().popBackStack()
        }

        binding.saveButton.setOnClickWithDebounce {
            updateNewFields(followers.toString(), followings.toString())
        }

    }

    private fun setFollowersList() {
        binding.edtFollowers.setArrayAdapter(peopleList.map { it.first } as ArrayList<String>)
        binding.edtFollowers.setOnItemClickListener { _, _, position, _ ->
            followers = peopleList[position].second
        }
    }

    private fun setFollowList() {
        binding.edtFollow.setArrayAdapter(peopleList.map { it.first } as ArrayList<String>)
        binding.edtFollow.setOnItemClickListener { _, _, position, _ ->
            followings = peopleList[position].second
        }
    }

    private fun updateNewFields(
        showFollower: String? = null,
        showFollowing: String? = null
    ) {
        pref.follower = followers
        pref.following = followings
        userViewModel.updatePreference(
            UpdateProfileRequest(
                UpdateProfileRequest.Payload(
                    show_follower = showFollower,
                    show_following = showFollowing,
                )
            )
        ).observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> {
                    snackBar(
                        it.message ?: getString(
                            R.string.something_went_wrong, getString(R.string.try_again)
                        )
                    )
                }

                Status.LOADING -> {}
                Status.SUCCESS -> {
                    findNavController().popBackStack()
                }
            }
        }
    }

}