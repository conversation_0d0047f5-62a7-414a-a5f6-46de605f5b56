<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    android:background="@drawable/bg_round_corners"
    android:orientation="vertical"
    android:paddingHorizontal="@dimen/_15sdp"
    android:paddingVertical="@dimen/_10sdp"
    app:layout_behavior="@string/bottom_sheet_behavior">

    <View
        android:id="@+id/view2"
        android:layout_width="@dimen/_40sdp"
        android:layout_height="4dp"
        android:layout_gravity="center"
        android:background="@drawable/bg_edit"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/imageView6"
        android:layout_width="@dimen/_25sdp"
        android:layout_height="@dimen/_25sdp"
        android:layout_marginTop="5dp"
        android:src="@drawable/ic_close_gray"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/view2" />

    <TextView
        android:id="@+id/btnEdit"
        style="@style/h2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_15sdp"
        android:gravity="center"
        android:text="@string/discard_media"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/imageView6" />

    <TextView
        android:id="@+id/textView43"
        style="@style/subtitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_15sdp"
        android:gravity="center"
        android:textColor="@color/gray"
        android:text="@string/going_back_will_delete_already_recorded_video"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/btnEdit" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_40sdp"
        android:layout_marginBottom="@dimen/_20sdp"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textView43">

        <androidx.cardview.widget.CardView
            android:id="@+id/btnCancel"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_5sdp"
            android:layout_weight="1"
            app:cardBackgroundColor="#ECECF1"
            app:cardCornerRadius="16dp"
            app:cardElevation="0dp"
            app:contentPadding="@dimen/_10sdp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="@string/cancel"
                android:textColor="@color/gray" />

        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:id="@+id/btnDiscard"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_5sdp"
            android:layout_weight="1"
            app:cardBackgroundColor="@color/semired"
            app:cardCornerRadius="16dp"
            app:cardElevation="0dp"
            app:contentPadding="@dimen/_10sdp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/btnReset">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="Discard"
                android:textColor="@color/redgradstart" />

        </androidx.cardview.widget.CardView>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>