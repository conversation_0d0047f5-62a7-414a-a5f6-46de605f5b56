<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="5dp"
    android:background="@color/white"
    app:cardCornerRadius="20dp"
    app:cardElevation="0dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/llBackground"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="@dimen/_5sdp">

        <ImageView
            android:id="@+id/imageView2"
            android:layout_width="@dimen/_40sdp"
            android:layout_height="@dimen/_40sdp"
            android:src="@drawable/sendgift"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvName"
            style="@style/text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_2sdp"
            app:layout_constraintEnd_toEndOf="@+id/imageView2"
            app:layout_constraintStart_toStartOf="@+id/imageView2"
            app:layout_constraintTop_toBottomOf="@+id/imageView2"
            tools:text="Heart" />

        <TextView
            android:id="@+id/textView8"
            style="@style/text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_2sdp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/ivHeart"
            app:layout_constraintStart_toStartOf="@+id/imageView2"
            app:layout_constraintTop_toBottomOf="@+id/tvName"
            tools:text="100" />

        <ImageView
            android:id="@+id/ivHeart"
            android:layout_width="@dimen/_20sdp"
            android:layout_height="0dp"
            android:src="@drawable/heart_colored"
            app:layout_constraintBottom_toBottomOf="@+id/textView8"
            app:layout_constraintEnd_toEndOf="@+id/imageView2"
            app:layout_constraintStart_toEndOf="@+id/textView8"
            app:layout_constraintTop_toTopOf="@+id/textView8" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>