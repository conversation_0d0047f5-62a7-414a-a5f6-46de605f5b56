package com.flashbid.luv.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.flashbid.luv.R
import com.flashbid.luv.databinding.ItemAlertBinding
import com.flashbid.luv.extensions.getTimeInAgo
import com.flashbid.luv.util.loadImageFromUrl
import com.flashbid.luv.extensions.setOnClickWithDebounce
import com.flashbid.luv.models.remote.AlertModel

class AlertsAdapter(
    val onClick: (AlertModel) -> Unit
) :
    RecyclerView.Adapter<AlertsAdapter.ViewHolder>() {

    private val list: ArrayList<AlertModel> = ArrayList()

    inner class ViewHolder(val binding: ItemAlertBinding) :
        RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val itemView =
            ItemAlertBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(itemView)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val model = list[position]

        with(holder.binding) {
            textView12.text = model.message
            tvUsername.text = root.context.getString(
                R.string.username_x_time, model.name,
                model.update_at.getTimeInAgo()
            )
            imageView4.loadImageFromUrl(model.image)
            root.setOnClickWithDebounce {
                onClick(model)
            }
        }

    }

    override fun getItemCount(): Int {
        return list.size
    }

    fun refresh(newList: ArrayList<AlertModel>) {
        list.clear()
        list.addAll(newList)
        notifyDataSetChanged()
    }

}