package com.flashbid.luv.ui.fragments.other

import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.OnBackPressedCallback
import androidx.navigation.fragment.findNavController
import com.flashbid.luv.R
import com.flashbid.luv.databinding.FragmentTopGifterFilterBinding
import com.flashbid.luv.databinding.FragmentTopLuversBinding
import com.flashbid.luv.extensions.setOnClickWithDebounce
import com.flashbid.luv.extensions.viewBinding
import com.flashbid.luv.viewmodels.BattleViewModel
import com.google.android.material.tabs.TabLayout

class TopGifterFilterFragment : Fragment(R.layout.fragment_top_gifter_filter) {

    private val binding by viewBinding(FragmentTopGifterFilterBinding::bind)
    private var period = "daily"
    private var userType = "regular"

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.imageView.setOnClickWithDebounce {
            findNavController().navigate(
                TopGifterFilterFragmentDirections.actionTopGifterFilterFragmentToTopLuversFragment(
                    period,
                    userType
                )
            )
        }

        initializePeriodTabs(arrayOf(getString(R.string.daily), getString(R.string.monthly)))

        initializeUserTabs(
            arrayOf(
                getString(R.string.top_regular_gifters),
                getString(R.string.top_brand_gifters)
            )
        )

        requireActivity().onBackPressedDispatcher.addCallback(viewLifecycleOwner,
            object : OnBackPressedCallback(true) {
                override fun handleOnBackPressed() {
                    findNavController().navigate(
                        TopGifterFilterFragmentDirections.actionTopGifterFilterFragmentToTopLuversFragment(
                            period,
                            userType
                        )
                    )
                }
            }
        )

    }

    private fun initializePeriodTabs(tabs: Array<String>) {
        for (tabName in tabs) {
            val tab = binding.tabLayout1.newTab().setText(tabName.replaceFirstChar { if (it.isLowerCase()) it.titlecase() else it.toString() })
            binding.tabLayout1.addTab(tab)
        }

        binding.tabLayout1.setTabTextColors(
            resources.getColor(R.color.black),
            resources.getColor(R.color.white)
        )

        binding.tabLayout1.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab) {
                if (tab.position == 0) period = "daily"
                else if (tab.position == 1) period = "monthly"
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {
            }

            override fun onTabReselected(tab: TabLayout.Tab?) {
            }
        })
    }

    private fun initializeUserTabs(tabs: Array<String>) {
        for (tabName in tabs) {
            val tab = binding.tabLayout2.newTab().setText(tabName)
            binding.tabLayout2.addTab(tab)
        }

        binding.tabLayout2.setTabTextColors(
            resources.getColor(R.color.black),
            resources.getColor(R.color.white)
        )

        binding.tabLayout2.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab) {
                if (tab.position == 0) userType = "regular"
                else if (tab.position == 1) userType = "brand"
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {
            }

            override fun onTabReselected(tab: TabLayout.Tab?) {
            }
        })
    }
}