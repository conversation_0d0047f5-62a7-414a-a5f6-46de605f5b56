package com.flashbid.luv.repositories

import androidx.lifecycle.LiveData
import androidx.lifecycle.liveData
import com.flashbid.luv.models.remote.AllPlansResponse
import com.flashbid.luv.models.remote.CreateKeyRequest
import com.flashbid.luv.models.remote.DiamondRateResponse
import com.flashbid.luv.models.remote.HistoryDetailResponse
import com.flashbid.luv.models.remote.MessageResponse
import com.flashbid.luv.models.remote.PayoutDataResponse
import com.flashbid.luv.models.remote.RandomUserResponse
import com.flashbid.luv.models.remote.RechargeRequest
import com.flashbid.luv.models.remote.SendLuvRequest
import com.flashbid.luv.models.remote.TransactionsResponse
import com.flashbid.luv.models.remote.UnlockKeyRequest
import com.flashbid.luv.models.remote.WeeklyLimitResponse
import com.flashbid.luv.models.remote.WithdrawRequest
import com.flashbid.luv.network.RemoteDataSource
import com.flashbid.luv.network.Resource
import kotlinx.coroutines.Dispatchers

class TransactionRepository(private val remoteDataSource: RemoteDataSource) {

    fun unlockKeyCrate(
        codeId: String, request: UnlockKeyRequest
    ): LiveData<Resource<MessageResponse>> = liveData(Dispatchers.IO) {
        emit(Resource.loading(null))
        val response = remoteDataSource.unlockKeyCrate(codeId, request)
        emit(response)
    }

    fun createKeyCrate(request: CreateKeyRequest): LiveData<Resource<MessageResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.createKeyCrate(request)
            emit(response)
        }

    fun getCommunityTransactions(page: Int, limit: Int): LiveData<Resource<TransactionsResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getCommunityTransactions(page, limit)
            emit(response)
        }

    fun rechargePlan(query: RechargeRequest): LiveData<Resource<MessageResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.rechargePlan(query)
            emit(response)
        }

    fun withdrawDiamonds(request: WithdrawRequest): LiveData<Resource<MessageResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.withdrawDiamonds(request)
            emit(response)
        }

    fun getHistoryDetail(request: String): LiveData<Resource<HistoryDetailResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getHistoryDetail(request)
            emit(response)
        }

    fun getAllPlans(): LiveData<Resource<AllPlansResponse>> = liveData(Dispatchers.IO) {
        emit(Resource.loading(null))
        val response = remoteDataSource.getAllPlans()
        emit(response)
    }

    fun getPayoutData(): LiveData<Resource<PayoutDataResponse>> = liveData(Dispatchers.IO) {
        emit(Resource.loading(null))
        val response = remoteDataSource.getPayoutData()
        emit(response)
    }

    fun getWithdrawLimit(): LiveData<Resource<WeeklyLimitResponse>> = liveData(Dispatchers.IO) {
        emit(Resource.loading(null))
        val response = remoteDataSource.getWithdrawLimit()
        emit(response)
    }

    fun getDiamondRate(codeId: String): LiveData<Resource<DiamondRateResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getDiamondRate(codeId)
            emit(response)
        }

    fun sendLuv(request: SendLuvRequest): LiveData<Resource<MessageResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.sendLuv(request)
            emit(response)
        }

    fun getRandomUser(): LiveData<Resource<RandomUserResponse>> = liveData(Dispatchers.IO) {
        emit(Resource.loading(null))
        val response = remoteDataSource.getRandomUser()
        emit(response)
    }

}