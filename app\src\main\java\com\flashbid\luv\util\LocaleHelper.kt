package com.flashbid.luv.util

import android.content.Context
import android.content.res.Configuration
import java.util.Locale

class LanguageUtil {

    companion object {

        fun setLocale(context: Context, languageCode: String): Context {
            return updateResources(context, languageCode)
        }

        private fun updateResources(context: Context, languageCode: String): Context {
            var contextVar = context
            val locale = Locale(languageCode)
            Locale.setDefault(locale)

            val resources = contextVar.resources
            val configuration = Configuration(resources.configuration)

            configuration.setLocale(locale)
            contextVar = contextVar.createConfigurationContext(configuration)

            return contextVar
        }
    }
}
