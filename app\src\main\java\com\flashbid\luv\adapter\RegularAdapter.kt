package com.flashbid.luv.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.navigation.NavController
import androidx.recyclerview.widget.RecyclerView
import com.flashbid.luv.databinding.ItemTitleRegularBinding
import com.flashbid.luv.extensions.setGridLayout
import com.flashbid.luv.models.remote.Brand
import com.flashbid.luv.models.remote.BrandCategories


class RegularAdapter(
    private val fragment: Fragment,
    private val categories: ArrayList<BrandCategories>,
    val onClick: (Brand) -> Unit,
    private val navController: NavController

)

    : RecyclerView.Adapter<RegularAdapter.ViewHolder>() {
    inner class ViewHolder(val binding: ItemTitleRegularBinding) : RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val itemView = ItemTitleRegularBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(itemView)
    }


    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val model = categories[position]

        with(holder.binding) {
            textView12.text = model.name

            when {
                model.data?.isNotEmpty() == true -> {
                    // Use BrandsItemAdapter when data exists
                    rcvBrands.apply {
                        setGridLayout(3)
                        adapter = BrandsItemAdapter(model.data, onClick, navController, false,false)
                    }
                }
                model.company_name.isNotEmpty() -> {
                    // Create a list with only this specific category (No global filtering)
                    val companyList = arrayListOf(
                        Brand(
                            amount = 0,
                            category_id = model.id,
                            company_image = "",
                            company_name = model.company_name,
                            create_at = model.create_at,
                            id = model.id,
                            position = 0,
                            update_at = model.update_at,
                            user_id = 0,
                            is_favourite = false
                        )
                    )

                    rcvBrands.apply {
                        setGridLayout(2)
                        adapter = RegularItemAdapter(companyList, navController) // Send only this category's data
                    }
                }
                else -> {
                    // Fallback: Use an empty adapter or a default one
                    rcvBrands.adapter = RegularItemAdapter(arrayListOf(), navController)
                }
            }
        }
    }




//    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
//
//        val model = categories[position]
//
//        with(holder.binding) {
//            textView12.text = model.name
////            tvSeeAll.setOnClickWithDebounce {
////                val action = TopLuversFragmentDirections.actionTopLuversFragmentToAllBrandsFragment(
////                    model.name, model.id,
////                    false,
////                    emptyArray()
////                )
////                fragment.findNavController().navigate(action)
////            }
//
//// Determine which adapter to use
//             when {
//                model.data?.isNotEmpty() == true -> {
//                    // Use BrandsItemAdapter when data exists
//                    rcvBrands.apply {
//                        setGridLayout(3)
//
//
//                        adapter = BrandsItemAdapter(model.data, onClick, navController,false)
//                    }
//
//                }
//                model.company_name.isNotEmpty() -> {
//                    // Use another adapter when only company_name exists
//                    val singleItemList = arrayListOf(
//                        Brand(
//                            amount = 0, // Default or appropriate value
//                            category_id = model.id,
//                            company_image = "", // Default or get from model if available
//                            company_name = model.company_name,
//                            create_at = model.create_at,
//                            id = model.id,
//                            position = 0, // Default position
//                            update_at = model.update_at,
//                            user_id = 0, // Default user_id
//                            is_favourite = false
//                        )
//                    )
//                    rcvBrands.apply {
//                        setGridLayout(2)
////                        setVerticalLayout()
////                        setHorizontalLayout()
//                        adapter = RegularItemAdapter(singleItemList)
//                    }
////                    RegularItemAdapter(singleItemList) // Replace with the correct adapter
//                }
//                else -> {
//                    // Fallback: Use an empty adapter or a default one
//
//                }
//            }
//
//        }
//
//    }

    override fun getItemCount(): Int {
        return categories.size
    }

    fun refresh(newList: ArrayList<BrandCategories>) {
        categories.clear()
        categories.addAll(newList)
        notifyDataSetChanged()
    }

}