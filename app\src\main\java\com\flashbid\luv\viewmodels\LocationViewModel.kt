package com.flashbid.luv.viewmodels

import androidx.lifecycle.LiveData
import androidx.lifecycle.ViewModel
import com.flashbid.luv.models.remote.*
import com.flashbid.luv.network.Resource
import com.flashbid.luv.repositories.LocationRepository

class LocationViewModel(
    private val locationRepository: LocationRepository
) : ViewModel() {

    fun confirmUserLocation(
        beacon_location: String,
        beacon_uuid: String,
        user_location: UserLocationRequest.UserLocation
    ): LiveData<Resource<MessageResponse>> {
        return locationRepository.confirmUserLocation(
            UserLocationRequest(
                beacon_location, beacon_uuid,
                user_location
            )
        )
    }

    fun nearbyUser(): LiveData<Resource<FollowerResponse>> {
        return locationRepository.nearbyUser()
    }

}