package com.flashbid.luv.di

import com.flashbid.luv.data.local.prefModule
import com.flashbid.luv.network.networkModule
import com.flashbid.luv.network.remoteDataSourceModule
import com.flashbid.luv.repositories.BattleRepository
import com.flashbid.luv.repositories.BeaconRepository
import com.flashbid.luv.repositories.BrandsRepository
import com.flashbid.luv.repositories.CodeRepository
import com.flashbid.luv.repositories.CommunityRepository
import com.flashbid.luv.repositories.GiftRepository
import com.flashbid.luv.repositories.LocationRepository
import com.flashbid.luv.repositories.StoryRepository
import com.flashbid.luv.repositories.TransactionRepository
import com.flashbid.luv.repositories.UserRepository
import com.flashbid.luv.viewmodels.AlertsViewModel
import com.flashbid.luv.viewmodels.BattleViewModel
import com.flashbid.luv.viewmodels.BeaconViewModel
import com.flashbid.luv.viewmodels.BrandsViewModel
import com.flashbid.luv.viewmodels.CodesViewModel
import com.flashbid.luv.viewmodels.CommunityViewModel
import com.flashbid.luv.viewmodels.GiftsViewModel
import com.flashbid.luv.viewmodels.InAppViewModel
import com.flashbid.luv.viewmodels.LocationViewModel
import com.flashbid.luv.viewmodels.StoryViewModel
import com.flashbid.luv.viewmodels.TransactionViewModel
import com.flashbid.luv.viewmodels.UserViewModel
import org.koin.androidx.viewmodel.dsl.viewModel
import org.koin.core.module.Module
import org.koin.dsl.module

val viewModules: Module = module {
    viewModel { AlertsViewModel(get()) }
    viewModel { BattleViewModel(get(), get()) }
    viewModel { BrandsViewModel(get()) }
    viewModel { CodesViewModel(get()) }
    viewModel { CommunityViewModel(get()) }
    viewModel { GiftsViewModel(get()) }
    viewModel { InAppViewModel(get()) }
    viewModel { LocationViewModel(get()) }
    viewModel { StoryViewModel(get()) }
    viewModel { TransactionViewModel(get()) }
    viewModel { UserViewModel(get()) }
    viewModel { BeaconViewModel(get()) }
}

val repoModules: Module = module {
    single { BattleRepository(get()) }
    single { BrandsRepository(get()) }
    single { CodeRepository(get()) }
    single { CommunityRepository(get()) }
    single { GiftRepository(get()) }
    single { LocationRepository(get()) }
    single { StoryRepository(get()) }
    single { TransactionRepository(get()) }
    single { UserRepository(get()) }
    single { BeaconRepository(get()) }
}

val koinModules = listOf(
    prefModule,
    networkModule,
    remoteDataSourceModule,
    viewModules,
    repoModules
)