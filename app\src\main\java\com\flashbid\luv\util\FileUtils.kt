package com.flashbid.luv.util

import android.Manifest
import android.graphics.Bitmap
import android.media.MediaScannerConnection
import android.util.Log
import androidx.fragment.app.Fragment
import com.karumi.dexter.Dexter
import com.karumi.dexter.MultiplePermissionsReport
import com.karumi.dexter.PermissionToken
import com.karumi.dexter.listener.multi.MultiplePermissionsListener
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.util.*

/**
 * <AUTHOR> Khalid
 */
object FileUtils {
    fun Fragment.requestGalleryPermissions() {
        Dexter.withActivity(requireActivity())
            .withPermissions(
                Manifest.permission.WRITE_EXTERNAL_STORAGE,
                Manifest.permission.READ_EXTERNAL_STORAGE,
                Manifest.permission.CAMERA
            )
            .withListener(object : MultiplePermissionsListener {
                override fun onPermissionsChecked(report: MultiplePermissionsReport) {
                }

                override fun onPermissionRationaleShouldBeShown(
                    permissions: List<com.karumi.dexter.listener.PermissionRequest?>?,
                    token: PermissionToken
                ) {
                    token.continuePermissionRequest()
                }
            }).withErrorListener {

            }
            .onSameThread()
            .check()
    }

    fun Fragment.saveImage(myBitmap: Bitmap): String {
        val bytes = ByteArrayOutputStream()
        myBitmap.compress(Bitmap.CompressFormat.JPEG, 100, bytes)
        val localStorage: File? = activity?.getExternalFilesDir(null)
        val storagePath = localStorage?.absolutePath
        val rootPath = "$storagePath/luv"
        val root = File(rootPath)
        if (!root.mkdirs()) {
            Log.i("Test", "This path is already exist: " + root.absolutePath)
        }
        try {
            val f = File("$root/${Calendar.getInstance().timeInMillis}.jpeg")
            f.createNewFile()
            val fo = FileOutputStream(f)
            fo.write(bytes.toByteArray())
            MediaScannerConnection.scanFile(
                context,
                arrayOf(f.path),
                arrayOf("image/jpeg", "image/jpg"),
                null
            )
            fo.close()
            return f.absolutePath
        } catch (e1: IOException) {
            e1.printStackTrace()
        }
        return ""
    }
}