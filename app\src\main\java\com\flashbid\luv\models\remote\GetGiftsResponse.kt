package com.flashbid.luv.models.remote

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

data class GetGiftsResponse(
    val error: Boolean,
    val list: List<ItemGift>
)

@Parcelize
data class ItemGift(
    val amount: Int? = 0,
    val create_at: String?,
    val id: Int,
    val counter: Int,
    val image: String?,
    val name: String,
    val status: Int?,
    val update_at: String?,
    val message: Int?=0
) : Parcelable {

}
