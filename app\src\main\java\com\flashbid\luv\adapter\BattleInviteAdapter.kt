package com.flashbid.luv.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.flashbid.luv.R
import com.flashbid.luv.databinding.ItemAlertBinding
import com.flashbid.luv.databinding.ItemInviteBinding
import com.flashbid.luv.extensions.getTimeInAgo
import com.flashbid.luv.util.loadImageFromUrl
import com.flashbid.luv.extensions.setOnClickWithDebounce
import com.flashbid.luv.models.battle.GiftModel
import com.flashbid.luv.models.battle.Model
import com.flashbid.luv.models.remote.AlertModel

class BattleInviteAdapter(private val inviteList: ArrayList<Model>) :
    RecyclerView.Adapter<BattleInviteAdapter.ViewHolder>() {

    inner class ViewHolder(val binding: ItemInviteBinding) :
        RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val itemView =
            ItemInviteBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(itemView)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val model = inviteList[position]

        with(holder.binding) {
            tvUserName.text = model.name
            tvUsernameId.text = model.id
        }

    }

    override fun getItemCount(): Int {
        return inviteList.size
    }
//
//    fun refresh(newList: ArrayList<AlertModel>) {
//        list.clear()
//        list.addAll(newList)
//        notifyDataSetChanged()
//    }

}