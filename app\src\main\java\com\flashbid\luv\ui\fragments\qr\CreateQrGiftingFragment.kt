package com.flashbid.luv.ui.fragments.qr

import android.content.ContentValues
import android.os.Bundle
import android.util.Log
import android.view.View
import androidx.core.widget.doAfterTextChanged
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.flashbid.luv.R
import com.flashbid.luv.adapter.GiftRangeAdapter
import com.flashbid.luv.databinding.FragmentCreateQrGiftingBinding
import com.flashbid.luv.extensions.*
import com.flashbid.luv.models.remote.CodeDetail
import com.flashbid.luv.models.remote.SponsoredResponse
import com.flashbid.luv.network.Status
import com.flashbid.luv.util.setArrayAdapter
import com.flashbid.luv.viewmodels.CodesViewModel
import org.koin.androidx.viewmodel.ext.android.viewModel

class CreateQrGiftingFragment : Fragment(R.layout.fragment_create_qr_gifting) {

    private val binding by viewBinding(FragmentCreateQrGiftingBinding::bind)
    private val viewModel: CodesViewModel by viewModel()
    private var duration: Int = -1
    private var giftRangeId: Int = -1
    private var numScan: Int = -1
    private var sponsoredID: Int = 0
    private val args by navArgs<CreateQrGiftingFragmentArgs>()

    private val durationList by lazy {
        arrayListOf<Pair<String, Int>>().apply {
            add(Pair(getString(R.string.days), 0))
            add(Pair(getString(R.string.weeks), 1))
            add(Pair(getString(R.string.months), 2))
            add(Pair(getString(R.string.years), 3))
        }
    }

    private val plansList = arrayListOf<Pair<String, Int>>().apply {
        add(Pair("1 - 10", 0))
        add(Pair("1 - 20", 1))
        add(Pair("1 - 50", 2))
        add(Pair("1 - 100", 3))
    }

    private val rangeAdapter by lazy {
        GiftRangeAdapter(plansList) {
            giftRangeId = plansList[it].second
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.imageView.setOnClickWithDebounce {
            findNavController().popBackStack()
        }

        setPlansList()
        setDurationList()
        setScansField()
        getSponsored()

        if (args.codeDetail != null) {
            binding.textView.text = getString(R.string.edit_luv_chest)
            setDefaultData(args.codeDetail!!)
        }

        binding.btnNext.setOnClickWithDebounce {
            if (duration != -1 && numScan != -1 && giftRangeId != -1) {
                if (args.codeDetail != null) {
                    updateCode(
                        duration,
                        binding.cbNeedKey.isChecked.convertToInt(),
                        numScan,
                        giftRangeId
                    )
                } else {
                    createCode(
                        duration,
                        binding.cbNeedKey.isChecked.convertToInt(),
                        numScan,
                        giftRangeId
                    )
                }
            } else snackBar(getString(R.string.fill_all_details))
        }

    }

    private fun setDefaultData(codeDetail: CodeDetail) {
        val planIndex = plansList.indexOf(plansList.find { it.second == codeDetail.plan })
        rangeAdapter.setIndex(planIndex)

        giftRangeId = plansList[planIndex].second

        val found = durationList.find { it.second == args.codeDetail?.duration }
        duration = found?.second ?: -1
        binding.acDays.setText(found?.first, false)

        binding.acScans.setText(args.codeDetail?.num_scan.toString())
        numScan = args.codeDetail?.num_scan ?: -1

        binding.cbNeedKey.isChecked = args.codeDetail?.need_key == 1
    }

    private fun setPlansList() {
        binding.rcvRange.apply {
            setGridLayout(4)
            adapter = rangeAdapter
        }
    }

    private fun setScansField() {
        binding.acScans.doAfterTextChanged {
            if (!it.isNullOrEmpty()) numScan = it.toString().toInt()
        }
    }

    private fun setSponsoredList(list: ArrayList<SponsoredResponse.Sponsored>) {
        val displayList = arrayListOf(getString(R.string.no_sponsor)) + list.map { it.company_name }

        binding.edtSponsored.setArrayAdapter(displayList as ArrayList<String>)

        // Default selection
        if (args.codeDetail == null) {
            sponsoredID = 0
            binding.edtSponsored.setText(getString(R.string.no_sponsor), false)
        } else {
            // If editing, try to preselect sponsor
            val found = list.find { it.company_name == args.codeDetail?.sponsor_company_name }
            sponsoredID = found?.user_id ?: 0
            binding.edtSponsored.setText(found?.company_name ?: getString(R.string.no_sponsor), false)
        }

        binding.edtSponsored.setOnItemClickListener { _, _, position, _ ->
            sponsoredID = if (position == 0) 0 else list[position - 1].user_id
        }
    }



    private fun setDurationList() {
        binding.acDays.setArrayAdapter(durationList.map { it.first } as ArrayList<String>)

        binding.acDays.setOnItemClickListener { _, _, position, _ ->
            duration = durationList[position].second
        }
    }

    private fun getSponsored() {
        viewModel.getSponsored().observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(it.message ?: getString(R.string.something_went_wrong, getString(R.string.try_again)))
                Status.LOADING -> {
                }
                Status.SUCCESS -> {
                    it.data?.let { it1 -> setSponsoredList(it1.message) }
                    //Log.d(ContentValues.TAG, "createCode: ${it.data?.message.toString()}")
                    //findNavController().popBackStack()
                }
            }
        }
    }

    private fun createCode(
        duration: Int,
        need_key: Int,
        num_scan: Int,
        plan: Int
    ) {
        viewModel.createCode(duration, need_key, num_scan, plan, sponsoredID).observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(it.message ?: getString(R.string.something_went_wrong, getString(R.string.try_again)))
                Status.LOADING -> {
                    snackBar(getString(R.string.creating_qr))
                }
                Status.SUCCESS -> {
                    Log.d(ContentValues.TAG, "createCode: ${it.data?.message.toString()}")
                    findNavController().popBackStack()
                }
            }
        }
    }



    private fun updateCode(
        duration: Int,
        need_key: Int,
        num_scan: Int,
        plan: Int
    ) {
        args.codeDetail?.qrcode?.let {
            viewModel.updateCodeDetail(
                it,
                duration,
                need_key,
                num_scan,
                plan,
                sponsoredID
            ).observe(viewLifecycleOwner) {
                when (it.status) {
                    Status.ERROR -> snackBar(it.message ?: getString(R.string.something_went_wrong, getString(R.string.try_again)))
                    Status.LOADING -> {
                    }

                    Status.SUCCESS -> {
                        Log.d(ContentValues.TAG, "createCode: ${it.data?.message.toString()}")
                        findNavController().popBackStack()
                    }
                }
            }
        }
    }

}