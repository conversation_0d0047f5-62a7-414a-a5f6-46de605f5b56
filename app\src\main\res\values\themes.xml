<resources>
    <style name="AppTheme" parent="Theme.Material3.Light.NoActionBar">
        <item name="colorPrimary">@color/md_theme_light_primary</item>
        <item name="colorOnPrimary">@color/md_theme_light_onPrimary</item>
        <item name="colorPrimaryContainer">@color/md_theme_light_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_light_onPrimaryContainer</item>
        <item name="colorSecondary">@color/md_theme_light_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_light_onSecondary</item>
        <item name="colorSecondaryContainer">@color/md_theme_light_secondaryContainer</item>
        <item name="colorOnSecondaryContainer">@color/md_theme_light_onSecondaryContainer</item>
        <item name="colorTertiary">@color/md_theme_light_tertiary</item>
        <item name="colorOnTertiary">@color/md_theme_light_onTertiary</item>
        <item name="colorTertiaryContainer">@color/md_theme_light_tertiaryContainer</item>
        <item name="colorOnTertiaryContainer">@color/md_theme_light_onTertiaryContainer</item>
        <item name="colorError">@color/md_theme_light_error</item>
        <item name="colorErrorContainer">@color/md_theme_light_errorContainer</item>
        <item name="colorOnError">@color/md_theme_light_onError</item>
        <item name="colorOnErrorContainer">@color/md_theme_light_onErrorContainer</item>
        <item name="android:colorBackground">@color/md_theme_light_background</item>
        <item name="colorOnBackground">@color/md_theme_light_onBackground</item>
        <item name="colorSurface">@color/md_theme_light_surface</item>
        <item name="colorOnSurface">@color/md_theme_light_onSurface</item>
        <item name="colorSurfaceVariant">@color/md_theme_light_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/md_theme_light_onSurfaceVariant</item>
        <item name="colorOutline">@color/md_theme_light_outline</item>
        <item name="colorOnSurfaceInverse">@color/md_theme_light_inverseOnSurface</item>
        <item name="colorSurfaceInverse">@color/md_theme_light_inverseSurface</item>
        <item name="colorPrimaryInverse">@color/md_theme_light_inversePrimary</item>
        <item name="android:fontFamily">@font/medium</item>
        <item name="android:textColor">@color/color_text_light</item>
    </style>

    <style name="h1" parent="@android:style/Widget.TextView">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">30sp</item>
        <item name="android:fontFamily">@font/semibold</item>
    </style>

    <style name="h2" parent="@android:style/Widget.TextView">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">24sp</item>
        <item name="android:fontFamily">@font/semibold</item>
    </style>

    <style name="h3" parent="@android:style/Widget.TextView">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">18sp</item>
        <item name="android:fontFamily">@font/medium</item>
    </style>

    <style name="subtitle" parent="@android:style/Widget.TextView">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">16sp</item>
        <item name="android:fontFamily">@font/semibold</item>
    </style>

    <style name="text" parent="@android:style/Widget.TextView">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">14sp</item>
        <item name="android:fontFamily">@font/medium</item>
    </style>

    <style name="small" parent="@android:style/Widget.TextView">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">12sp</item>
        <item name="android:fontFamily">@font/medium</item>
    </style>

    <style name="button">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">54dp</item>
        <item name="android:textSize">14sp</item>
        <item name="android:minWidth">200dp</item>
        <item name="android:fontFamily">@font/medium</item>
        <item name="android:background">@drawable/bg_button_primary</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:textColor">#ffffff</item>
    </style>

    <style name="outlinedButton" parent="TextAppearance.AppCompat.Widget.Button">
        <item name="android:layout_width">200dp</item>
        <item name="android:layout_height">48dp</item>
        <item name="android:textSize">14sp</item>
        <item name="android:fontFamily">@font/medium</item>
        <item name="android:background">@drawable/bg_button_outlined</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:textColor">#000000</item>
    </style>

    <style name="grayButton" parent="TextAppearance.AppCompat.Widget.Button">
        <item name="android:layout_width">200dp</item>
        <item name="android:layout_height">48dp</item>
        <item name="android:textSize">14sp</item>
        <item name="android:fontFamily">@font/medium</item>
        <item name="android:background">@drawable/bg_button_gray</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:textColor">@color/gray</item>
    </style>

    <style name="blueButton" parent="TextAppearance.AppCompat.Widget.Button">
        <item name="android:layout_width">200dp</item>
        <item name="android:layout_height">48dp</item>
        <item name="android:textSize">14sp</item>
        <item name="android:fontFamily">@font/medium</item>
        <item name="android:background">@drawable/bg_button_gray</item>
        <item name="android:backgroundTint">@color/semiblue</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:textColor">@color/blue</item>
        <item name="android:elevation">0dp</item>
    </style>

    <style name="CustomActiveIndicatorStyle">
        <item name="shapeAppearanceOverlay">@style/CustomActiveIndicatorShape</item>
    </style>

    <style name="CustomActiveIndicatorShape">
        <item name="background">@null</item>
    </style>

</resources>
