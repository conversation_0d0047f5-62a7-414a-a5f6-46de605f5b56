<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.fragments.alerts.AlertsFragment">

    <TextView
        android:id="@+id/textView20"
        style="@style/h2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_20sdp"
        android:text="@string/alerts"
        android:textSize="26sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/textView21"
        style="@style/subtitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:maxLines="1"
        android:text="@string/network_notifications"
        android:textColor="@color/gray"
        app:layout_constraintStart_toStartOf="@+id/textView20"
        app:layout_constraintTop_toBottomOf="@+id/textView20" />

    <LinearLayout
        android:id="@+id/llNoAlerts"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:layout_marginTop="@dimen/_50sdp"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textView21">

        <ImageView
            android:layout_width="@dimen/_60sdp"
            android:layout_height="@dimen/_60sdp"
            android:src="@drawable/ic_alert"
            tools:ignore="ContentDescription" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_10sdp"
            android:text="@string/you_have_no_notifications_nso_far"
            android:textAlignment="center"
            android:textColor="#444484" />

    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rcvNewAlerts"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_25sdp"
        android:background="@drawable/bg_round_corners"
        android:paddingTop="@dimen/_10sdp"
        android:paddingBottom="@dimen/_5sdp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textView21"
        tools:itemCount="2"
        tools:listitem="@layout/item_alert" />

    <TextView
        android:id="@+id/tvTagNew"
        style="@style/small"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_20sdp"
        android:layout_marginTop="@dimen/_25sdp"
        android:background="@drawable/bg_round_red_grad"
        android:paddingHorizontal="@dimen/_10sdp"
        android:paddingVertical="5dp"
        android:text="NEW"
        android:textColor="@color/white"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@+id/rcvNewAlerts"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textView21" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rcvReadAlerts"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:paddingTop="@dimen/_10sdp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/rcvNewAlerts" />


</androidx.constraintlayout.widget.ConstraintLayout>