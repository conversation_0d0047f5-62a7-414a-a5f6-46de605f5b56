package com.flashbid.luv.models.remote

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

data class UserDetailsResponse(
    val error: Boolean, val message: Message
) {
    data class Message(
        val daily_top_gift: Int?,
        val favourites: ArrayList<Favourite>?,
        val followers: String?,
        val following: String?,
        val global_ranking: String?,
        val is_followed: Int?,
        val top_sent_gift: String?,
        val total_diamonds_received: String?,
        val total_luv_sent: String?,
        val user_details: UserDetails?,
        val first_name: String?,
        val industry: String?,
        val last_name: String?,
        val photo: String?,
        val bio: String?,
        val user_id: Int?,
        val show_following: Int?,
        val show_follower: Int?,
        val id: Int?,
        val is_company: Int?,
        val is_fav: Boolean? = false,
        val username: String?,
        val age: String?,
        val gender: String
    )
}

@Parcelize
data class UserDetails(
    val bio: String? = "",
    val company_image: String? = "",
    val is_company: Int? = -1,
    val company_name: String? = "",
    val first_name: String?,
    val industry: String? = "",
    val last_name: String?,
    val photo: String?,
    val user_id: Int?,
    val id: Int?,
    val username: String?,
    val user_image: String? = "",
    val is_followed: Int? = -1,
    val is_fav: Boolean? = false,
    val show_following: Int? = -1,
    val show_follower: Int? = -1,
    val website: String? = ""
) : Parcelable
data class Favourite(
    val brand_user_id: Int?,
    val company_image: String?,
    val company_name: String?,
    val total_hearts_sent: String?
)