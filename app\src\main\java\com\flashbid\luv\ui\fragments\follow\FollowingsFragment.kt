package com.flashbid.luv.ui.fragments.follow

import android.os.Bundle
import android.view.View
import android.view.inputmethod.EditorInfo
import androidx.fragment.app.Fragment
import com.flashbid.luv.R
import com.flashbid.luv.adapter.FollowsAdapter
import com.flashbid.luv.databinding.FragmentFollowingsBinding
import com.flashbid.luv.extensions.hide
import com.flashbid.luv.extensions.setVerticalLayout
import com.flashbid.luv.extensions.show
import com.flashbid.luv.extensions.snackBar
import com.flashbid.luv.models.remote.UserDetails
import com.flashbid.luv.network.Status
import com.flashbid.luv.viewmodels.UserViewModel
import com.flashbid.luv.extensions.viewBinding
import org.koin.androidx.viewmodel.ext.android.viewModel

class FollowingsFragment :
    Fragment(R.layout.fragment_followings) {

    private lateinit var onSelectCallback: (UserDetails) -> Unit
    private val binding by viewBinding(FragmentFollowingsBinding::bind)
    private val list: ArrayList<UserDetails> = ArrayList()
    private val followAdapter by lazy { FollowsAdapter(list, false, onSelectCallback) }
    private val viewModel: UserViewModel by viewModel()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.rcvFollowings.apply {
            setVerticalLayout()
            adapter = followAdapter
        }

        getFollowings("")

        binding.edtSearch.imeOptions = EditorInfo.IME_ACTION_SEARCH
        binding.edtSearch.setOnEditorActionListener { _, actionId, _ ->
            if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                val query = binding.edtSearch.text.toString()
                if (query.isNotEmpty()) searchUser(query)
                else getFollowings(query)
                return@setOnEditorActionListener true
            }
            false
        }
    }

    private fun getFollowings(query: String) {
        viewModel.getFollowings(query).observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(it.message ?: getString(R.string.something_went_wrong, getString(R.string.try_again)))
                Status.LOADING -> {}
                Status.SUCCESS -> {
                    list.clear()
                    list.addAll(it.data?.list ?: ArrayList())
                    followAdapter.refresh()
                }
            }
        }
    }

    private fun searchUser(query: String) {
        viewModel.searchUser(query).observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(it.message ?: getString(R.string.something_went_wrong, getString(R.string.try_again)))
                Status.LOADING -> {}
                Status.SUCCESS -> {
                    list.clear()
                    list.addAll(it.data?.list ?: ArrayList())
                    followAdapter.refresh()
                    if (followAdapter.itemCount > 0) {
                        binding.rcvFollowings.show()
                        binding.llNoItems.hide()
                    } else {
                        binding.rcvFollowings.hide()
                        binding.llNoItems.show()
                    }
                }
            }
        }
    }

    companion object {
        fun newInstance(onSelectCallback: (UserDetails) -> Unit): FollowingsFragment {
            val fragment = FollowingsFragment()
            fragment.onSelectCallback = onSelectCallback
            return fragment
        }
    }

}