<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/_5sdp"
    app:cardBackgroundColor="@android:color/transparent"
    app:cardCornerRadius="@dimen/_16sdp"
    app:cardElevation="0dp">

    <LinearLayout
        android:id="@+id/llBackground"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_edit"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="@dimen/_15sdp">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/ivImage"
            android:layout_width="@dimen/_40sdp"
            android:layout_height="@dimen/_40sdp"
            android:background="#f1f1f1"
            android:scaleType="centerCrop"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/imageView"
            app:shapeAppearanceOverlay="@style/ShapeAppearance.Material3.Corner.Full" />


        <TextView
            android:id="@+id/tvBrandName"
            android:layout_width="@dimen/_60sdp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_10sdp"
            android:ellipsize="end"
            android:maxLines="1"
            android:gravity="center"
            android:textAlignment="center"
            tools:text="McDonalds" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvLuv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/gray"
                tools:text="100k" />

            <ImageView
                android:id="@+id/ivHeart"
                android:layout_width="@dimen/_18sdp"
                android:layout_height="@dimen/_18sdp"
                android:layout_marginStart="@dimen/_3sdp"
                android:src="@drawable/heart_colored" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>