<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.fragments.operations.sendLuv.SendLuvFragment">

    <ImageView
        android:id="@+id/imageView"
        android:layout_width="@dimen/_30sdp"
        android:layout_height="@dimen/_30sdp"
        android:layout_margin="@dimen/_20sdp"
        android:src="@drawable/ic_back_btn"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/textView"
        style="@style/h3"
        android:text="@string/send_luv"
        app:layout_constraintBottom_toTopOf="@+id/tvInfo"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/imageView" />

    <TextView
        android:id="@+id/tvInfo"
        style="@style/text"
        android:layout_width="wrap_content"
        android:gravity="center"
        android:text="@string/balance"
        android:textColor="@color/gray"
        app:layout_constraintBottom_toBottomOf="@+id/imageView"
        app:layout_constraintEnd_toStartOf="@+id/tvBalance"
        app:layout_constraintStart_toStartOf="@+id/textView"
        app:layout_constraintTop_toBottomOf="@+id/textView" />

    <TextView
        android:id="@+id/tvBalance"
        style="@style/text"
        android:layout_width="wrap_content"
        android:drawablePadding="@dimen/_3sdp"
        android:gravity="center"
        android:paddingStart="5dp"
        android:textColor="@color/gray"
        app:drawableEndCompat="@drawable/heart_small"
        app:layout_constraintBottom_toBottomOf="@+id/tvInfo"
        app:layout_constraintEnd_toEndOf="@+id/textView"
        app:layout_constraintStart_toEndOf="@+id/tvInfo"
        app:layout_constraintTop_toTopOf="@+id/tvInfo"
        tools:text="500" />

    <View
        android:id="@+id/view"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginTop="@dimen/_20sdp"
        android:background="@color/semigray"
        app:layout_constraintEnd_toEndOf="@+id/textView9"
        app:layout_constraintStart_toStartOf="@+id/imageView"
        app:layout_constraintTop_toBottomOf="@+id/tvInfo" />

    <TextView
        android:id="@+id/tvError"
        style="@style/text"
        android:layout_width="0dp"
        android:layout_marginTop="@dimen/_20sdp"
        android:gravity="center"
        android:padding="@dimen/_5sdp"
        android:textColor="@color/gray"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@+id/textView9"
        app:layout_constraintStart_toStartOf="@+id/imageView"
        app:layout_constraintTop_toBottomOf="@+id/tvInfo" />

    <TextView
        android:id="@+id/textView10"
        style="@style/text"
        android:layout_width="wrap_content"
        android:layout_marginTop="@dimen/_20sdp"
        android:gravity="center"
        android:text="@string/your_gift"
        android:textColor="@color/gray"
        app:layout_constraintStart_toStartOf="@+id/view"
        app:layout_constraintTop_toBottomOf="@+id/view" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rcvGifts"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:padding="@dimen/_5sdp"
        app:layout_constraintBottom_toTopOf="@+id/btnNext"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textView10"
        tools:itemCount="1"
        tools:listitem="@layout/item_send_luv" />

    <TextView
        android:id="@+id/textView9"
        style="@style/text"
        android:layout_width="wrap_content"
        android:layout_marginEnd="@dimen/_20sdp"
        android:gravity="center"
        android:text="@string/_1_2"
        android:textColor="@color/gray"
        app:layout_constraintBottom_toBottomOf="@+id/tvInfo"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/textView" />

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btnNext"
        style="@style/button"
        android:layout_marginBottom="@dimen/_20sdp"
        android:text="@string/next"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>