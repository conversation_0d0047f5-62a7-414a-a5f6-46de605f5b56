package com.flashbid.luv.ui.fragments.profile.others

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.flashbid.luv.R
import com.flashbid.luv.adapter.FavoriteAdapter
import com.flashbid.luv.databinding.FragmentUserProfileBinding
import com.flashbid.luv.extensions.*
import com.flashbid.luv.models.remote.Favourite
import com.flashbid.luv.models.remote.UserDetails
import com.flashbid.luv.models.remote.UserDetailsResponse
import com.flashbid.luv.network.Status
import com.flashbid.luv.util.HistoryMapping
import com.flashbid.luv.util.ParamCode
import com.flashbid.luv.util.getColor
import com.flashbid.luv.util.loadImageFromUrl
import com.flashbid.luv.viewmodels.UserViewModel
import org.koin.androidx.viewmodel.ext.android.viewModel

class UserProfileFragment : Fragment(R.layout.fragment_user_profile) {

    private val userViewModel: UserViewModel by viewModel()
    private val binding by viewBinding(FragmentUserProfileBinding::bind)
    private val args by navArgs<UserProfileFragmentArgs>()
    private val favList: ArrayList<Favourite> = ArrayList()
    private val favAdapter by lazy { FavoriteAdapter(favList, this::onFavClick) }
    private var userDetail: UserDetails? = null

    private fun onFavClick(item: Favourite) {
        val action =
            UserProfileFragmentDirections.actionUserProfileFragmentSelf(item.brand_user_id?:0)
        findNavController().navigate(action)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.imageView.setOnClickWithDebounce {
            findNavController().popBackStack()
        }

        binding.rcvFav.apply {
            setHorizontalLayout()
            adapter = favAdapter
        }

        getUserDetails(args.userId)

    }

    private fun getUserDetails(id: Int) {
        userViewModel.getOtherUserData(id)
            .observe(viewLifecycleOwner) {
                when (it.status) {
                    Status.ERROR -> snackBar(it.message ?: getString(R.string.something_went_wrong, getString(R.string.try_again)))
                    Status.LOADING -> {}
                    Status.SUCCESS -> {
                        val data = it.data?.message!!
                        setUserData(data)
                        userDetail = data.user_details
                        binding.btnSend.setOnClickWithDebounce {
                            val direction =
                                UserProfileFragmentDirections.actionUserProfileFragmentToSendLuvFragment(
                                    userDetail
                                )
                            findNavController().navigate(direction)
                        }
                    }
                }
            }
    }

    @SuppressLint("SetTextI18n")
    private fun setUserData(message: UserDetailsResponse.Message?) {
        with(binding) {

            if ((message?.user_details?.show_following
                    ?: message?.show_following) == HistoryMapping.PRIVACY.PEOPLE_FOLLOW
            ) {
                llFollowing.setOnClickListener {
                    findNavController().navigate(UserProfileFragmentDirections.actionUserProfileFragmentToOtherUserFollowingsFragment(
                        args.userId.toString(), false
                    ))
                }
            }

            if ((message?.user_details?.show_follower
                    ?: message?.show_follower) == HistoryMapping.PRIVACY.PEOPLE_FOLLOW
            ) {
                llFollower.setOnClickListener {
                    findNavController().navigate(UserProfileFragmentDirections.actionUserProfileFragmentToOtherUserFollowingsFragment(
                        args.userId.toString(), true
                    ))
                }
            }

            textView.loadImageFromUrl(message?.photo ?: message?.user_details?.photo)
            tvName.text = message?.user_details?.first_name + " " + message?.user_details?.last_name
            textView17.text = "@${message?.user_details?.username ?: ""}"
            tvBio.apply {
                if (message?.user_details?.bio.isNullOrEmpty()) {
                    text = getString(R.string.no_bio)
                    setTextColor(getColor(R.color.redgradstart))
                } else {
                    text = message?.user_details?.bio
                    setTextColor(getColor(R.color.gray))
                }
            }
            tvFollowers.text = message?.followers
            tvFollowing.text = message?.following
            tvDiamonds.text = message?.total_diamonds_received?.toInt()?.shortenAmount()
            tvHearts.text = message?.total_luv_sent?.toInt()?.shortenAmount()
            tvRank.text = message?.global_ranking
            tvTopDaily.text = message?.daily_top_gift.toString()
            tvSentGift.text = message?.top_sent_gift

            if (message?.favourites.isNullOrEmpty()) {
                llNoFavs.hide()
                clFavs.hide()
            } else {
                favList.clear()
                favList.addAll(message?.favourites ?: ArrayList())
                favAdapter.refresh()
                llNoFavs.hide()
                clFavs.show()
                if ((message?.favourites?.size ?: 0) > 10) binding.textView19.show()
                else binding.textView19.hide()
            }

            if (message?.is_followed == ParamCode.USER_FOLLOWED) {
                btnFollow.setUnfollowState()
                tvFollow.text = getString(R.string.following)
                tvFollow.setTextColor(tvFollow.getColor(R.color.gray))
                btnFollow.setOnClickWithDebounce {
                    unfollowUser(userDetail?.id ?: userDetail?.user_id ?: -1)
                }
            } else {
                btnFollow.setFollowState()
                tvFollow.text = getString(R.string.follow)
                tvFollow.setTextColor(tvFollow.getColor(R.color.blue))
                btnFollow.setOnClickWithDebounce {
                    followUser(userDetail?.id ?: userDetail?.user_id ?: -1)
                }
            }

            setFavouriteData(
                message?.is_fav ?: message?.user_details?.is_fav ?: false,
                (message?.is_company ?: message?.user_details?.is_company ?: -1) == 1
            )
        }
    }

    private fun followUser(id: Int) {
        userViewModel.followUser(id).observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(it.message ?: getString(R.string.something_went_wrong, getString(R.string.try_again)))
                Status.LOADING -> {}
                Status.SUCCESS -> {
                    binding.btnFollow.setUnfollowState()
                    binding.tvFollow.text = getString(R.string.following)
                    binding.tvFollow.setTextColor(binding.tvFollow.getColor(R.color.gray))
                    binding.btnFollow.setOnClickWithDebounce {
                        unfollowUser(userDetail?.id ?: userDetail?.user_id ?: -1)
                    }
                }
            }
        }
    }

    private fun unfollowUser(id: Int) {
        userViewModel.unfollowUser(id).observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(it.message ?: getString(R.string.something_went_wrong, getString(R.string.try_again)))
                Status.LOADING -> {}
                Status.SUCCESS -> {
                    binding.btnFollow.setFollowState()
                    binding.tvFollow.text = getString(R.string.follow)
                    binding.tvFollow.setTextColor(binding.tvFollow.getColor(R.color.blue))
                    binding.btnFollow.setOnClickWithDebounce {
                        followUser(userDetail?.id ?: userDetail?.user_id ?: -1)
                    }
                }
            }
        }
    }

    private fun setFavouriteData(isFav: Boolean, isBrand: Boolean) {
        if (isBrand) binding.brandIcon.show() else binding.brandIcon.hide()

        with(binding.ivFav) {
            if (isBrand) {
                if (isFav) setImageResource(R.drawable.round_star_24)
                 else setImageResource(R.drawable.round_star_border_24)
                show()
                setOnClickWithDebounce {
                    if (isFav) {
                        rmvFav(args.userId.toString())
                        setImageResource(R.drawable.round_star_border_24)
                    }
                    else {
                        addFav(args.userId)
                        setImageResource(R.drawable.round_star_24)
                    }
                }
            } else hide()
        }
    }

    private fun addFav(id: Int) {
        userViewModel.addFavBrands(arrayListOf(id)).observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(it.message ?: getString(R.string.something_went_wrong, getString(R.string.try_again)))
                Status.LOADING -> {}
                Status.SUCCESS -> {
                    getUserDetails(args.userId)
                }
            }
        }
    }

    private fun rmvFav(id: String) {
        userViewModel.rmvFavBrands(id).observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(it.message ?: getString(R.string.something_went_wrong, getString(R.string.try_again)))
                Status.LOADING -> {}
                Status.SUCCESS -> {
                    getUserDetails(args.userId)
                }
            }
        }
    }

}