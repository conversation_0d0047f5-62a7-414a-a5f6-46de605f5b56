package com.flashbid.luv.util

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Path
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import android.view.animation.Animation
import android.view.animation.Transformation
import androidx.core.content.ContextCompat

class FloatingHeartView(context: Context, attrs: AttributeSet?) : View(context, attrs) {
    private val paint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = ContextCompat.getColor(context, android.R.color.holo_red_dark)
    }
    private val path = Path()

    init {
        setOnClickListener {
            startAnimation(FloatingHeartAnimation(this))
        }
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        canvas.drawPath(path, paint)
    }

    override fun onTouchEvent(event: MotionEvent?): Boolean {
        when (event?.action) {
            MotionEvent.ACTION_DOWN -> {
                path.reset()
                path.moveTo(event.x, event.y)
                path.addCircle(event.x, event.y, 50f, Path.Direction.CW)
                invalidate()
            }
        }
        return super.onTouchEvent(event)
    }

    private class FloatingHeartAnimation(private val view: View) : Animation() {
        var startY: Float = 0f

        init {
            duration = 1000
        }

        override fun applyTransformation(interpolatedTime: Float, t: Transformation?) {
            super.applyTransformation(interpolatedTime, t)
            val dy = startY * (1 - interpolatedTime)
            view.translationY = dy
            view.alpha = 1 - interpolatedTime
            view.invalidate()
        }

        override fun start() {
            startY = view.translationY
            super.start()
        }

        override fun reset() {
            super.reset()
            view.alpha = 1.0f
            view.translationY = 0f
        }
    }
}
