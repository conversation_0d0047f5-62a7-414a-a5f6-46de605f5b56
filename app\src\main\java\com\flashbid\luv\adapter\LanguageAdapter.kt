package com.flashbid.luv.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.flashbid.luv.databinding.ItemLanguageBinding
import com.flashbid.luv.extensions.setOnClickWithDebounce

class LanguageAdapter(
    val list: ArrayList<Pair<String, String>>,
    val onClick: (String) -> Unit
) : RecyclerView.Adapter<LanguageAdapter.ViewHolder>() {

    inner class ViewHolder(val binding: ItemLanguageBinding) :
        RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val itemView =
            ItemLanguageBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(itemView)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val model = list[position]

        with(holder.binding) {
            textView12.text = model.first
            root.setOnClickWithDebounce {
                onClick(model.second)
            }
        }
    }

    override fun getItemCount(): Int {
        return list.size
    }

    fun refresh(newList: ArrayList<Pair<String, String>>) {
        list.clear()
        list.addAll(newList)
        notifyDataSetChanged()
    }

}