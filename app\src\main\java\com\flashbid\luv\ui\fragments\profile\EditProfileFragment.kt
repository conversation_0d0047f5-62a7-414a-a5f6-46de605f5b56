package com.flashbid.luv.ui.fragments.profile

import android.app.DatePickerDialog
import android.content.Intent
import android.graphics.Bitmap
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import android.view.View
import androidx.core.widget.doAfterTextChanged
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import com.flashbid.luv.R
import com.flashbid.luv.data.local.AppPreferences
import com.flashbid.luv.databinding.FragmentEditProfileBinding
import com.flashbid.luv.databinding.SheetAccountDeleteBinding
import com.flashbid.luv.databinding.SheetImagePickBinding
import com.flashbid.luv.extensions.*
import com.flashbid.luv.models.remote.Industry
import com.flashbid.luv.models.remote.UpdateProfileRequest
import com.flashbid.luv.network.Status
import com.flashbid.luv.util.BaseLiveData
import com.flashbid.luv.util.FileUtils.requestGalleryPermissions
import com.flashbid.luv.util.FileUtils.saveImage
import com.flashbid.luv.util.RequestCodes
import com.flashbid.luv.util.loadImageFromUrl
import com.flashbid.luv.util.setArrayAdapter
import com.flashbid.luv.viewmodels.UserViewModel
import com.google.android.material.bottomsheet.BottomSheetDialog
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody.Companion.asRequestBody
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.io.File
import java.io.IOException
import java.util.*

class EditProfileFragment : Fragment(R.layout.fragment_edit_profile) {

    private val userViewModel: UserViewModel by viewModel()
    private val binding by viewBinding(FragmentEditProfileBinding::bind)
    private val pref by inject<AppPreferences>()
    private val industryList: ArrayList<Industry> = ArrayList()
    private var industry: Int? = null
    private var gender = ""

    private val genderList by lazy {
        arrayListOf<String>().apply {
            add(getString(R.string.male))
            add(getString(R.string.female))
            add(getString(R.string.other))
        }
    }

    private fun showDeleteAccountSheet() {
        val sheet = BottomSheetDialog(requireContext())
        val view = SheetAccountDeleteBinding.inflate(layoutInflater)

        view.imageView6.setOnClickWithDebounce {
            sheet.dismiss()
        }
        view.btnCancel.setOnClickWithDebounce {
            sheet.dismiss()
        }

        view.btnDelete.setOnClickWithDebounce {
            sheet.dismiss()
            deleteAccount()
        }


        sheet.setCancelable(true)
        sheet.setContentView(view.root)
        sheet.show()
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        requestGalleryPermissions()

        setBasicProfileData()

        showBrandFields()

        getIndustries()

        setGenderList()

        binding.edtAge.setOnClickListener {
            // Get current date
            val c = Calendar.getInstance()
            val year = c.get(Calendar.YEAR)
            val month = c.get(Calendar.MONTH)
            val day = c.get(Calendar.DAY_OF_MONTH)

            // Create the DatePickerDialog
            val datePickerDialog =
                DatePickerDialog(requireContext(), { _, selectedYear, selectedMonth, selectedDay ->
                    val formattedDate = "${selectedYear}/${selectedMonth + 1}/${selectedDay}"
                    binding.edtAge.setText(formattedDate)
                }, year, month, day)

            // Set the maximum selectable date to today (since it's a date of birth)
            datePickerDialog.datePicker.maxDate = System.currentTimeMillis()

            datePickerDialog.show()
        }

        binding.btnPickNew.setOnClickWithDebounce {
            showImageSheet()
        }
        binding.btnDeleteAccount.setOnClickWithDebounce {
            showDeleteAccountSheet()
        }
        binding.btnRemove.setOnClickWithDebounce {
            updateNewFields(userImage = "")
        }

        binding.imageView.setOnClickWithDebounce {
            findNavController().popBackStack()
        }

        binding.saveButton.setOnClickWithDebounce {
            pref.genders = binding.edtGender.textToString()
            pref.userAge = binding.edtAge.textToString()
            checkNames(binding.edtName.textToString())
        }

        binding.edtBio.doAfterTextChanged {
            binding.tvBioCount.text = (it?.length ?: 0).toString()
        }

        binding.edtUsername.disableSpaces()
        binding.edtWebsite.disableSpaces()
        binding.edtEmail.disableSpaces()

        binding.edtEmail.isEnabled = pref.isEmailAccount

    }

    private fun setGenderList() {
        binding.edtGender.setArrayAdapter(genderList)
        binding.edtGender.setOnItemClickListener { _, _, position, _ ->
            gender = genderList[position]
        }
    }

    private fun setIndustryList(list: ArrayList<Industry>) {
        industryList.apply {
            clear()
            addAll(list)
        }
        binding.edtIndustry.setArrayAdapter(industryList.map { it.name } as ArrayList<String>)
        binding.edtIndustry.setOnItemClickListener { _, _, position, _ ->
            industry = industryList[position].id
        }
    }

    private fun checkNames(name: String) {
        val words = name.trim().split("\\s+".toRegex()).toTypedArray()
        val firstName = words.first()
        val lastName = if (words.size > 1) words.last() else ""
        if (pref.firstName != firstName || pref.lastName != lastName) {
            updateName(firstName, lastName)
        } else checkNewData()
    }

    private fun checkNewData() {
        with(binding) {
            if (edtUsername.checkIsEmpty()) {
                snackBar(getString(R.string.fill_all_details))
            } else if (edtName.checkIsEmpty()) {
                snackBar(getString(R.string.fill_all_details))
            } else {
                if (pref.isBrandProfile) {
                    if (edtWebsite.text.isNotEmpty()) {
                        if (edtWebsite.text.toString().isValidUrl()) {
                            updateNewFields(
                                username = edtUsername.textToString(),
                                bio = edtBio.textToString(),
                                industry_id = industry,
                                category_id = industry,
                                website = edtWebsite.textToString()
                            )
                        } else snackBar(getString(R.string.invalid_link))
                    } else {
                        updateNewFields(
                            username = edtUsername.textToString(),
                            bio = edtBio.textToString(),
                            industry_id = industry,
                            category_id = industry,
                            website = edtWebsite.textToString()
                        )
                    }
                } else {
                    updateNewFields(
                        username = edtUsername.textToString(), bio = edtBio.textToString(),
                        age = edtAge.textToString(),
                        gender = edtGender.textToString()
                    )
                }
            }
        }
    }

    private fun showBrandFields() {
        if (pref.isBrandProfile) binding.llBrand.show()
        else binding.llBrand.hide()
    }

    private fun updateNewFields(
        username: String? = null,
        bio: String? = null,
        website: String? = null,
        category_id: Int? = null,
        userImage: String? = null,
        companyImage: String? = null,
        companyName: String? = null,
        industry_id: Int? = null,
        isCompany: Int? = null,
        age: String? = null,
        gender: String? = null
    ) {
        userViewModel.updatePreference(
            UpdateProfileRequest(
                UpdateProfileRequest.Payload(
                    username = username,
                    bio = bio,
                    website = website,
                    category_id = category_id,
                    user_image = userImage,
                    company_image = companyImage,
                    company_name = companyName,
                    industry_id = industry_id,
                    is_company = isCompany,
                    fcm_token = pref.fcmToken,
                    age = age,
                    gender = gender,
                )
            )

        ).observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> {
                    snackBar(
                        it.message ?: getString(
                            R.string.something_went_wrong, getString(R.string.try_again)
                        )
                    )
                }

                Status.LOADING -> {}
                Status.SUCCESS -> {
                    findNavController().popBackStack()
                }
            }
        }
    }

    private fun getIndustries() {
        userViewModel.getIndustries().observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(
                    it.message ?: getString(
                        R.string.something_went_wrong, getString(R.string.try_again)
                    )
                )

                Status.LOADING -> {}
                Status.SUCCESS -> {
                    setIndustryList(it.data?.list ?: ArrayList())
                }
            }
        }
    }

    private fun deleteAccount() {
        userViewModel.deleteAccount().observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(
                    it.message ?: getString(
                        R.string.something_went_wrong, getString(R.string.try_again)
                    )
                )

                Status.LOADING -> {}
                Status.SUCCESS -> {
                    BaseLiveData.isAuthorized.postValue(false)
                }
            }
        }
    }

    private fun updateName(firstName: String, lastName: String?) {
        userViewModel.updateProfile(firstName, lastName).observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(
                    it.message ?: getString(
                        R.string.something_went_wrong, getString(R.string.try_again)
                    )
                )

                Status.LOADING -> {}
                Status.SUCCESS -> {
                    checkNewData()
                }
            }
        }
    }

    private fun setBasicProfileData() {
        binding.ivImage.loadImageFromUrl(pref.photo)
        binding.tvName.text = pref.firstName + " " + pref.lastName
        binding.edtName.setText(pref.firstName + " " + pref.lastName)
        binding.edtBio.setText(pref.bio)
        binding.edtEmail.setText(pref.email)
        binding.edtUsername.setText(pref.userName)
        binding.edtWebsite.setText(pref.website)
        binding.edtIndustry.setText(pref.industry, false)
        binding.edtAge.setText(pref.userAge)
        binding.edtGender.setText(pref.genders)
        binding.btnRemove.apply {
            if (pref.photo.isNullOrEmpty()) hide()
            else show()
        }
    }

    private fun showImageSheet() {
        val sheet = BottomSheetDialog(requireContext())
        val view = SheetImagePickBinding.inflate(layoutInflater)

        view.imageView6.setOnClickWithDebounce {
            sheet.dismiss()
        }

        view.btnCamera.setOnClickWithDebounce {
            sheet.dismiss()
            startActivityForResult(
                Intent(MediaStore.ACTION_IMAGE_CAPTURE), RequestCodes.CAMERA_IMAGE
            )
        }

        view.btnGallery.setOnClickWithDebounce {
            sheet.dismiss()
            val intent = Intent().apply {
                type = "image/*"
                action = Intent.ACTION_GET_CONTENT
            }
            startActivityForResult(
                Intent.createChooser(intent, getString(R.string.select_image)), RequestCodes.GALLERY_IMAGE
            )
        }

        sheet.setCancelable(true)
        sheet.setContentView(view.root)
        sheet.show()
    }

    private fun uploadProfileImage(image: Bitmap) {
        val file = File(saveImage(image))
        val contentType = "multipart/form-data".toMediaTypeOrNull()
        val part = MultipartBody.Part.createFormData(
            "file", file.name, file.asRequestBody(contentType)
        )
        userViewModel.uploadPicture(part).observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> {
                    snackBar(
                        it.message ?: getString(
                            R.string.something_went_wrong, getString(R.string.try_again)
                        )
                    )
                }

                Status.LOADING -> {}
                Status.SUCCESS -> {
                    pref.photo = it.data?.url
                    binding.ivImage.loadImageFromUrl(pref.photo)
                    updateNewFields(userImage = pref.photo)
                }
            }
        }
    }

    @Deprecated("Deprecated in Java")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (data != null) {
            if (requestCode == RequestCodes.GALLERY_IMAGE) {
                val contentURI: Uri? = data.data
                try {
                    val bitmap =
                        MediaStore.Images.Media.getBitmap(activity?.contentResolver, contentURI)
                    uploadProfileImage(bitmap)
                } catch (e: IOException) {
                    e.printStackTrace()
                    snackBar(getString(R.string.something_went_wrong))
                }
            }
            if (requestCode == RequestCodes.CAMERA_IMAGE) {
                try {
                    val bitmap = data.extras?.get("data") as Bitmap
                    uploadProfileImage(bitmap)
                } catch (e: IOException) {
                    e.printStackTrace()
                    snackBar(getString(R.string.something_went_wrong))
                }
            }
        }
    }

}