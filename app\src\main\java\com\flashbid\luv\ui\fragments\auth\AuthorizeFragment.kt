package com.flashbid.luv.ui.fragments.auth

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.flashbid.luv.R
import com.flashbid.luv.databinding.FragmentAuthorizeBinding
import com.flashbid.luv.extensions.snackBar
import com.flashbid.luv.extensions.viewBinding
import com.flashbid.luv.network.Status
import com.flashbid.luv.viewmodels.CodesViewModel
import org.koin.androidx.viewmodel.ext.android.viewModel

class AuthorizeFragment : Fragment(R.layout.fragment_authorize) {

    private val binding by viewBinding(FragmentAuthorizeBinding::bind)
    private val codesViewModel: CodesViewModel by viewModel()
    private val args by navArgs<AuthorizeFragmentArgs>()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.btnNext.setOnClickListener {

            getDetails(args.scanValue)
        }

        binding.textView35.setOnClickListener {
            findNavController().popBackStack()
        }
    }


    @SuppressLint("MissingPermission")
    private fun getDetails(scannedValue: String) {
        val value = scannedValue.replace("login|", "")
        codesViewModel.scanCode(value).observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> {
                    snackBar(
                        it.message ?: getString(
                            R.string.something_went_wrong,
                            getString(R.string.try_again)
                        )
                    )
                }

                Status.LOADING -> {}
                Status.SUCCESS -> {

                    val qr_type = it.data?.qr_type

                    if (qr_type != null && qr_type == "login") {
                        //snackBar("Login Successfully.")
                        findNavController().popBackStack(R.id.homeFragment, false)
                    } else {
                        snackBar(getString(R.string.qr_not_found))
                    }
                }
            }
        }
    }

}


