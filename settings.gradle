pluginManagement {
    repositories {
        gradlePluginPortal()
        google()
        mavenCentral()
        maven { url 'https://jitpack.io' }
        maven { url 'https://mvnrepository.com/artifact/com.wonderkiln/camerakit'}
        maven {
            // r8 maven
            url = uri("https://storage.googleapis.com/r8-releases/raw")
        }
    }
}

dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
        maven { url 'https://jitpack.io' }
        jcenter()
        maven { url 'https://mvnrepository.com/artifact/com.wonderkiln/camerakit'}
    }
}
rootProject.name = "Luv"
include ':app'
