package com.flashbid.luv.viewmodels

import androidx.lifecycle.LiveData
import androidx.lifecycle.ViewModel
import com.flashbid.luv.models.remote.*
import com.flashbid.luv.network.Resource
import com.flashbid.luv.repositories.CommunityRepository

class CommunityViewModel(
    private val communityRepository: CommunityRepository
) : ViewModel() {

    fun getCommunityStats(): LiveData<Resource<CommunityStatsResponse>> {
        return communityRepository.getCommunityStats()
    }

}