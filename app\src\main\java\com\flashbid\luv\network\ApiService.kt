package com.flashbid.luv.network

import com.flashbid.luv.models.battle.TopGiftersResponse
import com.flashbid.luv.models.remote.*
import okhttp3.MultipartBody
import okhttp3.RequestBody
import retrofit2.Call
import retrofit2.Response
import retrofit2.http.*

interface ApiService {

    @POST("v2/api/lambda/login")
    suspend fun login(
        @Body request: LoginRequest
    ): Response<LoginResponse>

    @POST("v3/api/custom/luv/google/code/mobile")
    suspend fun loginGoogle(
        @Body request: LoginGoogleRequest
    ): Response<LoginResponse>

    @POST("v3/api/custom/luv/apple/login/mobile")
    suspend fun loginApple(
        @Body request: LoginAppleRequest
    ): Response<LoginResponse>

    @POST("v3/api/custom/luv/register")
    suspend fun register(
        @Body request: LoginRequest
    ): Response<LoginResponse>

    @POST("v2/api/lambda/verify/email")
    suspend fun verifyEmail(
        @Body request: EmailRequest
    ): Response<MessageResponse>

    @POST("v2/api/lambda/verify/code")
    suspend fun verifyEmailCode(
        @Body request: EmailCodeRequest
    ): Response<MessageResponse>

    @POST("v2/api/lambda/mobile/forgot")
    suspend fun forgotPassword(
        @Body request: EmailRequest
    ): Response<MessageResponse>

    @POST("v2/api/lambda/mobile/reset")
    suspend fun resetPassword(
        @Body request: ResetRequest
    ): Response<MessageResponse>

    @POST("v2/api/lambda/profile")
    suspend fun updateProfile(
        @Body request: NameRequest
    ): Response<MessageResponse>

    @POST("v2/api/lambda/preference")
    suspend fun updatePreference(
        @Body request: UpdateProfileRequest
    ): Response<MessageResponse>

    @GET("v3/api/custom/luv/transactions")
    suspend fun getCommunityTransactions(
        @Query("page") page: Int,
        @Query("limit") limit: Int
    ): Response<TransactionsResponse>

    @GET("v3/api/custom/luv/community/stat")
    suspend fun getCommunityStats(): Response<CommunityStatsResponse>

    @GET("v3/api/custom/luv/user/details")
    suspend fun getUserDetails(): Response<UserDetailsResponse>

    @GET("v3/api/custom/luv/user/stats")
    suspend fun getUserStats(): Response<UserDetailsResponse>

    @GET("v3/api/custom/luv/history")
    suspend fun getUserHistory(
        @Query("page") page: Int,
        @Query("limit") limit: Int
    ): Response<UserHistoryResponse>

    @GET("v3/api/custom/luv/balance")
    suspend fun getBalance(): Response<BalanceResponse>

    @GET("v3/api/custom/luv/qrcode")
    suspend fun getUserCodes(): Response<GetCodesResponse>

    @GET("v3/api/custom/luv/qrcode/{code}")
    suspend fun getCodeDetail(@Path("code") code: String, @Query("sponsor") sponsor: Boolean?): Response<CodeDetailResponse>

    @GET("v3/api/custom/luv/qrcode/{code}")
    suspend fun getCodeDetailSponsor(@Path("code") code: String, @Query("sponsor") sponsor: Boolean?): Response<CodeDetailResponse>

    @DELETE("v3/api/custom/luv/qrcode/{code}")
    suspend fun deleteCode(@Path("code") code: String): Response<MessageResponse>

    @GET("v3/api/custom/luv/qrcode/scan/{code}")
    suspend fun scanCode(@Path("code") code: String): Response<ScanResponse>

    @GET("v3/api/custom/luv/diamond-rates/{amount}")
    suspend fun getDiamondRate(@Path("amount") amount: String): Response<DiamondRateResponse>

    @PUT("v3/api/custom/luv/qrcode/{code}")
    suspend fun updateCodeDetail(
        @Path("code") code: String, @Body request: CreateCode
    ): Response<MessageResponse>

    @PUT("v4/api/records/history/{id}")
    suspend fun updateVideoStatus(
        @Path("id") id: Int, @Body request: UpdateVideoStatusRequest
    ): Response<MessageResponse>

    @POST("v3/api/custom/luv/qrcode/create")
    suspend fun     createCode(@Body request: CreateCode): Response<MessageResponse>

    @GET("v3/api/custom/luv/sponsors")
    suspend fun getSponsored(): Response<SponsoredResponse>

    @POST("v3/api/custom/luv/follow")
    suspend fun followUser(@Body request: FollowRequest): Response<MessageResponse>

    @POST("v3/api/custom/luv/unfollow")
    suspend fun unfollowUser(@Body request: FollowRequest): Response<MessageResponse>

    @GET("v3/api/custom/luv/gifts")
    suspend fun getGifts(): Response<GetGiftsResponse>

    @GET("v3/api/custom/luv/follower")
    suspend fun getFollowers(@Query("search") search: String?): Response<FollowerResponse>

    @GET("v3/api/custom/luv/following")
    suspend fun getFollowings(@Query("search") search: String?): Response<FollowerResponse>

    @GET("v3/api/custom/luv/search-user")
    suspend fun searchUser(@Query("search") search: String?): Response<FollowerResponse>

    @POST("v3/api/custom/luv/send")
    suspend fun sendLuv(@Body request: SendLuvRequest): Response<MessageResponse>

    @POST("v3/api/custom/luv/user/stats")
    suspend fun getOtherUserData(@Body request: UserIdRequest): Response<UserDetailsResponse>

    @GET("v3/api/custom/luv/alerts")
    suspend fun getAlerts(): Response<AlertsResponse>

    @GET("v3/api/custom/luv/categories")
    suspend fun getTopBrands(@Query("period") period: String, @Query("user") user: String): Response<BrandCategoryResponse>

        @GET("v3/api/custom/luv/brands-by-category")
    suspend fun getAllBrandsByCategory(): Response<AllBrandsResponse>

    @GET("v3/api/custom/luv/brands")
    suspend fun getAllBrands(): Response<AllBrandsResponse>

    @GET("v3/api/custom/luv/top10")
    suspend fun getAllBrands(
        @Query("period") period: String,
        @Query("category") category: Int,
    ): Response<CategoryBrandsResponse>

    @GET("v3/api/custom/luv/recharge")
    suspend fun getAllPlans(): Response<AllPlansResponse>

    @GET("v3/api/custom/luv/payout")
    suspend fun getPayoutData(): Response<PayoutDataResponse>

    @GET("v3/api/custom/luv/quest")
    suspend fun getQuestTime(): Response<QuestTimeResponse>

    @POST("v3/api/custom/luv/recharge")
    suspend fun rechargePlan(@Body request: RechargeRequest): Response<MessageResponse>

    @POST("v3/api/custom/luv/withdraw")
    suspend fun withdrawDiamonds(@Body request: WithdrawRequest): Response<MessageResponse>

    @Multipart
    @POST("v2/api/lambda/s3/upload")
    suspend fun uploadPicture(
        @Part file: MultipartBody.Part
    ): Response<UploadResponse>

    @POST("v3/api/custom/luv/qrcode/unlock-chest/{code}")
    suspend fun unlockChest(
        @Path("code") code: String, @Body request: UnlockCrateRequest
    ): Response<MessageResponse>

    @POST("v3/api/custom/luv/user/favourite")
    suspend fun addFavBrands(
        @Body request: AddFavBrandsRequest
    ): Response<MessageResponse>

    @DELETE("v3/api/custom/luv/user/favourite/{id}")
    suspend fun rmvFavBrands(
        @Path("id") id: String
    ): Response<MessageResponse>

    @GET("v3/api/custom/luv/crate/unlock/{code}")
    suspend fun unlockCrate(
        @Path("code") code: String,
        @Query("crate_type") crateType: String,
        @Query("is_bonus") isBonus: Int?,
    ): Response<OpenCrateResponse>

    @GET("v3/api/custom/luv/quest/daily")
    suspend fun collectDailyReward(): Response<MessageResponse>

    @GET("v3/api/custom/luv/deleteaccount")
    suspend fun deleteAccount(): Response<MessageResponse>

    @GET("v3/api/custom/luv/withdraw")
    suspend fun getWithdrawLimit(): Response<WeeklyLimitResponse>

    @GET("v3/api/custom/luv/user/random")
    suspend fun getRandomUser(): Response<RandomUserResponse>

    @POST("v1/api/rest/category/GETALL")
    suspend fun getIndustries(): Response<IndustriesResponse>

    @POST("v2/api/lambda/refresh_token")
    fun refreshToken(@Body request: RefreshRequest): Call<RefreshResponse>

    @POST("v3/api/custom/luv/quest/refer")
    suspend fun getReferralCode(): Response<ReferralCodeResponse>

    @GET("v3/api/custom/luv/history/read/{id}")
    suspend fun getHistoryDetail(
        @Path("id") id: String
    ): Response<HistoryDetailResponse>

    @GET("v3/api/custom/luv/following/{id}")
    suspend fun getCustomFollowing(
        @Path("id") id: String?
    ): Response<CustomFollowingResponse>

    @GET("v3/api/custom/luv/follower/{id}")
    suspend fun getCustomFollower(
        @Path("id") id: String?
    ): Response<CustomFollowingResponse>

    @POST("v3/api/custom/luv/brand/create-key-crate")
    suspend fun createKeyCrate(
        @Body request: CreateKeyRequest
    ): Response<MessageResponse>

    @POST("v3/api/custom/luv/qrcode/unlock-key-crate/{code}")
    suspend fun unlockKeyCrate(
        @Path("code") code: String, @Body request: UnlockKeyRequest
    ): Response<MessageResponse>

    @Multipart
    @POST("v3/api/custom/luv/stories")
    suspend fun createStory(
        @Part file: MultipartBody.Part,
        @Part("duration") duration: Int,
        @Part("receiver_user_id") receiverUserId: RequestBody,
        @Part("transaction_id") transactionId: RequestBody,
        @Part("view_type") viewType: RequestBody
    ): Response<MessageResponse>

    @GET("v3/api/custom/luv/stories")
    suspend fun getStory(): Response<StoriesResponse>

    @GET("v3/api/custom/luv/report_category")
    suspend fun getReports(): Response<ReportCategoryResponse>

    @POST("v3/api/custom/luv/report")
    @FormUrlEncoded
    suspend fun createReport(
        @Field("type") type: String,
        @Field("type_id") type_id: String,
        @Field("comment") comment: String,
        @Field("category") category: Int
    ): Response<MessageResponse>

    @GET("v3/api/custom/luv/own_stories")
    suspend fun getYourOwnStory(
        @Body request: YourOwnStoryRequest
    ): Response<StoriesResponse>

    @GET("v3/api/custom/luv/join-battle/{channel}")
    suspend fun joinLive(
        @Path("channel") channel: String,
        @Query("inviter") inviter: String,
    ): Response<BattleDetailsResponse>

    @POST("v3/api/custom/luv/battle-request-response")
    suspend fun battleResponse(
        @Body request: BattleResponseRequest
    ): Response<MessageResponse>

    @GET("v3/api/custom/luv/leave-battle")
    suspend fun leaveBattle(): Response<MessageResponse>

    @GET("v3/api/custom/luv/leave-battle/{channel}")
    suspend fun leaveBattleAudience(
        @Path("channel") channel: String,
        @Query("inviter") inviter: String,
    ): Response<BattleDetailsResponse>

    @POST("v3/api/custom/luv/request-battle")
    suspend fun battleRequest(
        @Body request: BattleResponseRequest
    ): Response<MessageResponse>

    @GET("v3/api/custom/luv/battle-details/{channel}")
    suspend fun getBattleDetails(
        @Path("channel") channel: String,
    ): Response<BattleDetailsResponse>

    @POST("v3/api/custom/luv/share-battle-invite")
    suspend fun shareInvite(
        @Body request: ShareInviteRequest
    ): Response<MessageResponse>

    @POST("v3/api/custom/luv/counter")
    suspend fun counter(
        @Body request: CounterRequest
    ): Response<MessageResponse>

    @POST("v3/api/custom/luv/create-store")
    suspend fun createStores(
        @Body request: CreateStoresRequest
    ): Response<MessageResponse>

    @POST("v3/api/custom/luv/confirm-user-location")
    suspend fun confirmUserLocation(
        @Body request: UserLocationRequest
    ): Response<MessageResponse>

    @GET("v3/api/custom/luv/stores")
    suspend fun stores(): Response<MessageResponse>

    @POST("v3/api/custom/luv/nearby-user")
    suspend fun nearbyUser(): Response<FollowerResponse>

    @POST("v3/api/custom/luv/battle-rematch")
    suspend fun battleRematch(
         @Body request: BattleRematchRequest
    ): Response<MessageResponse>

    @POST("v3/api/custom/luv/battle-send-gift")
    suspend fun sendGift(
        @Body request: SendGiftRequest
    ): Response<MessageResponse>

    @GET("v3/api/custom/luv/battle-result/{channel}")
    suspend fun battleResult(
        @Path("channel") channel: String,
    ): Response<BattleResultResponse>

    @GET("v3/api/custom/luv/battle-end/{channelId}")
    suspend fun endBattle(
        @Path("channelId") channelId: String,
    ): Response<MessageResponse>

    @GET("v3/api/custom/luv/battle-top-ten/{channelId}")
    suspend fun getTopGifts(
        @Path("channelId") channelId: String,
        @Query("user_id") user_id: Int,
    ): Response<TopGiftersResponse>

    @POST("v3/api/custom/luv/beacon/uid")
    fun beacon(
        @Body request: BeaconRequest
    ): Response<BeaconDetailResponse>

    @POST("v3/api/custom/luv/beacon/claim")
    suspend fun beaconClaim(
        @Body request: BeaconClaimRequest
    ): Response<MessageResponse>

    @GET("v3/api/custom/luv/bundle/brands")
    suspend fun getCustomBrands(): Response<CustomBrandResponse>

    @GET("v3/api/custom/luv/beacon/brands")
    suspend fun getCustomBeaconBrands(): Response<CustomBrandResponse>

    @GET("v4/api/records/crate_bundle")
    suspend fun getCustomBrandNames(
        @Query("filter") filter: Int,
    ): Response<CustomBrandNameResponse>
}