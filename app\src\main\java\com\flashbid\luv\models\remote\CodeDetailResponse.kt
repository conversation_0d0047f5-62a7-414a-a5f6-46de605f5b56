package com.flashbid.luv.models.remote

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

data class CodeDetailResponse(
    val error: <PERSON><PERSON>an,
    val message: CodeDetail
)

@Parcelize
data class CodeDetail(
    val create_at: String?,
    val duration: Int?,
    val id: Int,
    val key_phrase: String?,
    val need_key: Int?,
    val num_scan: Int?,
    val plan: Int?,
    val sponsor_id: Int?,
    val qrcode: String?,
    val sponsor_company_name: String?,
    val status: Int?,
    val update_at: String?
) : Parcelable
