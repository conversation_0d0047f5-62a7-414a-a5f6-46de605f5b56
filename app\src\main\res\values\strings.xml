<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">Luv</string>
    <string name="home">Home</string>
    <string name="alerts">Alerts</string>
    <string name="quests">Quests</string>
    <string name="scan">Scan</string>

    <string name="on_board_first_1">1/4</string>
    <string name="on_board_first_2">All we need\nis LUV</string>
    <string name="on_board_first_3">Send love to your favorite streamers, content creators or anyone anywhere</string>

    <string name="on_board_second_1">2/4</string>
    <string name="on_board_second_2">LUV is all\naround you</string>
    <string name="on_board_second_3">Earn LUV by visiting your favorite brands and scanning their LUV code or get a LUV Airdrop</string>

    <string name="on_board_third_1">3/4</string>
    <string name="on_board_third_2">There’s LUV\ninside!</string>
    <string name="on_board_third_3">Find LUV inside participating products, find the QR and scan!</string>

    <string name="on_board_fourth_1">4/4</string>
    <string name="on_board_fourth_2">LUV will be\na crypto coin</string>
    <string name="on_board_fourth_3">In the near future you will be able to convert the LUV in your account to a new LUV coin when we launch the LUV wallet and blockchain.</string>
    <string name="on_board_start">Start</string>
    <string name="on_board_Next">Next</string>
    <string name="on_board_skip">Skip</string>

    <string name="google">Google</string>
    <string name="sign_in_to_start">Sign in to start</string>
    <string name="terms_of_service_amp_privacy_policy"><![CDATA[Terms of service & Privacy policy]]></string>
    <string name="by_clicking_i_agree_to_the">By clicking, I agree to the</string>
    <string name="sign_up">Sign up</string>
    <string name="sign_in">Sign in</string>
    <string name="email">Email</string>
    <string name="password">Password</string>
    <string name="don_t_have_an_account">Don’t have an account?</string>
    <string name="next">Next</string>
    <string name="finish">Finish</string>
    <string name="your_email">Your email</string>
    <string name="your_name">Your name</string>
    <string name="confirm_password">Confirm password</string>
    <string name="username">Username</string>
    <string name="name">Name</string>
    <string name="didn_t_get_code">Didn\'t get code?</string>
    <string name="write_your_email">Write your email</string>
    <string name="email_confirm_code">Email confirmation code</string>
    <string name="password_must">Password must be at least 8 characters long,\ncontain one number &amp; symbol</string>
    <string name="password_empty">Password must not be empty</string>
    <string name="fields_empty">Fields must not be empty</string>
    <string name="resend_code">Resend Code</string>
    <string name="resend_code_again">You can resend again in 30 sec</string>
    <string name="password_creation">Password creation</string>
    <string name="special_not_allowed">Special symbols and spaces not allowed</string>
    <string name="forgot_password">Forgot Password?</string>
    <string name="apple">Apple</string>
    <string name="send_luv">Send LUV</string>
    <string name="your_gift">Your gift</string>
    <string name="_1_2">1/2</string>
    <string name="receiver">Receiver</string>
    <string name="send">Send</string>
    <string name="scan_code">Scan code</string>
    <string name="search">Search</string>
    <string name="select_receiver_randomly">Select receiver randomly</string>
    <string name="balance">Balance: </string>
    <string name="place_the_qr_code_in_front_nof_the_square">Place the QR code in front\nof the square</string>
    <string name="withdraw">Withdraw</string>
    <string name="payout_method">Payout method</string>
    <string name="paypal_link">PayPal link</string>
    <string name="currency">Currency</string>
    <string name="country">Country</string>
    <string name="paypal_account_must_be_created_with_nsame_email_as_user_email">Paypal account must be created with\nsame email as user email.</string>
    <string name="_2_2">2/2</string>
    <string name="submit">Submit</string>
    <string name="once_submitted_your_request_will_be_processed_nvia_paypal_paypal_fees_may_result">Once submitted your request will be processed via Paypal, paypal fees may result.</string>
    <string name="diamonds">Diamonds</string>
    <string name="weekly_limit_used">Weekly limit used:</string>
    <string name="you_don_t_have_enough_diamonds">You don\'t have enough Diamonds</string>
    <string name="create_gifting_qr">Create gifting QR</string>
    <string name="edit_gifting_qr">Edit gifting QR</string>
    <string name="edit_luv_chest">Edit LUV Chest</string>
    <string name="qr_gifting">QR: Gifting</string>
    <string name="qr_receiving">QR: Receiving</string>
    <string name="number_of_scans">Number of scans</string>
    <string name="per">Per</string>
    <string name="select_the_frequency_to_reward_users_when_they_scan_your_qr_for_example_1_per_day">Select the frequency to reward users when they scan your QR for example 1 per day</string>
    <string name="gift_range">Gift Range</string>
    <string name="generate_key_users_will_be_able_nto_use_code_only_with_it">Generate key: users will be able\nto use code only with it</string>
    <string name="edit">Edit</string>
    <string name="share">Share</string>
    <string name="delete_code">Delete code</string>
    <string name="favorites">Favorites</string>
    <string name="see_all">See all</string>
    <string name="following">Following</string>
    <string name="follow">Follow</string>
    <string name="followers">Followers</string>
    <string name="network_notifications">Network notifications</string>
    <string name="daily_rewards_for_you">Daily rewards for you</string>
    <string name="refer_friends">Refer friends</string>
    <string name="_500x_luv_each">Earn 200 LUV each</string>
    <string name="open_luv_crate">Open Luv crate</string>
    <string name="get_100_extra_luv">Scan a LUV crate daily for 10 extra LUV</string>
    <string name="send_luv_get_luv">Send LUV randomly to earn 5 LUV</string>
    <string name="send_get_luv">Send LUV get LUV</string>
    <string name="qr_scanning">QR Scanning</string>
    <string name="usd">USD</string>
    <string name="status">Status</string>
    <string name="date_and_time">Date and time</string>
    <string name="transaction_id">Transaction ID</string>
    <string name="gift_received">Gift received</string>
    <string name="from">From</string>
    <string name="recharge">Recharge</string>
    <string name="luvs">LUVs</string>
    <string name="payment_method">Payment Method</string>
    <string name="gift_sent">Gift sent</string>
    <string name="good_morning">Good morning</string>
    <string name="community_stats">Community Stats</string>
    <string name="my_codes">My Qr codes</string>
    <string name="my_balance">My Balance</string>
    <string name="top_gifter">Top gifter</string>
    <string name="luv_sent">LUV sent</string>
    <string name="top_gift">Top gift</string>
    <string name="receiving">Receiving</string>
    <string name="gifting">Gifting</string>
    <string name="password_nreset">Password\nreset</string>
    <string name="set_new_password">Set new password</string>
    <string name="invalid_email">Invalid Email</string>
    <string name="invalid_password">Invalid Password</string>
    <string name="new_code_sent">New Code Sent</string>
    <string name="key_input">Key: <font fgcolor="#000000"><b>%1$s</b></font></string>
    <string name="no_key"><b>No Key</b></string>
    <string name="share_this_code_and_tell_your_friends_nto_show_you_some_luv">Share this code and tell your friends\nto show you some LUV</string>
    <string name="username_x_time">➞ @%1$s • %2$s</string>
    <string name="gift_x_time">%1$s Sent • %2$s</string>
    <string name="sender_x_receiver">%1$s ➞ %2$s</string>
    <string name="username_x">%1$s</string>
    <string name="users_who_scan_your_code_will_nreceive_a_luv_gift_from_you">Users who scan your code will\nreceive a LUV gift from you.</string>
    <string name="daily_top_gift">Daily top gift</string>
    <string name="top_sent_gift">Top sent gift</string>
    <string name="global_rank">Global rank</string>
    <string name="add_bio">Add bio</string>
    <string name="try_again">Try again</string>
    <string name="no_bio">No bio</string>
    <string name="to">To</string>
    <string name="switch_to_brand_account">Switch to <font fgcolor="#000000">brand</font> account</string>
    <string name="switch_to_personal_account">Switch to <font fgcolor="#000000">personal</font> account</string>
    <string name="no_favorites_brands">No favorites brands</string>
    <string name="pick">Pick</string>
    <string name="edit_profile">Edit profile</string>
    <string name="reset_password">Reset password</string>
    <string name="cancel">Cancel</string>
    <string name="are_you_sure_you_nwant_to_log_out">Are you sure you\nwant to log out?</string>
    <string name="profile_image">Profile image</string>
    <string name="log_out">Log out</string>
    <string name="remove">Remove</string>
    <string name="pick_new">Pick new</string>
    <string name="save">Save</string>
    <string name="bio">Bio</string>
    <string name="delete_account">Delete account</string>
    <string name="danger_zone">Danger zone</string>
    <string name="delete">Delete</string>
    <string name="set_language">Set language</string>
    <string name="select_from_gallery">Open gallery</string>
    <string name="open_camera">Open camera</string>
    <string name="use_luvs_to_buy_and_send_gifts_to_anyone_with_the_luv_app">Use LUVs to buy and send gifts to anyone with the LUV App.</string>
    <string name="has_left_you_a_luv_crate_nenter_key_to_open">%1$s\'s LUV Chest, enter key to open.</string>
    <string name="has_left_you_no_key"><b>%1$s</b> has left you a LUV crate, collect your LUV!</string>
    <string name="has_sent">%1$s\'s LUV Chest, open to claim LUV Coins..</string>
    <string name="key">Key</string>
    <string name="open">Open</string>
    <string name="amount">Amount</string>
    <string name="buy">Buy</string>
    <string name="all_the_luv_gifts_that_you_receive_are_converted_into_diamonds">All the LUV gifts that you receive are converted into diamonds.</string>
    <string name="withdraw_your_diamonds_to_your_paypal_in_usd">Withdraw your diamonds to your paypal in USD.</string>
    <string name="no_sponsor">No sponsor</string>
    <string name="brand">Brand</string>
    <string name="industry">Industry</string>
    <string name="_100">/100</string>
    <string name="top_luvers">Top LUVers</string>
    <string name="awesome">Awesome</string>
    <string name="daily_luv">Daily LUV</string>
    <string name="collect_your_5_luv_for_nsending_luv_daily">Collect your <b><font fgcolor="#000000">5 LUV</font></b> for\nsending LUV Daily</string>
    <string name="collect">Collect</string>
    <string name="time">%1$s : %2$s : %3$s</string>
    <string name="website">Website</string>
    <string name="favorite_brands">Favorite brands</string>
    <string name="this_information">This information helps us get the most relevant\nbrands to you to become a part of LUV</string>
    <string name="done">Done</string>
    <string name="you_have_no_notifications_nso_far">You have no notifications\nso far</string>
    <string name="photo_library">Photo Library</string>
    <string name="skip">Skip</string>
    <string name="something_went_wrong">Something went wrong, %1$s</string>
    <string name="profile_picture_transition" translatable="false">profile_transition</string>
    <string name="user_profile_transition" translatable="false">user_profile_transition</string>
    <string name="login_transition" translatable="false">login_transition</string>
    <string name="signup_transition" translatable="false">signup_transition</string>
    <string name="reset_transition" translatable="false">reset_transition</string>
    <string name="daily">Daily</string>
    <string name="monthly">Monthly</string>
    <string name="just_now">just now</string>
    <string name="minutes_ago">%1$s minutes ago</string>
    <string name="hours_ago">%1$s hours ago</string>
    <string name="hour_ago">%1$s hour ago</string>
    <string name="day_ago">%1$s day ago</string>
    <string name="days_ago">%1$s days ago</string>
    <string name="week_ago">%1$s week ago</string>
    <string name="weeks_ago">%1$s weeks ago</string>
    <string name="month_ago">%1$s month ago</string>
    <string name="months_ago">%1$s months ago</string>
    <string name="year_ago">%1$s year ago</string>
    <string name="years_ago">%1$s years ago</string>
    <string name="show_me_some_love">Show me some love</string>
    <string name="evening">Good Evening</string>
    <string name="morning">Good Morning</string>
    <string name="afternoon">Good Afternoon</string>
    <string name="opened_chest">Opened Chest</string>
    <string name="fill_all_details">Please fill all the details</string>
    <string name="creating_qr">Creating QR Code..</string>
    <string name="days">Days</string>
    <string name="weeks">Weeks</string>
    <string name="months">Months</string>
    <string name="years">Years</string>
    <string name="qr_not_found">QR Code not found</string>
    <string name="collected">Reward Collected</string>
    <string name="checkout_luv_app">Checkout the LUV App - it lets you send, receive and show LUV ❤️\n%1$s</string>
    <string name="invalid_link">Invalid website link</string>
    <string name="invalid_amount">Invalid amount</string>
    <string name="no_balance">No enough balance</string>
    <string name="invalid_paypal">PayPal link is invalid</string>
    <string name="invalid_email_password">Invalid email or password</string>
    <string name="agree_to_terms">You must agree to terms and conditions</string>
    <string name="password_mismatch">Password doesn\'t match</string>
    <string name="empty_password">Password is empty</string>
    <string name="completed">Completed</string>
    <string name="pending">PENDING</string>
    <string name="void_trans">VOID</string>
    <string name="suspended">SUSPENDED</string>
    <string name="change_language">Change language</string>
    <string name="no_transactions_nyet">No transactions\nyet</string>
    <string name="find_other_users_nopen_luv_crates_nopen_luv_chests">Find other users\nOpen LUV Crates\nOpen LUV Chests</string>
    <string name="congratulations">Congratulations!</string>
    <string name="top_gifte">Top Gifter</string>
    <string name="you_get_20_luv_when_you_spread_luv_by_inviting_others"><b><font fgcolor="#000000">%1$s</font></b> has joined the LUV Network using your referral code, you\'ve earned <b><font fgcolor="#000000">20 LUV</font></b> for spreading LUV.</string>
    <string name="invite_more">Invite more</string>
    <string name="username_s_luv_chest">%1$s\'s LUV Chest</string>
    <string name="found_nothing_by_your_search">Found nothing by your search</string>
    <string name="with_luv_from">With LUV from\n<b>@%1$s</b></string>
<!--    <string name="sponsored_by">Sponsored by:\n<b>@%1$s</b></string>-->
    <string name="gift_success">Gift sent successfully</string>
    <string name="withdraw_success">Withdrawal submitted successfully</string>
    <string name="recharge_success">Recharge successful</string>
    <string name="auth_login">Authorize login to LUV Network Web?</string>
    <string name="confirm">Confirm</string>
    <string name="authorize_login_to_nluv_network_web">Authorize login to \nLUV Network Web?</string>
    <string name="referral_award">Referral award</string>
    <string name="luv_crate">LUV crate</string>
    <string name="collect_luv_chest">Collect LUV Chest</string>
    <string name="luv_chest_qr">LUV Chest QR</string>
    <string name="sponsored_luv_drop">Sponsored LUV Drop</string>
    <string name="quest_reward">Quest Reward</string>
    <string name="age">Age</string>
    <string name="gender">Gender</string>
    <string name="male">Male</string>
    <string name="female">Female</string>
    <string name="other">Other</string>
    <string name="onlyme">Only me</string>
    <string name="inappropriate">Inappropriate</string>
    <string name="peopleifollow">People I follow</string>
    <string name="privacy_setting">Privacy setting</string>
    <string name="who_can_see_my_followers">Who can see my followers</string>
    <string name="privacy">Privacy</string>
    <string name="who_can_see_who_i_follow">Who can see who I follow</string>
    <string name="user">User</string>
    <string name="profile">Profile</string>
    <string name="setting">Settings</string>
    <string name="language">Language</string>
    <string name="empty">Empty</string>
    <string name="select_receiver_nearby">Select someone nearby</string>
    <string name="report_video">Report Video</string>
    <string name="why_are_you_reporting_this_video">Why are you reporting this video?</string>
    <string name="thanks_received">Thanks Received!</string>
    <string name="you_ve_received_a_thank_you_video_only_you_can_see_it_once">You’ve received a Thank you video, only you can see it Once!</string>
    <string name="start_luv_battle">Start LUV Battle</string>
    <string name="the_world_random_user">The world (Random user)</string>
    <string name="victor_declined_your_invite">%1$s declined your invite.</string>
    <string name="victor_challenged_you_to_a_battle"><b>%1$s</b> challenged you to a battle</string>
    <string name="send_a_thank_you_video">Send a thank you video</string>
    <string name="discard_media">Discard media?</string>
    <string name="going_back_will_delete_already_recorded_video">Going back will delete already recorded video.</string>
    <string name="thanks_sent_available_to_anyone_for_24h">Thanks sent! video will be available to everyone for 24h</string>
    <string name="thanks_sent_can_view_it_once">Thanks sent @%1$s can view it once!</string>
    <string name="invite">Invite\n</string>
    <string name="report">Report</string>
    <string name="battle">Battle</string>
    <string name="who_would_you_like_to_nchallenge_to_battle">Who would you like to\nchallenge to battle?</string>
    <string name="challenge_sent_waiting_for_reply">Challenge sent, waiting for reply…</string>
    <string name="sending_battle_request">Sending Battle Request...</string>
    <string name="we_couldn_t_find_a_rival_for_you_please_try_later">We couldn\'t find a rival for you,\nplease try later.</string>
    <string name="decline">Decline</string>
    <string name="join">Join</string>
    <string name="start_battle_instantly">Start Battle Instantly</string>
    <string name="your_comment">Your comment...</string>
    <string name="tap_to_counter">Tap to counter</string>
    <string name="shared_live_stream"><b>\@%1$s</b> shared a live battle join to watch!</string>
    <string name="rematch">Rematch</string>
    <string name="loser">Loser</string>
    <string name="you_lost">You lost!</string>
    <string name="you_won">You Won!</string>
    <string name="winner">Winner</string>
    <string name="battle_ended">Battle Ended!</string>
    <string name="waiting_for_participants">Waiting for participants ...</string>
    <string name="top_10_gift_senders">Top 10 gift senders</string>
    <string name="the_report_will_be_reviewed_promptly">The report will be reviewed promptly</string>
    <string name="end_live_battle">End Live Battle?</string>
    <string name="going_back_will_end_the_nlive_stream">Going back will End the\nLive Stream.</string>
    <string name="discard">Discard</string>
    <string name="end_stream">End Stream</string>
    <string name="thank_you_video_received">Thank you video\nreceived!</string>
    <string name="only_you_can_see_it_njust_once">Only you can see it,\n<b><font fgcolor="#000000">just once!</font></b></string>
    <string name="anyone_can_view_nfor_24h">Anyone can view\n<b><font fgcolor="#000000">for 24h</font></b></string>
    <string name="play">Play</string>
    <string name="nearby_requires_users_location_and_must_be_enable_in_settings">Nearby requires users location and must be enable in settings.</string>
    <string name="you_can_now_receive_a_luv_drop_from_people_bear_you_enable_in_settings">You can now receive a LUV Drop from people near you! Enable Now.</string>
    <string name="enable">Enable</string>
    <string name="filter">Filter</string>
    <string name="period">Period</string>
    <string name="user_type">User type</string>
    <string name="top_regular_gifters">Top Regular Gifters</string>
    <string name="top_brand_gifters">Top Brand Gifters</string>
    <string name="here_s_a_little_welcome_gift">Here’s a little welcome gift.</string>
    <string name="welcome_to_n_brand_name">Welcome to\n[Brand_Name]!</string>
    <string name="_10_luv">10 LUV</string>
    <string name="luv_giveaway">LUV Giveaway</string>
    <string name="earn_prizes_by_spreading_luv_send_a_gift_for_a_chance_to_win">Earn Prizes by spreading LUV!\nSend a gift for a chance to win</string>
    <string name="you_have_been_selected">You have been selected as the
 Top gifter for our next giveaway!</string>
    <string name="any_user_who_sends_you_the_specified_gift">Any user who sends you the
 specified gift during the giveaway
 will be automatically
 participating! let your followers
 know! See giveaway page for
 more details</string>
    <string name="see_more">See More</string>
    <string name="sponsored_by">Sponsored by: <font fgcolor="#000000"><b>%1$s</b></font></string>
    <string name="nearby">Nearby</string>
    <string name="anyone">Anyone</string>
    <string name="only_user_name">Only @%1$S</string>
    <!-- TODO: Remove or change this placeholder text -->
    <string name="hello_blank_fragment">Hello blank fragment</string>
    <string name="luv_drop">LUV Drop Received</string>
    <string name="my_qr_code">My QR Code</string>
    <string name="my_qr_codes">My QR Codes</string>
    <string name="my_luv_chest">My LUV Chest</string>
    <string name="create_luv_chest">Create LUV Chest</string>
    <string name="luv_chest_sent">LUV Chest Sent</string>
    <string name="opened_luv_cate">Opened LUV Crate</string>
    <string name="created_crate_bundle">Created Crate Bundle</string>
    <string name="sponsored_drop_sent">Sponsored Drop Sent</string>
    <string name="sponsored_drop_received">Sponsored Drop Received</string>
    <string name="luv_chest_received">Received LUV Chest</string>
    <string name="select_user">Select user</string>
    <string name="no_chat_message">There are no messages\nin the chat yet</string>
    <string name="batte_start">Battle will start in</string>
    <string name="time_left">Time Left</string>
    <string name="invite_subscriber">Invite subscribers</string>
    <string name="you">You</string>
    <string name="english">English</string>
    <string name="spanish">Spanish</string>
    <string name="portuguese">Portuguese</string>
    <string name="we_have_winner">We have a winner!</string>
    <string name="ticket_for_one">Ticket for one!</string>
    <string name="ticket_for_one_description">With your gift you have been entered for a chance to win!</string>
    <string name="earn_prizes">Earn Prizes by spreading LUV!\nSend a gift for a chance to win</string>
    <string name="congratulations_giveaway_winner">Congratulations to our giveaway event winner!</string>
    <string name="sponsored_drop">Sponsored Drop!</string>
    <string name="favorite_brand">From your favorite brand</string>
    <string name="luv">LUV</string>
    <string name="luv_shout_out">LUV Shout Out</string>
    <string name="system_luv_drop">System Luv Drop!</string>
    <string name="comming_with_luv_from">Incoming! with LUV from</string>
    <string name="the_luv_network">The LUV Network</string>
    <string name="gifts_sent">gifts sent</string>
    <string name="luv_drop_sent">LUV Drop Sent</string>
    <string name="reward">Reward</string>
    <string name="daily_luv_reward">Daily Luv Reward</string>
    <string name="invite_sent">Invite sent!</string>
    <string name="sent">sent</string>
    <string name="select_image">Select image</string>
    <string name="luv_referral">LUV Referral</string>
    <string name="sponsored_by_2">Sponsored by</string>
    <string name="delete_account_2">Are you sure you\nwant to delete\naccount?</string>
    <string name="quest_reward_scan_crate">Quest reward Scan crate</string>
    <string name="quest_reward_send_LUV">Quest reward send LUV</string>
    <string name="referral_reward">Quest reward referral</string>
    <string name="luv_create_received">LUV crate received</string>
    <string name="luv_create_sent">LUV crate sent</string>
    <string name="referral_reward_2">Referral reward</string>
    <string name="quest_reward_referral">Quest reward referral</string>
    <string name="user_join">joined the battle</string>
    <string name="you_dont_enough_balance">"You don't have enough balance."</string>
    <string name="add_a_message">"Add message"</string>
    <string name="tap_to_type">"Tap to type"</string>
    <string name="earn_luv_drops">Earn LUV Drops</string>
    <string name="visit_particopating_stores">Visit participating stores</string>
    <string name="luv_drop_with_every_visit">LUV Drop with every visit!</string>
    <string name="there_luv_inside_package">"There's LUV inside the package!"</string>
    <string name="luv_inside_package">These brands have LUV inside!</string>
    <string name="thanks_you_for_your_support"><b>@%1$s</b> says thank you for your support!</string>
    <string name="luv_crates">%1$s\nLUV Crates</string>
    <string name="opened_crate">Opened Crate</string>
    <string name="add_your_favorite_brands">Add your favorite brands to make it easier for them to Sponsor you with LUV Drops.</string>
</resources>
