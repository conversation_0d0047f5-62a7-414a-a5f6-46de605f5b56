<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/viewPager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:padding="@dimen/_10sdp"
        app:layout_constraintBottom_toTopOf="@+id/nextButton"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/skipButton" />

    <TextView
        android:id="@+id/skipButton"
        android:layout_width="wrap_content"
        android:layout_height="48dp"
        style="@style/grayButton"
        android:layout_margin="@dimen/_20sdp"
        android:gravity="center"
        android:paddingLeft="@dimen/_20sdp"
        android:paddingRight="@dimen/_20sdp"
        android:text="@string/on_board_skip"
        android:textAllCaps="false"
        android:textColor="@color/gray"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/nextButton"
        android:layout_width="wrap_content"
        android:layout_height="48dp"
        android:layout_marginBottom="@dimen/_20sdp"
        android:background="@drawable/bg_circle_red"
        android:gravity="center"
        android:paddingLeft="@dimen/_30sdp"
        android:paddingRight="@dimen/_30sdp"
        android:text="@string/on_board_Next"
        android:textAllCaps="false"
        android:textColor="#FFFFFF"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>