package com.flashbid.luv.viewmodels

import android.content.Context
import android.util.Log
import android.widget.FrameLayout
import androidx.lifecycle.LiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.viewModelScope
import com.flashbid.luv.BuildConfig
import com.flashbid.luv.data.local.AppPreferences
import com.flashbid.luv.extensions.hideSoftKeyboard
import com.flashbid.luv.models.battle.TopGiftersResponse
import com.flashbid.luv.models.battle.UnifiedStreamMessage
import com.flashbid.luv.models.remote.BattleDetailsResponse
import com.flashbid.luv.models.remote.BattleRematchRequest
import com.flashbid.luv.models.remote.BattleResponseRequest
import com.flashbid.luv.models.remote.ItemGift
import com.flashbid.luv.models.remote.MessageResponse
import com.flashbid.luv.models.remote.SendGiftRequest
import com.flashbid.luv.models.remote.ShareInviteRequest
import com.flashbid.luv.network.Resource
import com.flashbid.luv.repositories.BattleRepository
import com.google.gson.Gson
import io.agora.rtc2.ChannelMediaOptions
import io.agora.rtc2.Constants
import io.agora.rtc2.IRtcEngineEventHandler
import io.agora.rtc2.RtcEngine
import io.agora.rtc2.RtcEngineConfig
import io.agora.rtc2.video.VideoCanvas
import io.agora.rtm.ErrorInfo
import io.agora.rtm.ResultCallback
import io.agora.rtm.RtmChannel
import io.agora.rtm.RtmChannelAttribute
import io.agora.rtm.RtmChannelListener
import io.agora.rtm.RtmChannelMember
import io.agora.rtm.RtmClient
import io.agora.rtm.RtmClientListener
import io.agora.rtm.RtmFileMessage
import io.agora.rtm.RtmImageMessage
import io.agora.rtm.RtmMediaOperationProgress
import io.agora.rtm.RtmMessage
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

sealed class    ViewState {
    object Idle : ViewState()
    data class UserJoined(val uid: Int, val message: String) : ViewState()
    data class Error(val message: String) : ViewState()
    data class ChatReceived(val message: String) : ViewState()
    data class BattleDetailsLoaded(val details: BattleDetailsResponse?) : ViewState()
    data class BattleResult(val winner: Int, val loser: Int) : ViewState()
    data class AudienceLeave(val message: String) : ViewState()
    object BattleRematch : ViewState()
}

class BattleViewModel(
    private val battleRepository: BattleRepository,
    private val appPreferences: AppPreferences,
) : ViewModel() {

    private val TAG = "Battle-ViewModel"

    private val _viewState = MutableStateFlow<ViewState>(ViewState.Idle)
    val viewState: StateFlow<ViewState> = _viewState.asStateFlow()

    var latestBattleDetails: BattleDetailsResponse? = null

    val uid = appPreferences.userId ?: 0
    var userRole = UserRole.AUDIENCE
    var audienceMessage = ""

    enum class UserRole {
        AUDIENCE, OWNER_BROADCAST, OPPONENT_BROADCAST
    }

    private var rtmClient: RtmClient? = null
    private var rtmChannel: RtmChannel? = null
    private var agoraEngine: RtcEngine? = null

    fun setupRTMClient(context: Context, connectToRtmServer: () -> Unit) {
        Log.d(TAG, "Setting up RTM Client")
        rtmClient =
            RtmClient.createInstance(context, BuildConfig.AGORA_APP_ID, object : RtmClientListener {
                override fun onConnectionStateChanged(state: Int, reason: Int) {}

                override fun onMessageReceived(rtmMessage: RtmMessage, peerId: String) {
                    _viewState.value = ViewState.ChatReceived(rtmMessage.text)
                }

                override fun onImageMessageReceivedFromPeer(p0: RtmImageMessage?, p1: String?) {}

                override fun onFileMessageReceivedFromPeer(p0: RtmFileMessage?, p1: String?) {}

                override fun onMediaUploadingProgress(p0: RtmMediaOperationProgress?, p1: Long) {}

                override fun onMediaDownloadingProgress(p0: RtmMediaOperationProgress?, p1: Long) {}

                override fun onTokenExpired() {}

                override fun onPeersOnlineStatusChanged(p0: MutableMap<String, Int>?) {}
            })

        connectToRtmServer.invoke()
    }

    fun connectToRtmServer(token: String, channelName: String, id: String, inviterId: String?) {
        Log.d(TAG, "Connecting to RTM server: $channelName")
        rtmClient?.login(token, id, object : ResultCallback<Void> {
            override fun onSuccess(responseInfo: Void?) {
                joinRtmChannel(channelName, inviterId)
            }

            override fun onFailure(errorInfo: ErrorInfo) {
                _viewState.value =
                    ViewState.Error("connectToRtmServer onError: ${errorInfo.errorDescription}")
            }
        })
    }

    private fun joinRtmChannel(channelName: String, inviterId: String?) {
        Log.d(TAG, "Joining RTM channel: $channelName")
        try {
            val rtmChannelListener = object : RtmChannelListener {
                override fun onMemberCountUpdated(p0: Int) {}
                override fun onAttributesUpdated(p0: MutableList<RtmChannelAttribute>?) {}
                override fun onMessageReceived(message: RtmMessage, member: RtmChannelMember) {
                    _viewState.value = ViewState.ChatReceived(message.text)
                }

                override fun onImageMessageReceived(p0: RtmImageMessage?, p1: RtmChannelMember?) {}
                override fun onFileMessageReceived(p0: RtmFileMessage?, p1: RtmChannelMember?) {}
                override fun onMemberJoined(p0: RtmChannelMember?) {}
                override fun onMemberLeft(p0: RtmChannelMember?) {}
            }
            rtmChannel = rtmClient?.createChannel(channelName, rtmChannelListener)?.apply {
                join(object : ResultCallback<Void> {
                    override fun onSuccess(responseInfo: Void?) {
                        if (userRole == UserRole.AUDIENCE) {
                            sendAudienceMessage(inviterId)


                        }


                    }

                    override fun onFailure(errorInfo: ErrorInfo) {
                        _viewState.value =
                            ViewState.Error("joinRtmChannel onError: ${errorInfo.errorDescription}")
                    }
                })
            }
        } catch (e: Exception) {
            _viewState.value = ViewState.Error("joinRtmChannel exception: ${e.localizedMessage}")
        }
    }

    fun sendTextMessage(text: String, inviterId: String?, success: ((String) -> Unit))  {
        val message = UnifiedStreamMessage.TextMessage(
            UnifiedStreamMessage.TextMessage2(text,
                (appPreferences.firstName + " " + appPreferences.lastName),
                appPreferences.photo,
                ""+uid, inviterId)
        )
        sendUnifiedMessage(message, success)
    }

    fun sendGiftMessage(
        gift: ItemGift, inviterId: String?
    ) {
        /*val message = UnifiedStreamMessage.GiftMessage(
            UnifiedStreamMessage.GiftMessage2(""+gift.amount,
                gift.name,
                gift.image,
                ""+gift.id,
                appPreferences.photo,
                (appPreferences.firstName + " " + appPreferences.lastName),
                ""+uid,
                inviterId)
        )
        sendUnifiedMessage(message)*/

        //sendGift(gift, gift.id, inviterId!!.toInt(), gift.amount ?: 0)
        sendGift(gift, gift.id, inviterId!!.toInt(), gift.amount ?: 0)
    }

    fun sendGiftMessageDelay(
        gift: ItemGift, inviterId: String?
    ) {
        val message = UnifiedStreamMessage.GiftMessage(
            UnifiedStreamMessage.GiftMessage2(""+gift.amount,
                gift.name,
                gift.image,
                ""+gift.id,
                appPreferences.photo,
                (appPreferences.firstName + " " + appPreferences.lastName),
                ""+uid,
                inviterId)
        )
        sendUnifiedMessage(message)
    }


    fun sendAudienceMessage(
        inviterId: String?
    ) {
        Log.d(TAG, "Sending audience message")
        val message = UnifiedStreamMessage.AudienceMessage(
            UnifiedStreamMessage.AudienceMessage2(appPreferences.photo,
                (appPreferences.firstName + " " + appPreferences.lastName),
                uid.toString(),
                inviterId)
        )
        sendUnifiedMessage(message)

        //If Audience Join sent message like audience Joined
        sendTextMessage(audienceMessage, inviterId) {
        }
    }

    fun sendBattleStartMessage(
        timer: String, isStarted: Int
    ) {
         val message = UnifiedStreamMessage.BattleStartMessage(
            UnifiedStreamMessage.BattleStartMessage2(timer, isStarted)
        )
        sendUnifiedMessage(message)
    }

    fun sendBattleEndMessage(winnerId: Int)  {

        val message = UnifiedStreamMessage.BattleEndMessage(
            UnifiedStreamMessage.BattleEndMessage2("",
                "",
                ""+winnerId)
        )
        sendUnifiedMessage(message)
    }

    fun sendBattleClosedMessage()  {
        val message = UnifiedStreamMessage.BattleClosedMessage(
            UnifiedStreamMessage.BattleClosedMessage2(""+uid)

        )
        sendUnifiedMessage(message)
    }

    fun sendAudianceLeaveMessage(inviterId: String?)  {

        Log.d(TAG, "Sending audience leave message")
        val message = UnifiedStreamMessage.AudianceLeaveMessage(
            UnifiedStreamMessage.AudianceLeaveMessage2(appPreferences.photo,
                (appPreferences.firstName + " " + appPreferences.lastName),
                uid.toString(),
                inviterId)
        )
        sendUnifiedMessage(message)
    }

    fun sendBattleRematchdMessage(success: ((String) -> Unit))  {
        val message = UnifiedStreamMessage.BattleRematchMessage(
            UnifiedStreamMessage.BattleRematchMessage2(""+uid)

        )
        sendUnifiedMessage(message, success)
    }

    private fun sendUnifiedMessage(
        message: UnifiedStreamMessage, success: ((String) -> Unit)? = null
    ) {
        viewModelScope.launch(Dispatchers.IO) {
            val messageJson = Gson().toJson(message)
            val rtmMessage = rtmClient?.createMessage()?.apply { text = messageJson }
            rtmMessage?.let {
                rtmChannel?.sendMessage(it, object : ResultCallback<Void> {
                    override fun onSuccess(aVoid: Void?) {
                        _viewState.value = ViewState.ChatReceived(rtmMessage.text)
                        success?.invoke(rtmMessage.text)
                    }

                    override fun onFailure(errorInfo: ErrorInfo) {
                        _viewState.value =
                            ViewState.Error("joinRtmChannel exception: ${errorInfo.errorDescription}")
                    }
                })
            }
        }
    }

    fun battleResponse(
        status: String, uid: Int
    ): LiveData<Resource<MessageResponse>> {
        return battleRepository.battleResponse(
            BattleResponseRequest(status, uid)
        )
    }

    fun battleRequest(
        uid: Int
    ): LiveData<Resource<MessageResponse>> {
        return battleRepository.battleRequest(
            BattleResponseRequest(null, uid)
        )
    }

    fun shareInvite(
        channelName: String, userIds: List<Int>
    ): LiveData<Resource<MessageResponse>> {
        return battleRepository.shareInvite(
            ShareInviteRequest(channelName, userIds)
        )
    }

    fun battleRematch() {
        viewModelScope.launch(Dispatchers.IO) {
            battleRepository.battleRematch(BattleRematchRequest(uid)).onStart {}
                .catch { e -> _viewState.value = ViewState.Error(e.message ?: "Unknown Error") }
                .collect {
                    withContext(Dispatchers.Main) {
                        Log.d(TAG, "Battle rematch: $it")
                        _viewState.value = ViewState.BattleRematch
                    }
                }
        }
    }

    fun joinLive(channel: String, inviter: Int, message: String) {
        audienceMessage = message
        viewModelScope.launch(Dispatchers.IO) {
            battleRepository.joinLive(channel, ""+inviter).onStart {}
                .catch { e -> _viewState.value = ViewState.Error(e.message ?: "Unknown Error") }
                .collect {battleDetails ->
                    withContext(Dispatchers.Main) {
                        Log.d(TAG, "Battle Join: $battleDetails")
                        Log.d(TAG, "Battle Join: $channel ,,,,,,,,,,$inviter")
                    }
                }
        }
    }

    fun battleResult(channel: String) {
        viewModelScope.launch(Dispatchers.IO) {
            battleRepository.battleResult(channel).onStart {}
                .catch { e -> _viewState.value = ViewState.Error(e.message ?: "Unknown Error") }
                .collect {
                    withContext(Dispatchers.Main) {
                        _viewState.value = ViewState.BattleResult(it?.winner ?: 0, it?.loser ?: 0)
                        Log.d(TAG, "Battle Result: $it")
                    }
                }
        }
    }

    fun sendGift(gift: ItemGift, giftId: Int, inviterId: Int, amount: Int) {
        viewModelScope.launch(Dispatchers.IO) {
            battleRepository.sendGift(SendGiftRequest(giftId, inviterId)).onStart {}
                .catch { e -> _viewState.value = ViewState.Error(e.message ?: "Unknown Error") }
                .collect {
                    withContext(Dispatchers.Main) {
                        appPreferences.balanceLuv = appPreferences.balanceLuv - amount
                        Log.d(TAG, "Send Gift Result: $it")

                        sendGiftMessageDelay(gift, ""+inviterId)
                    }
                }
        }
    }

    fun getTopGifts(channelId: String, userID: Int, onResult: (TopGiftersResponse) -> Unit) {
        viewModelScope.launch(Dispatchers.IO) {
            battleRepository.getTopGifts(channelId, userID).onStart {}
                .catch { e -> _viewState.value = ViewState.Error(e.message ?: "Unknown Error") }
                .collect {
                    withContext(Dispatchers.Main) {
                        Log.d(TAG, "Battle Top gifts: $it")
                        if (it != null) {
                            onResult.invoke(it)
                        } else _viewState.value = ViewState.Error("No Gifts Sent")
                    }
                }
        }
    }

    fun endBattle(channelId: String) {
        viewModelScope.launch(Dispatchers.IO) {
            battleRepository.endBattle(channelId).onStart {}
                .catch { e -> _viewState.value = ViewState.Error(e.message ?: "Unknown Error") }
                .collect {
                    withContext(Dispatchers.Main) {
                        Log.d(TAG, "Battle Result: $it")
                    }
                }
        }
    }

    fun leaveBattle() {
        viewModelScope.launch(Dispatchers.IO) {
            battleRepository.leaveBattle().onStart {}
                .catch { e -> _viewState.value = ViewState.Error(e.message ?: "Unknown Error") }
                .collect {
                    withContext(Dispatchers.Main) {
                        Log.d(TAG, "Battle Leave: $it")
                    }
                }
        }
    }

    fun leaveBattleAudience(channel: String, inviter: Int) {
        viewModelScope.launch(Dispatchers.IO) {
            battleRepository.leaveBattleAudience(channel, ""+inviter).onStart {}
                .catch { e -> _viewState.value = ViewState.Error(e.message ?: "Unknown Error") }
                .collect {battleDetails ->
                    withContext(Dispatchers.Main) {
                        Log.d(TAG, "leaveBattleAudience: $battleDetails")
                        Log.d(TAG, "leaveBattleAudience: $channel ,,,,,,,,,,$inviter")
                        _viewState.value = ViewState.AudienceLeave("Leave")
                    }
                }
        }
    }

    fun loadBattleDetails(channel: String) {
        Log.d(TAG, "Loading battle details for channel: $channel")
        viewModelScope.launch(Dispatchers.IO) {
            battleRepository.getBattleDetails(channel).onStart {}
                .catch { e -> _viewState.value = ViewState.Error(e.message ?: "Unknown Error") }
                .collect { battleDetails ->
                    withContext(Dispatchers.Main) {
                        latestBattleDetails = battleDetails
                        _viewState.value = ViewState.BattleDetailsLoaded(battleDetails)
                    }
                }
        }
    }

    fun initializeAgoraEngine(context: Context, onSuccess: () -> Unit) {
        try {
            val config = RtcEngineConfig().apply {
                mContext = context
                mAppId = BuildConfig.AGORA_APP_ID
                mEventHandler = rtcEventHandler
            }
            agoraEngine = RtcEngine.create(config)
            if (agoraEngine == null) {
                Log.e(TAG, "Failed to initialize Agora Engine")
                return
            }
            Log.d(TAG, "Agora Engine initialized successfully")
            agoraEngine?.let {
                it.enableVideo()
                it.enableAudio()
                it.setDefaultAudioRoutetoSpeakerphone(true)
                Log.d(TAG, "Agora Engine initialized and configured")
                onSuccess.invoke()
            } ?: Log.e(TAG, "Failed to initialize Agora Engine")
        } catch (e: Exception) {
            Log.e(TAG, "Exception in Agora Engine initialization: ${e.message}")
        }
    }

    private val rtcEventHandler = object : IRtcEngineEventHandler() {
        override fun onUserJoined(uid: Int, elapsed: Int) {
            Log.d(TAG, "User joined: UID=$uid, Elapsed=$elapsed")
            _viewState.value = ViewState.UserJoined(uid, "User joined: $uid")
        }

        override fun onJoinChannelSuccess(channel: String, uid: Int, elapsed: Int) {
            Log.d(TAG, "Joined channel successfully: $channel, UID=$uid, Elapsed=$elapsed")
        }

        override fun onUserOffline(uid: Int, reason: Int) {
            Log.d(TAG, "User offline: UID=$uid, Reason=$reason")
        }

        override fun onError(err: Int) {
            Log.e(TAG, "Agora SDK Error: $err")
            _viewState.value = ViewState.Error("Agora SDK Error: $err")
        }
    }

    fun setupLocalVideo(context: Context): VideoCanvas {
        Log.d(TAG, "Setting up local video")
        val localSurfaceView = RtcEngine.CreateRendererView(context)
        viewModelScope.launch(Dispatchers.IO) {
            agoraEngine?.setupLocalVideo(
                VideoCanvas(localSurfaceView, VideoCanvas.RENDER_MODE_HIDDEN, uid)
            )
        }
        return VideoCanvas(localSurfaceView, VideoCanvas.RENDER_MODE_HIDDEN, uid)
    }


    fun setupRemoteVideo(
        context: Context, uid: Int, ownerContainer: FrameLayout, opponentContainer: FrameLayout
    ) {
        Log.d(TAG, "Setting up remote video for user ID: $uid")
        val battle = latestBattleDetails?.activeChannels
        if (battle == null) {
            Log.d(TAG, "No active channels found for remote video setup")
            return
        }

        fun setupVideo(container: FrameLayout, userId: Int) {
            Log.d(TAG, "Setting up video in container for user ID: $userId")
            val remoteSurfaceView = RtcEngine.CreateRendererView(context)
            remoteSurfaceView.setZOrderMediaOverlay(true)

            agoraEngine?.setupRemoteVideo(
                VideoCanvas(remoteSurfaceView, VideoCanvas.RENDER_MODE_FIT, userId)
            )
            container.removeAllViews()
            container.addView(remoteSurfaceView)
        }

        when {
            userRole == UserRole.OWNER_BROADCAST && uid != this.uid -> {
                Log.d(TAG, "User role is OWNER_BROADCAST. Setting up video for opponent.")
                setupVideo(opponentContainer, uid)
            }

            userRole == UserRole.OPPONENT_BROADCAST && uid != this.uid -> {
                Log.d(TAG, "User role is OPPONENT_BROADCAST. Setting up video for owner.")
                setupVideo(ownerContainer, uid)
            }

            userRole == UserRole.AUDIENCE -> {
                Log.d(TAG, "User role is AUDIENCE. Setting up video for both owner and opponent.")
                setupVideo(ownerContainer, battle.owner_user_id)
                setupVideo(opponentContainer, battle.opponent)
            }

            else -> Log.d(TAG, "User role not set or unrecognized")
        }
    }

    fun joinChannel(token: String, channelName: String) {
        Log.d(TAG, "Joining channel: $channelName with token: $token")
        val options = ChannelMediaOptions().apply {
            channelProfile = Constants.CHANNEL_PROFILE_LIVE_BROADCASTING
            clientRoleType = if (userRole == UserRole.AUDIENCE) Constants.CLIENT_ROLE_AUDIENCE
            else Constants.CLIENT_ROLE_BROADCASTER
        }

        agoraEngine?.joinChannel(token, channelName, uid, options)?.also {
            Log.d(TAG, "joinChannel result: $it")
        } ?: Log.d(TAG, "Agora Engine is not initialized")
    }

    fun setUserRole(battleDetails: BattleDetailsResponse.ActiveChannels) {
        Log.d(TAG, "Setting user role based on battle details")
        userRole = when (uid) {
            battleDetails.owner_user_id -> {
                Log.d(TAG, "User role set to OWNER_BROADCAST")
                UserRole.OWNER_BROADCAST
            }

            battleDetails.opponent -> {
                Log.d(TAG, "User role set to OPPONENT_BROADCAST")
                UserRole.OPPONENT_BROADCAST
            }

            else -> {
                Log.d(TAG, "User role set to AUDIENCE")
                UserRole.AUDIENCE
            }
        }
    }

    fun onDestroy() {
        leaveBattle()
        agoraEngine?.stopPreview()
        agoraEngine?.leaveChannel()
        rtmClient?.logout(null)
        rtmChannel?.leave(null)
        rtmClient?.release()
        RtcEngine.destroy()
    }

}