package com.flashbid.luv.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.flashbid.luv.R
import com.flashbid.luv.databinding.ItemCommunityTransactionBinding
import com.flashbid.luv.extensions.getTimeInAgo
import com.flashbid.luv.extensions.hide
import com.flashbid.luv.extensions.setOnClickWithDebounce
import com.flashbid.luv.extensions.show
import com.flashbid.luv.models.remote.OtherStory
import com.flashbid.luv.models.remote.Transaction
import com.flashbid.luv.util.loadImageFromUrl
import kotlin.reflect.KFunction1

class CommunityTransactionsAdapter(
    private val userClick: KFunction1<Int, Unit>,
    private val kFunction1: (ArrayList<OtherStory>, View) -> Unit
) :
    RecyclerView.Adapter<CommunityTransactionsAdapter.ViewHolder>() {

    private val list: ArrayList<Transaction> = ArrayList()

    inner class ViewHolder(val binding: ItemCommunityTransactionBinding) :
        RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val itemView =
            ItemCommunityTransactionBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            )
        return ViewHolder(itemView)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val model = list[position]

        with(holder.binding) {
            constraintLayout.setOnClickWithDebounce {
                userClick(model.sender_user_id)
            }
            textView12.text = root.context.getString(
                R.string.sender_x_receiver, model.sender,
                model.receiver
            )
            imageView4.loadImageFromUrl(model.sender_photo)
            imageView5.loadImageFromUrl(model.receiver_photo)

            if (model.story.isNullOrEmpty()) {
                constraintLayout4.hide()
            } else {
                constraintLayout4.show()
                rlView.setOnClickWithDebounce { kFunction1(model.story, rlView) }
            }
            tvUsername.text = root.context.getString(
                R.string.gift_x_time, model.gift_name ?: "Gift",
                model.update_at.getTimeInAgo()
            )
        }

    }

    override fun getItemCount(): Int {
        return list.size
    }

    fun refresh(newList: List<Transaction>) {
        val diffCallback = TransactionDiffCallback(list, newList)
        val diffResult = DiffUtil.calculateDiff(diffCallback)

        list.clear()
        list.addAll(newList)
        diffResult.dispatchUpdatesTo(this)
    }

    fun appendTransactions(newTransactions: List<Transaction>) {
        val start = this.list.size
        this.list.addAll(newTransactions)
        notifyItemRangeInserted(start, newTransactions.size)
    }

    inner class TransactionDiffCallback(
        private val oldList: List<Transaction>,
        private val newList: List<Transaction>
    ) : DiffUtil.Callback() {

        override fun getOldListSize(): Int = oldList.size

        override fun getNewListSize(): Int = newList.size

        override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            val oldItem = oldList[oldItemPosition]
            val newItem = newList[newItemPosition]
            return oldItem.sender_user_id == newItem.sender_user_id &&
                    oldItem.receiver_user_id == newItem.receiver_user_id &&
                    oldItem.update_at == newItem.update_at
        }

        override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return oldList[oldItemPosition] == newList[newItemPosition]
        }
    }


}