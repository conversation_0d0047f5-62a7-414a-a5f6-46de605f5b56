package com.flashbid.luv.ui.fragments.operations.sendLuv

import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.flashbid.luv.R
import com.flashbid.luv.adapter.GiftsAdapter
import com.flashbid.luv.data.local.AppPreferences
import com.flashbid.luv.databinding.FragmentSendLuvBinding
import com.flashbid.luv.extensions.setGridLayout
import com.flashbid.luv.extensions.setOnClickWithDebounce
import com.flashbid.luv.extensions.snackBar
import com.flashbid.luv.extensions.viewBinding
import com.flashbid.luv.models.remote.ItemGift
import com.flashbid.luv.network.Status
import com.flashbid.luv.viewmodels.GiftsViewModel
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel

class SendLuvFragment : Fragment(R.layout.fragment_send_luv) {

    private val binding by viewBinding(FragmentSendLuvBinding::bind)
    private val pref by inject<AppPreferences>()
    private val giftViewModel: GiftsViewModel by viewModel()
    private val giftsList: ArrayList<ItemGift> = ArrayList()
    private val args by navArgs<SendLuvFragmentArgs>()
    private val giftsAdapter by lazy { GiftsAdapter(giftsList, this::onGiftSelect) }
    private var giftItem: ItemGift? = null

    private fun onGiftSelect(item: ItemGift) {
        giftItem = item
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.tvBalance.text = pref.balanceLuv.toString()

        binding.rcvGifts.apply {
            setGridLayout(4)
            adapter = giftsAdapter
        }

        binding.imageView.setOnClickWithDebounce {
            findNavController().popBackStack()
        }

        getGifts()

        println("the item" + giftsList)

        binding.btnNext.setOnClickWithDebounce {
            println("the selected item" + giftItem)
            if (giftItem != null) {
                if (pref.balanceLuv >= (giftItem?.amount ?: 0)) {
                    val action =
                        SendLuvFragmentDirections.actionSendLuvFragmentToSelectReceiverFragment(
                            giftItem!!, args.receiver
                        )
                    findNavController().navigate(action)
                } else snackBar(getString(R.string.no_balance))
            } else snackBar(getString(R.string.something_went_wrong))
        }
    }

    private fun getGifts() {
        giftViewModel.getGifts().observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(it.message ?: getString(R.string.something_went_wrong, getString(R.string.try_again)))
                Status.LOADING -> {}
                Status.SUCCESS -> {
                    giftsList.clear()
                    giftsList.addAll(it.data?.list ?: ArrayList())
                    giftsAdapter.refresh()
                    // Debugging output
                    println("Gift List: $giftsList")
                    println("API Response: ${it.data?.list}")
                }
            }
        }
    }
}