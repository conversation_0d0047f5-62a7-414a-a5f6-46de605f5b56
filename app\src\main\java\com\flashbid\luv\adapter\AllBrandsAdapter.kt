package com.flashbid.luv.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.flashbid.luv.databinding.ItemFavBinding
import com.flashbid.luv.util.loadImageFromUrl
import com.flashbid.luv.extensions.setOnClickWithDebounce
import com.flashbid.luv.models.remote.BrandItem

class AllBrandsAdapter(
    private val list: MutableList<BrandItem>,
    val onClick: (BrandItem) -> Unit,
) : RecyclerView.Adapter<AllBrandsAdapter.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding =
            ItemFavBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = list[position]

        with(holder.binding) {
            tvBrandName.text = item.company_name.toString()
            tvLuv.text = item.amount.toString()
            ivImage.loadImageFromUrl(item.company_image)
        }

        holder.itemView.setOnClickWithDebounce {
            onClick(item)
        }

    }

    override fun getItemCount(): Int = list.size

    inner class ViewHolder(val binding: ItemFavBinding) :
        RecyclerView.ViewHolder(binding.root)

    fun refresh() = notifyDataSetChanged()

}