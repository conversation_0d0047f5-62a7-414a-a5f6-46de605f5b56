package com.flashbid.luv.util

import android.graphics.Canvas
import android.graphics.ColorFilter
import android.graphics.Paint
import android.graphics.PixelFormat
import android.graphics.RectF
import android.graphics.SweepGradient
import android.graphics.drawable.Drawable
import kotlin.math.min

class GradientCircularProgressDrawable(private val colors: IntArray) : Drawable() {
    private val paint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        style = Paint.Style.STROKE
        strokeWidth = 16f
        strokeCap = Paint.Cap.ROUND
    }
    private val startAngle = -90f
    private var sweepAngle = 0f

    fun setSweepAngle(angle: Float) {
        sweepAngle = angle
        invalidateSelf()
    }

    override fun draw(canvas: Canvas) {
        val bounds = bounds
        val size = min(bounds.width(), bounds.height())
        val rect = RectF(
            bounds.centerX() - size / 2 + paint.strokeWidth / 2,
            bounds.centerY() - size / 2 + paint.strokeWidth / 2,
            bounds.centerX() + size / 2 - paint.strokeWidth / 2,
            bounds.centerY() + size / 2 - paint.strokeWidth / 2
        )

        paint.shader =
            SweepGradient(bounds.centerX().toFloat(), bounds.centerY().toFloat(), colors, null)
        canvas.drawArc(rect, startAngle, sweepAngle, false, paint)
    }

    override fun setAlpha(alpha: Int) {
        paint.alpha = alpha
    }

    override fun setColorFilter(colorFilter: ColorFilter?) {
        paint.colorFilter = colorFilter
    }

    @Deprecated("Deprecated in Java",
        ReplaceWith("PixelFormat.TRANSLUCENT", "android.graphics.PixelFormat")
    )
    override fun getOpacity(): Int = PixelFormat.TRANSLUCENT
}

