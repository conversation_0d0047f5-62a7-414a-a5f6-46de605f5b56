<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    android:background="@drawable/bg_round_corners"
    android:orientation="vertical"
    android:paddingHorizontal="@dimen/_15sdp"
    android:paddingVertical="@dimen/_10sdp"
    app:layout_behavior="@string/bottom_sheet_behavior">

    <View
        android:id="@+id/view2"
        android:layout_width="@dimen/_40sdp"
        android:layout_height="4dp"
        android:layout_gravity="center"
        android:background="@drawable/bg_edit" />

    <ImageView
        android:id="@+id/imageView6"
        android:layout_width="@dimen/_25sdp"
        android:layout_height="@dimen/_25sdp"
        android:layout_gravity="end"
        android:layout_marginTop="5dp"
        android:src="@drawable/ic_close_gray" />

    <TextView
        android:id="@+id/textView36"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="@dimen/_12sdp"
        android:text="@string/select_user"
        android:textSize="@dimen/_20sdp" />

    <EditText
        android:id="@+id/edtSearch"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:autofillHints="false"
        android:background="@drawable/bg_edit"
        android:drawableStart="@drawable/search_small"
        android:drawablePadding="@dimen/_10sdp"
        android:hint="@string/search"
        android:inputType="text"
        android:maxLines="1"
        android:padding="@dimen/_8sdp"
        android:textColor="@color/gray"
        android:textSize="14sp" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rcvUsers"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_15sdp"
        tools:listitem="@layout/item_search" />

</LinearLayout>