package com.flashbid.luv.viewmodels

import androidx.lifecycle.LiveData
import androidx.lifecycle.ViewModel
import com.flashbid.luv.models.remote.*
import com.flashbid.luv.network.Resource
import com.flashbid.luv.repositories.GiftRepository

class GiftsViewModel(
    private val giftsRepository: GiftRepository
) : ViewModel() {

    fun getGifts(): LiveData<Resource<GetGiftsResponse>> {
        return giftsRepository.getGifts()
    }

    fun getQuestTime(): LiveData<Resource<QuestTimeResponse>> {
        return giftsRepository.getQuestTime()
    }

    fun collectDailyReward(): LiveData<Resource<MessageResponse>> {
        return giftsRepository.collectDailyReward()
    }

    fun unlockChest(code: String, key: String, needKey: Int): LiveData<Resource<MessageResponse>> {
        return giftsRepository.unlockChest(code, UnlockCrateRequest(key, needKey))
    }

    fun unlockCrate(code: String, crateType: String, isBonus: Int?): LiveData<Resource<OpenCrateResponse>> {
        return giftsRepository.unlockCrate(code, crateType, isBonus)
    }

    fun unlockKeyCrate(
        codeId: String,
        key_phrase: String
    ): LiveData<Resource<MessageResponse>> {
        return giftsRepository.unlockKeyCrate(codeId, UnlockKeyRequest(key_phrase))
    }

    fun getReferralCode(): LiveData<Resource<ReferralCodeResponse>> {
        return giftsRepository.getReferralCode()
    }

    fun getCustomBrands(): LiveData<Resource<CustomBrandResponse>> {
        return giftsRepository.getCustomBrands()
    }

    fun getCustomBeaconBrands(): LiveData<Resource<CustomBrandResponse>> {
        return giftsRepository.getCustomBeaconBrands()
    }

    fun getCustomBrandName(filter: Int): LiveData<Resource<CustomBrandNameResponse>> {
        return giftsRepository.getCustomBrandName(filter)
    }
}