package com.flashbid.luv.ui.fragments.story

import android.annotation.SuppressLint
import android.net.Uri
import android.os.Bundle
import android.os.CountDownTimer
import android.transition.TransitionInflater
import android.view.View
import android.widget.LinearLayout
import android.widget.ProgressBar
import androidx.core.content.ContextCompat
import androidx.core.view.doOnPreDraw
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.exoplayer.ExoPlayer
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.flashbid.luv.R
import com.flashbid.luv.databinding.FragmentUserStoryBinding
import com.flashbid.luv.extensions.hide
import com.flashbid.luv.extensions.setVisible
import com.flashbid.luv.extensions.show
import com.flashbid.luv.extensions.snackBar
import com.flashbid.luv.extensions.viewBinding
import com.flashbid.luv.models.remote.MessageResponse
import com.flashbid.luv.models.remote.OtherStory
import com.flashbid.luv.network.Resource
import com.flashbid.luv.network.Status
import com.flashbid.luv.util.loadImageFromUrl
import com.flashbid.luv.viewmodels.UserViewModel
import org.koin.androidx.viewmodel.ext.android.viewModel
import kotlin.math.roundToInt

class UserStoryFragment : Fragment(R.layout.fragment_user_story), Player.Listener {

    private val binding by viewBinding(FragmentUserStoryBinding::bind)
    private val args by navArgs<UserStoryFragmentArgs>()
    private val userViewModel: UserViewModel by viewModel()
    private var currentStoryIndex = 0
    private lateinit var progressBars: List<ProgressBar>
    private lateinit var timer: CountDownTimer
    private var storyDurationMs = 5000L
    private lateinit var player: ExoPlayer
    private var isTimerRunning = false
    //private var remainingTimeMs = storyDurationMs

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        sharedElementEnterTransition = TransitionInflater.from(requireContext())
            .inflateTransition(android.R.transition.move)
        sharedElementReturnTransition = TransitionInflater.from(requireContext())
            .inflateTransition(android.R.transition.move)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        postponeEnterTransition()
        view.doOnPreDraw { startPostponedEnterTransition() }

        val storyDetail = args.userStories.first()

        setupUserData(storyDetail)
        setupPlayer()
        setupListeners(storyDetail)
        initializeProgressBars()
        loadStory(args.userStories[currentStoryIndex])
    }

    private fun setupListeners(storyDetail: OtherStory) {
        binding.cdvFollow.setOnClickListener { followUser(storyDetail.gift_receiver_id) }
        binding.cdvFollowing.setOnClickListener { unfollowUser(storyDetail.gift_receiver_id) }

        binding.ivClose.setOnClickListener { findNavController().navigate(R.id.homeFragment) }

        binding.cvOptions.setOnClickListener {
            if (binding.cvReport.isVisible) binding.cvReport.hide()
            else binding.cvReport.show()
        }

        binding.cvReport.setOnClickListener {
            binding.cvReport.hide()
            findNavController().navigate(R.id.action_userStoryFragment_to_reportVideoFragment)
        }

    }

    private fun setupPlayer() {
        player = ExoPlayer.Builder(requireContext()).build().apply {
            addListener(this@UserStoryFragment)
            playWhenReady = true
            binding.playerView.player = this
        }
    }

    private fun loadStory(story: OtherStory) {
        binding.pgBar.show()
        if (::timer.isInitialized && isTimerRunning) {
            timer.cancel()
            isTimerRunning = false
        }

        progressBars[currentStoryIndex].progress = 0

        try {
            val mediaItem = MediaItem.fromUri(Uri.parse(story.url))
            player.setMediaItem(mediaItem)
            player.prepare()

            if (currentStoryIndex < args.userStories.size - 1) {
                val nextStory = args.userStories[currentStoryIndex + 1]
                val nextMediaItem = MediaItem.fromUri(Uri.parse(nextStory.url))
                player.addMediaItem(nextMediaItem)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun navigateToNextStory() {
        if (currentStoryIndex < args.userStories.size - 1) {
            currentStoryIndex++
            loadStory(args.userStories[currentStoryIndex])
        } else {
            findNavController().navigate(R.id.homeFragment)
        }
    }

    private fun initializeProgressBars() {
        binding.progressBarContainer.removeAllViews()
        progressBars = List(args.userStories.size) { index ->
            ProgressBar(context, null, android.R.attr.progressBarStyleHorizontal).apply {
                layoutParams = LinearLayout.LayoutParams(0, 10, 1f).apply {
                    leftMargin =
                        if (index > 0) (5 * resources.displayMetrics.density).toInt() else 0
                }
                progressDrawable =
                    ContextCompat.getDrawable(context, R.drawable.progress_bar_progress)
                max = 100
                progress = 0
            }.also {
                binding.progressBarContainer.addView(it)
            }
        }
    }

    private fun startStoryTimer(progressBar: ProgressBar) {
        //timer = object : CountDownTimer(storyDurationMs, 50) {

        progressBar.max = storyDurationMs.toInt()
        timer = object : CountDownTimer(storyDurationMs, 1) {
            override fun onTick(millisUntilFinished: Long) {
                //remainingTimeMs = millisUntilFinished
                val seconds = (millisUntilFinished / 1000) % 60

//                val progress =
                  //  ((storyDurationMs - millisUntilFinished).toFloat() / storyDurationMs * 100).roundToInt()
                progressBar.progress = (storyDurationMs - millisUntilFinished).toInt()
            }

            override fun onFinish() {
                progressBar.progress = progressBar.max
                navigateToNextStory()
            }
        }.start()
        isTimerRunning = true
    }

    @SuppressLint("UnsafeOptInUsageError")
    @Deprecated("Deprecated in Java")
    override fun onPlayerStateChanged(playWhenReady: Boolean, playbackState: Int) {
        when (playbackState) {
            Player.STATE_READY -> {
                binding.pgBar.hide()
                if (playWhenReady && !isTimerRunning) {
                    storyDurationMs = player.duration
                    startStoryTimer(progressBars[currentStoryIndex])
                }
            }

            Player.STATE_ENDED -> {
                if (currentStoryIndex < args.userStories.size - 1) {
                    navigateToNextStory()
                } else {
                    findNavController().navigate(R.id.homeFragment)
                }
            }

            Player.STATE_BUFFERING -> {
            }

            Player.STATE_IDLE -> {
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        if (::timer.isInitialized) {
            timer.cancel()
        }
        player.release()
    }

    private fun setupUserData(storyDetail: OtherStory) {
        if (storyDetail.is_following_gift_receiver == 1) {
            binding.cdvFollowing.show()
            binding.cdvFollow.hide()
        } else {
            binding.cdvFollowing.hide()
            binding.cdvFollow.show()
        }
        binding.ivUser.loadImageFromUrl(storyDetail.gift_sender_image)
        binding.tvUsername.text = storyDetail.gift_sender_name
        binding.tvReceiverUsername.text = storyDetail.gift_receiver_name
    }


    private fun followUser(id: Int) {
        userViewModel.followUser(id).observe(viewLifecycleOwner) {
            handleUserActionResponse(it, isFollowing = true)
        }
    }

    private fun unfollowUser(id: Int) {
        userViewModel.unfollowUser(id).observe(viewLifecycleOwner) {
            handleUserActionResponse(it, isFollowing = false)
        }
    }

    private fun handleUserActionResponse(response: Resource<MessageResponse>, isFollowing: Boolean) {
        when (response.status) {
            Status.ERROR -> showSnackBar(response.message)
            Status.LOADING -> {}
            Status.SUCCESS -> updateUserFollowStatus(isFollowing, response.data?.message)
        }
    }

    private fun showSnackBar(message: String?) {
        snackBar(message ?: getString(R.string.something_went_wrong, getString(R.string.try_again)))
    }

    private fun updateUserFollowStatus(isFollowing: Boolean, message: String?) {
        binding.cdvFollowing.setVisible(isFollowing)
        binding.cdvFollow.setVisible(!isFollowing)
        showSnackBar(message ?: "Success")
    }

    override fun onPause() {
        //findNavController().popBackStack(R.id.homeFragment, false)
        super.onPause()
    }

}