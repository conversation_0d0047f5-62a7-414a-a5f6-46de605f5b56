package com.flashbid.luv.ui.fragments.operations.withdraw

import android.os.Bundle
import android.view.View
import androidx.core.widget.doAfterTextChanged
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.flashbid.luv.MainActivity
import com.flashbid.luv.R
import com.flashbid.luv.data.local.AppPreferences
import com.flashbid.luv.databinding.FragmentWithdrawAmountSelectBinding
import com.flashbid.luv.extensions.*
import com.flashbid.luv.network.Status
import com.flashbid.luv.util.InAppConstants
import com.flashbid.luv.util.getColor
import com.flashbid.luv.viewmodels.TransactionViewModel
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel

class WithdrawAmountSelectFragment : Fragment(R.layout.fragment_withdraw_amount_select) {

    private val binding by viewBinding(FragmentWithdrawAmountSelectBinding::bind)
    private val viewModel: TransactionViewModel by viewModel()
    private val pref by inject<AppPreferences>()
    private val args by navArgs<WithdrawAmountSelectFragmentArgs>()
    private var amount = 0
    private var diamondRate = 0.0

    private fun Double.diamondWorth(): Double {
        return (this * diamondRate).formatDecimal().toDouble()
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        getDiamondRate()

        binding.tvLimit.text = "/${InAppConstants.WEEKLY_WITHDRAW_LIMIT}"
        binding.tvBalance.text = pref.balanceDiamond.toString()

        binding.imageView.setOnClickWithDebounce {
            findNavController().popBackStack()
        }

        binding.btnNext.setOnClickWithDebounce {
            if (amount > 0 && !checkLimitCompleted() && amount.toDouble().diamondWorth() >= InAppConstants.MIN_USD_WITHDRAW_LIMIT && amount <= pref.balanceDiamond) {
                withdrawAmount(args.country, args.currency, args.method, amount, args.link)
            } else {
                binding.tvError.setTextState(getString(R.string.invalid_amount), TextState.ERROR)
                binding.tvError.show()
            }
        }

        binding.edtAmount.doAfterTextChanged {
            amount = if (it.isNullOrEmpty()) 0
            else it.toString().toInt()
            checkLimitCompleted()
            binding.tvUsd.text = amount.toDouble().diamondWorth().toString()
        }

    }

    private fun withdrawAmount(
        country: String,
        currency: String,
        method: Int,
        amount: Int,
        link: String
    ) {
        viewModel.withdrawDiamonds(amount, country, currency, link, method)
            .observe(viewLifecycleOwner) {
                when (it.status) {
                    Status.ERROR -> snackBar(it.message ?: getString(R.string.something_went_wrong, getString(R.string.try_again)))
                    Status.LOADING -> {}
                    Status.SUCCESS -> {
                        (requireActivity() as MainActivity).showSnackBar(getString(R.string.withdraw_success), TextState.SUCCESS)
                        findNavController().navigate(R.id.homeFragment)
                    }
                }
            }
    }

    private fun getWeeklyLimit() {
        viewModel.getWithdrawLimit()
            .observe(viewLifecycleOwner) {
                when (it.status) {
                    Status.ERROR -> snackBar(it.message ?: getString(R.string.something_went_wrong, getString(R.string.try_again)))
                    Status.LOADING -> {}
                    Status.SUCCESS -> {
                        binding.edtAmount.isEnabled = true
                        binding.btnNext.isEnabled = true
                        binding.tvUsedLimit.text =
                            it.data?.message?.weekly_limit_used?.toDouble()?.diamondWorth().toString()
                        checkLimitCompleted()
                    }
                }
            }
    }

    private fun getDiamondRate() {
        viewModel.getDiamondRate("1")
            .observe(viewLifecycleOwner) {
                when (it.status) {
                    Status.ERROR -> snackBar(it.message ?: getString(R.string.something_went_wrong, getString(R.string.try_again)))
                    Status.LOADING -> {}
                    Status.SUCCESS -> {
                        diamondRate = it.data?.model?.usd ?: 0.0
                        getWeeklyLimit()
                    }
                }
            }
    }

    private fun checkLimitCompleted(): Boolean {
        return if ((binding.tvUsedLimit.text.toString()
                .toDouble().diamondWorth()
                .plus(amount.toDouble().diamondWorth())) >= InAppConstants.WEEKLY_WITHDRAW_LIMIT
        ) {
            binding.tvLimit.setTextColor(binding.tvLimit.getColor(R.color.redgradstart))
            true
        } else {
            binding.tvLimit.setTextColor(binding.tvLimit.getColor(R.color.black))
            binding.tvError.hide()
            false
        }
    }
}