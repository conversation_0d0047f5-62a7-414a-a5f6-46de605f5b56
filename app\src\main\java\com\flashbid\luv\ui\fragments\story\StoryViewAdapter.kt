package com.flashbid.luv.ui.fragments.story

import android.net.Uri
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.media3.common.MediaItem
import androidx.media3.common.PlaybackException
import androidx.media3.common.Player
import androidx.media3.common.util.UnstableApi
import androidx.media3.exoplayer.ExoPlayer
import androidx.recyclerview.widget.RecyclerView
import com.flashbid.luv.databinding.ItemStoryViewBinding
import com.flashbid.luv.models.remote.OtherStory

interface StoryPlaybackListener {
    fun onStoryPrepared(position: Int)
    fun onStoryCompleted(position: Int)
    fun onStoryError(position: Int)
}

class StoryViewAdapter(
    private val stories: Array<OtherStory>,
    private val player: ExoPlayer,
    private val playbackListener: StoryPlaybackListener
) : RecyclerView.Adapter<StoryViewAdapter.ViewHolder>() {

    inner class ViewHolder(val binding: ItemStoryViewBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(story: OtherStory) {
            with(binding) {
                player.setMediaItem(MediaItem.fromUri(Uri.parse(story.url)), true)
                player.playWhenReady = true
                playerView.useController = false
                playerView.player = player
                player.prepare()

                player.addListener(@UnstableApi object : Player.Listener {
                    @Deprecated("Deprecated in Java")
                    override fun onPlayerStateChanged(playWhenReady: Boolean, playbackState: Int) {
                        when (playbackState) {
                            Player.STATE_READY -> if (playWhenReady) {
                                playbackListener.onStoryPrepared(adapterPosition)
                            }
                            Player.STATE_ENDED -> playbackListener.onStoryCompleted(adapterPosition)
                        }
                    }

                    override fun onPlayerError(error: PlaybackException) {
                        playbackListener.onStoryError(adapterPosition)
                    }
                })
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding = ItemStoryViewBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val story = stories[position]
        holder.bind(story)
    }

    override fun getItemCount(): Int = stories.size
}

