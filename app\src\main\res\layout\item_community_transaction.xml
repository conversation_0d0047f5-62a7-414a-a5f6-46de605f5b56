<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingHorizontal="@dimen/_12sdp"
    android:paddingVertical="@dimen/_6sdp"
    android:id="@+id/constraintLayout">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/rlView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:transitionName="shared_story_transition"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <View
            android:id="@+id/constraintLayout4"
            android:layout_width="@dimen/_35sdp"
            android:layout_height="@dimen/_35sdp"
            android:background="@drawable/rectangle"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.5" />

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/imageView4"
            android:layout_width="@dimen/_30sdp"
            android:layout_height="@dimen/_30sdp"
            android:elevation="1dp"
            android:scaleType="centerCrop"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:shapeAppearanceOverlay="@style/ShapeAppearance.Material3.Corner.Full"
            app:srcCompat="@drawable/ic_user_placeholder" />

        <com.google.android.material.card.MaterialCardView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="2dp"
            app:cardCornerRadius="@dimen/_50sdp"
            app:cardElevation="4dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:strokeColor="@color/white">

            <ImageView
                android:id="@+id/imageView5"
                android:layout_width="@dimen/_15sdp"
                android:layout_height="@dimen/_15sdp"
                android:importantForAccessibility="no"
                android:scaleType="centerCrop"
                app:srcCompat="@drawable/user_placeholder" />

        </com.google.android.material.card.MaterialCardView>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clData"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/rlView"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.5">

        <TextView
            android:id="@+id/textView12"
            style="@style/text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_10sdp"
            android:ellipsize="end"
            android:maxLines="2"
            app:layout_constraintBottom_toTopOf="@+id/tvUsername"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Star Luv received" />

        <TextView
            android:id="@+id/tvUsername"
            style="@style/small"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/gray"
            android:transitionGroup="true"
            android:transitionName="@string/user_profile_transition"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="@+id/textView12"
            app:layout_constraintTop_toBottomOf="@+id/textView12"
            tools:text="@string/username_x_time" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>