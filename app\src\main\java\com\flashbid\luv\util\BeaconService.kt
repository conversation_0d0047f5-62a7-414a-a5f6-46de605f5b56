package com.flashbid.luv.util

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Intent
import android.os.Build
import android.os.IBinder
import android.util.Log
import androidx.core.app.NotificationCompat
import com.flashbid.luv.MainActivity
import com.flashbid.luv.R
import com.flashbid.luv.models.remote.BeaconDetailResponse
import com.flashbid.luv.models.remote.BeaconRequest
import com.flashbid.luv.repositories.BeaconRepository
import com.flashbid.luv.util.Constants.CLAIM_STORE_REWARD
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import org.altbeacon.beacon.Beacon
import org.altbeacon.beacon.BeaconConsumer
import org.altbeacon.beacon.BeaconManager
import org.altbeacon.beacon.BeaconParser
import org.altbeacon.beacon.Region
import org.koin.android.ext.android.inject
import kotlin.coroutines.CoroutineContext

class BeaconService : Service(), BeaconConsumer, CoroutineScope {

    private val beaconRepository: BeaconRepository by inject()
    private var beaconManager: BeaconManager? = null
    private val serviceJob = SupervisorJob()
    override val coroutineContext: CoroutineContext
        get() = Dispatchers.Main + serviceJob

    override fun onCreate() {
        super.onCreate()

        createNotificationChannel()

        val notificationIntent =
            Intent(this, MainActivity::class.java)
        val pendingIntent =
            PendingIntent.getActivity(this, 0, notificationIntent, PendingIntent.FLAG_IMMUTABLE)
        val notification: Notification = NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Store Gifts")
            .setContentText("Scanning for stores...")
            .setSmallIcon(R.drawable.app_icon)
            .setContentIntent(pendingIntent)
            .build()
        startForeground(1, notification)
        beaconManager = BeaconManager.getInstanceForApplication(this)
        beaconManager!!.beaconParsers.add(BeaconParser().setBeaconLayout("m:2-3=0215,i:4-19,i:20-21,i:22-23,p:24-24,d:25-25"))
        beaconManager!!.bind(this)
    }

    override fun onBind(intent: Intent): IBinder? {
        return null
    }

    override fun onBeaconServiceConnect() {
        beaconManager!!.addRangeNotifier { beacons, region ->
            if (beacons.isNotEmpty()) {
                val firstBeacon = beacons.sortedBy { it.distance }.iterator().next()
                Log.d(TAG, "The first beacon I see is about ${firstBeacon.distance} meters away.")
                val beaconRequest = BeaconRequest("74278BDA-B644-4520-8F0C-720EAF059935")

                launch {
                    val beaconResponse = beaconRepository.getBeacon(beaconRequest)
                    val beacon = beaconResponse.data?.data?.beacon?.first()
                    if (isThisMyStoreBeacon(beacon, firstBeacon)) {
                        showBeaconNotification(firstBeacon)
                        CLAIM_STORE_REWARD.postValue(beacon?.beacon_uid)
                    }
                }
            }
        }
        try {
            beaconManager!!.startRangingBeaconsInRegion(
                Region(
                    "myRangingUniqueId",
                    null,
                    null,
                    null
                )
            )
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val serviceChannel = NotificationChannel(
                CHANNEL_ID,
                "Beacon Service Channel",
                NotificationManager.IMPORTANCE_DEFAULT
            )
            val manager = getSystemService(
                NotificationManager::class.java
            )
            manager.createNotificationChannel(serviceChannel)
        }
    }

    private fun showBeaconNotification(beacon: Beacon) {
        val builder: NotificationCompat.Builder = NotificationCompat.Builder(this, CHANNEL_ID)
            .setSmallIcon(R.drawable.app_icon)
            .setContentTitle("${beacon.bluetoothAddress} Store Detected!")
            .setContentText(if (beacon.distance > MIN_CLAIM_DISTANCE) "Move closer to receive the reward..." else "You have found a brand store. Claiming store reward...")
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
        val notificationManager = getSystemService(NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(2, builder.build())
    }

    private fun isThisMyStoreBeacon(beacon: BeaconDetailResponse.Data.Beacon?, myBeacon: Beacon): Boolean {
        return beacon?.beacon_uid == myBeacon.serviceUuid.toString()
    }

    override fun onDestroy() {
        super.onDestroy()
        beaconManager!!.unbind(this)
    }

    companion object {
        private const val TAG = "BeaconService"
        private const val CHANNEL_ID = "BeaconServiceChannel"
        private const val MIN_CLAIM_DISTANCE = 10.0 // in meters
    }

}