<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:transitionName="shared_story_transition"
    android:layout_marginHorizontal="@dimen/_5sdp"
    android:layout_marginTop="@dimen/_5sdp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/constraintLayout3"
        android:layout_width="@dimen/_55sdp"
        android:layout_height="@dimen/_55sdp"
        android:background="@drawable/rectangle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivPerson"
        android:layout_width="@dimen/_48sdp"
        android:layout_height="@dimen/_48sdp"
        android:scaleType="centerCrop"
        android:src="@drawable/image"
        app:layout_constraintBottom_toBottomOf="@+id/constraintLayout3"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearance="@style/ShapeAppearance.Material3.Corner.Full" />

    <TextView
        android:id="@+id/tvPerson"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#80000031"
        android:textSize="@dimen/_10sdp"
        app:layout_constraintEnd_toEndOf="@+id/constraintLayout3"
        app:layout_constraintStart_toStartOf="@+id/constraintLayout3"
        app:layout_constraintTop_toBottomOf="@+id/constraintLayout3" />

</androidx.constraintlayout.widget.ConstraintLayout>