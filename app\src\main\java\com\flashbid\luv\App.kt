package com.flashbid.luv

import android.app.Application
import androidx.appcompat.app.AppCompatDelegate
import com.google.android.material.color.DynamicColors
import com.google.firebase.FirebaseApp
import com.flashbid.luv.di.koinModules
import org.koin.android.ext.koin.androidContext
import org.koin.core.context.startKoin

class App : Application() {

    override fun onCreate() {
        super.onCreate()

        AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO)


        DynamicColors.applyToActivitiesIfAvailable(this)

        FirebaseApp.initializeApp(this)

        startKoin {
            androidContext(this@App)
            modules(koinModules)
        }
    }
}
