package com.flashbid.luv.models.remote

data class BattleResultResponse(
    val activeChannels: ActiveChannels,
    val error: <PERSON><PERSON><PERSON>,
    val loser: Int,
    val winner: Int
) {
    data class ActiveChannels(
        val battle_start_time: Any,
        val channel_name: String,
        val chat_token: String,
        val create_at: String,
        val id: Int,
        val opponent: Int,
        val opponent_audience: Any,
        val opponent_gift_count: Any,
        val owner_audience: Any,
        val owner_gift_count: Int,
        val token: String,
        val update_at: String,
        val user_id: Int
    )
}