package com.flashbid.luv.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.flashbid.luv.databinding.ItemPersonBinding
import com.flashbid.luv.extensions.invisible
import com.flashbid.luv.extensions.setOnClickWithDebounce
import com.flashbid.luv.extensions.show
import com.flashbid.luv.models.remote.OtherStory
import com.flashbid.luv.util.loadImageFromUrl

class StoryAdapter(private val kFunction1: (ArrayList<OtherStory>, View) -> Unit) :
    RecyclerView.Adapter<StoryAdapter.ViewHolder>() {

    private val list: ArrayList<ArrayList<OtherStory>> = ArrayList()

    inner class ViewHolder(val binding: ItemPersonBinding) :
        RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val itemView =
            ItemPersonBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(itemView)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val model = list[position]

        with(holder.binding) {
            tvPerson.text = model.firstOrNull()?.gift_sender_name
            ivPerson.loadImageFromUrl(model.firstOrNull()?.gift_sender_image)
            root.setOnClickWithDebounce { kFunction1.invoke(model, root) }
        }

    }

    override fun getItemCount(): Int {
        return list.size
    }

    fun refresh(newList: ArrayList<ArrayList<OtherStory>>) {
        list.clear()
        list.addAll(newList)
        notifyDataSetChanged()
    }
}