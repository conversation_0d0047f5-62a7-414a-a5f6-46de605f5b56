package com.flashbid.luv.viewmodels

import androidx.lifecycle.LiveData
import androidx.lifecycle.ViewModel
import com.flashbid.luv.models.remote.*
import com.flashbid.luv.network.Resource
import com.flashbid.luv.repositories.BattleRepository
import com.flashbid.luv.repositories.BeaconRepository

class BeaconViewModel(
    private val beaconRepository: BeaconRepository
) : ViewModel() {

    suspend fun getBeacon(uid: String): Resource<BeaconDetailResponse> {
        return beaconRepository.getBeacon(
            BeaconRequest(uid)
        )
    }

    fun beaconClaim(uid: String): LiveData<Resource<MessageResponse>> {
        return beaconRepository.beaconClaim(
            BeaconClaimRequest(uid)
        )
    }

}