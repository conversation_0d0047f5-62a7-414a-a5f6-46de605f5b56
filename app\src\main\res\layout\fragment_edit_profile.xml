<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.fragments.profile.EditProfileFragment">

    <ImageView
        android:id="@+id/imageView"
        android:layout_width="@dimen/_30sdp"
        android:layout_height="@dimen/_30sdp"
        android:layout_margin="@dimen/_20sdp"
        android:src="@drawable/ic_back_btn"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="@drawable/ic_gift_box" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivImage"
        android:layout_width="@dimen/_50sdp"
        android:layout_height="@dimen/_50sdp"
        android:layout_marginTop="@dimen/_5sdp"
        android:scaleType="centerCrop"
        app:layout_constraintStart_toStartOf="@+id/textView17"
        app:layout_constraintTop_toBottomOf="@+id/textView17"
        app:shapeAppearanceOverlay="@style/ShapeAppearance.Material3.Corner.Full"
        app:srcCompat="@drawable/ic_user_placeholder" />

    <TextView
        android:id="@+id/saveButton"
        style="@style/small"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_marginEnd="@dimen/_20sdp"
        android:background="@drawable/bg_round_red_grad"
        android:gravity="center"
        android:paddingHorizontal="@dimen/_12sdp"
        android:paddingVertical="@dimen/_8sdp"
        android:text="@string/save"
        android:textColor="@color/white"
        app:layout_constraintBottom_toBottomOf="@+id/imageView"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/imageView" />

    <TextView
        android:id="@+id/tvName"
        style="@style/h3"
        android:layout_width="wrap_content"
        android:gravity="center"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@+id/imageView"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/imageView"
        tools:text="Floyd Miles" />

    <TextView
        android:id="@+id/textView17"
        style="@style/text"
        android:layout_marginTop="@dimen/_15sdp"
        android:gravity="center"
        android:text="@string/profile_image"
        android:textColor="@color/gray"
        app:layout_constraintStart_toStartOf="@+id/view"
        app:layout_constraintTop_toBottomOf="@+id/view" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_20sdp"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="@+id/ivImage"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toEndOf="@+id/ivImage"
        app:layout_constraintTop_toTopOf="@+id/ivImage">

        <androidx.cardview.widget.CardView
            android:id="@+id/btnRemove"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_5sdp"
            android:layout_weight="1"
            app:cardBackgroundColor="@color/semired"
            app:cardCornerRadius="16dp"
            app:cardElevation="0dp"
            app:contentPadding="@dimen/_8sdp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/btnReset">

            <TextView
                style="@style/small"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="@string/remove"
                android:textColor="@color/redgradstart" />

        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:id="@+id/btnPickNew"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_5sdp"
            android:layout_weight="1"
            app:cardBackgroundColor="@color/semiblue"
            app:cardCornerRadius="16dp"
            app:cardElevation="0dp"
            app:contentPadding="@dimen/_8sdp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/btnReset">

            <TextView
                style="@style/small"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="@string/pick_new"
                android:textColor="@color/blue" />

        </androidx.cardview.widget.CardView>

    </LinearLayout>

    <View
        android:id="@+id/view"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginHorizontal="@dimen/_20sdp"
        android:layout_marginTop="@dimen/_20sdp"
        android:background="@color/semigray"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvName" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:paddingHorizontal="@dimen/_20sdp"
        android:paddingVertical="@dimen/_10sdp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/ivImage"
        app:layout_constraintStart_toStartOf="@+id/ivImage"
        app:layout_constraintTop_toBottomOf="@+id/ivImage">

        <LinearLayout
            android:id="@+id/constraintLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                style="@style/small"
                android:layout_marginStart="@dimen/_8sdp"
                android:gravity="center"
                android:text="@string/name"
                android:textColor="@color/gray" />

            <EditText
                android:id="@+id/edtName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_edit"
                android:drawableStart="@drawable/profile"
                android:drawablePadding="@dimen/_10sdp"
                android:imeOptions="actionNext"
                android:maxLines="1"
                android:padding="@dimen/_10sdp"
                android:textSize="12sp" />

            <TextView
                style="@style/text"
                android:layout_marginStart="@dimen/_8sdp"
                android:layout_marginTop="@dimen/_15sdp"
                android:gravity="center"
                android:text="@string/username"
                android:textColor="@color/gray" />

            <EditText
                android:id="@+id/edtUsername"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_edit"
                android:digits="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz1234567890"
                android:drawableStart="@drawable/profile"
                android:drawablePadding="@dimen/_10sdp"
                android:imeOptions="actionNext"
                android:inputType="textFilter"
                android:maxLines="1"
                android:padding="@dimen/_10sdp"
                android:textSize="12sp" />

            <TextView
                style="@style/text"
                android:layout_marginStart="@dimen/_8sdp"
                android:layout_marginTop="@dimen/_15sdp"
                android:gravity="center"
                android:text="@string/age"
                android:textColor="@color/gray" />

            <EditText
                android:id="@+id/edtAge"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_edit"
                android:clickable="true"
                android:digits="1234567890"
                android:drawableStart="@drawable/ic_cake"
                android:drawablePadding="@dimen/_10sdp"
                android:focusable="false"
                android:imeOptions="actionNext"
                android:inputType="number"
                android:maxLines="1"
                android:padding="@dimen/_10sdp"
                android:textSize="12sp" />

            <TextView
                style="@style/text"
                android:layout_marginStart="@dimen/_8sdp"
                android:layout_marginTop="@dimen/_15sdp"
                android:gravity="center"
                android:text="@string/gender"
                android:textColor="@color/gray" />

            <com.google.android.material.textfield.TextInputLayout
                style="@style/Widget.Material3.TextInputLayout.FilledBox.ExposedDropdownMenu"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_edit"
                app:boxStrokeWidth="0dp">

                <com.google.android.material.textfield.MaterialAutoCompleteTextView
                    android:id="@+id/edtGender"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/bg_edit"
                    android:drawableStart="@drawable/ic_gender"
                    android:drawablePadding="@dimen/_10sdp"
                    android:imeOptions="actionNext"
                    android:inputType="none"
                    android:maxLines="1"
                    android:padding="10dp"
                    android:paddingHorizontal="@dimen/_10sdp"
                    android:textSize="14sp" />

            </com.google.android.material.textfield.TextInputLayout>

            <TextView
                style="@style/text"
                android:layout_marginStart="@dimen/_8sdp"
                android:layout_marginTop="@dimen/_15sdp"
                android:gravity="center"
                android:text="@string/email"
                android:textColor="@color/gray" />

            <EditText
                android:id="@+id/edtEmail"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_edit"
                android:drawableStart="@drawable/sms"
                android:drawablePadding="@dimen/_10sdp"
                android:imeOptions="actionNext"
                android:maxLines="1"
                android:padding="@dimen/_10sdp"
                android:textSize="12sp" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_15sdp"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/textView5"
                    style="@style/text"
                    android:layout_marginStart="@dimen/_8sdp"
                    android:gravity="center"
                    android:text="@string/bio"
                    android:textColor="@color/gray"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tvBioCount"
                    style="@style/text"
                    android:gravity="center"
                    android:textColor="@color/gray"
                    app:layout_constraintEnd_toStartOf="@+id/textView22"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="0" />

                <TextView
                    android:id="@+id/textView22"
                    style="@style/text"
                    android:layout_marginEnd="@dimen/_8sdp"
                    android:gravity="center"
                    android:text="@string/_100"
                    android:textColor="@color/gray"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <EditText
                    android:id="@+id/edtBio"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/bg_edit"
                    android:gravity="start"
                    android:imeOptions="actionNext"
                    android:maxLength="100"
                    android:minLines="2"
                    android:padding="@dimen/_10sdp"
                    android:paddingStart="@dimen/_30sdp"
                    android:paddingEnd="@dimen/_10sdp"
                    android:textAlignment="viewStart"
                    android:textSize="12sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.5"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/textView5"
                    app:layout_constraintVertical_bias="0.5" />

                <ImageView
                    android:layout_width="22dp"
                    android:layout_height="22dp"
                    android:layout_margin="10dp"
                    android:scaleType="fitStart"
                    android:src="@drawable/ic_info_ex"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/edtBio" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <LinearLayout
                android:id="@+id/llBrand"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">

                <TextView
                    style="@style/text"
                    android:layout_marginStart="@dimen/_8sdp"
                    android:layout_marginTop="@dimen/_15sdp"
                    android:gravity="center"
                    android:text="@string/industry"
                    android:textColor="@color/gray" />

                <com.google.android.material.textfield.TextInputLayout
                    style="@style/Widget.Material3.TextInputLayout.FilledBox.ExposedDropdownMenu"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/bg_edit"
                    app:boxStrokeWidth="0dp">

                    <com.google.android.material.textfield.MaterialAutoCompleteTextView
                        android:id="@+id/edtIndustry"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@drawable/bg_edit"
                        android:drawableStart="@drawable/shop"
                        android:drawablePadding="@dimen/_10sdp"
                        android:imeOptions="actionNext"
                        android:inputType="none"
                        android:padding="0dp"
                        android:paddingHorizontal="@dimen/_10sdp"
                        android:textSize="12sp" />

                </com.google.android.material.textfield.TextInputLayout>

                <TextView
                    style="@style/text"
                    android:layout_marginStart="@dimen/_8sdp"
                    android:layout_marginTop="@dimen/_15sdp"
                    android:gravity="center"
                    android:text="@string/website"
                    android:textColor="@color/gray" />

                <EditText
                    android:id="@+id/edtWebsite"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/bg_edit"
                    android:drawableStart="@drawable/ic_globe"
                    android:drawablePadding="@dimen/_10sdp"
                    android:imeOptions="actionNext"
                    android:inputType="text"
                    android:maxLines="1"
                    android:padding="@dimen/_10sdp"
                    android:textSize="12sp" />

            </LinearLayout>

            <TextView
                style="@style/text"
                android:layout_gravity="center"
                android:layout_marginTop="@dimen/_30sdp"
                android:text="@string/danger_zone"
                android:textColor="@color/gray"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.cardview.widget.CardView
                android:id="@+id/btnDeleteAccount"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_5sdp"
                app:cardBackgroundColor="@color/semired"
                app:cardCornerRadius="16dp"
                app:cardElevation="0dp"
                app:contentPadding="@dimen/_8sdp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/btnReset">

                <TextView
                    style="@style/small"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:text="@string/delete_account"
                    android:textColor="@color/redgradstart" />

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>