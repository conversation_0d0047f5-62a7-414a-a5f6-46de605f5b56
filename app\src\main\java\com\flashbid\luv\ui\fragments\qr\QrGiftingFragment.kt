package com.flashbid.luv.ui.fragments.qr

import android.content.ContentValues
import android.os.Bundle
import android.util.Log
import android.view.View
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.flashbid.luv.R
import com.flashbid.luv.data.local.AppPreferences
import com.flashbid.luv.databinding.FragmentQrGiftingBinding
import com.flashbid.luv.extensions.*
import com.flashbid.luv.models.remote.CodeDetail
import com.flashbid.luv.network.Status
import com.flashbid.luv.util.shareImage
import com.flashbid.luv.viewmodels.CodesViewModel
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel

class QrGiftingFragment : Fragment(R.layout.fragment_qr_gifting) {

    private val binding by viewBinding(FragmentQrGiftingBinding::bind)
    private val viewModel: CodesViewModel by viewModel()
    private val args by navArgs<QrGiftingFragmentArgs>()
    private var codeDetail: CodeDetail? = null
    private val pref by inject<AppPreferences>()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.tvName.text = getString(R.string.username_s_luv_chest,pref.userName)

        if (pref.isBrandProfile) binding.tvSponsored.show()
        else  binding.tvSponsored.hide()

        binding.tvSponsored.text = getString(R.string.sponsored_by,pref.userName)

        binding.imageView.setOnClickWithDebounce {
            findNavController().popBackStack()
        }

        binding.ivQrGifting.setImageBitmap(generateRoundedQRCode(requireContext(),args.qr,200,20f))

        binding.btnNext.setOnClickWithDebounce {
            shareImage(binding.qrView.createBitmapFromView())
        }

        binding.imageView1.setOnClickWithDebounce {
            if (codeDetail != null) {
                val action =
                    QrGiftingFragmentDirections.actionQrGiftingFragmentToCreateQrGiftingFragment(
                        codeDetail
                    )
                findNavController().navigate(action)
            }
        }

        getCodeDetail(args.qr)

        binding.btnDelete.setOnClickWithDebounce {
            deleteCode(args.qr)
        }

    }

    private fun getCodeDetail(qr: String) {
        viewModel.getCodeDetail(qr).observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(it.message ?: getString(R.string.something_went_wrong, getString(R.string.try_again)))
                Status.LOADING -> {}
                Status.SUCCESS -> {
                    Log.d(ContentValues.TAG, "getCodeDetail: ${it.data?.message.toString()}")
                    codeDetail = it.data?.message
                    if (codeDetail?.key_phrase?.isNotEmpty() == true)
                        binding.qrKey.text = getString(R.string.key_input, codeDetail?.key_phrase)
                    else binding.qrKey.text = getString(R.string.no_key)

                    if(codeDetail?.sponsor_id != null
                        && codeDetail?.sponsor_id != 0
                        && codeDetail?.sponsor_company_name?.isNotEmpty() == true) {
                        //getCodeDetailSponsor(qr)
                        binding.tvSponsored.show()
                        binding.tvSponsored.text = getString(R.string.sponsored_by,codeDetail?.sponsor_company_name)
                    } else {
                        binding.tvSponsored.hide()
                    }
                }
            }
        }
    }

    /*private fun getCodeDetailSponsor(qr: String) {
        viewModel.getCodeDetailSponsor(qr, true).observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(it.message ?: getString(R.string.something_went_wrong, getString(R.string.try_again)))
                Status.LOADING -> {}
                Status.SUCCESS -> {
                    Log.d(ContentValues.TAG, "getCodeDetail: ${it.data?.message.toString()}")
                    val companyName = it.data?.message?.company_name
                    if (companyName?.isNotEmpty() == true) {
                        codeDetail?.company_name = companyName
                    }
                    else {
                        codeDetail?.company_name = ""
                    }
                }
            }
        }
    }*/

    private fun deleteCode(qr: String) {
        viewModel.deleteCode(qr).observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(it.message ?: getString(R.string.something_went_wrong, getString(R.string.try_again)))
                Status.LOADING -> {}
                Status.SUCCESS -> {
                    Log.d(ContentValues.TAG, "deleteCode: ${it.data?.message.toString()}")
                    findNavController().popBackStack()
                }
            }
        }
    }

}