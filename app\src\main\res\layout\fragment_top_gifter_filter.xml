<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:context=".ui.fragments.other.TopGifterFilterFragment">

    <ImageView
        android:id="@+id/imageView"
        android:layout_width="@dimen/_30sdp"
        android:layout_height="@dimen/_30sdp"
        android:layout_margin="@dimen/_20sdp"
        android:src="@drawable/ic_back_btn"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/textView"
        style="@style/h3"
        android:text="@string/filter"
        app:layout_constraintBottom_toBottomOf="@+id/imageView"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/imageView" />

    <TextView
        android:id="@+id/textView38"
        style="@style/h3"
        android:layout_marginTop="@dimen/_30sdp"
        android:text="@string/period"
        app:layout_constraintStart_toStartOf="@+id/imageView"
        app:layout_constraintTop_toBottomOf="@+id/textView" />


    <androidx.cardview.widget.CardView
        android:id="@+id/cvTabs"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_10sdp"
        android:layout_marginHorizontal="@dimen/_20sdp"
        app:cardBackgroundColor="@color/bgGray"
        app:cardCornerRadius="16dp"
        app:cardElevation="0dp"
        app:contentPadding="6dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="@+id/imageView"
        app:layout_constraintTop_toBottomOf="@+id/textView38">

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tabLayout1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/bgGray"
            app:tabBackground="@drawable/tab_item_background"
            app:tabGravity="fill"
            app:tabIndicatorHeight="0dp"
            app:tabMode="fixed"
            app:tabRippleColor="@null" />

    </androidx.cardview.widget.CardView>

    <TextView
        android:id="@+id/textView39"
        style="@style/h3"
        android:layout_marginTop="@dimen/_20sdp"
        android:text="@string/user_type"
        app:layout_constraintStart_toStartOf="@+id/imageView"
        app:layout_constraintTop_toBottomOf="@+id/cvTabs" />


    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_20sdp"
        app:cardBackgroundColor="@color/bgGray"
        app:cardCornerRadius="16dp"
        app:cardElevation="0dp"
        app:contentPadding="6dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="@+id/imageView"
        app:layout_constraintTop_toBottomOf="@+id/textView39">

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tabLayout2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/bgGray"
            app:tabBackground="@drawable/tab_item_background"
            app:tabGravity="fill"
            app:tabIndicatorHeight="0dp"
            app:tabMode="fixed"
            app:tabRippleColor="@null" />

    </androidx.cardview.widget.CardView>

</androidx.constraintlayout.widget.ConstraintLayout>