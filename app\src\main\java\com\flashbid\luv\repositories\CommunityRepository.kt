package com.flashbid.luv.repositories

import androidx.lifecycle.LiveData
import androidx.lifecycle.liveData
import com.flashbid.luv.models.remote.CommunityStatsResponse
import com.flashbid.luv.network.RemoteDataSource
import com.flashbid.luv.network.Resource
import kotlinx.coroutines.Dispatchers

class CommunityRepository(private val remoteDataSource: RemoteDataSource) {

    fun getCommunityStats(): LiveData<Resource<CommunityStatsResponse>> = liveData(Dispatchers.IO) {
        emit(Resource.loading(null))
        val response = remoteDataSource.getCommunityStats()
        emit(response)
    }

}