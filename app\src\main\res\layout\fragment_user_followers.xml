<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.fragments.profile.UserFollowersFragment">

    <ImageView
        android:id="@+id/imageView"
        android:layout_width="@dimen/_30sdp"
        android:layout_height="@dimen/_30sdp"
        android:layout_margin="@dimen/_20sdp"
        android:src="@drawable/ic_back_btn"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/textView"
        style="@style/h3"
        android:text="@string/followers"
        app:layout_constraintBottom_toBottomOf="@+id/imageView"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/imageView" />

    <EditText
        android:id="@+id/edtSearch"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:autofillHints="false"
        android:background="@drawable/bg_edit"
        android:drawableStart="@drawable/search_small"
        android:drawablePadding="@dimen/_10sdp"
        android:hint="@string/search"
        android:layout_margin="@dimen/_20sdp"
        android:maxLines="1"
        android:textSize="14sp"
        android:inputType="text"
        android:padding="@dimen/_8sdp"
        android:textColor="@color/gray"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textView" />

    <View
        android:id="@+id/view"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_margin="@dimen/_20sdp"
        android:background="@color/semigray"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/edtSearch" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rcvUsers"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/view"
        tools:listitem="@layout/item_search" />

    <LinearLayout
        android:id="@+id/llNoItems"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        android:paddingVertical="@dimen/_20sdp"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@+id/view">

        <com.google.android.material.imageview.ShapeableImageView
            android:layout_width="@dimen/_80sdp"
            android:layout_height="@dimen/_80sdp"
            app:contentPadding="@dimen/_10sdp"
            app:shapeAppearance="@style/ShapeAppearance.Material3.Corner.Full"
            app:srcCompat="@drawable/search_btn" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_5sdp"
            android:gravity="center"
            android:text="@string/found_nothing_by_your_search" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>