<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.fragments.profile.others.UserProfileFragment">

    <ImageView
        android:id="@+id/imageView"
        android:layout_width="@dimen/_30sdp"
        android:layout_height="@dimen/_30sdp"
        android:layout_margin="@dimen/_20sdp"
        android:src="@drawable/ic_back_btn"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/brandIcon"
        style="@style/small"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_round_red_grad"
        android:elevation="1dp"
        android:gravity="center"
        android:paddingHorizontal="@dimen/_12sdp"
        android:paddingVertical="@dimen/_6sdp"
        android:text="@string/brand"
        android:textAllCaps="true"
        android:textColor="@color/white"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/textView"
        app:layout_constraintEnd_toEndOf="@+id/textView"
        app:layout_constraintStart_toStartOf="@+id/textView"
        app:layout_constraintTop_toBottomOf="@+id/textView" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/textView"
        android:layout_width="@dimen/_60sdp"
        android:layout_height="@dimen/_60sdp"
        android:scaleType="centerCrop"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/imageView"
        app:shapeAppearanceOverlay="@style/ShapeAppearance.Material3.Corner.Full"
        app:srcCompat="@drawable/ic_user_placeholder" />

    <ImageView
        android:id="@+id/ivFav"
        android:layout_width="@dimen/_30sdp"
        android:layout_height="@dimen/_30sdp"
        android:layout_margin="@dimen/_20sdp"
        android:src="@drawable/round_star_border_24"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@+id/linearLayout4"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/brandIcon">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tvName"
                style="@style/h2"
                android:layout_width="wrap_content"
                android:gravity="center"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="Floyd Miles" />

            <TextView
                android:id="@+id/textView17"
                style="@style/text"
                android:layout_width="wrap_content"
                android:gravity="center"
                android:textColor="@color/gray"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvName"
                tools:text="username" />

            <View
                android:id="@+id/view"
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_marginHorizontal="@dimen/_20sdp"
                android:layout_marginTop="@dimen/_10sdp"
                android:background="@color/semigray"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/textView17" />

            <TextView
                android:id="@+id/tvBio"
                style="@style/text"
                android:layout_width="wrap_content"
                android:layout_marginTop="@dimen/_10sdp"
                android:gravity="center"
                android:text="@string/add_bio"
                android:textColor="@color/redgradstart"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/view" />

            <LinearLayout
                android:id="@+id/linearLayout"
                android:layout_width="0dp"
                android:layout_height="@dimen/_80sdp"
                android:layout_marginHorizontal="10dp"
                android:layout_marginTop="@dimen/_10sdp"
                android:baselineAligned="false"
                android:orientation="horizontal"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvBio">

                <LinearLayout
                    android:id="@+id/llFollowing"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginEnd="3dp"
                    android:layout_weight="1"
                    android:background="@drawable/bg_edit"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tvFollowing"
                        style="@style/h2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/black"
                        android:textSize="24sp"
                        tools:text="100" />

                    <TextView
                        style="@style/text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/following"
                        android:textColor="@color/gray" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llFollower"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginEnd="3dp"
                    android:layout_weight="1"
                    android:background="@drawable/bg_edit"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tvFollowers"
                        style="@style/h2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/black"
                        android:textSize="24sp"
                        tools:text="25k" />

                    <TextView
                        style="@style/text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/followers"
                        android:textColor="@color/gray" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginEnd="3dp"
                    android:layout_weight="1"
                    android:background="@drawable/bg_edit"
                    android:gravity="center"
                    android:orientation="vertical">

                    <me.grantland.widget.AutofitTextView
                        android:id="@+id/tvDiamonds"
                        style="@style/h2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/black"
                        android:textSize="24sp"
                        android:maxLines="1"
                        android:paddingHorizontal="5dp"
                        tools:text="25k" />

                    <ImageView
                        android:layout_width="@dimen/_20sdp"
                        android:layout_height="@dimen/_20sdp"
                        android:src="@drawable/diamond_colored" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginEnd="3dp"
                    android:layout_weight="1"
                    android:background="@drawable/bg_edit"
                    android:gravity="center"
                    android:orientation="vertical">

                    <me.grantland.widget.AutofitTextView
                        android:id="@+id/tvHearts"
                        style="@style/h2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/black"
                        android:textSize="24sp"
                        android:maxLines="1"
                        android:paddingHorizontal="5dp"
                        tools:text="100" />

                    <ImageView
                        android:layout_width="@dimen/_20sdp"
                        android:layout_height="@dimen/_20sdp"
                        android:src="@drawable/heart_colored" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/linearLayout1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_10sdp"
                android:layout_marginTop="@dimen/_10sdp"
                android:baselineAligned="false"
                android:orientation="horizontal"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/linearLayout">

                <LinearLayout
                    android:id="@+id/llBackground"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_75sdp"
                    android:layout_marginEnd="3dp"
                    android:layout_weight="1"
                    android:background="@drawable/ic_bg_bottom"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tvRank"
                        style="@style/h2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/black"
                        android:textSize="24sp"
                        tools:text="08" />

                    <TextView
                        style="@style/text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/global_rank"
                        android:textColor="@color/gray" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_75sdp"
                    android:layout_marginEnd="3dp"
                    android:layout_weight="1"
                    android:background="@drawable/ic_bg_top"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageView
                        android:layout_width="@dimen/_20sdp"
                        android:layout_height="@dimen/_20sdp"
                        android:src="@drawable/ic_gift_box"
                        tools:ignore="ContentDescription" />

                    <TextView
                        style="@style/text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_5sdp"
                        android:text="@string/daily_top_gift"
                        android:textAlignment="center"
                        android:textColor="@color/gray" />

                    <TextView
                        android:id="@+id/tvTopDaily"
                        style="@style/text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:drawablePadding="5dp"
                        android:textColor="@color/redgradstart"
                        app:drawableEndCompat="@drawable/heart_small"
                        tools:text="2000" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_75sdp"
                    android:layout_weight="1"
                    android:background="@drawable/ic_bg_bottom"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageView
                        android:layout_width="@dimen/_20sdp"
                        android:layout_height="@dimen/_20sdp"
                        android:src="@drawable/ic_gift_box"
                        tools:ignore="ContentDescription" />

                    <TextView
                        style="@style/text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_5sdp"
                        android:text="@string/top_sent_gift"
                        android:textColor="@color/gray" />

                    <TextView
                        android:id="@+id/tvSentGift"
                        style="@style/text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:drawablePadding="5dp"
                        android:textColor="@color/redgradstart"
                        app:drawableEndCompat="@drawable/heart_small"
                        tools:text="1000" />

                </LinearLayout>

            </LinearLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/clFavs"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="@+id/linearLayout1"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toStartOf="@+id/linearLayout1"
                app:layout_constraintTop_toBottomOf="@+id/linearLayout1">

                <TextView
                    android:id="@+id/textView18"
                    style="@style/h3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/_20sdp"
                    android:gravity="center"
                    android:text="@string/favorites"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/textView19"
                    style="@style/subtitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/_20sdp"
                    android:gravity="center"
                    android:text="@string/see_all"
                    android:textColor="#F95050"
                    app:layout_constraintBottom_toBottomOf="@+id/textView18"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/textView18" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rcvFav"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/_10sdp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.5"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/textView18"
                    tools:itemCount="1"
                    tools:listitem="@layout/item_fav" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <LinearLayout
                android:id="@+id/llNoFavs"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_10sdp"
                android:gravity="center"
                android:orientation="vertical"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/linearLayout1">

                <TextView
                    style="@style/text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/no_favorites_brands" />

                <ImageView
                    android:layout_width="@dimen/_60sdp"
                    android:layout_height="@dimen/_60sdp"
                    android:layout_marginTop="@dimen/_10sdp"
                    android:src="@drawable/ic_plus_circle"
                    tools:ignore="ContentDescription" />

                <TextView
                    style="@style/text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_5sdp"
                    android:text="@string/pick"
                    android:textColor="@color/redgradstart" />

            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>

    <LinearLayout
        android:id="@+id/linearLayout4"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="@dimen/_10sdp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent">

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/btnFollow"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_10sdp"
            android:layout_weight="1"
            app:cardBackgroundColor="@color/md_theme_light_background"
            app:cardCornerRadius="25dp"
            app:cardElevation="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.5"
            app:strokeColor="@color/semigray">

            <TextView
                android:id="@+id/tvFollow"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:paddingHorizontal="@dimen/_15sdp"
                android:paddingVertical="@dimen/_10sdp"
                android:text="@string/following"
                android:textColor="@color/gray" />

        </com.google.android.material.card.MaterialCardView>

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/btnSend"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            app:cardBackgroundColor="@color/redgradstart"
            app:cardCornerRadius="25dp"
            app:cardElevation="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.5"
            app:strokeColor="@color/semigray"
            app:strokeWidth="0dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:paddingHorizontal="@dimen/_15sdp"
                android:paddingVertical="@dimen/_10sdp"
                android:text="@string/send_luv"
                android:textColor="@color/white" />

        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>