package com.flashbid.luv.repositories

import androidx.lifecycle.LiveData
import androidx.lifecycle.liveData
import com.flashbid.luv.models.battle.TopGiftersResponse
import com.flashbid.luv.models.remote.CustomBrandNameResponse
import com.flashbid.luv.models.remote.CustomBrandResponse
import com.flashbid.luv.models.remote.GetGiftsResponse
import com.flashbid.luv.models.remote.MessageResponse
import com.flashbid.luv.models.remote.OpenCrateResponse
import com.flashbid.luv.models.remote.QuestTimeResponse
import com.flashbid.luv.models.remote.ReferralCodeResponse
import com.flashbid.luv.models.remote.SendGiftRequest
import com.flashbid.luv.models.remote.UnlockCrateRequest
import com.flashbid.luv.models.remote.UnlockKeyRequest
import com.flashbid.luv.network.RemoteDataSource
import com.flashbid.luv.network.Resource
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow

class GiftRepository(private val remoteDataSource: RemoteDataSource) {

    fun getGifts(): LiveData<Resource<GetGiftsResponse>> = liveData(Dispatchers.IO) {
        emit(Resource.loading(null))
        val response = remoteDataSource.getGifts()
        emit(response)
    }

    fun unlockCrate(codeId: String, crateType: String, isBonus: Int?): LiveData<Resource<OpenCrateResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.unlockCrate(codeId, crateType, isBonus)
            emit(response)
        }

    fun unlockKeyCrate(
        codeId: String, request: UnlockKeyRequest
    ): LiveData<Resource<MessageResponse>> = liveData(Dispatchers.IO) {
        emit(Resource.loading(null))
        val response = remoteDataSource.unlockKeyCrate(codeId, request)
        emit(response)
    }

    fun unlockChest(
        codeId: String, request: UnlockCrateRequest
    ): LiveData<Resource<MessageResponse>> = liveData(Dispatchers.IO) {
        emit(Resource.loading(null))
        val response = remoteDataSource.unlockChest(codeId, request)
        emit(response)
    }

    fun getQuestTime(): LiveData<Resource<QuestTimeResponse>> = liveData(Dispatchers.IO) {
        emit(Resource.loading(null))
        val response = remoteDataSource.getQuestTime()
        emit(response)
    }

    fun collectDailyReward(): LiveData<Resource<MessageResponse>> = liveData(Dispatchers.IO) {
        emit(Resource.loading(null))
        val response = remoteDataSource.collectDailyReward()
        emit(response)
    }

    fun getReferralCode(
    ): LiveData<Resource<ReferralCodeResponse>> = liveData(Dispatchers.IO) {
        emit(Resource.loading(null))
        val response = remoteDataSource.getReferralCode(
        )
        emit(response)
    }

    fun getCustomBrands(
    ): LiveData<Resource<CustomBrandResponse>> = liveData(Dispatchers.IO) {
        emit(Resource.loading(null))
        val response = remoteDataSource.customBrands()
        emit(response)
    }

    fun getCustomBeaconBrands(
    ): LiveData<Resource<CustomBrandResponse>> = liveData(Dispatchers.IO) {
        emit(Resource.loading(null))
        val response = remoteDataSource.customBeaconBrands()
        emit(response)
    }

    fun getCustomBrandName(
        filter: Int
    ): LiveData<Resource<CustomBrandNameResponse>> = liveData(Dispatchers.IO) {
        emit(Resource.loading(null))
        val response = remoteDataSource.customBrandName(filter)
        emit(response)
    }

}