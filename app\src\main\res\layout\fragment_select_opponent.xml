<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.fragments.battle.SelectOpponentFragment">

    <ImageView
        android:id="@+id/ivBack"
        android:layout_width="@dimen/_30sdp"
        android:layout_height="@dimen/_30sdp"
        android:layout_margin="@dimen/_20sdp"
        android:elevation="10dp"
        android:src="@drawable/ic_back_btn"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="@drawable/ic_gift_box,ContentDescription" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/_50sdp">

            <ImageView
                android:id="@+id/imageView13"
                android:layout_width="@dimen/_80sdp"
                android:layout_height="@dimen/_80sdp"
                android:layout_marginTop="@dimen/_15sdp"
                android:src="@drawable/iv_cup"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvMainFirst"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16sdp"
                android:text="@string/start_luv_battle"
                android:textColor="@color/black"
                android:textSize="@dimen/_18sdp"
                app:layout_constraintEnd_toEndOf="@+id/imageView13"
                app:layout_constraintStart_toStartOf="@+id/imageView13"
                app:layout_constraintTop_toBottomOf="@+id/imageView13" />

            <TextView
                android:id="@+id/tvMainSecond"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_12sdp"
                android:gravity="center"
                android:text="@string/who_would_you_like_to_nchallenge_to_battle"
                android:textColor="@color/gray"
                android:textSize="@dimen/_12sdp"
                app:layout_constraintEnd_toEndOf="@+id/tvMainFirst"
                app:layout_constraintStart_toStartOf="@+id/tvMainFirst"
                app:layout_constraintTop_toBottomOf="@+id/tvMainFirst" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/constraintLayout5"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_20sdp"
                android:layout_marginTop="@dimen/_10sdp"
                android:background="@drawable/bg_corners"
                android:padding="@dimen/_10sdp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvMainSecond">

                <androidx.cardview.widget.CardView
                    android:id="@+id/cvUser"
                    android:layout_width="@dimen/_45sdp"
                    android:layout_height="@dimen/_45sdp"
                    app:cardCornerRadius="@dimen/_25sdp"
                    app:cardElevation="0dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <ImageView
                        android:id="@+id/ivUser"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:scaleType="centerCrop"
                        android:src="@drawable/image" />

                </androidx.cardview.widget.CardView>

                <TextView
                    android:id="@+id/tvUsername"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_10sdp"
                    android:text="Dianne Russell"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14sdp"
                    app:layout_constraintStart_toEndOf="@+id/cvUser"
                    app:layout_constraintTop_toTopOf="@+id/cvUser" />

                <TextView
                    android:id="@+id/tvName"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_10sdp"
                    tools:text="@username"
                    android:textColor="@color/gray"
                    android:textSize="@dimen/_12sdp"
                    app:layout_constraintStart_toEndOf="@+id/cvUser"
                    app:layout_constraintTop_toBottomOf="@+id/tvUsername" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/btnSearch"
                android:layout_width="match_parent"
                android:layout_height="@dimen/_170sdp"
                android:layout_marginHorizontal="@dimen/_20sdp"
                android:layout_marginTop="@dimen/_10sdp"
                android:background="@drawable/bg_round"
                android:orientation="vertical"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/constraintLayout5">

                <ImageView
                    android:id="@+id/imageView15"
                    android:layout_width="@dimen/_40sdp"
                    android:layout_height="@dimen/_40sdp"
                    android:layout_marginTop="@dimen/_15sdp"
                    android:src="@drawable/iv_search"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tvStatus"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_14sdp"
                    android:text="@string/sending_battle_request"
                    android:textColor="#99000000"
                    android:textSize="@dimen/_12sdp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/imageView15" />

                <TextView
                    android:id="@+id/skipButton"
                    style="@style/small"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:layout_marginVertical="@dimen/_20sdp"
                    android:layout_marginTop="6dp"
                    android:background="@drawable/bg_button_gray"
                    android:paddingHorizontal="@dimen/_15sdp"
                    android:paddingVertical="@dimen/_10sdp"
                    android:text="@string/cancel"
                    android:textColor="@color/gray"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvStatus" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/clDeclined"
                android:layout_width="match_parent"
                android:layout_height="@dimen/_170sdp"
                android:layout_marginHorizontal="@dimen/_20sdp"
                android:layout_marginTop="@dimen/_10sdp"
                android:background="@drawable/bg_edit_red"
                android:orientation="vertical"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/constraintLayout5">

                <ImageView
                    android:id="@+id/imageView16"
                    android:layout_width="@dimen/_40sdp"
                    android:layout_height="@dimen/_40sdp"
                    android:layout_marginTop="@dimen/_15sdp"
                    android:src="@drawable/iv_close"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tvDeclined"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_14sdp"
                    android:text="@string/victor_declined_your_invite"
                    android:textColor="#99000000"
                    android:textSize="@dimen/_13sdp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/imageView16" />

                <TextView
                    android:id="@+id/btnOk"
                    style="@style/small"
                    android:layout_width="80dp"
                    android:layout_height="0dp"
                    android:layout_marginVertical="@dimen/_20sdp"
                    android:layout_marginTop="6dp"
                    android:background="@drawable/bg_circle_red"
                    android:gravity="center"
                    android:paddingHorizontal="@dimen/_15sdp"
                    android:paddingVertical="@dimen/_10sdp"
                    android:text="@string/zxing_button_ok"
                    android:textColor="@color/white"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvDeclined" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <TextView
                android:id="@+id/btnStartBattle"
                style="@style/small"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_marginVertical="@dimen/_20sdp"
                android:background="@drawable/bg_circle_red"
                android:gravity="center"
                android:paddingHorizontal="@dimen/_15sdp"
                android:paddingVertical="@dimen/_8sdp"
                android:text="Start Battle"
                android:textColor="@color/white"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>