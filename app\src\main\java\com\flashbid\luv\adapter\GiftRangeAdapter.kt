package com.flashbid.luv.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.flashbid.luv.R
import com.flashbid.luv.databinding.ItemGiftrangeBinding
import com.flashbid.luv.util.getColor
import com.flashbid.luv.extensions.setOnClickWithDebounce

class GiftRangeAdapter(
    private val list: MutableList<Pair<String, Int>>,
    val onClick: (Int) -> Unit,
) : RecyclerView.Adapter<GiftRangeAdapter.ViewHolder>() {

    private var index = -1

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding =
            ItemGiftrangeBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = list[position]

        with(holder.binding) {
            tvRange.text = item.first
            if (holder.adapterPosition == index) {
                ivHeart.setColorFilter(ContextCompat.getColor(ivHeart.context, R.color.white))
                tvRange.setTextColor(llBackground.getColor(R.color.white))
                llBackground.setBackgroundResource(R.drawable.splash_bg_gradient)
            } else {
                ivHeart.setColorFilter(
                    ContextCompat.getColor(
                        ivHeart.context,
                        R.color.redgradstart
                    )
                )
                tvRange.setTextColor(llBackground.getColor(R.color.black))
                if (holder.adapterPosition % 2 == 1) {
                    llBackground.setBackgroundResource(R.drawable.ic_bg_top)
                } else {
                    llBackground.setBackgroundResource(R.drawable.ic_bg_bottom)
                }
            }
        }

        holder.itemView.setOnClickWithDebounce {
            onClick(item.second)
            index = holder.adapterPosition
            refresh()
        }

    }

    override fun getItemCount(): Int = list.size

    inner class ViewHolder(val binding: ItemGiftrangeBinding) :
        RecyclerView.ViewHolder(binding.root)

    fun setIndex(ind: Int?) {
        index = ind ?: -1
        refresh()
    }

    private fun refresh() = notifyDataSetChanged()

}