<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/_15sdp"
    android:background="@android:color/transparent">

    <androidx.cardview.widget.CardView
        android:id="@+id/dialogCardView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardBackgroundColor="@color/white"
        app:cardCornerRadius="@dimen/_26sdp"
        app:cardElevation="1dp"
        android:layout_margin="2dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.5">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingHorizontal="@dimen/_15sdp">

            <ImageView
                android:id="@+id/imageView8"
                android:layout_width="@dimen/_28sdp"
                android:layout_height="@dimen/_28sdp"
                android:layout_gravity="end"
                android:layout_marginTop="@dimen/_10sdp"
                android:src="@drawable/ic_close_gray"
                tools:ignore="ContentDescription" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="@dimen/_15sdp"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/imageView9"
                    android:layout_width="@dimen/_20sdp"
                    android:layout_height="@dimen/_20sdp"
                    android:layout_gravity="end"
                    android:src="@drawable/heart_outline"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:ignore="ContentDescription" />

                <TextView
                    android:id="@+id/textView27"
                    style="@style/subtitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_10sdp"
                    android:text="@string/luvs"
                    app:layout_constraintBottom_toBottomOf="@+id/imageView9"
                    app:layout_constraintStart_toEndOf="@+id/imageView9"
                    app:layout_constraintTop_toTopOf="@+id/imageView9" />

                <TextView
                    android:id="@+id/textView26"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/balance"
                    app:layout_constraintStart_toStartOf="@+id/textView27"
                    app:layout_constraintTop_toBottomOf="@+id/textView27" />

                <TextView
                    android:id="@+id/textView28"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="5dp"
                    android:textColor="@color/redgradstart"
                    app:layout_constraintStart_toEndOf="@+id/textView26"
                    app:layout_constraintTop_toBottomOf="@+id/textView27"
                    tools:text="1200" />

                <ImageView
                    android:layout_width="@dimen/_20sdp"
                    android:layout_height="@dimen/_20sdp"
                    android:layout_gravity="end"
                    android:src="@drawable/ic_baseline_arrow_downward_24"
                    app:layout_constraintBottom_toBottomOf="@+id/textView29"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/textView29"
                    app:tint="@color/gray"
                    tools:ignore="ContentDescription" />

                <TextView
                    android:id="@+id/textView29"
                    style="@style/small"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_5sdp"
                    android:text="@string/use_luvs_to_buy_and_send_gifts_to_anyone_with_the_luv_app"
                    android:textColor="@color/gray"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@+id/textView26"
                    app:layout_constraintTop_toBottomOf="@+id/textView26" />


            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="@dimen/_15sdp"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/imageView19"
                    android:layout_width="@dimen/_20sdp"
                    android:layout_height="@dimen/_20sdp"
                    android:layout_gravity="end"
                    android:src="@drawable/diamond"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:tint="@color/blue"
                    tools:ignore="ContentDescription" />

                <TextView
                    android:id="@+id/textView127"
                    style="@style/subtitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_10sdp"
                    android:text="@string/diamonds"
                    app:layout_constraintBottom_toBottomOf="@+id/imageView19"
                    app:layout_constraintStart_toEndOf="@+id/imageView19"
                    app:layout_constraintTop_toTopOf="@+id/imageView19" />

                <TextView
                    android:id="@+id/textView126"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/balance"
                    app:layout_constraintStart_toStartOf="@+id/textView127"
                    app:layout_constraintTop_toBottomOf="@+id/textView127" />

                <TextView
                    android:id="@+id/tvDiamonds"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="5dp"
                    android:textColor="@color/blue"
                    app:layout_constraintStart_toEndOf="@+id/textView126"
                    app:layout_constraintTop_toBottomOf="@+id/textView127"
                    tools:text="1200" />

                <ImageView
                    android:layout_width="@dimen/_20sdp"
                    android:layout_height="@dimen/_20sdp"
                    android:layout_gravity="end"
                    android:src="@drawable/ic_baseline_arrow_downward_24"
                    app:layout_constraintBottom_toBottomOf="@+id/textView129"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/textView129"
                    app:tint="@color/gray"
                    tools:ignore="ContentDescription" />

                <TextView
                    android:id="@+id/textView129"
                    style="@style/small"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_5sdp"
                    android:text="@string/all_the_luv_gifts_that_you_receive_are_converted_into_diamonds"
                    android:textColor="@color/gray"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@+id/textView126"
                    app:layout_constraintTop_toBottomOf="@+id/textView126" />


            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="@dimen/_15sdp"
                android:layout_marginBottom="@dimen/_35sdp"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/imageView11"
                    android:layout_width="@dimen/_20sdp"
                    android:layout_height="@dimen/_20sdp"
                    android:layout_gravity="end"
                    android:src="@drawable/dollar_square"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:tint="@color/green"
                    tools:ignore="ContentDescription" />

                <TextView
                    android:id="@+id/textView32"
                    style="@style/subtitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_10sdp"
                    android:text="@string/usd"
                    app:layout_constraintBottom_toBottomOf="@+id/imageView11"
                    app:layout_constraintStart_toEndOf="@+id/imageView11"
                    app:layout_constraintTop_toTopOf="@+id/imageView11" />

                <TextView
                    android:id="@+id/textView31"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/balance"
                    app:layout_constraintStart_toStartOf="@+id/textView32"
                    app:layout_constraintTop_toBottomOf="@+id/textView32" />

                <TextView
                    android:id="@+id/tvUsd"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="5dp"
                    android:textColor="@color/green"
                    app:layout_constraintBottom_toBottomOf="@+id/textView31"
                    app:layout_constraintStart_toEndOf="@+id/textView31"
                    app:layout_constraintTop_toTopOf="@+id/textView31"
                    tools:text="1200" />

                <ImageView
                    android:layout_width="@dimen/_20sdp"
                    android:layout_height="@dimen/_20sdp"
                    android:layout_gravity="end"
                    android:src="@drawable/ic_baseline_arrow_downward_24"
                    app:layout_constraintBottom_toBottomOf="@+id/textView30"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/textView30"
                    app:tint="@color/gray"
                    tools:ignore="ContentDescription" />

                <TextView
                    android:id="@+id/textView30"
                    style="@style/small"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_5sdp"
                    android:text="@string/withdraw_your_diamonds_to_your_paypal_in_usd"
                    android:textColor="@color/gray"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@+id/textView31"
                    app:layout_constraintTop_toBottomOf="@+id/textView31" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </LinearLayout>

    </androidx.cardview.widget.CardView>

</androidx.constraintlayout.widget.ConstraintLayout>
