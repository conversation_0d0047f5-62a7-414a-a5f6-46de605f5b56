package com.flashbid.luv.ui.fragments.battle

import com.flashbid.luv.models.battle.UnifiedStreamMessage
import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonDeserializer
import com.google.gson.JsonElement
import com.google.gson.JsonParseException
import java.lang.reflect.Type

class UnifiedStreamMessageDeserializer : JsonDeserializer<UnifiedStreamMessage> {
    override fun deserialize(json: JsonElement, typeOfT: Type, context: JsonDeserializationContext): UnifiedStreamMessage {
         val jsonObject = json.asJsonObject

        val unknownJSON = ""
        return when (val type = jsonObject.get("type").asString) {
            "TextMessage" -> context.deserialize(jsonObject, UnifiedStreamMessage.TextMessage::class.java)
            "GiftMessage" -> context.deserialize(jsonObject, UnifiedStreamMessage.GiftMessage::class.java)
            "AudienceMessage" -> context.deserialize(jsonObject, UnifiedStreamMessage.AudienceMessage::class.java)
            "BattleStartMessage" -> context.deserialize(jsonObject, UnifiedStreamMessage.BattleStartMessage::class.java)
            "BattleEndMessage" -> context.deserialize(jsonObject, UnifiedStreamMessage.BattleEndMessage::class.java)
            "BattleClosedMessage" -> context.deserialize(jsonObject, UnifiedStreamMessage.BattleClosedMessage::class.java)
            "BattleRematchMessage" -> context.deserialize(jsonObject, UnifiedStreamMessage.BattleRematchMessage::class.java)
            "AudianceLeaveMessage" -> context.deserialize(jsonObject, UnifiedStreamMessage.AudianceLeaveMessage::class.java)
            else -> throw JsonParseException("Unknown type in UnifiedStreamMessage: $type")
        }
    }
}
