1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.flashbid.luv"
4    android:versionCode="169"
5    android:versionName="6.3.8" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
8-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml
9        android:targetSdkVersion="34" />
9-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml
10
11    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
11-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:5:5-79
11-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:5:22-76
12    <uses-permission android:name="android.permission.CAMERA" />
12-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:6:5-65
12-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:6:22-62
13    <uses-permission android:name="android.permission.INTERNET" />
13-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:7:5-67
13-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:7:22-64
14    <uses-permission android:name="com.android.vending.BILLING" />
14-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:8:5-67
14-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:8:22-64
15    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
15-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:9:5-77
15-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:9:22-74
16    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
16-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:10:5-79
16-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:10:22-76
17    <uses-permission
17-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:11:5-74
18        android:name="android.permission.BLUETOOTH_ADMIN"
18-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:11:22-71
19        android:required="false" />
19-->[org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:12:9-33
20    <!-- <uses-permission android:name="android.permission.FOREGROUND_SERVICE" /> -->
21
22    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
22-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:14:5-74
22-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:14:22-72
23    <uses-permission android:name="android.permission.RECORD_AUDIO" />
23-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:15:5-71
23-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:15:22-68
24    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
24-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:16:5-80
24-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:16:22-77
25    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
25-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:17:5-76
25-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:17:22-73
26
27    <!-- For Android 12 and above devices, the following permission is also required. -->
28    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
28-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:20:5-76
28-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:20:22-73
29    <uses-permission
29-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:21:5-68
30        android:name="android.permission.BLUETOOTH"
30-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:21:22-65
31        android:required="false" />
31-->[org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:9:9-33
32    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
32-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:22:5-73
32-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:22:22-70
33    <uses-permission android:name="android.permission.BLUETOOTH_PRIVILEGED" />
33-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:23:5-79
33-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:23:22-76
34    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
34-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:25:5-80
34-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:25:22-78
35    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
35-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:26:5-80
35-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:26:22-77
36    <uses-permission android:name="android.permission.WAKE_LOCK" />
36-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:28:5-67
36-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:28:22-65
37    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
37-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:29:5-79
37-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:29:22-76
38
39    <uses-feature android:name="android.hardware.camera" />
39-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:31:5-60
39-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:31:19-57
40    <uses-feature android:name="android.hardware.camera.autofocus" />
40-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:32:5-70
40-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:32:19-67
41
42    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_LOCATION" />
42-->[org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:13:5-86
42-->[org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:13:22-83
43    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
43-->[org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:14:5-81
43-->[org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:14:22-78
44    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
44-->[org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:15:5-81
44-->[org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:15:22-78
45
46    <uses-feature
46-->[com.github.bruferrari:CameraKit-Android:0.10.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1f0df7c8b3a52f309b3391a1ea5030d2\transformed\jetified-CameraKit-Android-0.10.1\AndroidManifest.xml:16:5-18:36
47        android:name="android.hardware.camera.front"
47-->[com.github.bruferrari:CameraKit-Android:0.10.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1f0df7c8b3a52f309b3391a1ea5030d2\transformed\jetified-CameraKit-Android-0.10.1\AndroidManifest.xml:17:9-53
48        android:required="false" />
48-->[com.github.bruferrari:CameraKit-Android:0.10.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1f0df7c8b3a52f309b3391a1ea5030d2\transformed\jetified-CameraKit-Android-0.10.1\AndroidManifest.xml:18:9-33
49    <uses-feature
49-->[com.github.bruferrari:CameraKit-Android:0.10.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1f0df7c8b3a52f309b3391a1ea5030d2\transformed\jetified-CameraKit-Android-0.10.1\AndroidManifest.xml:19:5-21:36
50        android:name="android.hardware.microphone"
50-->[com.github.bruferrari:CameraKit-Android:0.10.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1f0df7c8b3a52f309b3391a1ea5030d2\transformed\jetified-CameraKit-Android-0.10.1\AndroidManifest.xml:20:9-51
51        android:required="false" /> <!-- Required by older versions of Google Play services to create IID tokens -->
51-->[com.github.bruferrari:CameraKit-Android:0.10.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1f0df7c8b3a52f309b3391a1ea5030d2\transformed\jetified-CameraKit-Android-0.10.1\AndroidManifest.xml:21:9-33
52    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
52-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\50f13cf14d327801350c587d0bf46851\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:28:5-82
52-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\50f13cf14d327801350c587d0bf46851\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:28:22-79
53    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
53-->[com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\231488c28dff287f2e7cb7e3e1ee51ab\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:25:5-79
53-->[com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\231488c28dff287f2e7cb7e3e1ee51ab\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:25:22-76
54    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
54-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1a1d4f07b33d8783f810104ef22bf50f\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:26:5-110
54-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1a1d4f07b33d8783f810104ef22bf50f\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:26:22-107
55
56    <uses-feature
56-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e090038d7f4ca21339112e8dc687487e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:36:5-38:36
57        android:name="android.hardware.camera.flash"
57-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e090038d7f4ca21339112e8dc687487e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:37:9-53
58        android:required="false" />
58-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e090038d7f4ca21339112e8dc687487e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:38:9-33
59    <uses-feature
59-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e090038d7f4ca21339112e8dc687487e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:39:5-41:36
60        android:name="android.hardware.screen.landscape"
60-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e090038d7f4ca21339112e8dc687487e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:40:9-57
61        android:required="false" />
61-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e090038d7f4ca21339112e8dc687487e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:41:9-33
62    <uses-feature
62-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e090038d7f4ca21339112e8dc687487e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:42:5-44:36
63        android:name="android.hardware.wifi"
63-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e090038d7f4ca21339112e8dc687487e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:43:9-45
64        android:required="false" />
64-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e090038d7f4ca21339112e8dc687487e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:44:9-33
65
66    <queries>
66-->[com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\bc76bf08a15cc9c299066e5371206aa1\transformed\jetified-billing-6.0.1\AndroidManifest.xml:12:5-16:15
67        <intent>
67-->[com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\bc76bf08a15cc9c299066e5371206aa1\transformed\jetified-billing-6.0.1\AndroidManifest.xml:13:9-15:18
68            <action android:name="com.android.vending.billing.InAppBillingService.BIND" />
68-->[com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\bc76bf08a15cc9c299066e5371206aa1\transformed\jetified-billing-6.0.1\AndroidManifest.xml:14:13-91
68-->[com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\bc76bf08a15cc9c299066e5371206aa1\transformed\jetified-billing-6.0.1\AndroidManifest.xml:14:21-88
69        </intent>
70    </queries>
71
72    <permission
72-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fdc9cfd33c5a21669dc8cdc95e0cd352\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
73        android:name="com.flashbid.luv.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
73-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fdc9cfd33c5a21669dc8cdc95e0cd352\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
74        android:protectionLevel="signature" />
74-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fdc9cfd33c5a21669dc8cdc95e0cd352\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
75
76    <uses-permission android:name="com.flashbid.luv.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
76-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fdc9cfd33c5a21669dc8cdc95e0cd352\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
76-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fdc9cfd33c5a21669dc8cdc95e0cd352\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
77
78    <application
78-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:34:5-93:19
79        android:name="com.flashbid.luv.App"
79-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:35:9-28
80        android:allowBackup="true"
80-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:36:9-35
81        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
81-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fdc9cfd33c5a21669dc8cdc95e0cd352\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
82        android:dataExtractionRules="@xml/data_extraction_rules"
82-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:37:9-65
83        android:debuggable="true"
84        android:extractNativeLibs="false"
85        android:fullBackupContent="@xml/backup_rules"
85-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:38:9-54
86        android:icon="@mipmap/app_icon"
86-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:39:9-40
87        android:label="@string/app_name"
87-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:40:9-41
88        android:largeHeap="true"
88-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:41:9-33
89        android:roundIcon="@mipmap/app_icon_round"
89-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:42:9-51
90        android:supportsRtl="true"
90-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:43:9-35
91        android:testOnly="true"
92        android:theme="@style/AppTheme" >
92-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:44:9-40
93        <activity
93-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:47:9-74:20
94            android:name="com.flashbid.luv.MainActivity"
94-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:48:13-41
95            android:exported="true"
95-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:49:13-36
96            android:launchMode="singleTask"
96-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:52:13-44
97            android:screenOrientation="portrait" >
97-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:50:13-49
98            <intent-filter>
98-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:53:13-57:29
99                <action android:name="android.intent.action.MAIN" />
99-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:54:17-69
99-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:54:25-66
100
101                <category android:name="android.intent.category.LAUNCHER" />
101-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:56:17-77
101-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:56:27-74
102            </intent-filter>
103            <intent-filter>
103-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:59:13-68:29
104                <action android:name="android.intent.action.VIEW" />
104-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:60:17-69
104-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:60:25-66
105
106                <category android:name="android.intent.category.DEFAULT" />
106-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:62:17-76
106-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:62:27-73
107                <category android:name="android.intent.category.BROWSABLE" />
107-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:63:17-78
107-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:63:27-75
108
109                <data
109-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:65:17-67:46
110                    android:host="luvapp.page.link"
110-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:66:21-52
111                    android:scheme="https" />
111-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:67:21-43
112            </intent-filter>
113
114            <meta-data
114-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:70:13-72:36
115                android:name="android.app.lib_name"
115-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:71:17-52
116                android:value="" />
116-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:72:17-33
117        </activity>
118
119        <service
119-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:76:9-82:19
120            android:name="com.flashbid.luv.fcm.MyFirebasePushNotifications"
120-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:77:13-60
121            android:exported="false" >
121-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:78:13-37
122            <intent-filter>
122-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:79:13-81:29
123                <action android:name="com.google.firebase.MESSAGING_EVENT" />
123-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:80:17-77
123-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:80:25-75
124            </intent-filter>
125        </service>
126        <service
126-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:85:9-87:39
127            android:name="com.flashbid.luv.util.BeaconService"
127-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:85:18-52
128            android:enabled="true"
128-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:86:13-35
129            android:exported="false" />
129-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:87:13-37
130
131        <!-- <service android:name=".services.BroadcastService" -->
132        <!-- android:enabled="true" -->
133        <!-- android:exported="false"/> -->
134
135        <service
135-->[com.google.firebase:firebase-dynamic-links-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8a1e83306d631bfa7be20217a9d58500\transformed\jetified-firebase-dynamic-links-ktx-21.1.0\AndroidManifest.xml:24:9-30:19
136            android:name="com.google.firebase.components.ComponentDiscoveryService"
136-->[com.google.firebase:firebase-dynamic-links-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8a1e83306d631bfa7be20217a9d58500\transformed\jetified-firebase-dynamic-links-ktx-21.1.0\AndroidManifest.xml:25:13-84
137            android:directBootAware="true"
137-->[com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\43505c5b2b7b6eb07641cdceee51b9d9\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:34:13-43
138            android:exported="false" >
138-->[com.google.firebase:firebase-dynamic-links-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8a1e83306d631bfa7be20217a9d58500\transformed\jetified-firebase-dynamic-links-ktx-21.1.0\AndroidManifest.xml:26:13-37
139            <meta-data
139-->[com.google.firebase:firebase-dynamic-links-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8a1e83306d631bfa7be20217a9d58500\transformed\jetified-firebase-dynamic-links-ktx-21.1.0\AndroidManifest.xml:27:13-29:85
140                android:name="com.google.firebase.components:com.google.firebase.dynamiclinks.ktx.FirebaseDynamicLinksKtxRegistrar"
140-->[com.google.firebase:firebase-dynamic-links-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8a1e83306d631bfa7be20217a9d58500\transformed\jetified-firebase-dynamic-links-ktx-21.1.0\AndroidManifest.xml:28:17-132
141                android:value="com.google.firebase.components.ComponentRegistrar" />
141-->[com.google.firebase:firebase-dynamic-links-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8a1e83306d631bfa7be20217a9d58500\transformed\jetified-firebase-dynamic-links-ktx-21.1.0\AndroidManifest.xml:29:17-82
142            <meta-data
142-->[com.google.firebase:firebase-crashlytics-ktx:18.3.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\dfd474a0c38a487413af5e9cc8d67afc\transformed\jetified-firebase-crashlytics-ktx-18.3.2\AndroidManifest.xml:26:13-28:85
143                android:name="com.google.firebase.components:com.google.firebase.crashlytics.ktx.FirebaseCrashlyticsKtxRegistrar"
143-->[com.google.firebase:firebase-crashlytics-ktx:18.3.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\dfd474a0c38a487413af5e9cc8d67afc\transformed\jetified-firebase-crashlytics-ktx-18.3.2\AndroidManifest.xml:27:17-130
144                android:value="com.google.firebase.components.ComponentRegistrar" />
144-->[com.google.firebase:firebase-crashlytics-ktx:18.3.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\dfd474a0c38a487413af5e9cc8d67afc\transformed\jetified-firebase-crashlytics-ktx-18.3.2\AndroidManifest.xml:28:17-82
145            <meta-data
145-->[com.google.firebase:firebase-analytics-ktx:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4633587b00bb6458a88b4bd9541faf20\transformed\jetified-firebase-analytics-ktx-21.2.0\AndroidManifest.xml:11:13-13:85
146                android:name="com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsKtxRegistrar"
146-->[com.google.firebase:firebase-analytics-ktx:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4633587b00bb6458a88b4bd9541faf20\transformed\jetified-firebase-analytics-ktx-21.2.0\AndroidManifest.xml:12:17-126
147                android:value="com.google.firebase.components.ComponentRegistrar" />
147-->[com.google.firebase:firebase-analytics-ktx:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4633587b00bb6458a88b4bd9541faf20\transformed\jetified-firebase-analytics-ktx-21.2.0\AndroidManifest.xml:13:17-82
148            <meta-data
148-->[com.google.firebase:firebase-auth-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ddbadfd7d70329eb339fcd0762c6a5a6\transformed\jetified-firebase-auth-ktx-21.1.0\AndroidManifest.xml:11:13-13:85
149                android:name="com.google.firebase.components:com.google.firebase.auth.ktx.FirebaseAuthKtxRegistrar"
149-->[com.google.firebase:firebase-auth-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ddbadfd7d70329eb339fcd0762c6a5a6\transformed\jetified-firebase-auth-ktx-21.1.0\AndroidManifest.xml:12:17-116
150                android:value="com.google.firebase.components.ComponentRegistrar" />
150-->[com.google.firebase:firebase-auth-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ddbadfd7d70329eb339fcd0762c6a5a6\transformed\jetified-firebase-auth-ktx-21.1.0\AndroidManifest.xml:13:17-82
151            <meta-data
151-->[com.google.firebase:firebase-messaging-ktx:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\4533ad2b63c6d6128c8298815b20a971\transformed\jetified-firebase-messaging-ktx-23.1.1\AndroidManifest.xml:28:13-30:85
152                android:name="com.google.firebase.components:com.google.firebase.messaging.ktx.FirebaseMessagingKtxRegistrar"
152-->[com.google.firebase:firebase-messaging-ktx:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\4533ad2b63c6d6128c8298815b20a971\transformed\jetified-firebase-messaging-ktx-23.1.1\AndroidManifest.xml:29:17-126
153                android:value="com.google.firebase.components.ComponentRegistrar" />
153-->[com.google.firebase:firebase-messaging-ktx:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\4533ad2b63c6d6128c8298815b20a971\transformed\jetified-firebase-messaging-ktx-23.1.1\AndroidManifest.xml:30:17-82
154            <meta-data
154-->[com.google.firebase:firebase-common-ktx:20.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\de59d1332438e2bc7040e58d2c13e54c\transformed\jetified-firebase-common-ktx-20.2.0\AndroidManifest.xml:14:13-16:85
155                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonKtxRegistrar"
155-->[com.google.firebase:firebase-common-ktx:20.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\de59d1332438e2bc7040e58d2c13e54c\transformed\jetified-firebase-common-ktx-20.2.0\AndroidManifest.xml:15:17-113
156                android:value="com.google.firebase.components.ComponentRegistrar" />
156-->[com.google.firebase:firebase-common-ktx:20.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\de59d1332438e2bc7040e58d2c13e54c\transformed\jetified-firebase-common-ktx-20.2.0\AndroidManifest.xml:16:17-82
157            <meta-data
157-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aab237e9eab9fdab0322490d74dca402\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:67:13-69:85
158                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
158-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aab237e9eab9fdab0322490d74dca402\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:68:17-109
159                android:value="com.google.firebase.components.ComponentRegistrar" />
159-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aab237e9eab9fdab0322490d74dca402\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:69:17-82
160            <meta-data
160-->[com.google.firebase:firebase-dynamic-links:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cacaab093d05956aa25d8a6eb172d90c\transformed\jetified-firebase-dynamic-links-21.1.0\AndroidManifest.xml:26:13-28:85
161                android:name="com.google.firebase.components:com.google.firebase.dynamiclinks.internal.FirebaseDynamicLinkRegistrar"
161-->[com.google.firebase:firebase-dynamic-links:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cacaab093d05956aa25d8a6eb172d90c\transformed\jetified-firebase-dynamic-links-21.1.0\AndroidManifest.xml:27:17-133
162                android:value="com.google.firebase.components.ComponentRegistrar" />
162-->[com.google.firebase:firebase-dynamic-links:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cacaab093d05956aa25d8a6eb172d90c\transformed\jetified-firebase-dynamic-links-21.1.0\AndroidManifest.xml:28:17-82
163            <meta-data
163-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\50f13cf14d327801350c587d0bf46851\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:55:13-57:85
164                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
164-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\50f13cf14d327801350c587d0bf46851\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:56:17-119
165                android:value="com.google.firebase.components.ComponentRegistrar" />
165-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\50f13cf14d327801350c587d0bf46851\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:57:17-82
166            <meta-data
166-->[com.google.firebase:firebase-crashlytics:18.3.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\c7c80e3f565c1b8cf2aedc222f8b8d9d\transformed\jetified-firebase-crashlytics-18.3.2\AndroidManifest.xml:17:13-19:85
167                android:name="com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar"
167-->[com.google.firebase:firebase-crashlytics:18.3.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\c7c80e3f565c1b8cf2aedc222f8b8d9d\transformed\jetified-firebase-crashlytics-18.3.2\AndroidManifest.xml:18:17-115
168                android:value="com.google.firebase.components.ComponentRegistrar" />
168-->[com.google.firebase:firebase-crashlytics:18.3.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\c7c80e3f565c1b8cf2aedc222f8b8d9d\transformed\jetified-firebase-crashlytics-18.3.2\AndroidManifest.xml:19:17-82
169            <meta-data
169-->[com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\231488c28dff287f2e7cb7e3e1ee51ab\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:31:13-33:85
170                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
170-->[com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\231488c28dff287f2e7cb7e3e1ee51ab\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:32:17-139
171                android:value="com.google.firebase.components.ComponentRegistrar" />
171-->[com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\231488c28dff287f2e7cb7e3e1ee51ab\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:33:17-82
172            <meta-data
172-->[com.google.firebase:firebase-installations:17.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1d9d7423538685835871cb5aa6ad5398\transformed\jetified-firebase-installations-17.1.0\AndroidManifest.xml:17:13-19:85
173                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
173-->[com.google.firebase:firebase-installations:17.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1d9d7423538685835871cb5aa6ad5398\transformed\jetified-firebase-installations-17.1.0\AndroidManifest.xml:18:17-127
174                android:value="com.google.firebase.components.ComponentRegistrar" />
174-->[com.google.firebase:firebase-installations:17.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1d9d7423538685835871cb5aa6ad5398\transformed\jetified-firebase-installations-17.1.0\AndroidManifest.xml:19:17-82
175            <meta-data
175-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13c74bafdc5d245406b0a8efef9b5ab\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:27:13-29:85
176                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
176-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13c74bafdc5d245406b0a8efef9b5ab\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:28:17-115
177                android:value="com.google.firebase.components.ComponentRegistrar" />
177-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13c74bafdc5d245406b0a8efef9b5ab\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:29:17-82
178        </service>
179
180        <activity
180-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\122f93913edc6855b2697d126574e839\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:27:9-29:72
181            android:name="com.karumi.dexter.DexterActivity"
181-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\122f93913edc6855b2697d126574e839\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:28:13-60
182            android:theme="@style/Dexter.Internal.Theme.Transparent" />
182-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\122f93913edc6855b2697d126574e839\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:29:13-69
183
184        <receiver
184-->[org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:18:9-26:20
185            android:name="org.altbeacon.beacon.startup.StartupBroadcastReceiver"
185-->[org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:19:13-81
186            android:exported="false" >
186-->[org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:20:13-37
187            <intent-filter>
187-->[org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:21:13-25:29
188                <action android:name="android.intent.action.BOOT_COMPLETED" />
188-->[org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:22:17-79
188-->[org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:22:25-76
189                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
189-->[org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:23:17-87
189-->[org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:23:25-84
190                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
190-->[org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:24:17-90
190-->[org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:24:25-87
191            </intent-filter>
192        </receiver>
193
194        <service
194-->[org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:28:9-34:38
195            android:name="org.altbeacon.beacon.service.BeaconService"
195-->[org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:29:13-70
196            android:enabled="true"
196-->[org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:30:13-35
197            android:exported="false"
197-->[org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:31:13-37
198            android:foregroundServiceType="location"
198-->[org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:32:13-53
199            android:isolatedProcess="false"
199-->[org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:33:13-44
200            android:label="beacon" />
200-->[org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:34:13-35
201        <service
201-->[org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:35:9-38:40
202            android:name="org.altbeacon.beacon.BeaconIntentProcessor"
202-->[org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:36:13-70
203            android:enabled="true"
203-->[org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:37:13-35
204            android:exported="false" />
204-->[org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:38:13-37
205        <service
205-->[org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:39:9-49:19
206            android:name="org.altbeacon.beacon.service.ScanJob"
206-->[org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:40:13-64
207            android:exported="false"
207-->[org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:41:13-37
208            android:permission="android.permission.BIND_JOB_SERVICE" >
208-->[org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:42:13-69
209            <meta-data
209-->[org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:43:13-45:45
210                android:name="immediateScanJobId"
210-->[org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:44:17-50
211                android:value="208352939" />
211-->[org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:45:17-42
212            <meta-data
212-->[org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:46:13-48:45
213                android:name="periodicScanJobId"
213-->[org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:47:17-49
214                android:value="208352940" />
214-->[org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:48:17-42
215        </service>
216        <service
216-->[org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:50:9-57:19
217            android:name="org.altbeacon.bluetooth.BluetoothTestJob"
217-->[org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:51:13-68
218            android:exported="false"
218-->[org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:52:13-37
219            android:permission="android.permission.BIND_JOB_SERVICE" >
219-->[org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:53:13-69
220            <meta-data
220-->[org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:54:13-56:46
221                android:name="jobId"
221-->[org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:55:17-37
222                android:value="1799803768" />
222-->[org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:56:17-43
223        </service>
224
225        <activity
225-->[com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\068cfc6cfd939845ffece9a7aaeffb7e\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:23:9-27:75
226            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
226-->[com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\068cfc6cfd939845ffece9a7aaeffb7e\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:24:13-93
227            android:excludeFromRecents="true"
227-->[com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\068cfc6cfd939845ffece9a7aaeffb7e\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:25:13-46
228            android:exported="false"
228-->[com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\068cfc6cfd939845ffece9a7aaeffb7e\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:26:13-37
229            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
229-->[com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\068cfc6cfd939845ffece9a7aaeffb7e\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:27:13-72
230        <!--
231            Service handling Google Sign-In user revocation. For apps that do not integrate with
232            Google Sign-In, this service will never be started.
233        -->
234        <service
234-->[com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\068cfc6cfd939845ffece9a7aaeffb7e\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:33:9-37:51
235            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
235-->[com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\068cfc6cfd939845ffece9a7aaeffb7e\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:34:13-89
236            android:exported="true"
236-->[com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\068cfc6cfd939845ffece9a7aaeffb7e\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:35:13-36
237            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
237-->[com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\068cfc6cfd939845ffece9a7aaeffb7e\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:36:13-107
238            android:visibleToInstantApps="true" />
238-->[com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\068cfc6cfd939845ffece9a7aaeffb7e\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:37:13-48
239
240        <activity
240-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aab237e9eab9fdab0322490d74dca402\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:27:9-44:20
241            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
241-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aab237e9eab9fdab0322490d74dca402\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:28:13-80
242            android:excludeFromRecents="true"
242-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aab237e9eab9fdab0322490d74dca402\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:29:13-46
243            android:exported="true"
243-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aab237e9eab9fdab0322490d74dca402\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:30:13-36
244            android:launchMode="singleTask"
244-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aab237e9eab9fdab0322490d74dca402\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:31:13-44
245            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
245-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aab237e9eab9fdab0322490d74dca402\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:32:13-72
246            <intent-filter>
246-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aab237e9eab9fdab0322490d74dca402\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:33:13-43:29
247                <action android:name="android.intent.action.VIEW" />
247-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:60:17-69
247-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:60:25-66
248
249                <category android:name="android.intent.category.DEFAULT" />
249-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:62:17-76
249-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:62:27-73
250                <category android:name="android.intent.category.BROWSABLE" />
250-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:63:17-78
250-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:63:27-75
251
252                <data
252-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:65:17-67:46
253                    android:host="firebase.auth"
253-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:66:21-52
254                    android:path="/"
255                    android:scheme="genericidp" />
255-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:67:21-43
256            </intent-filter>
257        </activity>
258        <activity
258-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aab237e9eab9fdab0322490d74dca402\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:45:9-62:20
259            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
259-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aab237e9eab9fdab0322490d74dca402\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:46:13-79
260            android:excludeFromRecents="true"
260-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aab237e9eab9fdab0322490d74dca402\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:47:13-46
261            android:exported="true"
261-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aab237e9eab9fdab0322490d74dca402\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:48:13-36
262            android:launchMode="singleTask"
262-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aab237e9eab9fdab0322490d74dca402\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:49:13-44
263            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
263-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aab237e9eab9fdab0322490d74dca402\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:50:13-72
264            <intent-filter>
264-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aab237e9eab9fdab0322490d74dca402\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:51:13-61:29
265                <action android:name="android.intent.action.VIEW" />
265-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:60:17-69
265-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:60:25-66
266
267                <category android:name="android.intent.category.DEFAULT" />
267-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:62:17-76
267-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:62:27-73
268                <category android:name="android.intent.category.BROWSABLE" />
268-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:63:17-78
268-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:63:27-75
269
270                <data
270-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:65:17-67:46
271                    android:host="firebase.auth"
271-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:66:21-52
272                    android:path="/"
273                    android:scheme="recaptcha" />
273-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:67:21-43
274            </intent-filter>
275        </activity>
276
277        <receiver
277-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\50f13cf14d327801350c587d0bf46851\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:31:9-38:20
278            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
278-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\50f13cf14d327801350c587d0bf46851\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:32:13-78
279            android:exported="true"
279-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\50f13cf14d327801350c587d0bf46851\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:33:13-36
280            android:permission="com.google.android.c2dm.permission.SEND" >
280-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\50f13cf14d327801350c587d0bf46851\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:34:13-73
281            <intent-filter>
281-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\50f13cf14d327801350c587d0bf46851\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:35:13-37:29
282                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
282-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\50f13cf14d327801350c587d0bf46851\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:36:17-81
282-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\50f13cf14d327801350c587d0bf46851\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:36:25-78
283            </intent-filter>
284        </receiver>
285        <!--
286             FirebaseMessagingService performs security checks at runtime,
287             but set to not exported to explicitly avoid allowing another app to call it.
288        -->
289        <service
289-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\50f13cf14d327801350c587d0bf46851\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:44:9-51:19
290            android:name="com.google.firebase.messaging.FirebaseMessagingService"
290-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\50f13cf14d327801350c587d0bf46851\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:45:13-82
291            android:directBootAware="true"
291-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\50f13cf14d327801350c587d0bf46851\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:46:13-43
292            android:exported="false" >
292-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\50f13cf14d327801350c587d0bf46851\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:47:13-37
293            <intent-filter android:priority="-500" >
293-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:79:13-81:29
294                <action android:name="com.google.firebase.MESSAGING_EVENT" />
294-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:80:17-77
294-->C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:80:25-75
295            </intent-filter>
296        </service>
297
298        <activity
298-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cdfce347bcc0c6410f73de8b2f931e7b\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
299            android:name="com.google.android.gms.common.api.GoogleApiActivity"
299-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cdfce347bcc0c6410f73de8b2f931e7b\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:19-85
300            android:exported="false"
300-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cdfce347bcc0c6410f73de8b2f931e7b\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:22:19-43
301            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
301-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cdfce347bcc0c6410f73de8b2f931e7b\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:21:19-78
302
303        <provider
303-->[com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\43505c5b2b7b6eb07641cdceee51b9d9\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:25:9-30:39
304            android:name="com.google.firebase.provider.FirebaseInitProvider"
304-->[com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\43505c5b2b7b6eb07641cdceee51b9d9\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:26:13-77
305            android:authorities="com.flashbid.luv.firebaseinitprovider"
305-->[com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\43505c5b2b7b6eb07641cdceee51b9d9\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:27:13-72
306            android:directBootAware="true"
306-->[com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\43505c5b2b7b6eb07641cdceee51b9d9\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:28:13-43
307            android:exported="false"
307-->[com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\43505c5b2b7b6eb07641cdceee51b9d9\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:29:13-37
308            android:initOrder="100" />
308-->[com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\43505c5b2b7b6eb07641cdceee51b9d9\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:30:13-36
309
310        <receiver
310-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1a1d4f07b33d8783f810104ef22bf50f\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:29:9-33:20
311            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
311-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1a1d4f07b33d8783f810104ef22bf50f\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:30:13-85
312            android:enabled="true"
312-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1a1d4f07b33d8783f810104ef22bf50f\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:31:13-35
313            android:exported="false" >
313-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1a1d4f07b33d8783f810104ef22bf50f\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:32:13-37
314        </receiver>
315
316        <service
316-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1a1d4f07b33d8783f810104ef22bf50f\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:35:9-38:40
317            android:name="com.google.android.gms.measurement.AppMeasurementService"
317-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1a1d4f07b33d8783f810104ef22bf50f\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:36:13-84
318            android:enabled="true"
318-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1a1d4f07b33d8783f810104ef22bf50f\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:37:13-35
319            android:exported="false" />
319-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1a1d4f07b33d8783f810104ef22bf50f\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:38:13-37
320        <service
320-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1a1d4f07b33d8783f810104ef22bf50f\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:39:9-43:72
321            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
321-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1a1d4f07b33d8783f810104ef22bf50f\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:40:13-87
322            android:enabled="true"
322-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1a1d4f07b33d8783f810104ef22bf50f\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:41:13-35
323            android:exported="false"
323-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1a1d4f07b33d8783f810104ef22bf50f\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:42:13-37
324            android:permission="android.permission.BIND_JOB_SERVICE" />
324-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1a1d4f07b33d8783f810104ef22bf50f\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:43:13-69
325
326        <meta-data
326-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b138217803023ee017e5a16beaa9ca30\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
327            android:name="com.google.android.gms.version"
327-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b138217803023ee017e5a16beaa9ca30\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
328            android:value="@integer/google_play_services_version" />
328-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b138217803023ee017e5a16beaa9ca30\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
329
330        <uses-library
330-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0c501d704c37b63fce0fcc21c8a605d7\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
331            android:name="androidx.window.extensions"
331-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0c501d704c37b63fce0fcc21c8a605d7\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
332            android:required="false" />
332-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0c501d704c37b63fce0fcc21c8a605d7\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
333        <uses-library
333-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0c501d704c37b63fce0fcc21c8a605d7\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
334            android:name="androidx.window.sidecar"
334-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0c501d704c37b63fce0fcc21c8a605d7\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
335            android:required="false" />
335-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0c501d704c37b63fce0fcc21c8a605d7\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
336
337        <activity
337-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e090038d7f4ca21339112e8dc687487e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:47:9-53:63
338            android:name="com.journeyapps.barcodescanner.CaptureActivity"
338-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e090038d7f4ca21339112e8dc687487e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:48:13-74
339            android:clearTaskOnLaunch="true"
339-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e090038d7f4ca21339112e8dc687487e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:49:13-45
340            android:screenOrientation="sensorLandscape"
340-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e090038d7f4ca21339112e8dc687487e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:50:13-56
341            android:stateNotNeeded="true"
341-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e090038d7f4ca21339112e8dc687487e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:51:13-42
342            android:theme="@style/zxing_CaptureTheme"
342-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e090038d7f4ca21339112e8dc687487e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:52:13-54
343            android:windowSoftInputMode="stateAlwaysHidden" />
343-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e090038d7f4ca21339112e8dc687487e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:53:13-60
344
345        <meta-data
345-->[com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\bc76bf08a15cc9c299066e5371206aa1\transformed\jetified-billing-6.0.1\AndroidManifest.xml:19:9-21:37
346            android:name="com.google.android.play.billingclient.version"
346-->[com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\bc76bf08a15cc9c299066e5371206aa1\transformed\jetified-billing-6.0.1\AndroidManifest.xml:20:13-73
347            android:value="6.0.1" />
347-->[com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\bc76bf08a15cc9c299066e5371206aa1\transformed\jetified-billing-6.0.1\AndroidManifest.xml:21:13-34
348
349        <activity
349-->[com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\bc76bf08a15cc9c299066e5371206aa1\transformed\jetified-billing-6.0.1\AndroidManifest.xml:23:9-27:75
350            android:name="com.android.billingclient.api.ProxyBillingActivity"
350-->[com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\bc76bf08a15cc9c299066e5371206aa1\transformed\jetified-billing-6.0.1\AndroidManifest.xml:24:13-78
351            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
351-->[com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\bc76bf08a15cc9c299066e5371206aa1\transformed\jetified-billing-6.0.1\AndroidManifest.xml:25:13-96
352            android:exported="false"
352-->[com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\bc76bf08a15cc9c299066e5371206aa1\transformed\jetified-billing-6.0.1\AndroidManifest.xml:26:13-37
353            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
353-->[com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\bc76bf08a15cc9c299066e5371206aa1\transformed\jetified-billing-6.0.1\AndroidManifest.xml:27:13-72
354
355        <service
355-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\dacfb878dcf3ada73c635c44ab3d3981\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
356            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
356-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\dacfb878dcf3ada73c635c44ab3d3981\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
357            android:exported="false" >
357-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\dacfb878dcf3ada73c635c44ab3d3981\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
358            <meta-data
358-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\dacfb878dcf3ada73c635c44ab3d3981\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
359                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
359-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\dacfb878dcf3ada73c635c44ab3d3981\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
360                android:value="cct" />
360-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\dacfb878dcf3ada73c635c44ab3d3981\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
361        </service>
362        <service
362-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\a5092a4592b4dfb55a0ebcf35f9d7ea4\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
363            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
363-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\a5092a4592b4dfb55a0ebcf35f9d7ea4\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
364            android:exported="false"
364-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\a5092a4592b4dfb55a0ebcf35f9d7ea4\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
365            android:permission="android.permission.BIND_JOB_SERVICE" >
365-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\a5092a4592b4dfb55a0ebcf35f9d7ea4\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
366        </service>
367
368        <receiver
368-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\a5092a4592b4dfb55a0ebcf35f9d7ea4\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
369            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
369-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\a5092a4592b4dfb55a0ebcf35f9d7ea4\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
370            android:exported="false" />
370-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\a5092a4592b4dfb55a0ebcf35f9d7ea4\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
371
372        <provider
372-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a0c43417dbd8eb111e7690e9d88b867f\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
373            android:name="androidx.startup.InitializationProvider"
373-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a0c43417dbd8eb111e7690e9d88b867f\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
374            android:authorities="com.flashbid.luv.androidx-startup"
374-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a0c43417dbd8eb111e7690e9d88b867f\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
375            android:exported="false" >
375-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a0c43417dbd8eb111e7690e9d88b867f\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
376            <meta-data
376-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a0c43417dbd8eb111e7690e9d88b867f\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
377                android:name="androidx.emoji2.text.EmojiCompatInitializer"
377-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a0c43417dbd8eb111e7690e9d88b867f\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
378                android:value="androidx.startup" />
378-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a0c43417dbd8eb111e7690e9d88b867f\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
379            <meta-data
379-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\2b0313006e36c3f090e112081263bb70\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
380                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
380-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\2b0313006e36c3f090e112081263bb70\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
381                android:value="androidx.startup" />
381-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\2b0313006e36c3f090e112081263bb70\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
382        </provider>
383    </application>
384
385</manifest>
