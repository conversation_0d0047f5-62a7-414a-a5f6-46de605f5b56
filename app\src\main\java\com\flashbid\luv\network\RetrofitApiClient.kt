package com.flashbid.luv.network

import android.content.ContentValues
import android.icu.util.TimeZone
import android.util.Log
import com.flashbid.luv.BuildConfig
import com.flashbid.luv.util.BaseLiveData
import com.flashbid.luv.util.Constants
import com.flashbid.luv.util.Constants.AUTH_TOKEN
import okhttp3.Interceptor
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.logging.HttpLoggingInterceptor
import org.koin.dsl.module
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit
import com.flashbid.luv.data.local.AppPreferences
import org.koin.android.ext.android.inject
import org.koin.java.KoinJavaComponent.inject
import org.koin.core.component.KoinComponent
import org.koin.core.component.get

val networkModule = module {
    single { RetrofitProvider.provideRetrofit() }
    factory { provideApi(get()) }
}
//private val pref by inject<AppPreferences>()
object RetrofitProvider : KoinComponent {
    fun provideRetrofit(): Retrofit {
        val interceptor = HttpLoggingInterceptor().setLevel(HttpLoggingInterceptor.Level.BODY)

        val pref: AppPreferences = get()
        val okHttpClient = OkHttpClient().newBuilder()
            .callTimeout(1, TimeUnit.MINUTES)
            .writeTimeout(1, TimeUnit.MINUTES)
            .readTimeout(1, TimeUnit.MINUTES)
            .addInterceptor(interceptor)
            .addInterceptor(Interceptor { chain ->
                val request: Request =
                    chain.request().newBuilder()
                        .addHeader("Content-Type", Constants.CONTENT_TYPE)
//                    .addHeader("Accept-Language", Constants.CURRENT_LOCALE)
                        .addHeader("Accept-Language", pref.appLanguage ?: "en")
                        .addHeader("x-project", BuildConfig.PROJECT_ID)
                        .addHeader("Authorization", AUTH_TOKEN.value ?: "")
                        .addHeader("X-Client-IP", Constants.PUBLIC_IP.value ?: "")
                        .addHeader("timezone", TimeZone.getDefault().id)
                        .build()
                val response = chain.proceed(request)
                Log.d(ContentValues.TAG, "API: ${request.body.toString()}")
                Log.d(ContentValues.TAG, "Authorization: ${AUTH_TOKEN.value ?: ""}")
                if (response.code == 401) {
                    Log.d(ContentValues.TAG, "Failed API: ${request.url.toString()}")
                    Log.d(ContentValues.TAG, "Failed API PARAMS: ${AUTH_TOKEN.value}")
                    BaseLiveData.isAuthorized.postValue(false)
                }
                response
            }).build()

        return Retrofit.Builder().baseUrl(BuildConfig.BASE_URL)
            .client(okHttpClient)
            .addConverterFactory(GsonConverterFactory.create())
            .build()
    }
}
fun provideApi(retrofit: Retrofit): ApiService = retrofit.create(ApiService::class.java)
