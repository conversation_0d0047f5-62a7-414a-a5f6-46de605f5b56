package com.flashbid.luv.repositories

import androidx.lifecycle.LiveData
import androidx.lifecycle.liveData
import com.flashbid.luv.models.remote.CodeDetailResponse
import com.flashbid.luv.models.remote.CreateCode
import com.flashbid.luv.models.remote.GetCodesResponse
import com.flashbid.luv.models.remote.MessageResponse
import com.flashbid.luv.models.remote.ScanResponse
import com.flashbid.luv.models.remote.SponsoredResponse
import com.flashbid.luv.network.RemoteDataSource
import com.flashbid.luv.network.Resource
import kotlinx.coroutines.Dispatchers

class CodeRepository(private val remoteDataSource: RemoteDataSource) {

    fun getCodeDetail(codeId: String): LiveData<Resource<CodeDetailResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getCodeDetail(codeId)
            emit(response)
        }

    fun getCodeDetailSponsor(codeId: String, sponsor: Boolean): LiveData<Resource<CodeDetailResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getCodeDetailSponsor(codeId, sponsor)
            emit(response)
        }

    fun deleteCode(codeId: String): LiveData<Resource<MessageResponse>> = liveData(Dispatchers.IO) {
        emit(Resource.loading(null))
        val response = remoteDataSource.deleteCode(codeId)
        emit(response)
    }

    fun scanCode(codeId: String): LiveData<Resource<ScanResponse>> = liveData(Dispatchers.IO) {
        emit(Resource.loading(null))
        val response = remoteDataSource.scanCode(codeId)
        emit(response)
    }

    fun createCode(request: CreateCode): LiveData<Resource<MessageResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.createCode(request)
            emit(response)
        }

    fun getSponsored(): LiveData<Resource<SponsoredResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getSponsored()
            emit(response)
        }

    fun getUserCodes(): LiveData<Resource<GetCodesResponse>> = liveData(Dispatchers.IO) {
        emit(Resource.loading(null))
        val response = remoteDataSource.getUserCodes()
        emit(response)
    }

    fun updateCodeDetail(codeId: String, request: CreateCode): LiveData<Resource<MessageResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.updateCodeDetail(codeId, request)
            emit(response)
        }

}