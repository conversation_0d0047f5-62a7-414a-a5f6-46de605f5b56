int anim abc_fade_in 0x7f010000
int anim abc_fade_out 0x7f010001
int anim abc_grow_fade_in_from_bottom 0x7f010002
int anim abc_popup_enter 0x7f010003
int anim abc_popup_exit 0x7f010004
int anim abc_shrink_fade_out_from_bottom 0x7f010005
int anim abc_slide_in_bottom 0x7f010006
int anim abc_slide_in_top 0x7f010007
int anim abc_slide_out_bottom 0x7f010008
int anim abc_slide_out_top 0x7f010009
int anim abc_tooltip_enter 0x7f01000a
int anim abc_tooltip_exit 0x7f01000b
int anim btn_checkbox_to_checked_box_inner_merged_animation 0x7f01000c
int anim btn_checkbox_to_checked_box_outer_merged_animation 0x7f01000d
int anim btn_checkbox_to_checked_icon_null_animation 0x7f01000e
int anim btn_checkbox_to_unchecked_box_inner_merged_animation 0x7f01000f
int anim btn_checkbox_to_unchecked_check_path_merged_animation 0x7f010010
int anim btn_checkbox_to_unchecked_icon_null_animation 0x7f010011
int anim btn_radio_to_off_mtrl_dot_group_animation 0x7f010012
int anim btn_radio_to_off_mtrl_ring_outer_animation 0x7f010013
int anim btn_radio_to_off_mtrl_ring_outer_path_animation 0x7f010014
int anim btn_radio_to_on_mtrl_dot_group_animation 0x7f010015
int anim btn_radio_to_on_mtrl_ring_outer_animation 0x7f010016
int anim btn_radio_to_on_mtrl_ring_outer_path_animation 0x7f010017
int anim design_bottom_sheet_slide_in 0x7f010018
int anim design_bottom_sheet_slide_out 0x7f010019
int anim design_snackbar_in 0x7f01001a
int anim design_snackbar_out 0x7f01001b
int anim fragment_fast_out_extra_slow_in 0x7f01001c
int anim linear_indeterminate_line1_head_interpolator 0x7f01001d
int anim linear_indeterminate_line1_tail_interpolator 0x7f01001e
int anim linear_indeterminate_line2_head_interpolator 0x7f01001f
int anim linear_indeterminate_line2_tail_interpolator 0x7f010020
int anim m3_bottom_sheet_slide_in 0x7f010021
int anim m3_bottom_sheet_slide_out 0x7f010022
int anim m3_motion_fade_enter 0x7f010023
int anim m3_motion_fade_exit 0x7f010024
int anim m3_side_sheet_slide_in 0x7f010025
int anim m3_side_sheet_slide_out 0x7f010026
int anim mtrl_bottom_sheet_slide_in 0x7f010027
int anim mtrl_bottom_sheet_slide_out 0x7f010028
int anim mtrl_card_lowers_interpolator 0x7f010029
int anim nav_default_enter_anim 0x7f01002a
int anim nav_default_exit_anim 0x7f01002b
int anim nav_default_pop_enter_anim 0x7f01002c
int anim nav_default_pop_exit_anim 0x7f01002d
int anim scale_anim 0x7f01002e
int anim slide_down 0x7f01002f
int anim slide_up 0x7f010030
int animator design_appbar_state_list_animator 0x7f020000
int animator design_fab_hide_motion_spec 0x7f020001
int animator design_fab_show_motion_spec 0x7f020002
int animator fragment_close_enter 0x7f020003
int animator fragment_close_exit 0x7f020004
int animator fragment_fade_enter 0x7f020005
int animator fragment_fade_exit 0x7f020006
int animator fragment_open_enter 0x7f020007
int animator fragment_open_exit 0x7f020008
int animator m3_btn_elevated_btn_state_list_anim 0x7f020009
int animator m3_btn_state_list_anim 0x7f02000a
int animator m3_card_elevated_state_list_anim 0x7f02000b
int animator m3_card_state_list_anim 0x7f02000c
int animator m3_chip_state_list_anim 0x7f02000d
int animator m3_elevated_chip_state_list_anim 0x7f02000e
int animator m3_extended_fab_change_size_collapse_motion_spec 0x7f02000f
int animator m3_extended_fab_change_size_expand_motion_spec 0x7f020010
int animator m3_extended_fab_hide_motion_spec 0x7f020011
int animator m3_extended_fab_show_motion_spec 0x7f020012
int animator m3_extended_fab_state_list_animator 0x7f020013
int animator mtrl_btn_state_list_anim 0x7f020014
int animator mtrl_btn_unelevated_state_list_anim 0x7f020015
int animator mtrl_card_state_list_anim 0x7f020016
int animator mtrl_chip_state_list_anim 0x7f020017
int animator mtrl_extended_fab_change_size_collapse_motion_spec 0x7f020018
int animator mtrl_extended_fab_change_size_expand_motion_spec 0x7f020019
int animator mtrl_extended_fab_hide_motion_spec 0x7f02001a
int animator mtrl_extended_fab_show_motion_spec 0x7f02001b
int animator mtrl_extended_fab_state_list_animator 0x7f02001c
int animator mtrl_fab_hide_motion_spec 0x7f02001d
int animator mtrl_fab_show_motion_spec 0x7f02001e
int animator mtrl_fab_transformation_sheet_collapse_spec 0x7f02001f
int animator mtrl_fab_transformation_sheet_expand_spec 0x7f020020
int animator nav_default_enter_anim 0x7f020021
int animator nav_default_exit_anim 0x7f020022
int animator nav_default_pop_enter_anim 0x7f020023
int animator nav_default_pop_exit_anim 0x7f020024
int array exo_controls_playback_speeds 0x7f030000
int attr action 0x7f040000
int attr actionBarDivider 0x7f040001
int attr actionBarItemBackground 0x7f040002
int attr actionBarPopupTheme 0x7f040003
int attr actionBarSize 0x7f040004
int attr actionBarSplitStyle 0x7f040005
int attr actionBarStyle 0x7f040006
int attr actionBarTabBarStyle 0x7f040007
int attr actionBarTabStyle 0x7f040008
int attr actionBarTabTextStyle 0x7f040009
int attr actionBarTheme 0x7f04000a
int attr actionBarWidgetTheme 0x7f04000b
int attr actionButtonStyle 0x7f04000c
int attr actionDropDownStyle 0x7f04000d
int attr actionLayout 0x7f04000e
int attr actionMenuTextAppearance 0x7f04000f
int attr actionMenuTextColor 0x7f040010
int attr actionModeBackground 0x7f040011
int attr actionModeCloseButtonStyle 0x7f040012
int attr actionModeCloseContentDescription 0x7f040013
int attr actionModeCloseDrawable 0x7f040014
int attr actionModeCopyDrawable 0x7f040015
int attr actionModeCutDrawable 0x7f040016
int attr actionModeFindDrawable 0x7f040017
int attr actionModePasteDrawable 0x7f040018
int attr actionModePopupWindowStyle 0x7f040019
int attr actionModeSelectAllDrawable 0x7f04001a
int attr actionModeShareDrawable 0x7f04001b
int attr actionModeSplitBackground 0x7f04001c
int attr actionModeStyle 0x7f04001d
int attr actionModeTheme 0x7f04001e
int attr actionModeWebSearchDrawable 0x7f04001f
int attr actionOverflowButtonStyle 0x7f040020
int attr actionOverflowMenuStyle 0x7f040021
int attr actionProviderClass 0x7f040022
int attr actionTextColorAlpha 0x7f040023
int attr actionViewClass 0x7f040024
int attr activityAction 0x7f040025
int attr activityChooserViewStyle 0x7f040026
int attr activityName 0x7f040027
int attr ad_marker_color 0x7f040028
int attr ad_marker_width 0x7f040029
int attr addElevationShadow 0x7f04002a
int attr alertDialogButtonGroupStyle 0x7f04002b
int attr alertDialogCenterButtons 0x7f04002c
int attr alertDialogStyle 0x7f04002d
int attr alertDialogTheme 0x7f04002e
int attr allowStacking 0x7f04002f
int attr alpha 0x7f040030
int attr alphabeticModifiers 0x7f040031
int attr altSrc 0x7f040032
int attr alwaysExpand 0x7f040033
int attr animateMenuItems 0x7f040034
int attr animateNavigationIcon 0x7f040035
int attr animate_relativeTo 0x7f040036
int attr animationMode 0x7f040037
int attr animation_enabled 0x7f040038
int attr appBarLayoutStyle 0x7f040039
int attr applyMotionScene 0x7f04003a
int attr arcMode 0x7f04003b
int attr argType 0x7f04003c
int attr arrowHeadLength 0x7f04003d
int attr arrowShaftLength 0x7f04003e
int attr artwork_display_mode 0x7f04003f
int attr attributeName 0x7f040040
int attr autoCompleteTextViewStyle 0x7f040041
int attr autoShowKeyboard 0x7f040042
int attr autoSizeMaxTextSize 0x7f040043
int attr autoSizeMinTextSize 0x7f040044
int attr autoSizePresetSizes 0x7f040045
int attr autoSizeStepGranularity 0x7f040046
int attr autoSizeTextType 0x7f040047
int attr autoTransition 0x7f040048
int attr auto_show 0x7f040049
int attr background 0x7f04004a
int attr backgroundColor 0x7f04004b
int attr backgroundInsetBottom 0x7f04004c
int attr backgroundInsetEnd 0x7f04004d
int attr backgroundInsetStart 0x7f04004e
int attr backgroundInsetTop 0x7f04004f
int attr backgroundOverlayColorAlpha 0x7f040050
int attr backgroundSplit 0x7f040051
int attr backgroundStacked 0x7f040052
int attr backgroundTint 0x7f040053
int attr backgroundTintMode 0x7f040054
int attr badgeGravity 0x7f040055
int attr badgeRadius 0x7f040056
int attr badgeStyle 0x7f040057
int attr badgeTextColor 0x7f040058
int attr badgeWidePadding 0x7f040059
int attr badgeWithTextRadius 0x7f04005a
int attr barLength 0x7f04005b
int attr bar_gravity 0x7f04005c
int attr bar_height 0x7f04005d
int attr barrierAllowsGoneWidgets 0x7f04005e
int attr barrierDirection 0x7f04005f
int attr barrierMargin 0x7f040060
int attr behavior_autoHide 0x7f040061
int attr behavior_autoShrink 0x7f040062
int attr behavior_draggable 0x7f040063
int attr behavior_expandedOffset 0x7f040064
int attr behavior_fitToContents 0x7f040065
int attr behavior_halfExpandedRatio 0x7f040066
int attr behavior_hideable 0x7f040067
int attr behavior_overlapTop 0x7f040068
int attr behavior_peekHeight 0x7f040069
int attr behavior_saveFlags 0x7f04006a
int attr behavior_significantVelocityThreshold 0x7f04006b
int attr behavior_skipCollapsed 0x7f04006c
int attr borderWidth 0x7f04006d
int attr borderlessButtonStyle 0x7f04006e
int attr bottomAppBarStyle 0x7f04006f
int attr bottomInsetScrimEnabled 0x7f040070
int attr bottomNavigationStyle 0x7f040071
int attr bottomSheetDialogTheme 0x7f040072
int attr bottomSheetDragHandleStyle 0x7f040073
int attr bottomSheetStyle 0x7f040074
int attr bottomTextPaddingDp 0x7f040075
int attr boxBackgroundColor 0x7f040076
int attr boxBackgroundMode 0x7f040077
int attr boxCollapsedPaddingTop 0x7f040078
int attr boxCornerRadiusBottomEnd 0x7f040079
int attr boxCornerRadiusBottomStart 0x7f04007a
int attr boxCornerRadiusTopEnd 0x7f04007b
int attr boxCornerRadiusTopStart 0x7f04007c
int attr boxStrokeColor 0x7f04007d
int attr boxStrokeErrorColor 0x7f04007e
int attr boxStrokeWidth 0x7f04007f
int attr boxStrokeWidthFocused 0x7f040080
int attr brightness 0x7f040081
int attr buffered_color 0x7f040082
int attr buttonBarButtonStyle 0x7f040083
int attr buttonBarNegativeButtonStyle 0x7f040084
int attr buttonBarNeutralButtonStyle 0x7f040085
int attr buttonBarPositiveButtonStyle 0x7f040086
int attr buttonBarStyle 0x7f040087
int attr buttonCompat 0x7f040088
int attr buttonGravity 0x7f040089
int attr buttonIcon 0x7f04008a
int attr buttonIconDimen 0x7f04008b
int attr buttonIconTint 0x7f04008c
int attr buttonIconTintMode 0x7f04008d
int attr buttonPanelSideLayout 0x7f04008e
int attr buttonSize 0x7f04008f
int attr buttonStyle 0x7f040090
int attr buttonStyleSmall 0x7f040091
int attr buttonTint 0x7f040092
int attr buttonTintMode 0x7f040093
int attr cardBackgroundColor 0x7f040094
int attr cardCornerRadius 0x7f040095
int attr cardElevation 0x7f040096
int attr cardForegroundColor 0x7f040097
int attr cardMaxElevation 0x7f040098
int attr cardPreventCornerOverlap 0x7f040099
int attr cardUseCompatPadding 0x7f04009a
int attr cardViewStyle 0x7f04009b
int attr centerIfNoTextEnabled 0x7f04009c
int attr chainUseRtl 0x7f04009d
int attr checkMarkCompat 0x7f04009e
int attr checkMarkTint 0x7f04009f
int attr checkMarkTintMode 0x7f0400a0
int attr checkboxStyle 0x7f0400a1
int attr checkedButton 0x7f0400a2
int attr checkedChip 0x7f0400a3
int attr checkedIcon 0x7f0400a4
int attr checkedIconEnabled 0x7f0400a5
int attr checkedIconGravity 0x7f0400a6
int attr checkedIconMargin 0x7f0400a7
int attr checkedIconSize 0x7f0400a8
int attr checkedIconTint 0x7f0400a9
int attr checkedIconVisible 0x7f0400aa
int attr checkedState 0x7f0400ab
int attr checkedTextViewStyle 0x7f0400ac
int attr chipBackgroundColor 0x7f0400ad
int attr chipCornerRadius 0x7f0400ae
int attr chipEndPadding 0x7f0400af
int attr chipGroupStyle 0x7f0400b0
int attr chipIcon 0x7f0400b1
int attr chipIconEnabled 0x7f0400b2
int attr chipIconSize 0x7f0400b3
int attr chipIconTint 0x7f0400b4
int attr chipIconVisible 0x7f0400b5
int attr chipMinHeight 0x7f0400b6
int attr chipMinTouchTargetSize 0x7f0400b7
int attr chipSpacing 0x7f0400b8
int attr chipSpacingHorizontal 0x7f0400b9
int attr chipSpacingVertical 0x7f0400ba
int attr chipStandaloneStyle 0x7f0400bb
int attr chipStartPadding 0x7f0400bc
int attr chipStrokeColor 0x7f0400bd
int attr chipStrokeWidth 0x7f0400be
int attr chipStyle 0x7f0400bf
int attr chipSurfaceColor 0x7f0400c0
int attr circleCrop 0x7f0400c1
int attr circleRadius 0x7f0400c2
int attr circularProgressIndicatorStyle 0x7f0400c3
int attr ckCropOutput 0x7f0400c4
int attr ckFacing 0x7f0400c5
int attr ckFlash 0x7f0400c6
int attr ckFocus 0x7f0400c7
int attr ckJpegQuality 0x7f0400c8
int attr ckMethod 0x7f0400c9
int attr ckPermissions 0x7f0400ca
int attr ckVideoQuality 0x7f0400cb
int attr ckZoom 0x7f0400cc
int attr clearTop 0x7f0400cd
int attr clickAction 0x7f0400ce
int attr clockFaceBackgroundColor 0x7f0400cf
int attr clockHandColor 0x7f0400d0
int attr clockIcon 0x7f0400d1
int attr clockNumberTextColor 0x7f0400d2
int attr closeIcon 0x7f0400d3
int attr closeIconEnabled 0x7f0400d4
int attr closeIconEndPadding 0x7f0400d5
int attr closeIconSize 0x7f0400d6
int attr closeIconStartPadding 0x7f0400d7
int attr closeIconTint 0x7f0400d8
int attr closeIconVisible 0x7f0400d9
int attr closeItemLayout 0x7f0400da
int attr collapseContentDescription 0x7f0400db
int attr collapseIcon 0x7f0400dc
int attr collapsedSize 0x7f0400dd
int attr collapsedTitleGravity 0x7f0400de
int attr collapsedTitleTextAppearance 0x7f0400df
int attr collapsedTitleTextColor 0x7f0400e0
int attr collapsingToolbarLayoutLargeSize 0x7f0400e1
int attr collapsingToolbarLayoutLargeStyle 0x7f0400e2
int attr collapsingToolbarLayoutMediumSize 0x7f0400e3
int attr collapsingToolbarLayoutMediumStyle 0x7f0400e4
int attr collapsingToolbarLayoutStyle 0x7f0400e5
int attr color 0x7f0400e6
int attr colorAccent 0x7f0400e7
int attr colorBackgroundFloating 0x7f0400e8
int attr colorBlue 0x7f0400e9
int attr colorBlueContainer 0x7f0400ea
int attr colorButtonNormal 0x7f0400eb
int attr colorContainer 0x7f0400ec
int attr colorControlActivated 0x7f0400ed
int attr colorControlHighlight 0x7f0400ee
int attr colorControlNormal 0x7f0400ef
int attr colorError 0x7f0400f0
int attr colorErrorContainer 0x7f0400f1
int attr colorGray 0x7f0400f2
int attr colorGrayContainer 0x7f0400f3
int attr colorGreen 0x7f0400f4
int attr colorGreenContainer 0x7f0400f5
int attr colorOnBackground 0x7f0400f6
int attr colorOnBlue 0x7f0400f7
int attr colorOnBlueContainer 0x7f0400f8
int attr colorOnContainer 0x7f0400f9
int attr colorOnContainerUnchecked 0x7f0400fa
int attr colorOnError 0x7f0400fb
int attr colorOnErrorContainer 0x7f0400fc
int attr colorOnGray 0x7f0400fd
int attr colorOnGrayContainer 0x7f0400fe
int attr colorOnGreen 0x7f0400ff
int attr colorOnGreenContainer 0x7f040100
int attr colorOnPrimary 0x7f040101
int attr colorOnPrimaryContainer 0x7f040102
int attr colorOnPrimarySurface 0x7f040103
int attr colorOnRedgradend 0x7f040104
int attr colorOnRedgradendContainer 0x7f040105
int attr colorOnRedgradstart 0x7f040106
int attr colorOnRedgradstartContainer 0x7f040107
int attr colorOnSecondary 0x7f040108
int attr colorOnSecondaryContainer 0x7f040109
int attr colorOnSemiblue 0x7f04010a
int attr colorOnSemiblueContainer 0x7f04010b
int attr colorOnSemigray 0x7f04010c
int attr colorOnSemigrayContainer 0x7f04010d
int attr colorOnSemigreen 0x7f04010e
int attr colorOnSemigreenContainer 0x7f04010f
int attr colorOnSemired 0x7f040110
int attr colorOnSemiredContainer 0x7f040111
int attr colorOnSurface 0x7f040112
int attr colorOnSurfaceInverse 0x7f040113
int attr colorOnSurfaceVariant 0x7f040114
int attr colorOnTertiary 0x7f040115
int attr colorOnTertiaryContainer 0x7f040116
int attr colorOutline 0x7f040117
int attr colorOutlineVariant 0x7f040118
int attr colorPrimary 0x7f040119
int attr colorPrimaryContainer 0x7f04011a
int attr colorPrimaryDark 0x7f04011b
int attr colorPrimaryInverse 0x7f04011c
int attr colorPrimarySurface 0x7f04011d
int attr colorPrimaryVariant 0x7f04011e
int attr colorRedgradend 0x7f04011f
int attr colorRedgradendContainer 0x7f040120
int attr colorRedgradstart 0x7f040121
int attr colorRedgradstartContainer 0x7f040122
int attr colorScheme 0x7f040123
int attr colorSecondary 0x7f040124
int attr colorSecondaryContainer 0x7f040125
int attr colorSecondaryVariant 0x7f040126
int attr colorSemiblue 0x7f040127
int attr colorSemiblueContainer 0x7f040128
int attr colorSemigray 0x7f040129
int attr colorSemigrayContainer 0x7f04012a
int attr colorSemigreen 0x7f04012b
int attr colorSemigreenContainer 0x7f04012c
int attr colorSemired 0x7f04012d
int attr colorSemiredContainer 0x7f04012e
int attr colorSurface 0x7f04012f
int attr colorSurfaceInverse 0x7f040130
int attr colorSurfaceVariant 0x7f040131
int attr colorSwitchThumbNormal 0x7f040132
int attr colorTertiary 0x7f040133
int attr colorTertiaryContainer 0x7f040134
int attr commitIcon 0x7f040135
int attr compatShadowEnabled 0x7f040136
int attr constraintSet 0x7f040137
int attr constraintSetEnd 0x7f040138
int attr constraintSetStart 0x7f040139
int attr constraint_referenced_ids 0x7f04013a
int attr constraints 0x7f04013b
int attr content 0x7f04013c
int attr contentDescription 0x7f04013d
int attr contentInsetEnd 0x7f04013e
int attr contentInsetEndWithActions 0x7f04013f
int attr contentInsetLeft 0x7f040140
int attr contentInsetRight 0x7f040141
int attr contentInsetStart 0x7f040142
int attr contentInsetStartWithNavigation 0x7f040143
int attr contentPadding 0x7f040144
int attr contentPaddingBottom 0x7f040145
int attr contentPaddingEnd 0x7f040146
int attr contentPaddingLeft 0x7f040147
int attr contentPaddingRight 0x7f040148
int attr contentPaddingStart 0x7f040149
int attr contentPaddingTop 0x7f04014a
int attr contentScrim 0x7f04014b
int attr contrast 0x7f04014c
int attr controlBackground 0x7f04014d
int attr controller_layout_id 0x7f04014e
int attr coordinatorLayoutStyle 0x7f04014f
int attr coplanarSiblingViewId 0x7f040150
int attr cornerFamily 0x7f040151
int attr cornerFamilyBottomLeft 0x7f040152
int attr cornerFamilyBottomRight 0x7f040153
int attr cornerFamilyTopLeft 0x7f040154
int attr cornerFamilyTopRight 0x7f040155
int attr cornerRadius 0x7f040156
int attr cornerSize 0x7f040157
int attr cornerSizeBottomLeft 0x7f040158
int attr cornerSizeBottomRight 0x7f040159
int attr cornerSizeTopLeft 0x7f04015a
int attr cornerSizeTopRight 0x7f04015b
int attr counterEnabled 0x7f04015c
int attr counterMaxLength 0x7f04015d
int attr counterOverflowTextAppearance 0x7f04015e
int attr counterOverflowTextColor 0x7f04015f
int attr counterTextAppearance 0x7f040160
int attr counterTextColor 0x7f040161
int attr crossfade 0x7f040162
int attr currentState 0x7f040163
int attr curveFit 0x7f040164
int attr customBoolean 0x7f040165
int attr customColorDrawableValue 0x7f040166
int attr customColorValue 0x7f040167
int attr customDimension 0x7f040168
int attr customFloatValue 0x7f040169
int attr customIntegerValue 0x7f04016a
int attr customNavigationLayout 0x7f04016b
int attr customPixelDimension 0x7f04016c
int attr customStringValue 0x7f04016d
int attr data 0x7f04016e
int attr dataPattern 0x7f04016f
int attr dayInvalidStyle 0x7f040170
int attr daySelectedStyle 0x7f040171
int attr dayStyle 0x7f040172
int attr dayTodayStyle 0x7f040173
int attr defaultDuration 0x7f040174
int attr defaultMarginsEnabled 0x7f040175
int attr defaultNavHost 0x7f040176
int attr defaultQueryHint 0x7f040177
int attr defaultScrollFlagsEnabled 0x7f040178
int attr defaultState 0x7f040179
int attr default_artwork 0x7f04017a
int attr deltaPolarAngle 0x7f04017b
int attr deltaPolarRadius 0x7f04017c
int attr deriveConstraintsFrom 0x7f04017d
int attr destination 0x7f04017e
int attr dialogCornerRadius 0x7f04017f
int attr dialogPreferredPadding 0x7f040180
int attr dialogTheme 0x7f040181
int attr displayOptions 0x7f040182
int attr distanceInBetween 0x7f040183
int attr divider 0x7f040184
int attr dividerColor 0x7f040185
int attr dividerHorizontal 0x7f040186
int attr dividerInsetEnd 0x7f040187
int attr dividerInsetStart 0x7f040188
int attr dividerPadding 0x7f040189
int attr dividerThickness 0x7f04018a
int attr dividerVertical 0x7f04018b
int attr dragDirection 0x7f04018c
int attr dragScale 0x7f04018d
int attr dragThreshold 0x7f04018e
int attr drawPath 0x7f04018f
int attr drawableBottomCompat 0x7f040190
int attr drawableEndCompat 0x7f040191
int attr drawableLeftCompat 0x7f040192
int attr drawableRightCompat 0x7f040193
int attr drawableSize 0x7f040194
int attr drawableStartCompat 0x7f040195
int attr drawableTint 0x7f040196
int attr drawableTintMode 0x7f040197
int attr drawableTopCompat 0x7f040198
int attr drawerArrowStyle 0x7f040199
int attr drawerLayoutCornerSize 0x7f04019a
int attr drawerLayoutStyle 0x7f04019b
int attr dropDownListViewStyle 0x7f04019c
int attr dropdownListPreferredItemHeight 0x7f04019d
int attr duration 0x7f04019e
int attr dynamicColorThemeOverlay 0x7f04019f
int attr editTextBackground 0x7f0401a0
int attr editTextColor 0x7f0401a1
int attr editTextStyle 0x7f0401a2
int attr elevation 0x7f0401a3
int attr elevationOverlayAccentColor 0x7f0401a4
int attr elevationOverlayColor 0x7f0401a5
int attr elevationOverlayEnabled 0x7f0401a6
int attr emojiCompatEnabled 0x7f0401a7
int attr enableEdgeToEdge 0x7f0401a8
int attr endIconCheckable 0x7f0401a9
int attr endIconContentDescription 0x7f0401aa
int attr endIconDrawable 0x7f0401ab
int attr endIconMinSize 0x7f0401ac
int attr endIconMode 0x7f0401ad
int attr endIconScaleType 0x7f0401ae
int attr endIconTint 0x7f0401af
int attr endIconTintMode 0x7f0401b0
int attr enforceMaterialTheme 0x7f0401b1
int attr enforceTextAppearance 0x7f0401b2
int attr ensureMinTouchTargetSize 0x7f0401b3
int attr enterAnim 0x7f0401b4
int attr errorAccessibilityLabel 0x7f0401b5
int attr errorAccessibilityLiveRegion 0x7f0401b6
int attr errorContentDescription 0x7f0401b7
int attr errorEnabled 0x7f0401b8
int attr errorIconDrawable 0x7f0401b9
int attr errorIconTint 0x7f0401ba
int attr errorIconTintMode 0x7f0401bb
int attr errorShown 0x7f0401bc
int attr errorTextAppearance 0x7f0401bd
int attr errorTextColor 0x7f0401be
int attr exitAnim 0x7f0401bf
int attr expandActivityOverflowButtonDrawable 0x7f0401c0
int attr expanded 0x7f0401c1
int attr expandedHintEnabled 0x7f0401c2
int attr expandedTitleGravity 0x7f0401c3
int attr expandedTitleMargin 0x7f0401c4
int attr expandedTitleMarginBottom 0x7f0401c5
int attr expandedTitleMarginEnd 0x7f0401c6
int attr expandedTitleMarginStart 0x7f0401c7
int attr expandedTitleMarginTop 0x7f0401c8
int attr expandedTitleTextAppearance 0x7f0401c9
int attr expandedTitleTextColor 0x7f0401ca
int attr extendMotionSpec 0x7f0401cb
int attr extendStrategy 0x7f0401cc
int attr extendedFloatingActionButtonPrimaryStyle 0x7f0401cd
int attr extendedFloatingActionButtonSecondaryStyle 0x7f0401ce
int attr extendedFloatingActionButtonStyle 0x7f0401cf
int attr extendedFloatingActionButtonSurfaceStyle 0x7f0401d0
int attr extendedFloatingActionButtonTertiaryStyle 0x7f0401d1
int attr extraMultilineHeightEnabled 0x7f0401d2
int attr fabAlignmentMode 0x7f0401d3
int attr fabAlignmentModeEndMargin 0x7f0401d4
int attr fabAnchorMode 0x7f0401d5
int attr fabAnimationMode 0x7f0401d6
int attr fabCradleMargin 0x7f0401d7
int attr fabCradleRoundedCornerRadius 0x7f0401d8
int attr fabCradleVerticalOffset 0x7f0401d9
int attr fabCustomSize 0x7f0401da
int attr fabSize 0x7f0401db
int attr fastScrollEnabled 0x7f0401dc
int attr fastScrollHorizontalThumbDrawable 0x7f0401dd
int attr fastScrollHorizontalTrackDrawable 0x7f0401de
int attr fastScrollVerticalThumbDrawable 0x7f0401df
int attr fastScrollVerticalTrackDrawable 0x7f0401e0
int attr fieldBgColor 0x7f0401e1
int attr fieldColor 0x7f0401e2
int attr fillerColor 0x7f0401e3
int attr fillerRadius 0x7f0401e4
int attr finishPrimaryWithSecondary 0x7f0401e5
int attr finishSecondaryWithPrimary 0x7f0401e6
int attr firstBaselineToTopHeight 0x7f0401e7
int attr floatingActionButtonLargePrimaryStyle 0x7f0401e8
int attr floatingActionButtonLargeSecondaryStyle 0x7f0401e9
int attr floatingActionButtonLargeStyle 0x7f0401ea
int attr floatingActionButtonLargeSurfaceStyle 0x7f0401eb
int attr floatingActionButtonLargeTertiaryStyle 0x7f0401ec
int attr floatingActionButtonPrimaryStyle 0x7f0401ed
int attr floatingActionButtonSecondaryStyle 0x7f0401ee
int attr floatingActionButtonSmallPrimaryStyle 0x7f0401ef
int attr floatingActionButtonSmallSecondaryStyle 0x7f0401f0
int attr floatingActionButtonSmallStyle 0x7f0401f1
int attr floatingActionButtonSmallSurfaceStyle 0x7f0401f2
int attr floatingActionButtonSmallTertiaryStyle 0x7f0401f3
int attr floatingActionButtonStyle 0x7f0401f4
int attr floatingActionButtonSurfaceStyle 0x7f0401f5
int attr floatingActionButtonTertiaryStyle 0x7f0401f6
int attr flow_firstHorizontalBias 0x7f0401f7
int attr flow_firstHorizontalStyle 0x7f0401f8
int attr flow_firstVerticalBias 0x7f0401f9
int attr flow_firstVerticalStyle 0x7f0401fa
int attr flow_horizontalAlign 0x7f0401fb
int attr flow_horizontalBias 0x7f0401fc
int attr flow_horizontalGap 0x7f0401fd
int attr flow_horizontalStyle 0x7f0401fe
int attr flow_lastHorizontalBias 0x7f0401ff
int attr flow_lastHorizontalStyle 0x7f040200
int attr flow_lastVerticalBias 0x7f040201
int attr flow_lastVerticalStyle 0x7f040202
int attr flow_maxElementsWrap 0x7f040203
int attr flow_padding 0x7f040204
int attr flow_verticalAlign 0x7f040205
int attr flow_verticalBias 0x7f040206
int attr flow_verticalGap 0x7f040207
int attr flow_verticalStyle 0x7f040208
int attr flow_wrapMode 0x7f040209
int attr font 0x7f04020a
int attr fontFamily 0x7f04020b
int attr fontProviderAuthority 0x7f04020c
int attr fontProviderCerts 0x7f04020d
int attr fontProviderFetchStrategy 0x7f04020e
int attr fontProviderFetchTimeout 0x7f04020f
int attr fontProviderPackage 0x7f040210
int attr fontProviderQuery 0x7f040211
int attr fontProviderSystemFontFamily 0x7f040212
int attr fontStyle 0x7f040213
int attr fontVariationSettings 0x7f040214
int attr fontWeight 0x7f040215
int attr forceApplySystemWindowInsetTop 0x7f040216
int attr forceDefaultNavigationOnClickListener 0x7f040217
int attr foregroundInsidePadding 0x7f040218
int attr framePosition 0x7f040219
int attr gapBetweenBars 0x7f04021a
int attr gestureInsetBottomIgnored 0x7f04021b
int attr goIcon 0x7f04021c
int attr graph 0x7f04021d
int attr haloColor 0x7f04021e
int attr haloRadius 0x7f04021f
int attr harmonizeBlue 0x7f040220
int attr harmonizeGray 0x7f040221
int attr harmonizeGreen 0x7f040222
int attr harmonizeRedgradend 0x7f040223
int attr harmonizeRedgradstart 0x7f040224
int attr harmonizeSemiblue 0x7f040225
int attr harmonizeSemigray 0x7f040226
int attr harmonizeSemigreen 0x7f040227
int attr harmonizeSemired 0x7f040228
int attr headerLayout 0x7f040229
int attr height 0x7f04022a
int attr helperText 0x7f04022b
int attr helperTextEnabled 0x7f04022c
int attr helperTextTextAppearance 0x7f04022d
int attr helperTextTextColor 0x7f04022e
int attr hideAnimationBehavior 0x7f04022f
int attr hideMotionSpec 0x7f040230
int attr hideNavigationIcon 0x7f040231
int attr hideOnContentScroll 0x7f040232
int attr hideOnScroll 0x7f040233
int attr hide_during_ads 0x7f040234
int attr hide_on_touch 0x7f040235
int attr highlightColor 0x7f040236
int attr highlightEnabled 0x7f040237
int attr highlightSingleFieldMode 0x7f040238
int attr highlightType 0x7f040239
int attr hintAnimationEnabled 0x7f04023a
int attr hintEnabled 0x7f04023b
int attr hintTextAppearance 0x7f04023c
int attr hintTextColor 0x7f04023d
int attr homeAsUpIndicator 0x7f04023e
int attr homeLayout 0x7f04023f
int attr horizontalOffset 0x7f040240
int attr horizontalOffsetWithText 0x7f040241
int attr hoveredFocusedTranslationZ 0x7f040242
int attr icon 0x7f040243
int attr iconEndPadding 0x7f040244
int attr iconGravity 0x7f040245
int attr iconPadding 0x7f040246
int attr iconSize 0x7f040247
int attr iconStartPadding 0x7f040248
int attr iconTint 0x7f040249
int attr iconTintMode 0x7f04024a
int attr iconifiedByDefault 0x7f04024b
int attr imageAspectRatio 0x7f04024c
int attr imageAspectRatioAdjust 0x7f04024d
int attr imageButtonStyle 0x7f04024e
int attr indeterminateAnimationType 0x7f04024f
int attr indeterminateProgressStyle 0x7f040250
int attr indicatorColor 0x7f040251
int attr indicatorDirectionCircular 0x7f040252
int attr indicatorDirectionLinear 0x7f040253
int attr indicatorInset 0x7f040254
int attr indicatorSize 0x7f040255
int attr initialActivityCount 0x7f040256
int attr insetForeground 0x7f040257
int attr isCursorEnabled 0x7f040258
int attr isCustomBackground 0x7f040259
int attr isLightTheme 0x7f04025a
int attr isMaterial3DynamicColorApplied 0x7f04025b
int attr isMaterial3Theme 0x7f04025c
int attr isMaterialTheme 0x7f04025d
int attr itemActiveIndicatorStyle 0x7f04025e
int attr itemBackground 0x7f04025f
int attr itemFillColor 0x7f040260
int attr itemHorizontalPadding 0x7f040261
int attr itemHorizontalTranslationEnabled 0x7f040262
int attr itemIconPadding 0x7f040263
int attr itemIconSize 0x7f040264
int attr itemIconTint 0x7f040265
int attr itemMaxLines 0x7f040266
int attr itemMinHeight 0x7f040267
int attr itemPadding 0x7f040268
int attr itemPaddingBottom 0x7f040269
int attr itemPaddingTop 0x7f04026a
int attr itemRippleColor 0x7f04026b
int attr itemShapeAppearance 0x7f04026c
int attr itemShapeAppearanceOverlay 0x7f04026d
int attr itemShapeFillColor 0x7f04026e
int attr itemShapeInsetBottom 0x7f04026f
int attr itemShapeInsetEnd 0x7f040270
int attr itemShapeInsetStart 0x7f040271
int attr itemShapeInsetTop 0x7f040272
int attr itemSpacing 0x7f040273
int attr itemStrokeColor 0x7f040274
int attr itemStrokeWidth 0x7f040275
int attr itemTextAppearance 0x7f040276
int attr itemTextAppearanceActive 0x7f040277
int attr itemTextAppearanceInactive 0x7f040278
int attr itemTextColor 0x7f040279
int attr itemVerticalPadding 0x7f04027a
int attr keep_content_on_player_reset 0x7f04027b
int attr keyPositionType 0x7f04027c
int attr keyboardIcon 0x7f04027d
int attr keylines 0x7f04027e
int attr lStar 0x7f04027f
int attr labelBehavior 0x7f040280
int attr labelStyle 0x7f040281
int attr labelVisibilityMode 0x7f040282
int attr lastBaselineToBottomHeight 0x7f040283
int attr lastItemDecorated 0x7f040284
int attr launchSingleTop 0x7f040285
int attr layout 0x7f040286
int attr layoutDescription 0x7f040287
int attr layoutDuringTransition 0x7f040288
int attr layoutManager 0x7f040289
int attr layout_anchor 0x7f04028a
int attr layout_anchorGravity 0x7f04028b
int attr layout_behavior 0x7f04028c
int attr layout_collapseMode 0x7f04028d
int attr layout_collapseParallaxMultiplier 0x7f04028e
int attr layout_constrainedHeight 0x7f04028f
int attr layout_constrainedWidth 0x7f040290
int attr layout_constraintBaseline_creator 0x7f040291
int attr layout_constraintBaseline_toBaselineOf 0x7f040292
int attr layout_constraintBottom_creator 0x7f040293
int attr layout_constraintBottom_toBottomOf 0x7f040294
int attr layout_constraintBottom_toTopOf 0x7f040295
int attr layout_constraintCircle 0x7f040296
int attr layout_constraintCircleAngle 0x7f040297
int attr layout_constraintCircleRadius 0x7f040298
int attr layout_constraintDimensionRatio 0x7f040299
int attr layout_constraintEnd_toEndOf 0x7f04029a
int attr layout_constraintEnd_toStartOf 0x7f04029b
int attr layout_constraintGuide_begin 0x7f04029c
int attr layout_constraintGuide_end 0x7f04029d
int attr layout_constraintGuide_percent 0x7f04029e
int attr layout_constraintHeight_default 0x7f04029f
int attr layout_constraintHeight_max 0x7f0402a0
int attr layout_constraintHeight_min 0x7f0402a1
int attr layout_constraintHeight_percent 0x7f0402a2
int attr layout_constraintHorizontal_bias 0x7f0402a3
int attr layout_constraintHorizontal_chainStyle 0x7f0402a4
int attr layout_constraintHorizontal_weight 0x7f0402a5
int attr layout_constraintLeft_creator 0x7f0402a6
int attr layout_constraintLeft_toLeftOf 0x7f0402a7
int attr layout_constraintLeft_toRightOf 0x7f0402a8
int attr layout_constraintRight_creator 0x7f0402a9
int attr layout_constraintRight_toLeftOf 0x7f0402aa
int attr layout_constraintRight_toRightOf 0x7f0402ab
int attr layout_constraintStart_toEndOf 0x7f0402ac
int attr layout_constraintStart_toStartOf 0x7f0402ad
int attr layout_constraintTag 0x7f0402ae
int attr layout_constraintTop_creator 0x7f0402af
int attr layout_constraintTop_toBottomOf 0x7f0402b0
int attr layout_constraintTop_toTopOf 0x7f0402b1
int attr layout_constraintVertical_bias 0x7f0402b2
int attr layout_constraintVertical_chainStyle 0x7f0402b3
int attr layout_constraintVertical_weight 0x7f0402b4
int attr layout_constraintWidth_default 0x7f0402b5
int attr layout_constraintWidth_max 0x7f0402b6
int attr layout_constraintWidth_min 0x7f0402b7
int attr layout_constraintWidth_percent 0x7f0402b8
int attr layout_dodgeInsetEdges 0x7f0402b9
int attr layout_editor_absoluteX 0x7f0402ba
int attr layout_editor_absoluteY 0x7f0402bb
int attr layout_goneMarginBottom 0x7f0402bc
int attr layout_goneMarginEnd 0x7f0402bd
int attr layout_goneMarginLeft 0x7f0402be
int attr layout_goneMarginRight 0x7f0402bf
int attr layout_goneMarginStart 0x7f0402c0
int attr layout_goneMarginTop 0x7f0402c1
int attr layout_insetEdge 0x7f0402c2
int attr layout_keyline 0x7f0402c3
int attr layout_optimizationLevel 0x7f0402c4
int attr layout_scrollEffect 0x7f0402c5
int attr layout_scrollFlags 0x7f0402c6
int attr layout_scrollInterpolator 0x7f0402c7
int attr liftOnScroll 0x7f0402c8
int attr liftOnScrollColor 0x7f0402c9
int attr liftOnScrollTargetViewId 0x7f0402ca
int attr limitBoundsTo 0x7f0402cb
int attr lineHeight 0x7f0402cc
int attr lineSpacing 0x7f0402cd
int attr lineThickness 0x7f0402ce
int attr linearProgressIndicatorStyle 0x7f0402cf
int attr listChoiceBackgroundIndicator 0x7f0402d0
int attr listChoiceIndicatorMultipleAnimated 0x7f0402d1
int attr listChoiceIndicatorSingleAnimated 0x7f0402d2
int attr listDividerAlertDialog 0x7f0402d3
int attr listItemLayout 0x7f0402d4
int attr listLayout 0x7f0402d5
int attr listMenuViewStyle 0x7f0402d6
int attr listPopupWindowStyle 0x7f0402d7
int attr listPreferredItemHeight 0x7f0402d8
int attr listPreferredItemHeightLarge 0x7f0402d9
int attr listPreferredItemHeightSmall 0x7f0402da
int attr listPreferredItemPaddingEnd 0x7f0402db
int attr listPreferredItemPaddingLeft 0x7f0402dc
int attr listPreferredItemPaddingRight 0x7f0402dd
int attr listPreferredItemPaddingStart 0x7f0402de
int attr logo 0x7f0402df
int attr logoAdjustViewBounds 0x7f0402e0
int attr logoDescription 0x7f0402e1
int attr logoScaleType 0x7f0402e2
int attr marginHorizontal 0x7f0402e3
int attr marginLeftSystemWindowInsets 0x7f0402e4
int attr marginRightSystemWindowInsets 0x7f0402e5
int attr marginTopSystemWindowInsets 0x7f0402e6
int attr materialAlertDialogBodyTextStyle 0x7f0402e7
int attr materialAlertDialogButtonSpacerVisibility 0x7f0402e8
int attr materialAlertDialogTheme 0x7f0402e9
int attr materialAlertDialogTitleIconStyle 0x7f0402ea
int attr materialAlertDialogTitlePanelStyle 0x7f0402eb
int attr materialAlertDialogTitleTextStyle 0x7f0402ec
int attr materialButtonOutlinedStyle 0x7f0402ed
int attr materialButtonStyle 0x7f0402ee
int attr materialButtonToggleGroupStyle 0x7f0402ef
int attr materialCalendarDay 0x7f0402f0
int attr materialCalendarDayOfWeekLabel 0x7f0402f1
int attr materialCalendarFullscreenTheme 0x7f0402f2
int attr materialCalendarHeaderCancelButton 0x7f0402f3
int attr materialCalendarHeaderConfirmButton 0x7f0402f4
int attr materialCalendarHeaderDivider 0x7f0402f5
int attr materialCalendarHeaderLayout 0x7f0402f6
int attr materialCalendarHeaderSelection 0x7f0402f7
int attr materialCalendarHeaderTitle 0x7f0402f8
int attr materialCalendarHeaderToggleButton 0x7f0402f9
int attr materialCalendarMonth 0x7f0402fa
int attr materialCalendarMonthNavigationButton 0x7f0402fb
int attr materialCalendarStyle 0x7f0402fc
int attr materialCalendarTheme 0x7f0402fd
int attr materialCalendarYearNavigationButton 0x7f0402fe
int attr materialCardViewElevatedStyle 0x7f0402ff
int attr materialCardViewFilledStyle 0x7f040300
int attr materialCardViewOutlinedStyle 0x7f040301
int attr materialCardViewStyle 0x7f040302
int attr materialCircleRadius 0x7f040303
int attr materialClockStyle 0x7f040304
int attr materialDisplayDividerStyle 0x7f040305
int attr materialDividerHeavyStyle 0x7f040306
int attr materialDividerStyle 0x7f040307
int attr materialIconButtonFilledStyle 0x7f040308
int attr materialIconButtonFilledTonalStyle 0x7f040309
int attr materialIconButtonOutlinedStyle 0x7f04030a
int attr materialIconButtonStyle 0x7f04030b
int attr materialSearchBarStyle 0x7f04030c
int attr materialSearchViewPrefixStyle 0x7f04030d
int attr materialSearchViewStyle 0x7f04030e
int attr materialSwitchStyle 0x7f04030f
int attr materialThemeOverlay 0x7f040310
int attr materialTimePickerStyle 0x7f040311
int attr materialTimePickerTheme 0x7f040312
int attr materialTimePickerTitleStyle 0x7f040313
int attr maxAcceleration 0x7f040314
int attr maxActionInlineWidth 0x7f040315
int attr maxButtonHeight 0x7f040316
int attr maxCharacterCount 0x7f040317
int attr maxHeight 0x7f040318
int attr maxImageSize 0x7f040319
int attr maxLines 0x7f04031a
int attr maxVelocity 0x7f04031b
int attr maxWidth 0x7f04031c
int attr measureWithLargestChild 0x7f04031d
int attr menu 0x7f04031e
int attr menuAlignmentMode 0x7f04031f
int attr menuGravity 0x7f040320
int attr mimeType 0x7f040321
int attr minHeight 0x7f040322
int attr minHideDelay 0x7f040323
int attr minSeparation 0x7f040324
int attr minTextSize 0x7f040325
int attr minTouchTargetSize 0x7f040326
int attr minWidth 0x7f040327
int attr mock_diagonalsColor 0x7f040328
int attr mock_label 0x7f040329
int attr mock_labelBackgroundColor 0x7f04032a
int attr mock_labelColor 0x7f04032b
int attr mock_showDiagonals 0x7f04032c
int attr mock_showLabel 0x7f04032d
int attr motionDebug 0x7f04032e
int attr motionDurationExtraLong1 0x7f04032f
int attr motionDurationExtraLong2 0x7f040330
int attr motionDurationExtraLong3 0x7f040331
int attr motionDurationExtraLong4 0x7f040332
int attr motionDurationLong1 0x7f040333
int attr motionDurationLong2 0x7f040334
int attr motionDurationLong3 0x7f040335
int attr motionDurationLong4 0x7f040336
int attr motionDurationMedium1 0x7f040337
int attr motionDurationMedium2 0x7f040338
int attr motionDurationMedium3 0x7f040339
int attr motionDurationMedium4 0x7f04033a
int attr motionDurationShort1 0x7f04033b
int attr motionDurationShort2 0x7f04033c
int attr motionDurationShort3 0x7f04033d
int attr motionDurationShort4 0x7f04033e
int attr motionEasingAccelerated 0x7f04033f
int attr motionEasingDecelerated 0x7f040340
int attr motionEasingEmphasized 0x7f040341
int attr motionEasingEmphasizedAccelerateInterpolator 0x7f040342
int attr motionEasingEmphasizedDecelerateInterpolator 0x7f040343
int attr motionEasingEmphasizedInterpolator 0x7f040344
int attr motionEasingLinear 0x7f040345
int attr motionEasingLinearInterpolator 0x7f040346
int attr motionEasingStandard 0x7f040347
int attr motionEasingStandardAccelerateInterpolator 0x7f040348
int attr motionEasingStandardDecelerateInterpolator 0x7f040349
int attr motionEasingStandardInterpolator 0x7f04034a
int attr motionInterpolator 0x7f04034b
int attr motionPath 0x7f04034c
int attr motionPathRotate 0x7f04034d
int attr motionProgress 0x7f04034e
int attr motionStagger 0x7f04034f
int attr motionTarget 0x7f040350
int attr motion_postLayoutCollision 0x7f040351
int attr motion_triggerOnCollision 0x7f040352
int attr moveWhenScrollAtTop 0x7f040353
int attr multiChoiceItemLayout 0x7f040354
int attr navGraph 0x7f040355
int attr navigationContentDescription 0x7f040356
int attr navigationIcon 0x7f040357
int attr navigationIconTint 0x7f040358
int attr navigationMode 0x7f040359
int attr navigationRailStyle 0x7f04035a
int attr navigationViewStyle 0x7f04035b
int attr nestedScrollFlags 0x7f04035c
int attr nestedScrollViewStyle 0x7f04035d
int attr nestedScrollable 0x7f04035e
int attr noOfFields 0x7f04035f
int attr nullable 0x7f040360
int attr number 0x7f040361
int attr numericModifiers 0x7f040362
int attr onCross 0x7f040363
int attr onHide 0x7f040364
int attr onNegativeCross 0x7f040365
int attr onPositiveCross 0x7f040366
int attr onShow 0x7f040367
int attr onTouchUp 0x7f040368
int attr overlapAnchor 0x7f040369
int attr overlay 0x7f04036a
int attr paddingBottomNoButtons 0x7f04036b
int attr paddingBottomSystemWindowInsets 0x7f04036c
int attr paddingEnd 0x7f04036d
int attr paddingLeftSystemWindowInsets 0x7f04036e
int attr paddingRightSystemWindowInsets 0x7f04036f
int attr paddingStart 0x7f040370
int attr paddingTopNoTitle 0x7f040371
int attr paddingTopSystemWindowInsets 0x7f040372
int attr panelBackground 0x7f040373
int attr panelMenuListTheme 0x7f040374
int attr panelMenuListWidth 0x7f040375
int attr passwordToggleContentDescription 0x7f040376
int attr passwordToggleDrawable 0x7f040377
int attr passwordToggleEnabled 0x7f040378
int attr passwordToggleTint 0x7f040379
int attr passwordToggleTintMode 0x7f04037a
int attr pathMotionArc 0x7f04037b
int attr path_percent 0x7f04037c
int attr percentHeight 0x7f04037d
int attr percentWidth 0x7f04037e
int attr percentX 0x7f04037f
int attr percentY 0x7f040380
int attr perpendicularPath_percent 0x7f040381
int attr pivotAnchor 0x7f040382
int attr placeholderActivityName 0x7f040383
int attr placeholderText 0x7f040384
int attr placeholderTextAppearance 0x7f040385
int attr placeholderTextColor 0x7f040386
int attr placeholder_emptyVisibility 0x7f040387
int attr played_ad_marker_color 0x7f040388
int attr played_color 0x7f040389
int attr player_layout_id 0x7f04038a
int attr popEnterAnim 0x7f04038b
int attr popExitAnim 0x7f04038c
int attr popUpTo 0x7f04038d
int attr popUpToInclusive 0x7f04038e
int attr popUpToSaveState 0x7f04038f
int attr popupMenuBackground 0x7f040390
int attr popupMenuStyle 0x7f040391
int attr popupTheme 0x7f040392
int attr popupWindowStyle 0x7f040393
int attr postSplashScreenTheme 0x7f040394
int attr precision 0x7f040395
int attr prefixText 0x7f040396
int attr prefixTextAppearance 0x7f040397
int attr prefixTextColor 0x7f040398
int attr preserveIconSpacing 0x7f040399
int attr pressedTranslationZ 0x7f04039a
int attr primaryActivityName 0x7f04039b
int attr progressBarPadding 0x7f04039c
int attr progressBarStyle 0x7f04039d
int attr queryBackground 0x7f04039e
int attr queryHint 0x7f04039f
int attr queryPatterns 0x7f0403a0
int attr radioButtonStyle 0x7f0403a1
int attr rangeFillColor 0x7f0403a2
int attr ratingBarStyle 0x7f0403a3
int attr ratingBarStyleIndicator 0x7f0403a4
int attr ratingBarStyleSmall 0x7f0403a5
int attr recyclerViewStyle 0x7f0403a6
int attr region_heightLessThan 0x7f0403a7
int attr region_heightMoreThan 0x7f0403a8
int attr region_widthLessThan 0x7f0403a9
int attr region_widthMoreThan 0x7f0403aa
int attr removeEmbeddedFabElevation 0x7f0403ab
int attr repeat_toggle_modes 0x7f0403ac
int attr resize_mode 0x7f0403ad
int attr restoreState 0x7f0403ae
int attr reverseLayout 0x7f0403af
int attr rippleColor 0x7f0403b0
int attr round 0x7f0403b1
int attr roundPercent 0x7f0403b2
int attr route 0x7f0403b3
int attr saturation 0x7f0403b4
int attr scopeUris 0x7f0403b5
int attr scrimAnimationDuration 0x7f0403b6
int attr scrimBackground 0x7f0403b7
int attr scrimVisibleHeightTrigger 0x7f0403b8
int attr scrollingPagerIndicatorStyle 0x7f0403b9
int attr scrubber_color 0x7f0403ba
int attr scrubber_disabled_size 0x7f0403bb
int attr scrubber_dragged_size 0x7f0403bc
int attr scrubber_drawable 0x7f0403bd
int attr scrubber_enabled_size 0x7f0403be
int attr searchHintIcon 0x7f0403bf
int attr searchIcon 0x7f0403c0
int attr searchPrefixText 0x7f0403c1
int attr searchViewStyle 0x7f0403c2
int attr secondaryActivityAction 0x7f0403c3
int attr secondaryActivityName 0x7f0403c4
int attr seekBarStyle 0x7f0403c5
int attr selectableItemBackground 0x7f0403c6
int attr selectableItemBackgroundBorderless 0x7f0403c7
int attr selectionRequired 0x7f0403c8
int attr selectorColor 0x7f0403c9
int attr selectorSize 0x7f0403ca
int attr shapeAppearance 0x7f0403cb
int attr shapeAppearanceCornerExtraLarge 0x7f0403cc
int attr shapeAppearanceCornerExtraSmall 0x7f0403cd
int attr shapeAppearanceCornerLarge 0x7f0403ce
int attr shapeAppearanceCornerMedium 0x7f0403cf
int attr shapeAppearanceCornerSmall 0x7f0403d0
int attr shapeAppearanceLargeComponent 0x7f0403d1
int attr shapeAppearanceMediumComponent 0x7f0403d2
int attr shapeAppearanceOverlay 0x7f0403d3
int attr shapeAppearanceSmallComponent 0x7f0403d4
int attr shapeCornerFamily 0x7f0403d5
int attr shortcutMatchRequired 0x7f0403d6
int attr shouldRemoveExpandedCorners 0x7f0403d7
int attr showAnimationBehavior 0x7f0403d8
int attr showAsAction 0x7f0403d9
int attr showDelay 0x7f0403da
int attr showDividers 0x7f0403db
int attr showMotionSpec 0x7f0403dc
int attr showPaths 0x7f0403dd
int attr showText 0x7f0403de
int attr showTitle 0x7f0403df
int attr show_buffering 0x7f0403e0
int attr show_fastforward_button 0x7f0403e1
int attr show_next_button 0x7f0403e2
int attr show_previous_button 0x7f0403e3
int attr show_rewind_button 0x7f0403e4
int attr show_shuffle_button 0x7f0403e5
int attr show_subtitle_button 0x7f0403e6
int attr show_timeout 0x7f0403e7
int attr show_vr_button 0x7f0403e8
int attr shrinkMotionSpec 0x7f0403e9
int attr shutter_background_color 0x7f0403ea
int attr sideSheetDialogTheme 0x7f0403eb
int attr sideSheetModalStyle 0x7f0403ec
int attr simpleItemLayout 0x7f0403ed
int attr simpleItemSelectedColor 0x7f0403ee
int attr simpleItemSelectedRippleColor 0x7f0403ef
int attr simpleItems 0x7f0403f0
int attr singleChoiceItemLayout 0x7f0403f1
int attr singleLine 0x7f0403f2
int attr singleSelection 0x7f0403f3
int attr sizePercent 0x7f0403f4
int attr sizeToFit 0x7f0403f5
int attr sliderStyle 0x7f0403f6
int attr snackbarButtonStyle 0x7f0403f7
int attr snackbarStyle 0x7f0403f8
int attr snackbarTextViewStyle 0x7f0403f9
int attr spanCount 0x7f0403fa
int attr spi_dotColor 0x7f0403fb
int attr spi_dotMinimumSize 0x7f0403fc
int attr spi_dotSelectedColor 0x7f0403fd
int attr spi_dotSelectedSize 0x7f0403fe
int attr spi_dotSize 0x7f0403ff
int attr spi_dotSpacing 0x7f040400
int attr spi_looped 0x7f040401
int attr spi_orientation 0x7f040402
int attr spi_visibleDotCount 0x7f040403
int attr spi_visibleDotThreshold 0x7f040404
int attr spinBars 0x7f040405
int attr spinnerDropDownItemStyle 0x7f040406
int attr spinnerStyle 0x7f040407
int attr splashScreenIconSize 0x7f040408
int attr splitLayoutDirection 0x7f040409
int attr splitMinSmallestWidth 0x7f04040a
int attr splitMinWidth 0x7f04040b
int attr splitRatio 0x7f04040c
int attr splitTrack 0x7f04040d
int attr srcCompat 0x7f04040e
int attr stackFromEnd 0x7f04040f
int attr staggered 0x7f040410
int attr startDestination 0x7f040411
int attr startIconCheckable 0x7f040412
int attr startIconContentDescription 0x7f040413
int attr startIconDrawable 0x7f040414
int attr startIconMinSize 0x7f040415
int attr startIconScaleType 0x7f040416
int attr startIconTint 0x7f040417
int attr startIconTintMode 0x7f040418
int attr state_above_anchor 0x7f040419
int attr state_collapsed 0x7f04041a
int attr state_collapsible 0x7f04041b
int attr state_dragged 0x7f04041c
int attr state_error 0x7f04041d
int attr state_indeterminate 0x7f04041e
int attr state_liftable 0x7f04041f
int attr state_lifted 0x7f040420
int attr state_with_icon 0x7f040421
int attr statusBarBackground 0x7f040422
int attr statusBarForeground 0x7f040423
int attr statusBarScrim 0x7f040424
int attr strokeColor 0x7f040425
int attr strokeWidth 0x7f040426
int attr subMenuArrow 0x7f040427
int attr subheaderColor 0x7f040428
int attr subheaderInsetEnd 0x7f040429
int attr subheaderInsetStart 0x7f04042a
int attr subheaderTextAppearance 0x7f04042b
int attr submitBackground 0x7f04042c
int attr subtitle 0x7f04042d
int attr subtitleCentered 0x7f04042e
int attr subtitleTextAppearance 0x7f04042f
int attr subtitleTextColor 0x7f040430
int attr subtitleTextStyle 0x7f040431
int attr suffixText 0x7f040432
int attr suffixTextAppearance 0x7f040433
int attr suffixTextColor 0x7f040434
int attr suggestionRowLayout 0x7f040435
int attr surface_type 0x7f040436
int attr swipeRefreshLayoutProgressSpinnerBackgroundColor 0x7f040437
int attr switchMinWidth 0x7f040438
int attr switchPadding 0x7f040439
int attr switchStyle 0x7f04043a
int attr switchTextAppearance 0x7f04043b
int attr tabBackground 0x7f04043c
int attr tabContentStart 0x7f04043d
int attr tabGravity 0x7f04043e
int attr tabIconTint 0x7f04043f
int attr tabIconTintMode 0x7f040440
int attr tabIndicator 0x7f040441
int attr tabIndicatorAnimationDuration 0x7f040442
int attr tabIndicatorAnimationMode 0x7f040443
int attr tabIndicatorColor 0x7f040444
int attr tabIndicatorFullWidth 0x7f040445
int attr tabIndicatorGravity 0x7f040446
int attr tabIndicatorHeight 0x7f040447
int attr tabInlineLabel 0x7f040448
int attr tabMaxWidth 0x7f040449
int attr tabMinWidth 0x7f04044a
int attr tabMode 0x7f04044b
int attr tabPadding 0x7f04044c
int attr tabPaddingBottom 0x7f04044d
int attr tabPaddingEnd 0x7f04044e
int attr tabPaddingStart 0x7f04044f
int attr tabPaddingTop 0x7f040450
int attr tabRippleColor 0x7f040451
int attr tabSecondaryStyle 0x7f040452
int attr tabSelectedTextAppearance 0x7f040453
int attr tabSelectedTextColor 0x7f040454
int attr tabStyle 0x7f040455
int attr tabTextAppearance 0x7f040456
int attr tabTextColor 0x7f040457
int attr tabUnboundedRipple 0x7f040458
int attr targetId 0x7f040459
int attr targetPackage 0x7f04045a
int attr telltales_tailColor 0x7f04045b
int attr telltales_tailScale 0x7f04045c
int attr telltales_velocityMode 0x7f04045d
int attr textAllCaps 0x7f04045e
int attr textAppearanceBody1 0x7f04045f
int attr textAppearanceBody2 0x7f040460
int attr textAppearanceBodyLarge 0x7f040461
int attr textAppearanceBodyMedium 0x7f040462
int attr textAppearanceBodySmall 0x7f040463
int attr textAppearanceButton 0x7f040464
int attr textAppearanceCaption 0x7f040465
int attr textAppearanceDisplayLarge 0x7f040466
int attr textAppearanceDisplayMedium 0x7f040467
int attr textAppearanceDisplaySmall 0x7f040468
int attr textAppearanceHeadline1 0x7f040469
int attr textAppearanceHeadline2 0x7f04046a
int attr textAppearanceHeadline3 0x7f04046b
int attr textAppearanceHeadline4 0x7f04046c
int attr textAppearanceHeadline5 0x7f04046d
int attr textAppearanceHeadline6 0x7f04046e
int attr textAppearanceHeadlineLarge 0x7f04046f
int attr textAppearanceHeadlineMedium 0x7f040470
int attr textAppearanceHeadlineSmall 0x7f040471
int attr textAppearanceLabelLarge 0x7f040472
int attr textAppearanceLabelMedium 0x7f040473
int attr textAppearanceLabelSmall 0x7f040474
int attr textAppearanceLargePopupMenu 0x7f040475
int attr textAppearanceLineHeightEnabled 0x7f040476
int attr textAppearanceListItem 0x7f040477
int attr textAppearanceListItemSecondary 0x7f040478
int attr textAppearanceListItemSmall 0x7f040479
int attr textAppearanceOverline 0x7f04047a
int attr textAppearancePopupMenuHeader 0x7f04047b
int attr textAppearanceSearchResultSubtitle 0x7f04047c
int attr textAppearanceSearchResultTitle 0x7f04047d
int attr textAppearanceSmallPopupMenu 0x7f04047e
int attr textAppearanceSubtitle1 0x7f04047f
int attr textAppearanceSubtitle2 0x7f040480
int attr textAppearanceTitleLarge 0x7f040481
int attr textAppearanceTitleMedium 0x7f040482
int attr textAppearanceTitleSmall 0x7f040483
int attr textColorAlertDialogListItem 0x7f040484
int attr textColorSearchUrl 0x7f040485
int attr textEndPadding 0x7f040486
int attr textInputFilledDenseStyle 0x7f040487
int attr textInputFilledExposedDropdownMenuStyle 0x7f040488
int attr textInputFilledStyle 0x7f040489
int attr textInputLayoutFocusedRectEnabled 0x7f04048a
int attr textInputOutlinedDenseStyle 0x7f04048b
int attr textInputOutlinedExposedDropdownMenuStyle 0x7f04048c
int attr textInputOutlinedStyle 0x7f04048d
int attr textInputStyle 0x7f04048e
int attr textLocale 0x7f04048f
int attr textStartPadding 0x7f040490
int attr theme 0x7f040491
int attr thickness 0x7f040492
int attr thumbColor 0x7f040493
int attr thumbElevation 0x7f040494
int attr thumbIcon 0x7f040495
int attr thumbIconTint 0x7f040496
int attr thumbIconTintMode 0x7f040497
int attr thumbRadius 0x7f040498
int attr thumbStrokeColor 0x7f040499
int attr thumbStrokeWidth 0x7f04049a
int attr thumbTextPadding 0x7f04049b
int attr thumbTint 0x7f04049c
int attr thumbTintMode 0x7f04049d
int attr tickColor 0x7f04049e
int attr tickColorActive 0x7f04049f
int attr tickColorInactive 0x7f0404a0
int attr tickMark 0x7f0404a1
int attr tickMarkTint 0x7f0404a2
int attr tickMarkTintMode 0x7f0404a3
int attr tickVisible 0x7f0404a4
int attr time_bar_min_update_interval 0x7f0404a5
int attr tint 0x7f0404a6
int attr tintMode 0x7f0404a7
int attr tintNavigationIcon 0x7f0404a8
int attr title 0x7f0404a9
int attr titleCentered 0x7f0404aa
int attr titleCollapseMode 0x7f0404ab
int attr titleEnabled 0x7f0404ac
int attr titleMargin 0x7f0404ad
int attr titleMarginBottom 0x7f0404ae
int attr titleMarginEnd 0x7f0404af
int attr titleMarginStart 0x7f0404b0
int attr titleMarginTop 0x7f0404b1
int attr titleMargins 0x7f0404b2
int attr titlePositionInterpolator 0x7f0404b3
int attr titleTextAppearance 0x7f0404b4
int attr titleTextColor 0x7f0404b5
int attr titleTextEllipsize 0x7f0404b6
int attr titleTextStyle 0x7f0404b7
int attr toggleCheckedStateOnClick 0x7f0404b8
int attr toolbarId 0x7f0404b9
int attr toolbarNavigationButtonStyle 0x7f0404ba
int attr toolbarStyle 0x7f0404bb
int attr toolbarSurfaceStyle 0x7f0404bc
int attr tooltipForegroundColor 0x7f0404bd
int attr tooltipFrameBackground 0x7f0404be
int attr tooltipStyle 0x7f0404bf
int attr tooltipText 0x7f0404c0
int attr topInsetScrimEnabled 0x7f0404c1
int attr touchAnchorId 0x7f0404c2
int attr touchAnchorSide 0x7f0404c3
int attr touchRegionId 0x7f0404c4
int attr touch_target_height 0x7f0404c5
int attr track 0x7f0404c6
int attr trackColor 0x7f0404c7
int attr trackColorActive 0x7f0404c8
int attr trackColorInactive 0x7f0404c9
int attr trackCornerRadius 0x7f0404ca
int attr trackDecoration 0x7f0404cb
int attr trackDecorationTint 0x7f0404cc
int attr trackDecorationTintMode 0x7f0404cd
int attr trackHeight 0x7f0404ce
int attr trackThickness 0x7f0404cf
int attr trackTint 0x7f0404d0
int attr trackTintMode 0x7f0404d1
int attr transitionDisable 0x7f0404d2
int attr transitionEasing 0x7f0404d3
int attr transitionFlags 0x7f0404d4
int attr transitionPathRotate 0x7f0404d5
int attr transitionShapeAppearance 0x7f0404d6
int attr triggerId 0x7f0404d7
int attr triggerReceiver 0x7f0404d8
int attr triggerSlack 0x7f0404d9
int attr ttcIndex 0x7f0404da
int attr unplayed_color 0x7f0404db
int attr uri 0x7f0404dc
int attr useCompatPadding 0x7f0404dd
int attr useDrawerArrowDrawable 0x7f0404de
int attr useMaterialThemeColors 0x7f0404df
int attr use_artwork 0x7f0404e0
int attr use_controller 0x7f0404e1
int attr values 0x7f0404e2
int attr verticalOffset 0x7f0404e3
int attr verticalOffsetWithText 0x7f0404e4
int attr viewInflaterClass 0x7f0404e5
int attr visibilityMode 0x7f0404e6
int attr voiceIcon 0x7f0404e7
int attr warmth 0x7f0404e8
int attr waveDecay 0x7f0404e9
int attr waveOffset 0x7f0404ea
int attr wavePeriod 0x7f0404eb
int attr waveShape 0x7f0404ec
int attr waveVariesBy 0x7f0404ed
int attr windowActionBar 0x7f0404ee
int attr windowActionBarOverlay 0x7f0404ef
int attr windowActionModeOverlay 0x7f0404f0
int attr windowFixedHeightMajor 0x7f0404f1
int attr windowFixedHeightMinor 0x7f0404f2
int attr windowFixedWidthMajor 0x7f0404f3
int attr windowFixedWidthMinor 0x7f0404f4
int attr windowMinWidthMajor 0x7f0404f5
int attr windowMinWidthMinor 0x7f0404f6
int attr windowNoTitle 0x7f0404f7
int attr windowSplashScreenAnimatedIcon 0x7f0404f8
int attr windowSplashScreenAnimationDuration 0x7f0404f9
int attr windowSplashScreenBackground 0x7f0404fa
int attr windowSplashScreenIconBackgroundColor 0x7f0404fb
int attr yearSelectedStyle 0x7f0404fc
int attr yearStyle 0x7f0404fd
int attr yearTodayStyle 0x7f0404fe
int attr zxing_framing_rect_height 0x7f0404ff
int attr zxing_framing_rect_width 0x7f040500
int attr zxing_possible_result_points 0x7f040501
int attr zxing_preview_scaling_strategy 0x7f040502
int attr zxing_result_view 0x7f040503
int attr zxing_scanner_layout 0x7f040504
int attr zxing_use_texture_view 0x7f040505
int attr zxing_viewfinder_laser 0x7f040506
int attr zxing_viewfinder_laser_visibility 0x7f040507
int attr zxing_viewfinder_mask 0x7f040508
int bool abc_action_bar_embed_tabs 0x7f050000
int bool abc_config_actionMenuItemAllCaps 0x7f050001
int bool mtrl_btn_textappearance_all_caps 0x7f050002
int color abc_background_cache_hint_selector_material_dark 0x7f060000
int color abc_background_cache_hint_selector_material_light 0x7f060001
int color abc_btn_colored_borderless_text_material 0x7f060002
int color abc_btn_colored_text_material 0x7f060003
int color abc_color_highlight_material 0x7f060004
int color abc_decor_view_status_guard 0x7f060005
int color abc_decor_view_status_guard_light 0x7f060006
int color abc_hint_foreground_material_dark 0x7f060007
int color abc_hint_foreground_material_light 0x7f060008
int color abc_primary_text_disable_only_material_dark 0x7f060009
int color abc_primary_text_disable_only_material_light 0x7f06000a
int color abc_primary_text_material_dark 0x7f06000b
int color abc_primary_text_material_light 0x7f06000c
int color abc_search_url_text 0x7f06000d
int color abc_search_url_text_normal 0x7f06000e
int color abc_search_url_text_pressed 0x7f06000f
int color abc_search_url_text_selected 0x7f060010
int color abc_secondary_text_material_dark 0x7f060011
int color abc_secondary_text_material_light 0x7f060012
int color abc_tint_btn_checkable 0x7f060013
int color abc_tint_default 0x7f060014
int color abc_tint_edittext 0x7f060015
int color abc_tint_seek_thumb 0x7f060016
int color abc_tint_spinner 0x7f060017
int color abc_tint_switch_track 0x7f060018
int color accent_material_dark 0x7f060019
int color accent_material_light 0x7f06001a
int color androidx_core_ripple_material_light 0x7f06001b
int color androidx_core_secondary_text_default_material_light 0x7f06001c
int color app_icon_background 0x7f06001d
int color background_floating_material_dark 0x7f06001e
int color background_floating_material_light 0x7f06001f
int color background_material_dark 0x7f060020
int color background_material_light 0x7f060021
int color bgGray 0x7f060022
int color black 0x7f060023
int color black90 0x7f060024
int color blue 0x7f060025
int color bright_foreground_disabled_material_dark 0x7f060026
int color bright_foreground_disabled_material_light 0x7f060027
int color bright_foreground_inverse_material_dark 0x7f060028
int color bright_foreground_inverse_material_light 0x7f060029
int color bright_foreground_material_dark 0x7f06002a
int color bright_foreground_material_light 0x7f06002b
int color browser_actions_bg_grey 0x7f06002c
int color browser_actions_divider_color 0x7f06002d
int color browser_actions_text_color 0x7f06002e
int color browser_actions_title_color 0x7f06002f
int color button_material_dark 0x7f060030
int color button_material_light 0x7f060031
int color cardview_dark_background 0x7f060032
int color cardview_light_background 0x7f060033
int color cardview_shadow_end_color 0x7f060034
int color cardview_shadow_start_color 0x7f060035
int color color_text_dark 0x7f060036
int color color_text_light 0x7f060037
int color common_google_signin_btn_text_dark 0x7f060038
int color common_google_signin_btn_text_dark_default 0x7f060039
int color common_google_signin_btn_text_dark_disabled 0x7f06003a
int color common_google_signin_btn_text_dark_focused 0x7f06003b
int color common_google_signin_btn_text_dark_pressed 0x7f06003c
int color common_google_signin_btn_text_light 0x7f06003d
int color common_google_signin_btn_text_light_default 0x7f06003e
int color common_google_signin_btn_text_light_disabled 0x7f06003f
int color common_google_signin_btn_text_light_focused 0x7f060040
int color common_google_signin_btn_text_light_pressed 0x7f060041
int color common_google_signin_btn_tint 0x7f060042
int color design_bottom_navigation_shadow_color 0x7f060043
int color design_box_stroke_color 0x7f060044
int color design_dark_default_color_background 0x7f060045
int color design_dark_default_color_error 0x7f060046
int color design_dark_default_color_on_background 0x7f060047
int color design_dark_default_color_on_error 0x7f060048
int color design_dark_default_color_on_primary 0x7f060049
int color design_dark_default_color_on_secondary 0x7f06004a
int color design_dark_default_color_on_surface 0x7f06004b
int color design_dark_default_color_primary 0x7f06004c
int color design_dark_default_color_primary_dark 0x7f06004d
int color design_dark_default_color_primary_variant 0x7f06004e
int color design_dark_default_color_secondary 0x7f06004f
int color design_dark_default_color_secondary_variant 0x7f060050
int color design_dark_default_color_surface 0x7f060051
int color design_default_color_background 0x7f060052
int color design_default_color_error 0x7f060053
int color design_default_color_on_background 0x7f060054
int color design_default_color_on_error 0x7f060055
int color design_default_color_on_primary 0x7f060056
int color design_default_color_on_secondary 0x7f060057
int color design_default_color_on_surface 0x7f060058
int color design_default_color_primary 0x7f060059
int color design_default_color_primary_dark 0x7f06005a
int color design_default_color_primary_variant 0x7f06005b
int color design_default_color_secondary 0x7f06005c
int color design_default_color_secondary_variant 0x7f06005d
int color design_default_color_surface 0x7f06005e
int color design_error 0x7f06005f
int color design_fab_shadow_end_color 0x7f060060
int color design_fab_shadow_mid_color 0x7f060061
int color design_fab_shadow_start_color 0x7f060062
int color design_fab_stroke_end_inner_color 0x7f060063
int color design_fab_stroke_end_outer_color 0x7f060064
int color design_fab_stroke_top_inner_color 0x7f060065
int color design_fab_stroke_top_outer_color 0x7f060066
int color design_icon_tint 0x7f060067
int color design_snackbar_background_color 0x7f060068
int color dim_foreground_disabled_material_dark 0x7f060069
int color dim_foreground_disabled_material_light 0x7f06006a
int color dim_foreground_material_dark 0x7f06006b
int color dim_foreground_material_light 0x7f06006c
int color error_color_material_dark 0x7f06006d
int color error_color_material_light 0x7f06006e
int color exo_black_opacity_60 0x7f06006f
int color exo_black_opacity_70 0x7f060070
int color exo_bottom_bar_background 0x7f060071
int color exo_edit_mode_background_color 0x7f060072
int color exo_error_message_background_color 0x7f060073
int color exo_styled_error_message_background 0x7f060074
int color exo_white 0x7f060075
int color exo_white_opacity_70 0x7f060076
int color foreground_material_dark 0x7f060077
int color foreground_material_light 0x7f060078
int color gray 0x7f060079
int color green 0x7f06007a
int color greenStepDone 0x7f06007b
int color highlighted_text_material_dark 0x7f06007c
int color highlighted_text_material_light 0x7f06007d
int color ic_launcher_background 0x7f06007e
int color inactivePinFieldColor 0x7f06007f
int color lightBlack 0x7f060080
int color m3_appbar_overlay_color 0x7f060081
int color m3_assist_chip_icon_tint_color 0x7f060082
int color m3_assist_chip_stroke_color 0x7f060083
int color m3_button_background_color_selector 0x7f060084
int color m3_button_foreground_color_selector 0x7f060085
int color m3_button_outline_color_selector 0x7f060086
int color m3_button_ripple_color 0x7f060087
int color m3_button_ripple_color_selector 0x7f060088
int color m3_calendar_item_disabled_text 0x7f060089
int color m3_calendar_item_stroke_color 0x7f06008a
int color m3_card_foreground_color 0x7f06008b
int color m3_card_ripple_color 0x7f06008c
int color m3_card_stroke_color 0x7f06008d
int color m3_checkbox_button_icon_tint 0x7f06008e
int color m3_checkbox_button_tint 0x7f06008f
int color m3_chip_assist_text_color 0x7f060090
int color m3_chip_background_color 0x7f060091
int color m3_chip_ripple_color 0x7f060092
int color m3_chip_stroke_color 0x7f060093
int color m3_chip_text_color 0x7f060094
int color m3_dark_default_color_primary_text 0x7f060095
int color m3_dark_default_color_secondary_text 0x7f060096
int color m3_dark_highlighted_text 0x7f060097
int color m3_dark_hint_foreground 0x7f060098
int color m3_dark_primary_text_disable_only 0x7f060099
int color m3_default_color_primary_text 0x7f06009a
int color m3_default_color_secondary_text 0x7f06009b
int color m3_dynamic_dark_default_color_primary_text 0x7f06009c
int color m3_dynamic_dark_default_color_secondary_text 0x7f06009d
int color m3_dynamic_dark_highlighted_text 0x7f06009e
int color m3_dynamic_dark_hint_foreground 0x7f06009f
int color m3_dynamic_dark_primary_text_disable_only 0x7f0600a0
int color m3_dynamic_default_color_primary_text 0x7f0600a1
int color m3_dynamic_default_color_secondary_text 0x7f0600a2
int color m3_dynamic_highlighted_text 0x7f0600a3
int color m3_dynamic_hint_foreground 0x7f0600a4
int color m3_dynamic_primary_text_disable_only 0x7f0600a5
int color m3_efab_ripple_color_selector 0x7f0600a6
int color m3_elevated_chip_background_color 0x7f0600a7
int color m3_fab_efab_background_color_selector 0x7f0600a8
int color m3_fab_efab_foreground_color_selector 0x7f0600a9
int color m3_fab_ripple_color_selector 0x7f0600aa
int color m3_filled_icon_button_container_color_selector 0x7f0600ab
int color m3_highlighted_text 0x7f0600ac
int color m3_hint_foreground 0x7f0600ad
int color m3_icon_button_icon_color_selector 0x7f0600ae
int color m3_navigation_bar_item_with_indicator_icon_tint 0x7f0600af
int color m3_navigation_bar_item_with_indicator_label_tint 0x7f0600b0
int color m3_navigation_bar_ripple_color_selector 0x7f0600b1
int color m3_navigation_item_background_color 0x7f0600b2
int color m3_navigation_item_icon_tint 0x7f0600b3
int color m3_navigation_item_ripple_color 0x7f0600b4
int color m3_navigation_item_text_color 0x7f0600b5
int color m3_popupmenu_overlay_color 0x7f0600b6
int color m3_primary_text_disable_only 0x7f0600b7
int color m3_radiobutton_button_tint 0x7f0600b8
int color m3_radiobutton_ripple_tint 0x7f0600b9
int color m3_ref_palette_black 0x7f0600ba
int color m3_ref_palette_dynamic_neutral0 0x7f0600bb
int color m3_ref_palette_dynamic_neutral10 0x7f0600bc
int color m3_ref_palette_dynamic_neutral100 0x7f0600bd
int color m3_ref_palette_dynamic_neutral20 0x7f0600be
int color m3_ref_palette_dynamic_neutral30 0x7f0600bf
int color m3_ref_palette_dynamic_neutral40 0x7f0600c0
int color m3_ref_palette_dynamic_neutral50 0x7f0600c1
int color m3_ref_palette_dynamic_neutral60 0x7f0600c2
int color m3_ref_palette_dynamic_neutral70 0x7f0600c3
int color m3_ref_palette_dynamic_neutral80 0x7f0600c4
int color m3_ref_palette_dynamic_neutral90 0x7f0600c5
int color m3_ref_palette_dynamic_neutral95 0x7f0600c6
int color m3_ref_palette_dynamic_neutral99 0x7f0600c7
int color m3_ref_palette_dynamic_neutral_variant0 0x7f0600c8
int color m3_ref_palette_dynamic_neutral_variant10 0x7f0600c9
int color m3_ref_palette_dynamic_neutral_variant100 0x7f0600ca
int color m3_ref_palette_dynamic_neutral_variant20 0x7f0600cb
int color m3_ref_palette_dynamic_neutral_variant30 0x7f0600cc
int color m3_ref_palette_dynamic_neutral_variant40 0x7f0600cd
int color m3_ref_palette_dynamic_neutral_variant50 0x7f0600ce
int color m3_ref_palette_dynamic_neutral_variant60 0x7f0600cf
int color m3_ref_palette_dynamic_neutral_variant70 0x7f0600d0
int color m3_ref_palette_dynamic_neutral_variant80 0x7f0600d1
int color m3_ref_palette_dynamic_neutral_variant90 0x7f0600d2
int color m3_ref_palette_dynamic_neutral_variant95 0x7f0600d3
int color m3_ref_palette_dynamic_neutral_variant99 0x7f0600d4
int color m3_ref_palette_dynamic_primary0 0x7f0600d5
int color m3_ref_palette_dynamic_primary10 0x7f0600d6
int color m3_ref_palette_dynamic_primary100 0x7f0600d7
int color m3_ref_palette_dynamic_primary20 0x7f0600d8
int color m3_ref_palette_dynamic_primary30 0x7f0600d9
int color m3_ref_palette_dynamic_primary40 0x7f0600da
int color m3_ref_palette_dynamic_primary50 0x7f0600db
int color m3_ref_palette_dynamic_primary60 0x7f0600dc
int color m3_ref_palette_dynamic_primary70 0x7f0600dd
int color m3_ref_palette_dynamic_primary80 0x7f0600de
int color m3_ref_palette_dynamic_primary90 0x7f0600df
int color m3_ref_palette_dynamic_primary95 0x7f0600e0
int color m3_ref_palette_dynamic_primary99 0x7f0600e1
int color m3_ref_palette_dynamic_secondary0 0x7f0600e2
int color m3_ref_palette_dynamic_secondary10 0x7f0600e3
int color m3_ref_palette_dynamic_secondary100 0x7f0600e4
int color m3_ref_palette_dynamic_secondary20 0x7f0600e5
int color m3_ref_palette_dynamic_secondary30 0x7f0600e6
int color m3_ref_palette_dynamic_secondary40 0x7f0600e7
int color m3_ref_palette_dynamic_secondary50 0x7f0600e8
int color m3_ref_palette_dynamic_secondary60 0x7f0600e9
int color m3_ref_palette_dynamic_secondary70 0x7f0600ea
int color m3_ref_palette_dynamic_secondary80 0x7f0600eb
int color m3_ref_palette_dynamic_secondary90 0x7f0600ec
int color m3_ref_palette_dynamic_secondary95 0x7f0600ed
int color m3_ref_palette_dynamic_secondary99 0x7f0600ee
int color m3_ref_palette_dynamic_tertiary0 0x7f0600ef
int color m3_ref_palette_dynamic_tertiary10 0x7f0600f0
int color m3_ref_palette_dynamic_tertiary100 0x7f0600f1
int color m3_ref_palette_dynamic_tertiary20 0x7f0600f2
int color m3_ref_palette_dynamic_tertiary30 0x7f0600f3
int color m3_ref_palette_dynamic_tertiary40 0x7f0600f4
int color m3_ref_palette_dynamic_tertiary50 0x7f0600f5
int color m3_ref_palette_dynamic_tertiary60 0x7f0600f6
int color m3_ref_palette_dynamic_tertiary70 0x7f0600f7
int color m3_ref_palette_dynamic_tertiary80 0x7f0600f8
int color m3_ref_palette_dynamic_tertiary90 0x7f0600f9
int color m3_ref_palette_dynamic_tertiary95 0x7f0600fa
int color m3_ref_palette_dynamic_tertiary99 0x7f0600fb
int color m3_ref_palette_error0 0x7f0600fc
int color m3_ref_palette_error10 0x7f0600fd
int color m3_ref_palette_error100 0x7f0600fe
int color m3_ref_palette_error20 0x7f0600ff
int color m3_ref_palette_error30 0x7f060100
int color m3_ref_palette_error40 0x7f060101
int color m3_ref_palette_error50 0x7f060102
int color m3_ref_palette_error60 0x7f060103
int color m3_ref_palette_error70 0x7f060104
int color m3_ref_palette_error80 0x7f060105
int color m3_ref_palette_error90 0x7f060106
int color m3_ref_palette_error95 0x7f060107
int color m3_ref_palette_error99 0x7f060108
int color m3_ref_palette_neutral0 0x7f060109
int color m3_ref_palette_neutral10 0x7f06010a
int color m3_ref_palette_neutral100 0x7f06010b
int color m3_ref_palette_neutral20 0x7f06010c
int color m3_ref_palette_neutral30 0x7f06010d
int color m3_ref_palette_neutral40 0x7f06010e
int color m3_ref_palette_neutral50 0x7f06010f
int color m3_ref_palette_neutral60 0x7f060110
int color m3_ref_palette_neutral70 0x7f060111
int color m3_ref_palette_neutral80 0x7f060112
int color m3_ref_palette_neutral90 0x7f060113
int color m3_ref_palette_neutral95 0x7f060114
int color m3_ref_palette_neutral99 0x7f060115
int color m3_ref_palette_neutral_variant0 0x7f060116
int color m3_ref_palette_neutral_variant10 0x7f060117
int color m3_ref_palette_neutral_variant100 0x7f060118
int color m3_ref_palette_neutral_variant20 0x7f060119
int color m3_ref_palette_neutral_variant30 0x7f06011a
int color m3_ref_palette_neutral_variant40 0x7f06011b
int color m3_ref_palette_neutral_variant50 0x7f06011c
int color m3_ref_palette_neutral_variant60 0x7f06011d
int color m3_ref_palette_neutral_variant70 0x7f06011e
int color m3_ref_palette_neutral_variant80 0x7f06011f
int color m3_ref_palette_neutral_variant90 0x7f060120
int color m3_ref_palette_neutral_variant95 0x7f060121
int color m3_ref_palette_neutral_variant99 0x7f060122
int color m3_ref_palette_primary0 0x7f060123
int color m3_ref_palette_primary10 0x7f060124
int color m3_ref_palette_primary100 0x7f060125
int color m3_ref_palette_primary20 0x7f060126
int color m3_ref_palette_primary30 0x7f060127
int color m3_ref_palette_primary40 0x7f060128
int color m3_ref_palette_primary50 0x7f060129
int color m3_ref_palette_primary60 0x7f06012a
int color m3_ref_palette_primary70 0x7f06012b
int color m3_ref_palette_primary80 0x7f06012c
int color m3_ref_palette_primary90 0x7f06012d
int color m3_ref_palette_primary95 0x7f06012e
int color m3_ref_palette_primary99 0x7f06012f
int color m3_ref_palette_secondary0 0x7f060130
int color m3_ref_palette_secondary10 0x7f060131
int color m3_ref_palette_secondary100 0x7f060132
int color m3_ref_palette_secondary20 0x7f060133
int color m3_ref_palette_secondary30 0x7f060134
int color m3_ref_palette_secondary40 0x7f060135
int color m3_ref_palette_secondary50 0x7f060136
int color m3_ref_palette_secondary60 0x7f060137
int color m3_ref_palette_secondary70 0x7f060138
int color m3_ref_palette_secondary80 0x7f060139
int color m3_ref_palette_secondary90 0x7f06013a
int color m3_ref_palette_secondary95 0x7f06013b
int color m3_ref_palette_secondary99 0x7f06013c
int color m3_ref_palette_tertiary0 0x7f06013d
int color m3_ref_palette_tertiary10 0x7f06013e
int color m3_ref_palette_tertiary100 0x7f06013f
int color m3_ref_palette_tertiary20 0x7f060140
int color m3_ref_palette_tertiary30 0x7f060141
int color m3_ref_palette_tertiary40 0x7f060142
int color m3_ref_palette_tertiary50 0x7f060143
int color m3_ref_palette_tertiary60 0x7f060144
int color m3_ref_palette_tertiary70 0x7f060145
int color m3_ref_palette_tertiary80 0x7f060146
int color m3_ref_palette_tertiary90 0x7f060147
int color m3_ref_palette_tertiary95 0x7f060148
int color m3_ref_palette_tertiary99 0x7f060149
int color m3_ref_palette_white 0x7f06014a
int color m3_selection_control_ripple_color_selector 0x7f06014b
int color m3_simple_item_ripple_color 0x7f06014c
int color m3_slider_active_track_color 0x7f06014d
int color m3_slider_halo_color 0x7f06014e
int color m3_slider_inactive_track_color 0x7f06014f
int color m3_slider_thumb_color 0x7f060150
int color m3_switch_thumb_tint 0x7f060151
int color m3_switch_track_tint 0x7f060152
int color m3_sys_color_dark_background 0x7f060153
int color m3_sys_color_dark_error 0x7f060154
int color m3_sys_color_dark_error_container 0x7f060155
int color m3_sys_color_dark_inverse_on_surface 0x7f060156
int color m3_sys_color_dark_inverse_primary 0x7f060157
int color m3_sys_color_dark_inverse_surface 0x7f060158
int color m3_sys_color_dark_on_background 0x7f060159
int color m3_sys_color_dark_on_error 0x7f06015a
int color m3_sys_color_dark_on_error_container 0x7f06015b
int color m3_sys_color_dark_on_primary 0x7f06015c
int color m3_sys_color_dark_on_primary_container 0x7f06015d
int color m3_sys_color_dark_on_secondary 0x7f06015e
int color m3_sys_color_dark_on_secondary_container 0x7f06015f
int color m3_sys_color_dark_on_surface 0x7f060160
int color m3_sys_color_dark_on_surface_variant 0x7f060161
int color m3_sys_color_dark_on_tertiary 0x7f060162
int color m3_sys_color_dark_on_tertiary_container 0x7f060163
int color m3_sys_color_dark_outline 0x7f060164
int color m3_sys_color_dark_outline_variant 0x7f060165
int color m3_sys_color_dark_primary 0x7f060166
int color m3_sys_color_dark_primary_container 0x7f060167
int color m3_sys_color_dark_secondary 0x7f060168
int color m3_sys_color_dark_secondary_container 0x7f060169
int color m3_sys_color_dark_surface 0x7f06016a
int color m3_sys_color_dark_surface_variant 0x7f06016b
int color m3_sys_color_dark_tertiary 0x7f06016c
int color m3_sys_color_dark_tertiary_container 0x7f06016d
int color m3_sys_color_dynamic_dark_background 0x7f06016e
int color m3_sys_color_dynamic_dark_inverse_on_surface 0x7f06016f
int color m3_sys_color_dynamic_dark_inverse_primary 0x7f060170
int color m3_sys_color_dynamic_dark_inverse_surface 0x7f060171
int color m3_sys_color_dynamic_dark_on_background 0x7f060172
int color m3_sys_color_dynamic_dark_on_primary 0x7f060173
int color m3_sys_color_dynamic_dark_on_primary_container 0x7f060174
int color m3_sys_color_dynamic_dark_on_secondary 0x7f060175
int color m3_sys_color_dynamic_dark_on_secondary_container 0x7f060176
int color m3_sys_color_dynamic_dark_on_surface 0x7f060177
int color m3_sys_color_dynamic_dark_on_surface_variant 0x7f060178
int color m3_sys_color_dynamic_dark_on_tertiary 0x7f060179
int color m3_sys_color_dynamic_dark_on_tertiary_container 0x7f06017a
int color m3_sys_color_dynamic_dark_outline 0x7f06017b
int color m3_sys_color_dynamic_dark_outline_variant 0x7f06017c
int color m3_sys_color_dynamic_dark_primary 0x7f06017d
int color m3_sys_color_dynamic_dark_primary_container 0x7f06017e
int color m3_sys_color_dynamic_dark_secondary 0x7f06017f
int color m3_sys_color_dynamic_dark_secondary_container 0x7f060180
int color m3_sys_color_dynamic_dark_surface 0x7f060181
int color m3_sys_color_dynamic_dark_surface_variant 0x7f060182
int color m3_sys_color_dynamic_dark_tertiary 0x7f060183
int color m3_sys_color_dynamic_dark_tertiary_container 0x7f060184
int color m3_sys_color_dynamic_light_background 0x7f060185
int color m3_sys_color_dynamic_light_inverse_on_surface 0x7f060186
int color m3_sys_color_dynamic_light_inverse_primary 0x7f060187
int color m3_sys_color_dynamic_light_inverse_surface 0x7f060188
int color m3_sys_color_dynamic_light_on_background 0x7f060189
int color m3_sys_color_dynamic_light_on_primary 0x7f06018a
int color m3_sys_color_dynamic_light_on_primary_container 0x7f06018b
int color m3_sys_color_dynamic_light_on_secondary 0x7f06018c
int color m3_sys_color_dynamic_light_on_secondary_container 0x7f06018d
int color m3_sys_color_dynamic_light_on_surface 0x7f06018e
int color m3_sys_color_dynamic_light_on_surface_variant 0x7f06018f
int color m3_sys_color_dynamic_light_on_tertiary 0x7f060190
int color m3_sys_color_dynamic_light_on_tertiary_container 0x7f060191
int color m3_sys_color_dynamic_light_outline 0x7f060192
int color m3_sys_color_dynamic_light_outline_variant 0x7f060193
int color m3_sys_color_dynamic_light_primary 0x7f060194
int color m3_sys_color_dynamic_light_primary_container 0x7f060195
int color m3_sys_color_dynamic_light_secondary 0x7f060196
int color m3_sys_color_dynamic_light_secondary_container 0x7f060197
int color m3_sys_color_dynamic_light_surface 0x7f060198
int color m3_sys_color_dynamic_light_surface_variant 0x7f060199
int color m3_sys_color_dynamic_light_tertiary 0x7f06019a
int color m3_sys_color_dynamic_light_tertiary_container 0x7f06019b
int color m3_sys_color_light_background 0x7f06019c
int color m3_sys_color_light_error 0x7f06019d
int color m3_sys_color_light_error_container 0x7f06019e
int color m3_sys_color_light_inverse_on_surface 0x7f06019f
int color m3_sys_color_light_inverse_primary 0x7f0601a0
int color m3_sys_color_light_inverse_surface 0x7f0601a1
int color m3_sys_color_light_on_background 0x7f0601a2
int color m3_sys_color_light_on_error 0x7f0601a3
int color m3_sys_color_light_on_error_container 0x7f0601a4
int color m3_sys_color_light_on_primary 0x7f0601a5
int color m3_sys_color_light_on_primary_container 0x7f0601a6
int color m3_sys_color_light_on_secondary 0x7f0601a7
int color m3_sys_color_light_on_secondary_container 0x7f0601a8
int color m3_sys_color_light_on_surface 0x7f0601a9
int color m3_sys_color_light_on_surface_variant 0x7f0601aa
int color m3_sys_color_light_on_tertiary 0x7f0601ab
int color m3_sys_color_light_on_tertiary_container 0x7f0601ac
int color m3_sys_color_light_outline 0x7f0601ad
int color m3_sys_color_light_outline_variant 0x7f0601ae
int color m3_sys_color_light_primary 0x7f0601af
int color m3_sys_color_light_primary_container 0x7f0601b0
int color m3_sys_color_light_secondary 0x7f0601b1
int color m3_sys_color_light_secondary_container 0x7f0601b2
int color m3_sys_color_light_surface 0x7f0601b3
int color m3_sys_color_light_surface_variant 0x7f0601b4
int color m3_sys_color_light_tertiary 0x7f0601b5
int color m3_sys_color_light_tertiary_container 0x7f0601b6
int color m3_tabs_icon_color 0x7f0601b7
int color m3_tabs_icon_color_secondary 0x7f0601b8
int color m3_tabs_ripple_color 0x7f0601b9
int color m3_tabs_ripple_color_secondary 0x7f0601ba
int color m3_tabs_text_color 0x7f0601bb
int color m3_tabs_text_color_secondary 0x7f0601bc
int color m3_text_button_background_color_selector 0x7f0601bd
int color m3_text_button_foreground_color_selector 0x7f0601be
int color m3_text_button_ripple_color_selector 0x7f0601bf
int color m3_textfield_filled_background_color 0x7f0601c0
int color m3_textfield_indicator_text_color 0x7f0601c1
int color m3_textfield_input_text_color 0x7f0601c2
int color m3_textfield_label_color 0x7f0601c3
int color m3_textfield_stroke_color 0x7f0601c4
int color m3_timepicker_button_background_color 0x7f0601c5
int color m3_timepicker_button_ripple_color 0x7f0601c6
int color m3_timepicker_button_text_color 0x7f0601c7
int color m3_timepicker_clock_text_color 0x7f0601c8
int color m3_timepicker_display_background_color 0x7f0601c9
int color m3_timepicker_display_ripple_color 0x7f0601ca
int color m3_timepicker_display_stroke_color 0x7f0601cb
int color m3_timepicker_display_text_color 0x7f0601cc
int color m3_timepicker_secondary_text_button_ripple_color 0x7f0601cd
int color m3_timepicker_secondary_text_button_text_color 0x7f0601ce
int color m3_tonal_button_ripple_color_selector 0x7f0601cf
int color material_blue_grey_800 0x7f0601d0
int color material_blue_grey_900 0x7f0601d1
int color material_blue_grey_950 0x7f0601d2
int color material_cursor_color 0x7f0601d3
int color material_deep_teal_200 0x7f0601d4
int color material_deep_teal_500 0x7f0601d5
int color material_divider_color 0x7f0601d6
int color material_dynamic_neutral0 0x7f0601d7
int color material_dynamic_neutral10 0x7f0601d8
int color material_dynamic_neutral100 0x7f0601d9
int color material_dynamic_neutral20 0x7f0601da
int color material_dynamic_neutral30 0x7f0601db
int color material_dynamic_neutral40 0x7f0601dc
int color material_dynamic_neutral50 0x7f0601dd
int color material_dynamic_neutral60 0x7f0601de
int color material_dynamic_neutral70 0x7f0601df
int color material_dynamic_neutral80 0x7f0601e0
int color material_dynamic_neutral90 0x7f0601e1
int color material_dynamic_neutral95 0x7f0601e2
int color material_dynamic_neutral99 0x7f0601e3
int color material_dynamic_neutral_variant0 0x7f0601e4
int color material_dynamic_neutral_variant10 0x7f0601e5
int color material_dynamic_neutral_variant100 0x7f0601e6
int color material_dynamic_neutral_variant20 0x7f0601e7
int color material_dynamic_neutral_variant30 0x7f0601e8
int color material_dynamic_neutral_variant40 0x7f0601e9
int color material_dynamic_neutral_variant50 0x7f0601ea
int color material_dynamic_neutral_variant60 0x7f0601eb
int color material_dynamic_neutral_variant70 0x7f0601ec
int color material_dynamic_neutral_variant80 0x7f0601ed
int color material_dynamic_neutral_variant90 0x7f0601ee
int color material_dynamic_neutral_variant95 0x7f0601ef
int color material_dynamic_neutral_variant99 0x7f0601f0
int color material_dynamic_primary0 0x7f0601f1
int color material_dynamic_primary10 0x7f0601f2
int color material_dynamic_primary100 0x7f0601f3
int color material_dynamic_primary20 0x7f0601f4
int color material_dynamic_primary30 0x7f0601f5
int color material_dynamic_primary40 0x7f0601f6
int color material_dynamic_primary50 0x7f0601f7
int color material_dynamic_primary60 0x7f0601f8
int color material_dynamic_primary70 0x7f0601f9
int color material_dynamic_primary80 0x7f0601fa
int color material_dynamic_primary90 0x7f0601fb
int color material_dynamic_primary95 0x7f0601fc
int color material_dynamic_primary99 0x7f0601fd
int color material_dynamic_secondary0 0x7f0601fe
int color material_dynamic_secondary10 0x7f0601ff
int color material_dynamic_secondary100 0x7f060200
int color material_dynamic_secondary20 0x7f060201
int color material_dynamic_secondary30 0x7f060202
int color material_dynamic_secondary40 0x7f060203
int color material_dynamic_secondary50 0x7f060204
int color material_dynamic_secondary60 0x7f060205
int color material_dynamic_secondary70 0x7f060206
int color material_dynamic_secondary80 0x7f060207
int color material_dynamic_secondary90 0x7f060208
int color material_dynamic_secondary95 0x7f060209
int color material_dynamic_secondary99 0x7f06020a
int color material_dynamic_tertiary0 0x7f06020b
int color material_dynamic_tertiary10 0x7f06020c
int color material_dynamic_tertiary100 0x7f06020d
int color material_dynamic_tertiary20 0x7f06020e
int color material_dynamic_tertiary30 0x7f06020f
int color material_dynamic_tertiary40 0x7f060210
int color material_dynamic_tertiary50 0x7f060211
int color material_dynamic_tertiary60 0x7f060212
int color material_dynamic_tertiary70 0x7f060213
int color material_dynamic_tertiary80 0x7f060214
int color material_dynamic_tertiary90 0x7f060215
int color material_dynamic_tertiary95 0x7f060216
int color material_dynamic_tertiary99 0x7f060217
int color material_grey_100 0x7f060218
int color material_grey_300 0x7f060219
int color material_grey_50 0x7f06021a
int color material_grey_600 0x7f06021b
int color material_grey_800 0x7f06021c
int color material_grey_850 0x7f06021d
int color material_grey_900 0x7f06021e
int color material_harmonized_color_error 0x7f06021f
int color material_harmonized_color_error_container 0x7f060220
int color material_harmonized_color_on_error 0x7f060221
int color material_harmonized_color_on_error_container 0x7f060222
int color material_on_background_disabled 0x7f060223
int color material_on_background_emphasis_high_type 0x7f060224
int color material_on_background_emphasis_medium 0x7f060225
int color material_on_primary_disabled 0x7f060226
int color material_on_primary_emphasis_high_type 0x7f060227
int color material_on_primary_emphasis_medium 0x7f060228
int color material_on_surface_disabled 0x7f060229
int color material_on_surface_emphasis_high_type 0x7f06022a
int color material_on_surface_emphasis_medium 0x7f06022b
int color material_on_surface_stroke 0x7f06022c
int color material_personalized_color_background 0x7f06022d
int color material_personalized_color_error 0x7f06022e
int color material_personalized_color_error_container 0x7f06022f
int color material_personalized_color_on_background 0x7f060230
int color material_personalized_color_on_error 0x7f060231
int color material_personalized_color_on_error_container 0x7f060232
int color material_personalized_color_on_primary 0x7f060233
int color material_personalized_color_on_primary_container 0x7f060234
int color material_personalized_color_on_secondary 0x7f060235
int color material_personalized_color_on_secondary_container 0x7f060236
int color material_personalized_color_on_surface 0x7f060237
int color material_personalized_color_on_surface_inverse 0x7f060238
int color material_personalized_color_on_surface_variant 0x7f060239
int color material_personalized_color_on_tertiary 0x7f06023a
int color material_personalized_color_on_tertiary_container 0x7f06023b
int color material_personalized_color_primary 0x7f06023c
int color material_personalized_color_primary_container 0x7f06023d
int color material_personalized_color_primary_inverse 0x7f06023e
int color material_personalized_color_secondary 0x7f06023f
int color material_personalized_color_secondary_container 0x7f060240
int color material_personalized_color_surface 0x7f060241
int color material_personalized_color_surface_inverse 0x7f060242
int color material_personalized_color_surface_outline 0x7f060243
int color material_personalized_color_surface_variant 0x7f060244
int color material_personalized_color_tertiary 0x7f060245
int color material_personalized_color_tertiary_container 0x7f060246
int color material_slider_active_tick_marks_color 0x7f060247
int color material_slider_active_track_color 0x7f060248
int color material_slider_halo_color 0x7f060249
int color material_slider_inactive_tick_marks_color 0x7f06024a
int color material_slider_inactive_track_color 0x7f06024b
int color material_slider_thumb_color 0x7f06024c
int color material_timepicker_button_background 0x7f06024d
int color material_timepicker_button_stroke 0x7f06024e
int color material_timepicker_clock_text_color 0x7f06024f
int color material_timepicker_clockface 0x7f060250
int color material_timepicker_modebutton_tint 0x7f060251
int color md_theme_dark_background 0x7f060252
int color md_theme_dark_error 0x7f060253
int color md_theme_dark_errorContainer 0x7f060254
int color md_theme_dark_inverseOnSurface 0x7f060255
int color md_theme_dark_inversePrimary 0x7f060256
int color md_theme_dark_inverseSurface 0x7f060257
int color md_theme_dark_onBackground 0x7f060258
int color md_theme_dark_onError 0x7f060259
int color md_theme_dark_onErrorContainer 0x7f06025a
int color md_theme_dark_onPrimary 0x7f06025b
int color md_theme_dark_onPrimaryContainer 0x7f06025c
int color md_theme_dark_onSecondary 0x7f06025d
int color md_theme_dark_onSecondaryContainer 0x7f06025e
int color md_theme_dark_onSurface 0x7f06025f
int color md_theme_dark_onSurfaceVariant 0x7f060260
int color md_theme_dark_onTertiary 0x7f060261
int color md_theme_dark_onTertiaryContainer 0x7f060262
int color md_theme_dark_outline 0x7f060263
int color md_theme_dark_outlineVariant 0x7f060264
int color md_theme_dark_primary 0x7f060265
int color md_theme_dark_primaryContainer 0x7f060266
int color md_theme_dark_scrim 0x7f060267
int color md_theme_dark_secondary 0x7f060268
int color md_theme_dark_secondaryContainer 0x7f060269
int color md_theme_dark_shadow 0x7f06026a
int color md_theme_dark_surface 0x7f06026b
int color md_theme_dark_surfaceTint 0x7f06026c
int color md_theme_dark_surfaceVariant 0x7f06026d
int color md_theme_dark_tertiary 0x7f06026e
int color md_theme_dark_tertiaryContainer 0x7f06026f
int color md_theme_light_background 0x7f060270
int color md_theme_light_error 0x7f060271
int color md_theme_light_errorContainer 0x7f060272
int color md_theme_light_inverseOnSurface 0x7f060273
int color md_theme_light_inversePrimary 0x7f060274
int color md_theme_light_inverseSurface 0x7f060275
int color md_theme_light_onBackground 0x7f060276
int color md_theme_light_onError 0x7f060277
int color md_theme_light_onErrorContainer 0x7f060278
int color md_theme_light_onPrimary 0x7f060279
int color md_theme_light_onPrimaryContainer 0x7f06027a
int color md_theme_light_onSecondary 0x7f06027b
int color md_theme_light_onSecondaryContainer 0x7f06027c
int color md_theme_light_onSurface 0x7f06027d
int color md_theme_light_onSurfaceVariant 0x7f06027e
int color md_theme_light_onTertiary 0x7f06027f
int color md_theme_light_onTertiaryContainer 0x7f060280
int color md_theme_light_outline 0x7f060281
int color md_theme_light_outlineVariant 0x7f060282
int color md_theme_light_primary 0x7f060283
int color md_theme_light_primaryContainer 0x7f060284
int color md_theme_light_scrim 0x7f060285
int color md_theme_light_secondary 0x7f060286
int color md_theme_light_secondaryContainer 0x7f060287
int color md_theme_light_shadow 0x7f060288
int color md_theme_light_surface 0x7f060289
int color md_theme_light_surfaceTint 0x7f06028a
int color md_theme_light_surfaceVariant 0x7f06028b
int color md_theme_light_tertiary 0x7f06028c
int color md_theme_light_tertiaryContainer 0x7f06028d
int color mtrl_btn_bg_color_selector 0x7f06028e
int color mtrl_btn_ripple_color 0x7f06028f
int color mtrl_btn_stroke_color_selector 0x7f060290
int color mtrl_btn_text_btn_bg_color_selector 0x7f060291
int color mtrl_btn_text_btn_ripple_color 0x7f060292
int color mtrl_btn_text_color_disabled 0x7f060293
int color mtrl_btn_text_color_selector 0x7f060294
int color mtrl_btn_transparent_bg_color 0x7f060295
int color mtrl_calendar_item_stroke_color 0x7f060296
int color mtrl_calendar_selected_range 0x7f060297
int color mtrl_card_view_foreground 0x7f060298
int color mtrl_card_view_ripple 0x7f060299
int color mtrl_chip_background_color 0x7f06029a
int color mtrl_chip_close_icon_tint 0x7f06029b
int color mtrl_chip_surface_color 0x7f06029c
int color mtrl_chip_text_color 0x7f06029d
int color mtrl_choice_chip_background_color 0x7f06029e
int color mtrl_choice_chip_ripple_color 0x7f06029f
int color mtrl_choice_chip_text_color 0x7f0602a0
int color mtrl_error 0x7f0602a1
int color mtrl_fab_bg_color_selector 0x7f0602a2
int color mtrl_fab_icon_text_color_selector 0x7f0602a3
int color mtrl_fab_ripple_color 0x7f0602a4
int color mtrl_filled_background_color 0x7f0602a5
int color mtrl_filled_icon_tint 0x7f0602a6
int color mtrl_filled_stroke_color 0x7f0602a7
int color mtrl_indicator_text_color 0x7f0602a8
int color mtrl_navigation_bar_colored_item_tint 0x7f0602a9
int color mtrl_navigation_bar_colored_ripple_color 0x7f0602aa
int color mtrl_navigation_bar_item_tint 0x7f0602ab
int color mtrl_navigation_bar_ripple_color 0x7f0602ac
int color mtrl_navigation_item_background_color 0x7f0602ad
int color mtrl_navigation_item_icon_tint 0x7f0602ae
int color mtrl_navigation_item_text_color 0x7f0602af
int color mtrl_on_primary_text_btn_text_color_selector 0x7f0602b0
int color mtrl_on_surface_ripple_color 0x7f0602b1
int color mtrl_outlined_icon_tint 0x7f0602b2
int color mtrl_outlined_stroke_color 0x7f0602b3
int color mtrl_popupmenu_overlay_color 0x7f0602b4
int color mtrl_scrim_color 0x7f0602b5
int color mtrl_switch_thumb_icon_tint 0x7f0602b6
int color mtrl_switch_thumb_tint 0x7f0602b7
int color mtrl_switch_track_decoration_tint 0x7f0602b8
int color mtrl_switch_track_tint 0x7f0602b9
int color mtrl_tabs_colored_ripple_color 0x7f0602ba
int color mtrl_tabs_icon_color_selector 0x7f0602bb
int color mtrl_tabs_icon_color_selector_colored 0x7f0602bc
int color mtrl_tabs_legacy_text_color_selector 0x7f0602bd
int color mtrl_tabs_ripple_color 0x7f0602be
int color mtrl_text_btn_text_color_selector 0x7f0602bf
int color mtrl_textinput_default_box_stroke_color 0x7f0602c0
int color mtrl_textinput_disabled_color 0x7f0602c1
int color mtrl_textinput_filled_box_default_background_color 0x7f0602c2
int color mtrl_textinput_focused_box_stroke_color 0x7f0602c3
int color mtrl_textinput_hovered_box_stroke_color 0x7f0602c4
int color notableSaleInstructionsBackgroundColor 0x7f0602c5
int color notification_action_color_filter 0x7f0602c6
int color notification_icon_bg_color 0x7f0602c7
int color notification_material_background_media_default_color 0x7f0602c8
int color pinFieldLibraryAccent 0x7f0602c9
int color pinFieldLibraryTransparent 0x7f0602ca
int color primary_dark_material_dark 0x7f0602cb
int color primary_dark_material_light 0x7f0602cc
int color primary_material_dark 0x7f0602cd
int color primary_material_light 0x7f0602ce
int color primary_text_default_material_dark 0x7f0602cf
int color primary_text_default_material_light 0x7f0602d0
int color primary_text_disabled_material_dark 0x7f0602d1
int color primary_text_disabled_material_light 0x7f0602d2
int color redgradend 0x7f0602d3
int color redgradstart 0x7f0602d4
int color ripple_material_dark 0x7f0602d5
int color ripple_material_light 0x7f0602d6
int color secondary_text_default_material_dark 0x7f0602d7
int color secondary_text_default_material_light 0x7f0602d8
int color secondary_text_disabled_material_dark 0x7f0602d9
int color secondary_text_disabled_material_light 0x7f0602da
int color seed 0x7f0602db
int color semiblue 0x7f0602dc
int color semigray 0x7f0602dd
int color semigreen 0x7f0602de
int color semired 0x7f0602df
int color switch_thumb_disabled_material_dark 0x7f0602e0
int color switch_thumb_disabled_material_light 0x7f0602e1
int color switch_thumb_material_dark 0x7f0602e2
int color switch_thumb_material_light 0x7f0602e3
int color switch_thumb_normal_material_dark 0x7f0602e4
int color switch_thumb_normal_material_light 0x7f0602e5
int color tab_selected 0x7f0602e6
int color tab_unselected 0x7f0602e7
int color tooltip_background_dark 0x7f0602e8
int color tooltip_background_light 0x7f0602e9
int color white 0x7f0602ea
int color white70 0x7f0602eb
int color white90 0x7f0602ec
int color yellow 0x7f0602ed
int color zxing_custom_possible_result_points 0x7f0602ee
int color zxing_custom_result_view 0x7f0602ef
int color zxing_custom_viewfinder_laser 0x7f0602f0
int color zxing_custom_viewfinder_mask 0x7f0602f1
int color zxing_possible_result_points 0x7f0602f2
int color zxing_result_view 0x7f0602f3
int color zxing_status_text 0x7f0602f4
int color zxing_transparent 0x7f0602f5
int color zxing_viewfinder_laser 0x7f0602f6
int color zxing_viewfinder_mask 0x7f0602f7
int dimen _100sdp 0x7f070000
int dimen _101sdp 0x7f070001
int dimen _102sdp 0x7f070002
int dimen _103sdp 0x7f070003
int dimen _104sdp 0x7f070004
int dimen _105sdp 0x7f070005
int dimen _106sdp 0x7f070006
int dimen _107sdp 0x7f070007
int dimen _108sdp 0x7f070008
int dimen _109sdp 0x7f070009
int dimen _10sdp 0x7f07000a
int dimen _110sdp 0x7f07000b
int dimen _111sdp 0x7f07000c
int dimen _112sdp 0x7f07000d
int dimen _113sdp 0x7f07000e
int dimen _114sdp 0x7f07000f
int dimen _115sdp 0x7f070010
int dimen _116sdp 0x7f070011
int dimen _117sdp 0x7f070012
int dimen _118sdp 0x7f070013
int dimen _119sdp 0x7f070014
int dimen _11sdp 0x7f070015
int dimen _120sdp 0x7f070016
int dimen _121sdp 0x7f070017
int dimen _122sdp 0x7f070018
int dimen _123sdp 0x7f070019
int dimen _124sdp 0x7f07001a
int dimen _125sdp 0x7f07001b
int dimen _126sdp 0x7f07001c
int dimen _127sdp 0x7f07001d
int dimen _128sdp 0x7f07001e
int dimen _129sdp 0x7f07001f
int dimen _12sdp 0x7f070020
int dimen _130sdp 0x7f070021
int dimen _131sdp 0x7f070022
int dimen _132sdp 0x7f070023
int dimen _133sdp 0x7f070024
int dimen _134sdp 0x7f070025
int dimen _135sdp 0x7f070026
int dimen _136sdp 0x7f070027
int dimen _137sdp 0x7f070028
int dimen _138sdp 0x7f070029
int dimen _139sdp 0x7f07002a
int dimen _13sdp 0x7f07002b
int dimen _140sdp 0x7f07002c
int dimen _141sdp 0x7f07002d
int dimen _142sdp 0x7f07002e
int dimen _143sdp 0x7f07002f
int dimen _144sdp 0x7f070030
int dimen _145sdp 0x7f070031
int dimen _146sdp 0x7f070032
int dimen _147sdp 0x7f070033
int dimen _148sdp 0x7f070034
int dimen _149sdp 0x7f070035
int dimen _14sdp 0x7f070036
int dimen _150sdp 0x7f070037
int dimen _151sdp 0x7f070038
int dimen _152sdp 0x7f070039
int dimen _153sdp 0x7f07003a
int dimen _154sdp 0x7f07003b
int dimen _155sdp 0x7f07003c
int dimen _156sdp 0x7f07003d
int dimen _157sdp 0x7f07003e
int dimen _158sdp 0x7f07003f
int dimen _159sdp 0x7f070040
int dimen _15sdp 0x7f070041
int dimen _160sdp 0x7f070042
int dimen _161sdp 0x7f070043
int dimen _162sdp 0x7f070044
int dimen _163sdp 0x7f070045
int dimen _164sdp 0x7f070046
int dimen _165sdp 0x7f070047
int dimen _166sdp 0x7f070048
int dimen _167sdp 0x7f070049
int dimen _168sdp 0x7f07004a
int dimen _169sdp 0x7f07004b
int dimen _16sdp 0x7f07004c
int dimen _170sdp 0x7f07004d
int dimen _171sdp 0x7f07004e
int dimen _172sdp 0x7f07004f
int dimen _173sdp 0x7f070050
int dimen _174sdp 0x7f070051
int dimen _175sdp 0x7f070052
int dimen _176sdp 0x7f070053
int dimen _177sdp 0x7f070054
int dimen _178sdp 0x7f070055
int dimen _179sdp 0x7f070056
int dimen _17sdp 0x7f070057
int dimen _180sdp 0x7f070058
int dimen _181sdp 0x7f070059
int dimen _182sdp 0x7f07005a
int dimen _183sdp 0x7f07005b
int dimen _184sdp 0x7f07005c
int dimen _185sdp 0x7f07005d
int dimen _186sdp 0x7f07005e
int dimen _187sdp 0x7f07005f
int dimen _188sdp 0x7f070060
int dimen _189sdp 0x7f070061
int dimen _18sdp 0x7f070062
int dimen _190sdp 0x7f070063
int dimen _191sdp 0x7f070064
int dimen _192sdp 0x7f070065
int dimen _193sdp 0x7f070066
int dimen _194sdp 0x7f070067
int dimen _195sdp 0x7f070068
int dimen _196sdp 0x7f070069
int dimen _197sdp 0x7f07006a
int dimen _198sdp 0x7f07006b
int dimen _199sdp 0x7f07006c
int dimen _19sdp 0x7f07006d
int dimen _1sdp 0x7f07006e
int dimen _200sdp 0x7f07006f
int dimen _201sdp 0x7f070070
int dimen _202sdp 0x7f070071
int dimen _203sdp 0x7f070072
int dimen _204sdp 0x7f070073
int dimen _205sdp 0x7f070074
int dimen _206sdp 0x7f070075
int dimen _207sdp 0x7f070076
int dimen _208sdp 0x7f070077
int dimen _209sdp 0x7f070078
int dimen _20sdp 0x7f070079
int dimen _210sdp 0x7f07007a
int dimen _211sdp 0x7f07007b
int dimen _212sdp 0x7f07007c
int dimen _213sdp 0x7f07007d
int dimen _214sdp 0x7f07007e
int dimen _215sdp 0x7f07007f
int dimen _216sdp 0x7f070080
int dimen _217sdp 0x7f070081
int dimen _218sdp 0x7f070082
int dimen _219sdp 0x7f070083
int dimen _21sdp 0x7f070084
int dimen _220sdp 0x7f070085
int dimen _221sdp 0x7f070086
int dimen _222sdp 0x7f070087
int dimen _223sdp 0x7f070088
int dimen _224sdp 0x7f070089
int dimen _225sdp 0x7f07008a
int dimen _226sdp 0x7f07008b
int dimen _227sdp 0x7f07008c
int dimen _228sdp 0x7f07008d
int dimen _229sdp 0x7f07008e
int dimen _22sdp 0x7f07008f
int dimen _230sdp 0x7f070090
int dimen _231sdp 0x7f070091
int dimen _232sdp 0x7f070092
int dimen _233sdp 0x7f070093
int dimen _234sdp 0x7f070094
int dimen _235sdp 0x7f070095
int dimen _236sdp 0x7f070096
int dimen _237sdp 0x7f070097
int dimen _238sdp 0x7f070098
int dimen _239sdp 0x7f070099
int dimen _23sdp 0x7f07009a
int dimen _240sdp 0x7f07009b
int dimen _241sdp 0x7f07009c
int dimen _242sdp 0x7f07009d
int dimen _243sdp 0x7f07009e
int dimen _244sdp 0x7f07009f
int dimen _245sdp 0x7f0700a0
int dimen _246sdp 0x7f0700a1
int dimen _247sdp 0x7f0700a2
int dimen _248sdp 0x7f0700a3
int dimen _249sdp 0x7f0700a4
int dimen _24sdp 0x7f0700a5
int dimen _250sdp 0x7f0700a6
int dimen _251sdp 0x7f0700a7
int dimen _252sdp 0x7f0700a8
int dimen _253sdp 0x7f0700a9
int dimen _254sdp 0x7f0700aa
int dimen _255sdp 0x7f0700ab
int dimen _256sdp 0x7f0700ac
int dimen _257sdp 0x7f0700ad
int dimen _258sdp 0x7f0700ae
int dimen _259sdp 0x7f0700af
int dimen _25sdp 0x7f0700b0
int dimen _260sdp 0x7f0700b1
int dimen _261sdp 0x7f0700b2
int dimen _262sdp 0x7f0700b3
int dimen _263sdp 0x7f0700b4
int dimen _264sdp 0x7f0700b5
int dimen _265sdp 0x7f0700b6
int dimen _266sdp 0x7f0700b7
int dimen _267sdp 0x7f0700b8
int dimen _268sdp 0x7f0700b9
int dimen _269sdp 0x7f0700ba
int dimen _26sdp 0x7f0700bb
int dimen _270sdp 0x7f0700bc
int dimen _271sdp 0x7f0700bd
int dimen _272sdp 0x7f0700be
int dimen _273sdp 0x7f0700bf
int dimen _274sdp 0x7f0700c0
int dimen _275sdp 0x7f0700c1
int dimen _276sdp 0x7f0700c2
int dimen _277sdp 0x7f0700c3
int dimen _278sdp 0x7f0700c4
int dimen _279sdp 0x7f0700c5
int dimen _27sdp 0x7f0700c6
int dimen _280sdp 0x7f0700c7
int dimen _281sdp 0x7f0700c8
int dimen _282sdp 0x7f0700c9
int dimen _283sdp 0x7f0700ca
int dimen _284sdp 0x7f0700cb
int dimen _285sdp 0x7f0700cc
int dimen _286sdp 0x7f0700cd
int dimen _287sdp 0x7f0700ce
int dimen _288sdp 0x7f0700cf
int dimen _289sdp 0x7f0700d0
int dimen _28sdp 0x7f0700d1
int dimen _290sdp 0x7f0700d2
int dimen _291sdp 0x7f0700d3
int dimen _292sdp 0x7f0700d4
int dimen _293sdp 0x7f0700d5
int dimen _294sdp 0x7f0700d6
int dimen _295sdp 0x7f0700d7
int dimen _296sdp 0x7f0700d8
int dimen _297sdp 0x7f0700d9
int dimen _298sdp 0x7f0700da
int dimen _299sdp 0x7f0700db
int dimen _29sdp 0x7f0700dc
int dimen _2sdp 0x7f0700dd
int dimen _300sdp 0x7f0700de
int dimen _301sdp 0x7f0700df
int dimen _302sdp 0x7f0700e0
int dimen _303sdp 0x7f0700e1
int dimen _304sdp 0x7f0700e2
int dimen _305sdp 0x7f0700e3
int dimen _306sdp 0x7f0700e4
int dimen _307sdp 0x7f0700e5
int dimen _308sdp 0x7f0700e6
int dimen _309sdp 0x7f0700e7
int dimen _30sdp 0x7f0700e8
int dimen _310sdp 0x7f0700e9
int dimen _311sdp 0x7f0700ea
int dimen _312sdp 0x7f0700eb
int dimen _313sdp 0x7f0700ec
int dimen _314sdp 0x7f0700ed
int dimen _315sdp 0x7f0700ee
int dimen _316sdp 0x7f0700ef
int dimen _317sdp 0x7f0700f0
int dimen _318sdp 0x7f0700f1
int dimen _319sdp 0x7f0700f2
int dimen _31sdp 0x7f0700f3
int dimen _320sdp 0x7f0700f4
int dimen _321sdp 0x7f0700f5
int dimen _322sdp 0x7f0700f6
int dimen _323sdp 0x7f0700f7
int dimen _324sdp 0x7f0700f8
int dimen _325sdp 0x7f0700f9
int dimen _326sdp 0x7f0700fa
int dimen _327sdp 0x7f0700fb
int dimen _328sdp 0x7f0700fc
int dimen _329sdp 0x7f0700fd
int dimen _32sdp 0x7f0700fe
int dimen _330sdp 0x7f0700ff
int dimen _331sdp 0x7f070100
int dimen _332sdp 0x7f070101
int dimen _333sdp 0x7f070102
int dimen _334sdp 0x7f070103
int dimen _335sdp 0x7f070104
int dimen _336sdp 0x7f070105
int dimen _337sdp 0x7f070106
int dimen _338sdp 0x7f070107
int dimen _339sdp 0x7f070108
int dimen _33sdp 0x7f070109
int dimen _340sdp 0x7f07010a
int dimen _341sdp 0x7f07010b
int dimen _342sdp 0x7f07010c
int dimen _343sdp 0x7f07010d
int dimen _344sdp 0x7f07010e
int dimen _345sdp 0x7f07010f
int dimen _346sdp 0x7f070110
int dimen _347sdp 0x7f070111
int dimen _348sdp 0x7f070112
int dimen _349sdp 0x7f070113
int dimen _34sdp 0x7f070114
int dimen _350sdp 0x7f070115
int dimen _351sdp 0x7f070116
int dimen _352sdp 0x7f070117
int dimen _353sdp 0x7f070118
int dimen _354sdp 0x7f070119
int dimen _355sdp 0x7f07011a
int dimen _356sdp 0x7f07011b
int dimen _357sdp 0x7f07011c
int dimen _358sdp 0x7f07011d
int dimen _359sdp 0x7f07011e
int dimen _35sdp 0x7f07011f
int dimen _360sdp 0x7f070120
int dimen _361sdp 0x7f070121
int dimen _362sdp 0x7f070122
int dimen _363sdp 0x7f070123
int dimen _364sdp 0x7f070124
int dimen _365sdp 0x7f070125
int dimen _366sdp 0x7f070126
int dimen _367sdp 0x7f070127
int dimen _368sdp 0x7f070128
int dimen _369sdp 0x7f070129
int dimen _36sdp 0x7f07012a
int dimen _370sdp 0x7f07012b
int dimen _371sdp 0x7f07012c
int dimen _372sdp 0x7f07012d
int dimen _373sdp 0x7f07012e
int dimen _374sdp 0x7f07012f
int dimen _375sdp 0x7f070130
int dimen _376sdp 0x7f070131
int dimen _377sdp 0x7f070132
int dimen _378sdp 0x7f070133
int dimen _379sdp 0x7f070134
int dimen _37sdp 0x7f070135
int dimen _380sdp 0x7f070136
int dimen _381sdp 0x7f070137
int dimen _382sdp 0x7f070138
int dimen _383sdp 0x7f070139
int dimen _384sdp 0x7f07013a
int dimen _385sdp 0x7f07013b
int dimen _386sdp 0x7f07013c
int dimen _387sdp 0x7f07013d
int dimen _388sdp 0x7f07013e
int dimen _389sdp 0x7f07013f
int dimen _38sdp 0x7f070140
int dimen _390sdp 0x7f070141
int dimen _391sdp 0x7f070142
int dimen _392sdp 0x7f070143
int dimen _393sdp 0x7f070144
int dimen _394sdp 0x7f070145
int dimen _395sdp 0x7f070146
int dimen _396sdp 0x7f070147
int dimen _397sdp 0x7f070148
int dimen _398sdp 0x7f070149
int dimen _399sdp 0x7f07014a
int dimen _39sdp 0x7f07014b
int dimen _3sdp 0x7f07014c
int dimen _400sdp 0x7f07014d
int dimen _401sdp 0x7f07014e
int dimen _402sdp 0x7f07014f
int dimen _403sdp 0x7f070150
int dimen _404sdp 0x7f070151
int dimen _405sdp 0x7f070152
int dimen _406sdp 0x7f070153
int dimen _407sdp 0x7f070154
int dimen _408sdp 0x7f070155
int dimen _409sdp 0x7f070156
int dimen _40sdp 0x7f070157
int dimen _410sdp 0x7f070158
int dimen _411sdp 0x7f070159
int dimen _412sdp 0x7f07015a
int dimen _413sdp 0x7f07015b
int dimen _414sdp 0x7f07015c
int dimen _415sdp 0x7f07015d
int dimen _416sdp 0x7f07015e
int dimen _417sdp 0x7f07015f
int dimen _418sdp 0x7f070160
int dimen _419sdp 0x7f070161
int dimen _41sdp 0x7f070162
int dimen _420sdp 0x7f070163
int dimen _421sdp 0x7f070164
int dimen _422sdp 0x7f070165
int dimen _423sdp 0x7f070166
int dimen _424sdp 0x7f070167
int dimen _425sdp 0x7f070168
int dimen _426sdp 0x7f070169
int dimen _427sdp 0x7f07016a
int dimen _428sdp 0x7f07016b
int dimen _429sdp 0x7f07016c
int dimen _42sdp 0x7f07016d
int dimen _430sdp 0x7f07016e
int dimen _431sdp 0x7f07016f
int dimen _432sdp 0x7f070170
int dimen _433sdp 0x7f070171
int dimen _434sdp 0x7f070172
int dimen _435sdp 0x7f070173
int dimen _436sdp 0x7f070174
int dimen _437sdp 0x7f070175
int dimen _438sdp 0x7f070176
int dimen _439sdp 0x7f070177
int dimen _43sdp 0x7f070178
int dimen _440sdp 0x7f070179
int dimen _441sdp 0x7f07017a
int dimen _442sdp 0x7f07017b
int dimen _443sdp 0x7f07017c
int dimen _444sdp 0x7f07017d
int dimen _445sdp 0x7f07017e
int dimen _446sdp 0x7f07017f
int dimen _447sdp 0x7f070180
int dimen _448sdp 0x7f070181
int dimen _449sdp 0x7f070182
int dimen _44sdp 0x7f070183
int dimen _450sdp 0x7f070184
int dimen _451sdp 0x7f070185
int dimen _452sdp 0x7f070186
int dimen _453sdp 0x7f070187
int dimen _454sdp 0x7f070188
int dimen _455sdp 0x7f070189
int dimen _456sdp 0x7f07018a
int dimen _457sdp 0x7f07018b
int dimen _458sdp 0x7f07018c
int dimen _459sdp 0x7f07018d
int dimen _45sdp 0x7f07018e
int dimen _460sdp 0x7f07018f
int dimen _461sdp 0x7f070190
int dimen _462sdp 0x7f070191
int dimen _463sdp 0x7f070192
int dimen _464sdp 0x7f070193
int dimen _465sdp 0x7f070194
int dimen _466sdp 0x7f070195
int dimen _467sdp 0x7f070196
int dimen _468sdp 0x7f070197
int dimen _469sdp 0x7f070198
int dimen _46sdp 0x7f070199
int dimen _470sdp 0x7f07019a
int dimen _471sdp 0x7f07019b
int dimen _472sdp 0x7f07019c
int dimen _473sdp 0x7f07019d
int dimen _474sdp 0x7f07019e
int dimen _475sdp 0x7f07019f
int dimen _476sdp 0x7f0701a0
int dimen _477sdp 0x7f0701a1
int dimen _478sdp 0x7f0701a2
int dimen _479sdp 0x7f0701a3
int dimen _47sdp 0x7f0701a4
int dimen _480sdp 0x7f0701a5
int dimen _481sdp 0x7f0701a6
int dimen _482sdp 0x7f0701a7
int dimen _483sdp 0x7f0701a8
int dimen _484sdp 0x7f0701a9
int dimen _485sdp 0x7f0701aa
int dimen _486sdp 0x7f0701ab
int dimen _487sdp 0x7f0701ac
int dimen _488sdp 0x7f0701ad
int dimen _489sdp 0x7f0701ae
int dimen _48sdp 0x7f0701af
int dimen _490sdp 0x7f0701b0
int dimen _491sdp 0x7f0701b1
int dimen _492sdp 0x7f0701b2
int dimen _493sdp 0x7f0701b3
int dimen _494sdp 0x7f0701b4
int dimen _495sdp 0x7f0701b5
int dimen _496sdp 0x7f0701b6
int dimen _497sdp 0x7f0701b7
int dimen _498sdp 0x7f0701b8
int dimen _499sdp 0x7f0701b9
int dimen _49sdp 0x7f0701ba
int dimen _4sdp 0x7f0701bb
int dimen _500sdp 0x7f0701bc
int dimen _501sdp 0x7f0701bd
int dimen _502sdp 0x7f0701be
int dimen _503sdp 0x7f0701bf
int dimen _504sdp 0x7f0701c0
int dimen _505sdp 0x7f0701c1
int dimen _506sdp 0x7f0701c2
int dimen _507sdp 0x7f0701c3
int dimen _508sdp 0x7f0701c4
int dimen _509sdp 0x7f0701c5
int dimen _50sdp 0x7f0701c6
int dimen _510sdp 0x7f0701c7
int dimen _511sdp 0x7f0701c8
int dimen _512sdp 0x7f0701c9
int dimen _513sdp 0x7f0701ca
int dimen _514sdp 0x7f0701cb
int dimen _515sdp 0x7f0701cc
int dimen _516sdp 0x7f0701cd
int dimen _517sdp 0x7f0701ce
int dimen _518sdp 0x7f0701cf
int dimen _519sdp 0x7f0701d0
int dimen _51sdp 0x7f0701d1
int dimen _520sdp 0x7f0701d2
int dimen _521sdp 0x7f0701d3
int dimen _522sdp 0x7f0701d4
int dimen _523sdp 0x7f0701d5
int dimen _524sdp 0x7f0701d6
int dimen _525sdp 0x7f0701d7
int dimen _526sdp 0x7f0701d8
int dimen _527sdp 0x7f0701d9
int dimen _528sdp 0x7f0701da
int dimen _529sdp 0x7f0701db
int dimen _52sdp 0x7f0701dc
int dimen _530sdp 0x7f0701dd
int dimen _531sdp 0x7f0701de
int dimen _532sdp 0x7f0701df
int dimen _533sdp 0x7f0701e0
int dimen _534sdp 0x7f0701e1
int dimen _535sdp 0x7f0701e2
int dimen _536sdp 0x7f0701e3
int dimen _537sdp 0x7f0701e4
int dimen _538sdp 0x7f0701e5
int dimen _539sdp 0x7f0701e6
int dimen _53sdp 0x7f0701e7
int dimen _540sdp 0x7f0701e8
int dimen _541sdp 0x7f0701e9
int dimen _542sdp 0x7f0701ea
int dimen _543sdp 0x7f0701eb
int dimen _544sdp 0x7f0701ec
int dimen _545sdp 0x7f0701ed
int dimen _546sdp 0x7f0701ee
int dimen _547sdp 0x7f0701ef
int dimen _548sdp 0x7f0701f0
int dimen _549sdp 0x7f0701f1
int dimen _54sdp 0x7f0701f2
int dimen _550sdp 0x7f0701f3
int dimen _551sdp 0x7f0701f4
int dimen _552sdp 0x7f0701f5
int dimen _553sdp 0x7f0701f6
int dimen _554sdp 0x7f0701f7
int dimen _555sdp 0x7f0701f8
int dimen _556sdp 0x7f0701f9
int dimen _557sdp 0x7f0701fa
int dimen _558sdp 0x7f0701fb
int dimen _559sdp 0x7f0701fc
int dimen _55sdp 0x7f0701fd
int dimen _560sdp 0x7f0701fe
int dimen _561sdp 0x7f0701ff
int dimen _562sdp 0x7f070200
int dimen _563sdp 0x7f070201
int dimen _564sdp 0x7f070202
int dimen _565sdp 0x7f070203
int dimen _566sdp 0x7f070204
int dimen _567sdp 0x7f070205
int dimen _568sdp 0x7f070206
int dimen _569sdp 0x7f070207
int dimen _56sdp 0x7f070208
int dimen _570sdp 0x7f070209
int dimen _571sdp 0x7f07020a
int dimen _572sdp 0x7f07020b
int dimen _573sdp 0x7f07020c
int dimen _574sdp 0x7f07020d
int dimen _575sdp 0x7f07020e
int dimen _576sdp 0x7f07020f
int dimen _577sdp 0x7f070210
int dimen _578sdp 0x7f070211
int dimen _579sdp 0x7f070212
int dimen _57sdp 0x7f070213
int dimen _580sdp 0x7f070214
int dimen _581sdp 0x7f070215
int dimen _582sdp 0x7f070216
int dimen _583sdp 0x7f070217
int dimen _584sdp 0x7f070218
int dimen _585sdp 0x7f070219
int dimen _586sdp 0x7f07021a
int dimen _587sdp 0x7f07021b
int dimen _588sdp 0x7f07021c
int dimen _589sdp 0x7f07021d
int dimen _58sdp 0x7f07021e
int dimen _590sdp 0x7f07021f
int dimen _591sdp 0x7f070220
int dimen _592sdp 0x7f070221
int dimen _593sdp 0x7f070222
int dimen _594sdp 0x7f070223
int dimen _595sdp 0x7f070224
int dimen _596sdp 0x7f070225
int dimen _597sdp 0x7f070226
int dimen _598sdp 0x7f070227
int dimen _599sdp 0x7f070228
int dimen _59sdp 0x7f070229
int dimen _5sdp 0x7f07022a
int dimen _600sdp 0x7f07022b
int dimen _60sdp 0x7f07022c
int dimen _61sdp 0x7f07022d
int dimen _62sdp 0x7f07022e
int dimen _63sdp 0x7f07022f
int dimen _64sdp 0x7f070230
int dimen _65sdp 0x7f070231
int dimen _66sdp 0x7f070232
int dimen _67sdp 0x7f070233
int dimen _68sdp 0x7f070234
int dimen _69sdp 0x7f070235
int dimen _6sdp 0x7f070236
int dimen _70sdp 0x7f070237
int dimen _71sdp 0x7f070238
int dimen _72sdp 0x7f070239
int dimen _73sdp 0x7f07023a
int dimen _74sdp 0x7f07023b
int dimen _75sdp 0x7f07023c
int dimen _76sdp 0x7f07023d
int dimen _77sdp 0x7f07023e
int dimen _78sdp 0x7f07023f
int dimen _79sdp 0x7f070240
int dimen _7sdp 0x7f070241
int dimen _80sdp 0x7f070242
int dimen _81sdp 0x7f070243
int dimen _82sdp 0x7f070244
int dimen _83sdp 0x7f070245
int dimen _84sdp 0x7f070246
int dimen _85sdp 0x7f070247
int dimen _86sdp 0x7f070248
int dimen _87sdp 0x7f070249
int dimen _88sdp 0x7f07024a
int dimen _89sdp 0x7f07024b
int dimen _8sdp 0x7f07024c
int dimen _90sdp 0x7f07024d
int dimen _91sdp 0x7f07024e
int dimen _92sdp 0x7f07024f
int dimen _93sdp 0x7f070250
int dimen _94sdp 0x7f070251
int dimen _95sdp 0x7f070252
int dimen _96sdp 0x7f070253
int dimen _97sdp 0x7f070254
int dimen _98sdp 0x7f070255
int dimen _99sdp 0x7f070256
int dimen _9sdp 0x7f070257
int dimen _minus10sdp 0x7f070258
int dimen _minus11sdp 0x7f070259
int dimen _minus12sdp 0x7f07025a
int dimen _minus13sdp 0x7f07025b
int dimen _minus14sdp 0x7f07025c
int dimen _minus15sdp 0x7f07025d
int dimen _minus16sdp 0x7f07025e
int dimen _minus17sdp 0x7f07025f
int dimen _minus18sdp 0x7f070260
int dimen _minus19sdp 0x7f070261
int dimen _minus1sdp 0x7f070262
int dimen _minus20sdp 0x7f070263
int dimen _minus21sdp 0x7f070264
int dimen _minus22sdp 0x7f070265
int dimen _minus23sdp 0x7f070266
int dimen _minus24sdp 0x7f070267
int dimen _minus25sdp 0x7f070268
int dimen _minus26sdp 0x7f070269
int dimen _minus27sdp 0x7f07026a
int dimen _minus28sdp 0x7f07026b
int dimen _minus29sdp 0x7f07026c
int dimen _minus2sdp 0x7f07026d
int dimen _minus30sdp 0x7f07026e
int dimen _minus31sdp 0x7f07026f
int dimen _minus32sdp 0x7f070270
int dimen _minus33sdp 0x7f070271
int dimen _minus34sdp 0x7f070272
int dimen _minus35sdp 0x7f070273
int dimen _minus36sdp 0x7f070274
int dimen _minus37sdp 0x7f070275
int dimen _minus38sdp 0x7f070276
int dimen _minus39sdp 0x7f070277
int dimen _minus3sdp 0x7f070278
int dimen _minus40sdp 0x7f070279
int dimen _minus41sdp 0x7f07027a
int dimen _minus42sdp 0x7f07027b
int dimen _minus43sdp 0x7f07027c
int dimen _minus44sdp 0x7f07027d
int dimen _minus45sdp 0x7f07027e
int dimen _minus46sdp 0x7f07027f
int dimen _minus47sdp 0x7f070280
int dimen _minus48sdp 0x7f070281
int dimen _minus49sdp 0x7f070282
int dimen _minus4sdp 0x7f070283
int dimen _minus50sdp 0x7f070284
int dimen _minus51sdp 0x7f070285
int dimen _minus52sdp 0x7f070286
int dimen _minus53sdp 0x7f070287
int dimen _minus54sdp 0x7f070288
int dimen _minus55sdp 0x7f070289
int dimen _minus56sdp 0x7f07028a
int dimen _minus57sdp 0x7f07028b
int dimen _minus58sdp 0x7f07028c
int dimen _minus59sdp 0x7f07028d
int dimen _minus5sdp 0x7f07028e
int dimen _minus60sdp 0x7f07028f
int dimen _minus6sdp 0x7f070290
int dimen _minus7sdp 0x7f070291
int dimen _minus8sdp 0x7f070292
int dimen _minus9sdp 0x7f070293
int dimen abc_action_bar_content_inset_material 0x7f070294
int dimen abc_action_bar_content_inset_with_nav 0x7f070295
int dimen abc_action_bar_default_height_material 0x7f070296
int dimen abc_action_bar_default_padding_end_material 0x7f070297
int dimen abc_action_bar_default_padding_start_material 0x7f070298
int dimen abc_action_bar_elevation_material 0x7f070299
int dimen abc_action_bar_icon_vertical_padding_material 0x7f07029a
int dimen abc_action_bar_overflow_padding_end_material 0x7f07029b
int dimen abc_action_bar_overflow_padding_start_material 0x7f07029c
int dimen abc_action_bar_stacked_max_height 0x7f07029d
int dimen abc_action_bar_stacked_tab_max_width 0x7f07029e
int dimen abc_action_bar_subtitle_bottom_margin_material 0x7f07029f
int dimen abc_action_bar_subtitle_top_margin_material 0x7f0702a0
int dimen abc_action_button_min_height_material 0x7f0702a1
int dimen abc_action_button_min_width_material 0x7f0702a2
int dimen abc_action_button_min_width_overflow_material 0x7f0702a3
int dimen abc_alert_dialog_button_bar_height 0x7f0702a4
int dimen abc_alert_dialog_button_dimen 0x7f0702a5
int dimen abc_button_inset_horizontal_material 0x7f0702a6
int dimen abc_button_inset_vertical_material 0x7f0702a7
int dimen abc_button_padding_horizontal_material 0x7f0702a8
int dimen abc_button_padding_vertical_material 0x7f0702a9
int dimen abc_cascading_menus_min_smallest_width 0x7f0702aa
int dimen abc_config_prefDialogWidth 0x7f0702ab
int dimen abc_control_corner_material 0x7f0702ac
int dimen abc_control_inset_material 0x7f0702ad
int dimen abc_control_padding_material 0x7f0702ae
int dimen abc_dialog_corner_radius_material 0x7f0702af
int dimen abc_dialog_fixed_height_major 0x7f0702b0
int dimen abc_dialog_fixed_height_minor 0x7f0702b1
int dimen abc_dialog_fixed_width_major 0x7f0702b2
int dimen abc_dialog_fixed_width_minor 0x7f0702b3
int dimen abc_dialog_list_padding_bottom_no_buttons 0x7f0702b4
int dimen abc_dialog_list_padding_top_no_title 0x7f0702b5
int dimen abc_dialog_min_width_major 0x7f0702b6
int dimen abc_dialog_min_width_minor 0x7f0702b7
int dimen abc_dialog_padding_material 0x7f0702b8
int dimen abc_dialog_padding_top_material 0x7f0702b9
int dimen abc_dialog_title_divider_material 0x7f0702ba
int dimen abc_disabled_alpha_material_dark 0x7f0702bb
int dimen abc_disabled_alpha_material_light 0x7f0702bc
int dimen abc_dropdownitem_icon_width 0x7f0702bd
int dimen abc_dropdownitem_text_padding_left 0x7f0702be
int dimen abc_dropdownitem_text_padding_right 0x7f0702bf
int dimen abc_edit_text_inset_bottom_material 0x7f0702c0
int dimen abc_edit_text_inset_horizontal_material 0x7f0702c1
int dimen abc_edit_text_inset_top_material 0x7f0702c2
int dimen abc_floating_window_z 0x7f0702c3
int dimen abc_list_item_height_large_material 0x7f0702c4
int dimen abc_list_item_height_material 0x7f0702c5
int dimen abc_list_item_height_small_material 0x7f0702c6
int dimen abc_list_item_padding_horizontal_material 0x7f0702c7
int dimen abc_panel_menu_list_width 0x7f0702c8
int dimen abc_progress_bar_height_material 0x7f0702c9
int dimen abc_search_view_preferred_height 0x7f0702ca
int dimen abc_search_view_preferred_width 0x7f0702cb
int dimen abc_seekbar_track_background_height_material 0x7f0702cc
int dimen abc_seekbar_track_progress_height_material 0x7f0702cd
int dimen abc_select_dialog_padding_start_material 0x7f0702ce
int dimen abc_star_big 0x7f0702cf
int dimen abc_star_medium 0x7f0702d0
int dimen abc_star_small 0x7f0702d1
int dimen abc_switch_padding 0x7f0702d2
int dimen abc_text_size_body_1_material 0x7f0702d3
int dimen abc_text_size_body_2_material 0x7f0702d4
int dimen abc_text_size_button_material 0x7f0702d5
int dimen abc_text_size_caption_material 0x7f0702d6
int dimen abc_text_size_display_1_material 0x7f0702d7
int dimen abc_text_size_display_2_material 0x7f0702d8
int dimen abc_text_size_display_3_material 0x7f0702d9
int dimen abc_text_size_display_4_material 0x7f0702da
int dimen abc_text_size_headline_material 0x7f0702db
int dimen abc_text_size_large_material 0x7f0702dc
int dimen abc_text_size_medium_material 0x7f0702dd
int dimen abc_text_size_menu_header_material 0x7f0702de
int dimen abc_text_size_menu_material 0x7f0702df
int dimen abc_text_size_small_material 0x7f0702e0
int dimen abc_text_size_subhead_material 0x7f0702e1
int dimen abc_text_size_subtitle_material_toolbar 0x7f0702e2
int dimen abc_text_size_title_material 0x7f0702e3
int dimen abc_text_size_title_material_toolbar 0x7f0702e4
int dimen appcompat_dialog_background_inset 0x7f0702e5
int dimen browser_actions_context_menu_max_width 0x7f0702e6
int dimen browser_actions_context_menu_min_padding 0x7f0702e7
int dimen cardview_compat_inset_shadow 0x7f0702e8
int dimen cardview_default_elevation 0x7f0702e9
int dimen cardview_default_radius 0x7f0702ea
int dimen clock_face_margin_start 0x7f0702eb
int dimen compat_button_inset_horizontal_material 0x7f0702ec
int dimen compat_button_inset_vertical_material 0x7f0702ed
int dimen compat_button_padding_horizontal_material 0x7f0702ee
int dimen compat_button_padding_vertical_material 0x7f0702ef
int dimen compat_control_corner_material 0x7f0702f0
int dimen compat_notification_large_icon_max_height 0x7f0702f1
int dimen compat_notification_large_icon_max_width 0x7f0702f2
int dimen def_drawer_elevation 0x7f0702f3
int dimen design_appbar_elevation 0x7f0702f4
int dimen design_bottom_navigation_active_item_max_width 0x7f0702f5
int dimen design_bottom_navigation_active_item_min_width 0x7f0702f6
int dimen design_bottom_navigation_active_text_size 0x7f0702f7
int dimen design_bottom_navigation_elevation 0x7f0702f8
int dimen design_bottom_navigation_height 0x7f0702f9
int dimen design_bottom_navigation_icon_size 0x7f0702fa
int dimen design_bottom_navigation_item_max_width 0x7f0702fb
int dimen design_bottom_navigation_item_min_width 0x7f0702fc
int dimen design_bottom_navigation_label_padding 0x7f0702fd
int dimen design_bottom_navigation_margin 0x7f0702fe
int dimen design_bottom_navigation_shadow_height 0x7f0702ff
int dimen design_bottom_navigation_text_size 0x7f070300
int dimen design_bottom_sheet_elevation 0x7f070301
int dimen design_bottom_sheet_modal_elevation 0x7f070302
int dimen design_bottom_sheet_peek_height_min 0x7f070303
int dimen design_fab_border_width 0x7f070304
int dimen design_fab_elevation 0x7f070305
int dimen design_fab_image_size 0x7f070306
int dimen design_fab_size_mini 0x7f070307
int dimen design_fab_size_normal 0x7f070308
int dimen design_fab_translation_z_hovered_focused 0x7f070309
int dimen design_fab_translation_z_pressed 0x7f07030a
int dimen design_navigation_elevation 0x7f07030b
int dimen design_navigation_icon_padding 0x7f07030c
int dimen design_navigation_icon_size 0x7f07030d
int dimen design_navigation_item_horizontal_padding 0x7f07030e
int dimen design_navigation_item_icon_padding 0x7f07030f
int dimen design_navigation_item_vertical_padding 0x7f070310
int dimen design_navigation_max_width 0x7f070311
int dimen design_navigation_padding_bottom 0x7f070312
int dimen design_navigation_separator_vertical_padding 0x7f070313
int dimen design_snackbar_action_inline_max_width 0x7f070314
int dimen design_snackbar_action_text_color_alpha 0x7f070315
int dimen design_snackbar_background_corner_radius 0x7f070316
int dimen design_snackbar_elevation 0x7f070317
int dimen design_snackbar_extra_spacing_horizontal 0x7f070318
int dimen design_snackbar_max_width 0x7f070319
int dimen design_snackbar_min_width 0x7f07031a
int dimen design_snackbar_padding_horizontal 0x7f07031b
int dimen design_snackbar_padding_vertical 0x7f07031c
int dimen design_snackbar_padding_vertical_2lines 0x7f07031d
int dimen design_snackbar_text_size 0x7f07031e
int dimen design_tab_max_width 0x7f07031f
int dimen design_tab_scrollable_min_width 0x7f070320
int dimen design_tab_text_size 0x7f070321
int dimen design_tab_text_size_2line 0x7f070322
int dimen design_textinput_caption_translate_y 0x7f070323
int dimen disabled_alpha_material_dark 0x7f070324
int dimen disabled_alpha_material_light 0x7f070325
int dimen exo_error_message_height 0x7f070326
int dimen exo_error_message_margin_bottom 0x7f070327
int dimen exo_error_message_text_padding_horizontal 0x7f070328
int dimen exo_error_message_text_padding_vertical 0x7f070329
int dimen exo_error_message_text_size 0x7f07032a
int dimen exo_icon_horizontal_margin 0x7f07032b
int dimen exo_icon_padding 0x7f07032c
int dimen exo_icon_padding_bottom 0x7f07032d
int dimen exo_icon_size 0x7f07032e
int dimen exo_icon_text_size 0x7f07032f
int dimen exo_media_button_height 0x7f070330
int dimen exo_media_button_width 0x7f070331
int dimen exo_setting_width 0x7f070332
int dimen exo_settings_height 0x7f070333
int dimen exo_settings_icon_size 0x7f070334
int dimen exo_settings_main_text_size 0x7f070335
int dimen exo_settings_offset 0x7f070336
int dimen exo_settings_sub_text_size 0x7f070337
int dimen exo_settings_text_height 0x7f070338
int dimen exo_small_icon_height 0x7f070339
int dimen exo_small_icon_horizontal_margin 0x7f07033a
int dimen exo_small_icon_padding_horizontal 0x7f07033b
int dimen exo_small_icon_padding_vertical 0x7f07033c
int dimen exo_small_icon_width 0x7f07033d
int dimen exo_styled_bottom_bar_height 0x7f07033e
int dimen exo_styled_bottom_bar_margin_top 0x7f07033f
int dimen exo_styled_bottom_bar_time_padding 0x7f070340
int dimen exo_styled_controls_padding 0x7f070341
int dimen exo_styled_minimal_controls_margin_bottom 0x7f070342
int dimen exo_styled_progress_bar_height 0x7f070343
int dimen exo_styled_progress_dragged_thumb_size 0x7f070344
int dimen exo_styled_progress_enabled_thumb_size 0x7f070345
int dimen exo_styled_progress_layout_height 0x7f070346
int dimen exo_styled_progress_margin_bottom 0x7f070347
int dimen exo_styled_progress_touch_target_height 0x7f070348
int dimen fastscroll_default_thickness 0x7f070349
int dimen fastscroll_margin 0x7f07034a
int dimen fastscroll_minimum_range 0x7f07034b
int dimen highlight_alpha_material_colored 0x7f07034c
int dimen highlight_alpha_material_dark 0x7f07034d
int dimen highlight_alpha_material_light 0x7f07034e
int dimen hint_alpha_material_dark 0x7f07034f
int dimen hint_alpha_material_light 0x7f070350
int dimen hint_pressed_alpha_material_dark 0x7f070351
int dimen hint_pressed_alpha_material_light 0x7f070352
int dimen item_touch_helper_max_drag_scroll_per_frame 0x7f070353
int dimen item_touch_helper_swipe_escape_max_velocity 0x7f070354
int dimen item_touch_helper_swipe_escape_velocity 0x7f070355
int dimen m3_alert_dialog_action_bottom_padding 0x7f070356
int dimen m3_alert_dialog_action_top_padding 0x7f070357
int dimen m3_alert_dialog_corner_size 0x7f070358
int dimen m3_alert_dialog_elevation 0x7f070359
int dimen m3_alert_dialog_icon_margin 0x7f07035a
int dimen m3_alert_dialog_icon_size 0x7f07035b
int dimen m3_alert_dialog_title_bottom_margin 0x7f07035c
int dimen m3_appbar_expanded_title_margin_bottom 0x7f07035d
int dimen m3_appbar_expanded_title_margin_horizontal 0x7f07035e
int dimen m3_appbar_scrim_height_trigger 0x7f07035f
int dimen m3_appbar_scrim_height_trigger_large 0x7f070360
int dimen m3_appbar_scrim_height_trigger_medium 0x7f070361
int dimen m3_appbar_size_compact 0x7f070362
int dimen m3_appbar_size_large 0x7f070363
int dimen m3_appbar_size_medium 0x7f070364
int dimen m3_badge_horizontal_offset 0x7f070365
int dimen m3_badge_radius 0x7f070366
int dimen m3_badge_vertical_offset 0x7f070367
int dimen m3_badge_with_text_horizontal_offset 0x7f070368
int dimen m3_badge_with_text_radius 0x7f070369
int dimen m3_badge_with_text_vertical_offset 0x7f07036a
int dimen m3_bottom_nav_item_active_indicator_height 0x7f07036b
int dimen m3_bottom_nav_item_active_indicator_margin_horizontal 0x7f07036c
int dimen m3_bottom_nav_item_active_indicator_width 0x7f07036d
int dimen m3_bottom_nav_item_padding_bottom 0x7f07036e
int dimen m3_bottom_nav_item_padding_top 0x7f07036f
int dimen m3_bottom_nav_min_height 0x7f070370
int dimen m3_bottom_sheet_drag_handle_bottom_padding 0x7f070371
int dimen m3_bottom_sheet_elevation 0x7f070372
int dimen m3_bottom_sheet_modal_elevation 0x7f070373
int dimen m3_bottomappbar_fab_cradle_margin 0x7f070374
int dimen m3_bottomappbar_fab_cradle_rounded_corner_radius 0x7f070375
int dimen m3_bottomappbar_fab_cradle_vertical_offset 0x7f070376
int dimen m3_bottomappbar_fab_end_margin 0x7f070377
int dimen m3_bottomappbar_height 0x7f070378
int dimen m3_bottomappbar_horizontal_padding 0x7f070379
int dimen m3_btn_dialog_btn_min_width 0x7f07037a
int dimen m3_btn_dialog_btn_spacing 0x7f07037b
int dimen m3_btn_disabled_elevation 0x7f07037c
int dimen m3_btn_disabled_translation_z 0x7f07037d
int dimen m3_btn_elevated_btn_elevation 0x7f07037e
int dimen m3_btn_elevation 0x7f07037f
int dimen m3_btn_icon_btn_padding_left 0x7f070380
int dimen m3_btn_icon_btn_padding_right 0x7f070381
int dimen m3_btn_icon_only_default_padding 0x7f070382
int dimen m3_btn_icon_only_default_size 0x7f070383
int dimen m3_btn_icon_only_icon_padding 0x7f070384
int dimen m3_btn_icon_only_min_width 0x7f070385
int dimen m3_btn_inset 0x7f070386
int dimen m3_btn_max_width 0x7f070387
int dimen m3_btn_padding_bottom 0x7f070388
int dimen m3_btn_padding_left 0x7f070389
int dimen m3_btn_padding_right 0x7f07038a
int dimen m3_btn_padding_top 0x7f07038b
int dimen m3_btn_stroke_size 0x7f07038c
int dimen m3_btn_text_btn_icon_padding_left 0x7f07038d
int dimen m3_btn_text_btn_icon_padding_right 0x7f07038e
int dimen m3_btn_text_btn_padding_left 0x7f07038f
int dimen m3_btn_text_btn_padding_right 0x7f070390
int dimen m3_btn_translation_z_base 0x7f070391
int dimen m3_btn_translation_z_hovered 0x7f070392
int dimen m3_card_dragged_z 0x7f070393
int dimen m3_card_elevated_dragged_z 0x7f070394
int dimen m3_card_elevated_elevation 0x7f070395
int dimen m3_card_elevated_hovered_z 0x7f070396
int dimen m3_card_elevation 0x7f070397
int dimen m3_card_hovered_z 0x7f070398
int dimen m3_card_stroke_width 0x7f070399
int dimen m3_chip_checked_hovered_translation_z 0x7f07039a
int dimen m3_chip_corner_size 0x7f07039b
int dimen m3_chip_disabled_translation_z 0x7f07039c
int dimen m3_chip_dragged_translation_z 0x7f07039d
int dimen m3_chip_elevated_elevation 0x7f07039e
int dimen m3_chip_hovered_translation_z 0x7f07039f
int dimen m3_chip_icon_size 0x7f0703a0
int dimen m3_comp_assist_chip_container_height 0x7f0703a1
int dimen m3_comp_assist_chip_elevated_container_elevation 0x7f0703a2
int dimen m3_comp_assist_chip_flat_container_elevation 0x7f0703a3
int dimen m3_comp_assist_chip_flat_outline_width 0x7f0703a4
int dimen m3_comp_assist_chip_with_icon_icon_size 0x7f0703a5
int dimen m3_comp_bottom_app_bar_container_elevation 0x7f0703a6
int dimen m3_comp_bottom_app_bar_container_height 0x7f0703a7
int dimen m3_comp_checkbox_selected_disabled_container_opacity 0x7f0703a8
int dimen m3_comp_circular_progress_indicator_active_indicator_width 0x7f0703a9
int dimen m3_comp_divider_thickness 0x7f0703aa
int dimen m3_comp_elevated_button_container_elevation 0x7f0703ab
int dimen m3_comp_elevated_button_disabled_container_elevation 0x7f0703ac
int dimen m3_comp_extended_fab_primary_container_elevation 0x7f0703ad
int dimen m3_comp_extended_fab_primary_container_height 0x7f0703ae
int dimen m3_comp_extended_fab_primary_focus_container_elevation 0x7f0703af
int dimen m3_comp_extended_fab_primary_focus_state_layer_opacity 0x7f0703b0
int dimen m3_comp_extended_fab_primary_hover_container_elevation 0x7f0703b1
int dimen m3_comp_extended_fab_primary_hover_state_layer_opacity 0x7f0703b2
int dimen m3_comp_extended_fab_primary_icon_size 0x7f0703b3
int dimen m3_comp_extended_fab_primary_pressed_container_elevation 0x7f0703b4
int dimen m3_comp_extended_fab_primary_pressed_state_layer_opacity 0x7f0703b5
int dimen m3_comp_fab_primary_container_elevation 0x7f0703b6
int dimen m3_comp_fab_primary_container_height 0x7f0703b7
int dimen m3_comp_fab_primary_focus_state_layer_opacity 0x7f0703b8
int dimen m3_comp_fab_primary_hover_container_elevation 0x7f0703b9
int dimen m3_comp_fab_primary_hover_state_layer_opacity 0x7f0703ba
int dimen m3_comp_fab_primary_icon_size 0x7f0703bb
int dimen m3_comp_fab_primary_large_container_height 0x7f0703bc
int dimen m3_comp_fab_primary_large_icon_size 0x7f0703bd
int dimen m3_comp_fab_primary_pressed_container_elevation 0x7f0703be
int dimen m3_comp_fab_primary_pressed_state_layer_opacity 0x7f0703bf
int dimen m3_comp_fab_primary_small_container_height 0x7f0703c0
int dimen m3_comp_fab_primary_small_icon_size 0x7f0703c1
int dimen m3_comp_filled_autocomplete_menu_container_elevation 0x7f0703c2
int dimen m3_comp_filled_button_container_elevation 0x7f0703c3
int dimen m3_comp_filled_button_with_icon_icon_size 0x7f0703c4
int dimen m3_comp_filled_text_field_disabled_active_indicator_opacity 0x7f0703c5
int dimen m3_comp_filter_chip_container_height 0x7f0703c6
int dimen m3_comp_filter_chip_elevated_container_elevation 0x7f0703c7
int dimen m3_comp_filter_chip_flat_container_elevation 0x7f0703c8
int dimen m3_comp_filter_chip_flat_unselected_outline_width 0x7f0703c9
int dimen m3_comp_filter_chip_with_icon_icon_size 0x7f0703ca
int dimen m3_comp_input_chip_container_elevation 0x7f0703cb
int dimen m3_comp_input_chip_container_height 0x7f0703cc
int dimen m3_comp_input_chip_unselected_outline_width 0x7f0703cd
int dimen m3_comp_input_chip_with_avatar_avatar_size 0x7f0703ce
int dimen m3_comp_input_chip_with_leading_icon_leading_icon_size 0x7f0703cf
int dimen m3_comp_linear_progress_indicator_active_indicator_height 0x7f0703d0
int dimen m3_comp_navigation_rail_active_indicator_height 0x7f0703d1
int dimen m3_comp_navigation_rail_active_indicator_width 0x7f0703d2
int dimen m3_comp_navigation_rail_container_elevation 0x7f0703d3
int dimen m3_comp_navigation_rail_container_width 0x7f0703d4
int dimen m3_comp_navigation_rail_icon_size 0x7f0703d5
int dimen m3_comp_outlined_autocomplete_menu_container_elevation 0x7f0703d6
int dimen m3_comp_outlined_button_disabled_outline_opacity 0x7f0703d7
int dimen m3_comp_outlined_button_outline_width 0x7f0703d8
int dimen m3_comp_outlined_text_field_disabled_input_text_opacity 0x7f0703d9
int dimen m3_comp_outlined_text_field_disabled_label_text_opacity 0x7f0703da
int dimen m3_comp_outlined_text_field_disabled_supporting_text_opacity 0x7f0703db
int dimen m3_comp_outlined_text_field_focus_outline_width 0x7f0703dc
int dimen m3_comp_outlined_text_field_outline_width 0x7f0703dd
int dimen m3_comp_primary_navigation_tab_active_focus_state_layer_opacity 0x7f0703de
int dimen m3_comp_primary_navigation_tab_active_hover_state_layer_opacity 0x7f0703df
int dimen m3_comp_primary_navigation_tab_active_indicator_height 0x7f0703e0
int dimen m3_comp_primary_navigation_tab_active_pressed_state_layer_opacity 0x7f0703e1
int dimen m3_comp_primary_navigation_tab_divider_height 0x7f0703e2
int dimen m3_comp_primary_navigation_tab_inactive_focus_state_layer_opacity 0x7f0703e3
int dimen m3_comp_primary_navigation_tab_inactive_hover_state_layer_opacity 0x7f0703e4
int dimen m3_comp_primary_navigation_tab_inactive_pressed_state_layer_opacity 0x7f0703e5
int dimen m3_comp_primary_navigation_tab_with_icon_icon_size 0x7f0703e6
int dimen m3_comp_search_bar_avatar_size 0x7f0703e7
int dimen m3_comp_search_bar_container_elevation 0x7f0703e8
int dimen m3_comp_search_bar_container_height 0x7f0703e9
int dimen m3_comp_search_bar_hover_state_layer_opacity 0x7f0703ea
int dimen m3_comp_search_bar_pressed_state_layer_opacity 0x7f0703eb
int dimen m3_comp_search_view_container_elevation 0x7f0703ec
int dimen m3_comp_search_view_docked_header_container_height 0x7f0703ed
int dimen m3_comp_search_view_full_screen_header_container_height 0x7f0703ee
int dimen m3_comp_secondary_navigation_tab_active_indicator_height 0x7f0703ef
int dimen m3_comp_secondary_navigation_tab_focus_state_layer_opacity 0x7f0703f0
int dimen m3_comp_secondary_navigation_tab_hover_state_layer_opacity 0x7f0703f1
int dimen m3_comp_secondary_navigation_tab_pressed_state_layer_opacity 0x7f0703f2
int dimen m3_comp_sheet_bottom_docked_modal_container_elevation 0x7f0703f3
int dimen m3_comp_sheet_bottom_docked_standard_container_elevation 0x7f0703f4
int dimen m3_comp_slider_disabled_active_track_opacity 0x7f0703f5
int dimen m3_comp_slider_disabled_handle_opacity 0x7f0703f6
int dimen m3_comp_slider_disabled_inactive_track_opacity 0x7f0703f7
int dimen m3_comp_slider_inactive_track_height 0x7f0703f8
int dimen m3_comp_suggestion_chip_container_height 0x7f0703f9
int dimen m3_comp_suggestion_chip_elevated_container_elevation 0x7f0703fa
int dimen m3_comp_suggestion_chip_flat_container_elevation 0x7f0703fb
int dimen m3_comp_suggestion_chip_flat_outline_width 0x7f0703fc
int dimen m3_comp_suggestion_chip_with_leading_icon_leading_icon_size 0x7f0703fd
int dimen m3_comp_switch_disabled_selected_handle_opacity 0x7f0703fe
int dimen m3_comp_switch_disabled_selected_icon_opacity 0x7f0703ff
int dimen m3_comp_switch_disabled_track_opacity 0x7f070400
int dimen m3_comp_switch_disabled_unselected_handle_opacity 0x7f070401
int dimen m3_comp_switch_disabled_unselected_icon_opacity 0x7f070402
int dimen m3_comp_switch_selected_focus_state_layer_opacity 0x7f070403
int dimen m3_comp_switch_selected_hover_state_layer_opacity 0x7f070404
int dimen m3_comp_switch_selected_pressed_state_layer_opacity 0x7f070405
int dimen m3_comp_switch_track_height 0x7f070406
int dimen m3_comp_switch_track_width 0x7f070407
int dimen m3_comp_switch_unselected_focus_state_layer_opacity 0x7f070408
int dimen m3_comp_switch_unselected_hover_state_layer_opacity 0x7f070409
int dimen m3_comp_switch_unselected_pressed_state_layer_opacity 0x7f07040a
int dimen m3_comp_text_button_focus_state_layer_opacity 0x7f07040b
int dimen m3_comp_text_button_hover_state_layer_opacity 0x7f07040c
int dimen m3_comp_text_button_pressed_state_layer_opacity 0x7f07040d
int dimen m3_datepicker_elevation 0x7f07040e
int dimen m3_divider_heavy_thickness 0x7f07040f
int dimen m3_extended_fab_bottom_padding 0x7f070410
int dimen m3_extended_fab_end_padding 0x7f070411
int dimen m3_extended_fab_icon_padding 0x7f070412
int dimen m3_extended_fab_min_height 0x7f070413
int dimen m3_extended_fab_start_padding 0x7f070414
int dimen m3_extended_fab_top_padding 0x7f070415
int dimen m3_fab_border_width 0x7f070416
int dimen m3_fab_corner_size 0x7f070417
int dimen m3_fab_translation_z_hovered_focused 0x7f070418
int dimen m3_fab_translation_z_pressed 0x7f070419
int dimen m3_large_fab_max_image_size 0x7f07041a
int dimen m3_large_fab_size 0x7f07041b
int dimen m3_menu_elevation 0x7f07041c
int dimen m3_navigation_drawer_layout_corner_size 0x7f07041d
int dimen m3_navigation_item_horizontal_padding 0x7f07041e
int dimen m3_navigation_item_icon_padding 0x7f07041f
int dimen m3_navigation_item_shape_inset_bottom 0x7f070420
int dimen m3_navigation_item_shape_inset_end 0x7f070421
int dimen m3_navigation_item_shape_inset_start 0x7f070422
int dimen m3_navigation_item_shape_inset_top 0x7f070423
int dimen m3_navigation_item_vertical_padding 0x7f070424
int dimen m3_navigation_menu_divider_horizontal_padding 0x7f070425
int dimen m3_navigation_menu_headline_horizontal_padding 0x7f070426
int dimen m3_navigation_rail_default_width 0x7f070427
int dimen m3_navigation_rail_elevation 0x7f070428
int dimen m3_navigation_rail_icon_size 0x7f070429
int dimen m3_navigation_rail_item_active_indicator_height 0x7f07042a
int dimen m3_navigation_rail_item_active_indicator_margin_horizontal 0x7f07042b
int dimen m3_navigation_rail_item_active_indicator_width 0x7f07042c
int dimen m3_navigation_rail_item_min_height 0x7f07042d
int dimen m3_navigation_rail_item_padding_bottom 0x7f07042e
int dimen m3_navigation_rail_item_padding_top 0x7f07042f
int dimen m3_ripple_default_alpha 0x7f070430
int dimen m3_ripple_focused_alpha 0x7f070431
int dimen m3_ripple_hovered_alpha 0x7f070432
int dimen m3_ripple_pressed_alpha 0x7f070433
int dimen m3_ripple_selectable_pressed_alpha 0x7f070434
int dimen m3_searchbar_elevation 0x7f070435
int dimen m3_searchbar_height 0x7f070436
int dimen m3_searchbar_margin_horizontal 0x7f070437
int dimen m3_searchbar_margin_vertical 0x7f070438
int dimen m3_searchbar_outlined_stroke_width 0x7f070439
int dimen m3_searchbar_padding_start 0x7f07043a
int dimen m3_searchbar_text_margin_start_no_navigation_icon 0x7f07043b
int dimen m3_searchbar_text_size 0x7f07043c
int dimen m3_searchview_divider_size 0x7f07043d
int dimen m3_searchview_elevation 0x7f07043e
int dimen m3_searchview_height 0x7f07043f
int dimen m3_side_sheet_modal_elevation 0x7f070440
int dimen m3_side_sheet_standard_elevation 0x7f070441
int dimen m3_side_sheet_width 0x7f070442
int dimen m3_simple_item_color_hovered_alpha 0x7f070443
int dimen m3_simple_item_color_selected_alpha 0x7f070444
int dimen m3_slider_inactive_track_height 0x7f070445
int dimen m3_slider_thumb_elevation 0x7f070446
int dimen m3_small_fab_max_image_size 0x7f070447
int dimen m3_small_fab_size 0x7f070448
int dimen m3_snackbar_action_text_color_alpha 0x7f070449
int dimen m3_snackbar_margin 0x7f07044a
int dimen m3_sys_elevation_level0 0x7f07044b
int dimen m3_sys_elevation_level1 0x7f07044c
int dimen m3_sys_elevation_level2 0x7f07044d
int dimen m3_sys_elevation_level3 0x7f07044e
int dimen m3_sys_elevation_level4 0x7f07044f
int dimen m3_sys_elevation_level5 0x7f070450
int dimen m3_sys_motion_easing_emphasized_accelerate_control_x1 0x7f070451
int dimen m3_sys_motion_easing_emphasized_accelerate_control_x2 0x7f070452
int dimen m3_sys_motion_easing_emphasized_accelerate_control_y1 0x7f070453
int dimen m3_sys_motion_easing_emphasized_accelerate_control_y2 0x7f070454
int dimen m3_sys_motion_easing_emphasized_decelerate_control_x1 0x7f070455
int dimen m3_sys_motion_easing_emphasized_decelerate_control_x2 0x7f070456
int dimen m3_sys_motion_easing_emphasized_decelerate_control_y1 0x7f070457
int dimen m3_sys_motion_easing_emphasized_decelerate_control_y2 0x7f070458
int dimen m3_sys_motion_easing_legacy_accelerate_control_x1 0x7f070459
int dimen m3_sys_motion_easing_legacy_accelerate_control_x2 0x7f07045a
int dimen m3_sys_motion_easing_legacy_accelerate_control_y1 0x7f07045b
int dimen m3_sys_motion_easing_legacy_accelerate_control_y2 0x7f07045c
int dimen m3_sys_motion_easing_legacy_control_x1 0x7f07045d
int dimen m3_sys_motion_easing_legacy_control_x2 0x7f07045e
int dimen m3_sys_motion_easing_legacy_control_y1 0x7f07045f
int dimen m3_sys_motion_easing_legacy_control_y2 0x7f070460
int dimen m3_sys_motion_easing_legacy_decelerate_control_x1 0x7f070461
int dimen m3_sys_motion_easing_legacy_decelerate_control_x2 0x7f070462
int dimen m3_sys_motion_easing_legacy_decelerate_control_y1 0x7f070463
int dimen m3_sys_motion_easing_legacy_decelerate_control_y2 0x7f070464
int dimen m3_sys_motion_easing_linear_control_x1 0x7f070465
int dimen m3_sys_motion_easing_linear_control_x2 0x7f070466
int dimen m3_sys_motion_easing_linear_control_y1 0x7f070467
int dimen m3_sys_motion_easing_linear_control_y2 0x7f070468
int dimen m3_sys_motion_easing_standard_accelerate_control_x1 0x7f070469
int dimen m3_sys_motion_easing_standard_accelerate_control_x2 0x7f07046a
int dimen m3_sys_motion_easing_standard_accelerate_control_y1 0x7f07046b
int dimen m3_sys_motion_easing_standard_accelerate_control_y2 0x7f07046c
int dimen m3_sys_motion_easing_standard_control_x1 0x7f07046d
int dimen m3_sys_motion_easing_standard_control_x2 0x7f07046e
int dimen m3_sys_motion_easing_standard_control_y1 0x7f07046f
int dimen m3_sys_motion_easing_standard_control_y2 0x7f070470
int dimen m3_sys_motion_easing_standard_decelerate_control_x1 0x7f070471
int dimen m3_sys_motion_easing_standard_decelerate_control_x2 0x7f070472
int dimen m3_sys_motion_easing_standard_decelerate_control_y1 0x7f070473
int dimen m3_sys_motion_easing_standard_decelerate_control_y2 0x7f070474
int dimen m3_sys_state_dragged_state_layer_opacity 0x7f070475
int dimen m3_sys_state_focus_state_layer_opacity 0x7f070476
int dimen m3_sys_state_hover_state_layer_opacity 0x7f070477
int dimen m3_sys_state_pressed_state_layer_opacity 0x7f070478
int dimen m3_timepicker_display_stroke_width 0x7f070479
int dimen m3_timepicker_window_elevation 0x7f07047a
int dimen m3_toolbar_text_size_title 0x7f07047b
int dimen material_bottom_sheet_max_width 0x7f07047c
int dimen material_clock_display_height 0x7f07047d
int dimen material_clock_display_padding 0x7f07047e
int dimen material_clock_display_width 0x7f07047f
int dimen material_clock_face_margin_top 0x7f070480
int dimen material_clock_hand_center_dot_radius 0x7f070481
int dimen material_clock_hand_padding 0x7f070482
int dimen material_clock_hand_stroke_width 0x7f070483
int dimen material_clock_number_text_size 0x7f070484
int dimen material_clock_period_toggle_height 0x7f070485
int dimen material_clock_period_toggle_horizontal_gap 0x7f070486
int dimen material_clock_period_toggle_vertical_gap 0x7f070487
int dimen material_clock_period_toggle_width 0x7f070488
int dimen material_clock_size 0x7f070489
int dimen material_cursor_inset 0x7f07048a
int dimen material_cursor_width 0x7f07048b
int dimen material_divider_thickness 0x7f07048c
int dimen material_emphasis_disabled 0x7f07048d
int dimen material_emphasis_disabled_background 0x7f07048e
int dimen material_emphasis_high_type 0x7f07048f
int dimen material_emphasis_medium 0x7f070490
int dimen material_filled_edittext_font_1_3_padding_bottom 0x7f070491
int dimen material_filled_edittext_font_1_3_padding_top 0x7f070492
int dimen material_filled_edittext_font_2_0_padding_bottom 0x7f070493
int dimen material_filled_edittext_font_2_0_padding_top 0x7f070494
int dimen material_font_1_3_box_collapsed_padding_top 0x7f070495
int dimen material_font_2_0_box_collapsed_padding_top 0x7f070496
int dimen material_helper_text_default_padding_top 0x7f070497
int dimen material_helper_text_font_1_3_padding_horizontal 0x7f070498
int dimen material_helper_text_font_1_3_padding_top 0x7f070499
int dimen material_input_text_to_prefix_suffix_padding 0x7f07049a
int dimen material_textinput_default_width 0x7f07049b
int dimen material_textinput_max_width 0x7f07049c
int dimen material_textinput_min_width 0x7f07049d
int dimen material_time_picker_minimum_screen_height 0x7f07049e
int dimen material_time_picker_minimum_screen_width 0x7f07049f
int dimen material_timepicker_dialog_buttons_margin_top 0x7f0704a0
int dimen mtrl_alert_dialog_background_inset_bottom 0x7f0704a1
int dimen mtrl_alert_dialog_background_inset_end 0x7f0704a2
int dimen mtrl_alert_dialog_background_inset_start 0x7f0704a3
int dimen mtrl_alert_dialog_background_inset_top 0x7f0704a4
int dimen mtrl_alert_dialog_picker_background_inset 0x7f0704a5
int dimen mtrl_badge_horizontal_edge_offset 0x7f0704a6
int dimen mtrl_badge_long_text_horizontal_padding 0x7f0704a7
int dimen mtrl_badge_radius 0x7f0704a8
int dimen mtrl_badge_text_horizontal_edge_offset 0x7f0704a9
int dimen mtrl_badge_text_size 0x7f0704aa
int dimen mtrl_badge_toolbar_action_menu_item_horizontal_offset 0x7f0704ab
int dimen mtrl_badge_toolbar_action_menu_item_vertical_offset 0x7f0704ac
int dimen mtrl_badge_with_text_radius 0x7f0704ad
int dimen mtrl_bottomappbar_fabOffsetEndMode 0x7f0704ae
int dimen mtrl_bottomappbar_fab_bottom_margin 0x7f0704af
int dimen mtrl_bottomappbar_fab_cradle_margin 0x7f0704b0
int dimen mtrl_bottomappbar_fab_cradle_rounded_corner_radius 0x7f0704b1
int dimen mtrl_bottomappbar_fab_cradle_vertical_offset 0x7f0704b2
int dimen mtrl_bottomappbar_height 0x7f0704b3
int dimen mtrl_btn_corner_radius 0x7f0704b4
int dimen mtrl_btn_dialog_btn_min_width 0x7f0704b5
int dimen mtrl_btn_disabled_elevation 0x7f0704b6
int dimen mtrl_btn_disabled_z 0x7f0704b7
int dimen mtrl_btn_elevation 0x7f0704b8
int dimen mtrl_btn_focused_z 0x7f0704b9
int dimen mtrl_btn_hovered_z 0x7f0704ba
int dimen mtrl_btn_icon_btn_padding_left 0x7f0704bb
int dimen mtrl_btn_icon_padding 0x7f0704bc
int dimen mtrl_btn_inset 0x7f0704bd
int dimen mtrl_btn_letter_spacing 0x7f0704be
int dimen mtrl_btn_max_width 0x7f0704bf
int dimen mtrl_btn_padding_bottom 0x7f0704c0
int dimen mtrl_btn_padding_left 0x7f0704c1
int dimen mtrl_btn_padding_right 0x7f0704c2
int dimen mtrl_btn_padding_top 0x7f0704c3
int dimen mtrl_btn_pressed_z 0x7f0704c4
int dimen mtrl_btn_snackbar_margin_horizontal 0x7f0704c5
int dimen mtrl_btn_stroke_size 0x7f0704c6
int dimen mtrl_btn_text_btn_icon_padding 0x7f0704c7
int dimen mtrl_btn_text_btn_padding_left 0x7f0704c8
int dimen mtrl_btn_text_btn_padding_right 0x7f0704c9
int dimen mtrl_btn_text_size 0x7f0704ca
int dimen mtrl_btn_z 0x7f0704cb
int dimen mtrl_calendar_action_confirm_button_min_width 0x7f0704cc
int dimen mtrl_calendar_action_height 0x7f0704cd
int dimen mtrl_calendar_action_padding 0x7f0704ce
int dimen mtrl_calendar_bottom_padding 0x7f0704cf
int dimen mtrl_calendar_content_padding 0x7f0704d0
int dimen mtrl_calendar_day_corner 0x7f0704d1
int dimen mtrl_calendar_day_height 0x7f0704d2
int dimen mtrl_calendar_day_horizontal_padding 0x7f0704d3
int dimen mtrl_calendar_day_today_stroke 0x7f0704d4
int dimen mtrl_calendar_day_vertical_padding 0x7f0704d5
int dimen mtrl_calendar_day_width 0x7f0704d6
int dimen mtrl_calendar_days_of_week_height 0x7f0704d7
int dimen mtrl_calendar_dialog_background_inset 0x7f0704d8
int dimen mtrl_calendar_header_content_padding 0x7f0704d9
int dimen mtrl_calendar_header_content_padding_fullscreen 0x7f0704da
int dimen mtrl_calendar_header_divider_thickness 0x7f0704db
int dimen mtrl_calendar_header_height 0x7f0704dc
int dimen mtrl_calendar_header_height_fullscreen 0x7f0704dd
int dimen mtrl_calendar_header_selection_line_height 0x7f0704de
int dimen mtrl_calendar_header_text_padding 0x7f0704df
int dimen mtrl_calendar_header_toggle_margin_bottom 0x7f0704e0
int dimen mtrl_calendar_header_toggle_margin_top 0x7f0704e1
int dimen mtrl_calendar_landscape_header_width 0x7f0704e2
int dimen mtrl_calendar_maximum_default_fullscreen_minor_axis 0x7f0704e3
int dimen mtrl_calendar_month_horizontal_padding 0x7f0704e4
int dimen mtrl_calendar_month_vertical_padding 0x7f0704e5
int dimen mtrl_calendar_navigation_bottom_padding 0x7f0704e6
int dimen mtrl_calendar_navigation_height 0x7f0704e7
int dimen mtrl_calendar_navigation_top_padding 0x7f0704e8
int dimen mtrl_calendar_pre_l_text_clip_padding 0x7f0704e9
int dimen mtrl_calendar_selection_baseline_to_top_fullscreen 0x7f0704ea
int dimen mtrl_calendar_selection_text_baseline_to_bottom 0x7f0704eb
int dimen mtrl_calendar_selection_text_baseline_to_bottom_fullscreen 0x7f0704ec
int dimen mtrl_calendar_selection_text_baseline_to_top 0x7f0704ed
int dimen mtrl_calendar_text_input_padding_top 0x7f0704ee
int dimen mtrl_calendar_title_baseline_to_top 0x7f0704ef
int dimen mtrl_calendar_title_baseline_to_top_fullscreen 0x7f0704f0
int dimen mtrl_calendar_year_corner 0x7f0704f1
int dimen mtrl_calendar_year_height 0x7f0704f2
int dimen mtrl_calendar_year_horizontal_padding 0x7f0704f3
int dimen mtrl_calendar_year_vertical_padding 0x7f0704f4
int dimen mtrl_calendar_year_width 0x7f0704f5
int dimen mtrl_card_checked_icon_margin 0x7f0704f6
int dimen mtrl_card_checked_icon_size 0x7f0704f7
int dimen mtrl_card_corner_radius 0x7f0704f8
int dimen mtrl_card_dragged_z 0x7f0704f9
int dimen mtrl_card_elevation 0x7f0704fa
int dimen mtrl_card_spacing 0x7f0704fb
int dimen mtrl_chip_pressed_translation_z 0x7f0704fc
int dimen mtrl_chip_text_size 0x7f0704fd
int dimen mtrl_exposed_dropdown_menu_popup_elevation 0x7f0704fe
int dimen mtrl_exposed_dropdown_menu_popup_vertical_offset 0x7f0704ff
int dimen mtrl_exposed_dropdown_menu_popup_vertical_padding 0x7f070500
int dimen mtrl_extended_fab_bottom_padding 0x7f070501
int dimen mtrl_extended_fab_disabled_elevation 0x7f070502
int dimen mtrl_extended_fab_disabled_translation_z 0x7f070503
int dimen mtrl_extended_fab_elevation 0x7f070504
int dimen mtrl_extended_fab_end_padding 0x7f070505
int dimen mtrl_extended_fab_end_padding_icon 0x7f070506
int dimen mtrl_extended_fab_icon_size 0x7f070507
int dimen mtrl_extended_fab_icon_text_spacing 0x7f070508
int dimen mtrl_extended_fab_min_height 0x7f070509
int dimen mtrl_extended_fab_min_width 0x7f07050a
int dimen mtrl_extended_fab_start_padding 0x7f07050b
int dimen mtrl_extended_fab_start_padding_icon 0x7f07050c
int dimen mtrl_extended_fab_top_padding 0x7f07050d
int dimen mtrl_extended_fab_translation_z_base 0x7f07050e
int dimen mtrl_extended_fab_translation_z_hovered_focused 0x7f07050f
int dimen mtrl_extended_fab_translation_z_pressed 0x7f070510
int dimen mtrl_fab_elevation 0x7f070511
int dimen mtrl_fab_min_touch_target 0x7f070512
int dimen mtrl_fab_translation_z_hovered_focused 0x7f070513
int dimen mtrl_fab_translation_z_pressed 0x7f070514
int dimen mtrl_high_ripple_default_alpha 0x7f070515
int dimen mtrl_high_ripple_focused_alpha 0x7f070516
int dimen mtrl_high_ripple_hovered_alpha 0x7f070517
int dimen mtrl_high_ripple_pressed_alpha 0x7f070518
int dimen mtrl_low_ripple_default_alpha 0x7f070519
int dimen mtrl_low_ripple_focused_alpha 0x7f07051a
int dimen mtrl_low_ripple_hovered_alpha 0x7f07051b
int dimen mtrl_low_ripple_pressed_alpha 0x7f07051c
int dimen mtrl_min_touch_target_size 0x7f07051d
int dimen mtrl_navigation_bar_item_default_icon_size 0x7f07051e
int dimen mtrl_navigation_bar_item_default_margin 0x7f07051f
int dimen mtrl_navigation_elevation 0x7f070520
int dimen mtrl_navigation_item_horizontal_padding 0x7f070521
int dimen mtrl_navigation_item_icon_padding 0x7f070522
int dimen mtrl_navigation_item_icon_size 0x7f070523
int dimen mtrl_navigation_item_shape_horizontal_margin 0x7f070524
int dimen mtrl_navigation_item_shape_vertical_margin 0x7f070525
int dimen mtrl_navigation_rail_active_text_size 0x7f070526
int dimen mtrl_navigation_rail_compact_width 0x7f070527
int dimen mtrl_navigation_rail_default_width 0x7f070528
int dimen mtrl_navigation_rail_elevation 0x7f070529
int dimen mtrl_navigation_rail_icon_margin 0x7f07052a
int dimen mtrl_navigation_rail_icon_size 0x7f07052b
int dimen mtrl_navigation_rail_margin 0x7f07052c
int dimen mtrl_navigation_rail_text_bottom_margin 0x7f07052d
int dimen mtrl_navigation_rail_text_size 0x7f07052e
int dimen mtrl_progress_circular_inset 0x7f07052f
int dimen mtrl_progress_circular_inset_extra_small 0x7f070530
int dimen mtrl_progress_circular_inset_medium 0x7f070531
int dimen mtrl_progress_circular_inset_small 0x7f070532
int dimen mtrl_progress_circular_radius 0x7f070533
int dimen mtrl_progress_circular_size 0x7f070534
int dimen mtrl_progress_circular_size_extra_small 0x7f070535
int dimen mtrl_progress_circular_size_medium 0x7f070536
int dimen mtrl_progress_circular_size_small 0x7f070537
int dimen mtrl_progress_circular_track_thickness_extra_small 0x7f070538
int dimen mtrl_progress_circular_track_thickness_medium 0x7f070539
int dimen mtrl_progress_circular_track_thickness_small 0x7f07053a
int dimen mtrl_progress_indicator_full_rounded_corner_radius 0x7f07053b
int dimen mtrl_progress_track_thickness 0x7f07053c
int dimen mtrl_shape_corner_size_large_component 0x7f07053d
int dimen mtrl_shape_corner_size_medium_component 0x7f07053e
int dimen mtrl_shape_corner_size_small_component 0x7f07053f
int dimen mtrl_slider_halo_radius 0x7f070540
int dimen mtrl_slider_label_padding 0x7f070541
int dimen mtrl_slider_label_radius 0x7f070542
int dimen mtrl_slider_label_square_side 0x7f070543
int dimen mtrl_slider_thumb_elevation 0x7f070544
int dimen mtrl_slider_thumb_radius 0x7f070545
int dimen mtrl_slider_track_height 0x7f070546
int dimen mtrl_slider_track_side_padding 0x7f070547
int dimen mtrl_slider_widget_height 0x7f070548
int dimen mtrl_snackbar_action_text_color_alpha 0x7f070549
int dimen mtrl_snackbar_background_corner_radius 0x7f07054a
int dimen mtrl_snackbar_background_overlay_color_alpha 0x7f07054b
int dimen mtrl_snackbar_margin 0x7f07054c
int dimen mtrl_snackbar_message_margin_horizontal 0x7f07054d
int dimen mtrl_snackbar_padding_horizontal 0x7f07054e
int dimen mtrl_switch_text_padding 0x7f07054f
int dimen mtrl_switch_thumb_elevation 0x7f070550
int dimen mtrl_switch_thumb_size 0x7f070551
int dimen mtrl_switch_track_height 0x7f070552
int dimen mtrl_switch_track_width 0x7f070553
int dimen mtrl_textinput_box_corner_radius_medium 0x7f070554
int dimen mtrl_textinput_box_corner_radius_small 0x7f070555
int dimen mtrl_textinput_box_label_cutout_padding 0x7f070556
int dimen mtrl_textinput_box_stroke_width_default 0x7f070557
int dimen mtrl_textinput_box_stroke_width_focused 0x7f070558
int dimen mtrl_textinput_counter_margin_start 0x7f070559
int dimen mtrl_textinput_end_icon_margin_start 0x7f07055a
int dimen mtrl_textinput_outline_box_expanded_padding 0x7f07055b
int dimen mtrl_textinput_start_icon_margin_end 0x7f07055c
int dimen mtrl_toolbar_default_height 0x7f07055d
int dimen mtrl_tooltip_arrowSize 0x7f07055e
int dimen mtrl_tooltip_cornerSize 0x7f07055f
int dimen mtrl_tooltip_minHeight 0x7f070560
int dimen mtrl_tooltip_minWidth 0x7f070561
int dimen mtrl_tooltip_padding 0x7f070562
int dimen mtrl_transition_shared_axis_slide_distance 0x7f070563
int dimen notification_action_icon_size 0x7f070564
int dimen notification_action_text_size 0x7f070565
int dimen notification_big_circle_margin 0x7f070566
int dimen notification_content_margin_start 0x7f070567
int dimen notification_large_icon_height 0x7f070568
int dimen notification_large_icon_width 0x7f070569
int dimen notification_main_column_padding_top 0x7f07056a
int dimen notification_media_narrow_margin 0x7f07056b
int dimen notification_right_icon_size 0x7f07056c
int dimen notification_right_side_padding_top 0x7f07056d
int dimen notification_small_icon_background_padding 0x7f07056e
int dimen notification_small_icon_size_as_large 0x7f07056f
int dimen notification_subtext_size 0x7f070570
int dimen notification_top_pad 0x7f070571
int dimen notification_top_pad_large_text 0x7f070572
int dimen sliding_pane_detail_pane_width 0x7f070573
int dimen splashscreen_icon_mask_size_no_background 0x7f070574
int dimen splashscreen_icon_mask_size_with_background 0x7f070575
int dimen splashscreen_icon_mask_stroke_no_background 0x7f070576
int dimen splashscreen_icon_mask_stroke_with_background 0x7f070577
int dimen splashscreen_icon_size 0x7f070578
int dimen splashscreen_icon_size_no_background 0x7f070579
int dimen splashscreen_icon_size_with_background 0x7f07057a
int dimen tooltip_corner_radius 0x7f07057b
int dimen tooltip_horizontal_padding 0x7f07057c
int dimen tooltip_margin 0x7f07057d
int dimen tooltip_precise_anchor_extra_offset 0x7f07057e
int dimen tooltip_precise_anchor_threshold 0x7f07057f
int dimen tooltip_vertical_padding 0x7f070580
int dimen tooltip_y_offset_non_touch 0x7f070581
int dimen tooltip_y_offset_touch 0x7f070582
int drawable abc_ab_share_pack_mtrl_alpha 0x7f080028
int drawable abc_action_bar_item_background_material 0x7f080029
int drawable abc_btn_borderless_material 0x7f08002a
int drawable abc_btn_check_material 0x7f08002b
int drawable abc_btn_check_material_anim 0x7f08002c
int drawable abc_btn_check_to_on_mtrl_000 0x7f08002d
int drawable abc_btn_check_to_on_mtrl_015 0x7f08002e
int drawable abc_btn_colored_material 0x7f08002f
int drawable abc_btn_default_mtrl_shape 0x7f080030
int drawable abc_btn_radio_material 0x7f080031
int drawable abc_btn_radio_material_anim 0x7f080032
int drawable abc_btn_radio_to_on_mtrl_000 0x7f080033
int drawable abc_btn_radio_to_on_mtrl_015 0x7f080034
int drawable abc_btn_switch_to_on_mtrl_00001 0x7f080035
int drawable abc_btn_switch_to_on_mtrl_00012 0x7f080036
int drawable abc_cab_background_internal_bg 0x7f080037
int drawable abc_cab_background_top_material 0x7f080038
int drawable abc_cab_background_top_mtrl_alpha 0x7f080039
int drawable abc_control_background_material 0x7f08003a
int drawable abc_dialog_material_background 0x7f08003b
int drawable abc_edit_text_material 0x7f08003c
int drawable abc_ic_ab_back_material 0x7f08003d
int drawable abc_ic_arrow_drop_right_black_24dp 0x7f08003e
int drawable abc_ic_clear_material 0x7f08003f
int drawable abc_ic_commit_search_api_mtrl_alpha 0x7f080040
int drawable abc_ic_go_search_api_material 0x7f080041
int drawable abc_ic_menu_copy_mtrl_am_alpha 0x7f080042
int drawable abc_ic_menu_cut_mtrl_alpha 0x7f080043
int drawable abc_ic_menu_overflow_material 0x7f080044
int drawable abc_ic_menu_paste_mtrl_am_alpha 0x7f080045
int drawable abc_ic_menu_selectall_mtrl_alpha 0x7f080046
int drawable abc_ic_menu_share_mtrl_alpha 0x7f080047
int drawable abc_ic_search_api_material 0x7f080048
int drawable abc_ic_voice_search_api_material 0x7f080049
int drawable abc_item_background_holo_dark 0x7f08004a
int drawable abc_item_background_holo_light 0x7f08004b
int drawable abc_list_divider_material 0x7f08004c
int drawable abc_list_divider_mtrl_alpha 0x7f08004d
int drawable abc_list_focused_holo 0x7f08004e
int drawable abc_list_longpressed_holo 0x7f08004f
int drawable abc_list_pressed_holo_dark 0x7f080050
int drawable abc_list_pressed_holo_light 0x7f080051
int drawable abc_list_selector_background_transition_holo_dark 0x7f080052
int drawable abc_list_selector_background_transition_holo_light 0x7f080053
int drawable abc_list_selector_disabled_holo_dark 0x7f080054
int drawable abc_list_selector_disabled_holo_light 0x7f080055
int drawable abc_list_selector_holo_dark 0x7f080056
int drawable abc_list_selector_holo_light 0x7f080057
int drawable abc_menu_hardkey_panel_mtrl_mult 0x7f080058
int drawable abc_popup_background_mtrl_mult 0x7f080059
int drawable abc_ratingbar_indicator_material 0x7f08005a
int drawable abc_ratingbar_material 0x7f08005b
int drawable abc_ratingbar_small_material 0x7f08005c
int drawable abc_scrubber_control_off_mtrl_alpha 0x7f08005d
int drawable abc_scrubber_control_to_pressed_mtrl_000 0x7f08005e
int drawable abc_scrubber_control_to_pressed_mtrl_005 0x7f08005f
int drawable abc_scrubber_primary_mtrl_alpha 0x7f080060
int drawable abc_scrubber_track_mtrl_alpha 0x7f080061
int drawable abc_seekbar_thumb_material 0x7f080062
int drawable abc_seekbar_tick_mark_material 0x7f080063
int drawable abc_seekbar_track_material 0x7f080064
int drawable abc_spinner_mtrl_am_alpha 0x7f080065
int drawable abc_spinner_textfield_background_material 0x7f080066
int drawable abc_star_black_48dp 0x7f080067
int drawable abc_star_half_black_48dp 0x7f080068
int drawable abc_switch_thumb_material 0x7f080069
int drawable abc_switch_track_mtrl_alpha 0x7f08006a
int drawable abc_tab_indicator_material 0x7f08006b
int drawable abc_tab_indicator_mtrl_alpha 0x7f08006c
int drawable abc_text_cursor_material 0x7f08006d
int drawable abc_text_select_handle_left_mtrl 0x7f08006e
int drawable abc_text_select_handle_middle_mtrl 0x7f08006f
int drawable abc_text_select_handle_right_mtrl 0x7f080070
int drawable abc_textfield_activated_mtrl_alpha 0x7f080071
int drawable abc_textfield_default_mtrl_alpha 0x7f080072
int drawable abc_textfield_search_activated_mtrl_alpha 0x7f080073
int drawable abc_textfield_search_default_mtrl_alpha 0x7f080074
int drawable abc_textfield_search_material 0x7f080075
int drawable abc_vector_test 0x7f080076
int drawable annbg 0x7f080077
int drawable app_icon 0x7f080078
int drawable apple 0x7f080079
int drawable arrow_forward 0x7f08007a
int drawable avd_hide_password 0x7f08007b
int drawable avd_show_password 0x7f08007c
int drawable back_btn 0x7f08007d
int drawable bg 0x7f08007e
int drawable bg_btn_gray 0x7f08007f
int drawable bg_button_gray 0x7f080080
int drawable bg_button_outlined 0x7f080081
int drawable bg_button_primary 0x7f080082
int drawable bg_button_primary_light 0x7f080083
int drawable bg_circle_gray 0x7f080084
int drawable bg_circle_red 0x7f080085
int drawable bg_corners 0x7f080086
int drawable bg_corners_top 0x7f080087
int drawable bg_edit 0x7f080088
int drawable bg_edit_blue 0x7f080089
int drawable bg_edit_comment 0x7f08008a
int drawable bg_edit_red 0x7f08008b
int drawable bg_image 0x7f08008c
int drawable bg_round 0x7f08008d
int drawable bg_round_black 0x7f08008e
int drawable bg_round_corners 0x7f08008f
int drawable bg_round_red_grad 0x7f080090
int drawable blank 0x7f080091
int drawable brands_welcome 0x7f080092
int drawable btn_checkbox_checked_mtrl 0x7f080093
int drawable btn_checkbox_checked_to_unchecked_mtrl_animation 0x7f080094
int drawable btn_checkbox_unchecked_mtrl 0x7f080095
int drawable btn_checkbox_unchecked_to_checked_mtrl_animation 0x7f080096
int drawable btn_radio_off_mtrl 0x7f080097
int drawable btn_radio_off_to_on_mtrl_animation 0x7f080098
int drawable btn_radio_on_mtrl 0x7f080099
int drawable btn_radio_on_to_off_mtrl_animation 0x7f08009a
int drawable common_full_open_on_phone 0x7f08009b
int drawable common_google_signin_btn_icon_dark 0x7f08009c
int drawable common_google_signin_btn_icon_dark_focused 0x7f08009d
int drawable common_google_signin_btn_icon_dark_normal 0x7f08009e
int drawable common_google_signin_btn_icon_dark_normal_background 0x7f08009f
int drawable common_google_signin_btn_icon_disabled 0x7f0800a0
int drawable common_google_signin_btn_icon_light 0x7f0800a1
int drawable common_google_signin_btn_icon_light_focused 0x7f0800a2
int drawable common_google_signin_btn_icon_light_normal 0x7f0800a3
int drawable common_google_signin_btn_icon_light_normal_background 0x7f0800a4
int drawable common_google_signin_btn_text_dark 0x7f0800a5
int drawable common_google_signin_btn_text_dark_focused 0x7f0800a6
int drawable common_google_signin_btn_text_dark_normal 0x7f0800a7
int drawable common_google_signin_btn_text_dark_normal_background 0x7f0800a8
int drawable common_google_signin_btn_text_disabled 0x7f0800a9
int drawable common_google_signin_btn_text_light 0x7f0800aa
int drawable common_google_signin_btn_text_light_focused 0x7f0800ab
int drawable common_google_signin_btn_text_light_normal 0x7f0800ac
int drawable common_google_signin_btn_text_light_normal_background 0x7f0800ad
int drawable compat_splash_screen 0x7f0800ae
int drawable compat_splash_screen_no_icon_background 0x7f0800af
int drawable cross 0x7f0800b0
int drawable cross_circle 0x7f0800b1
int drawable cup 0x7f0800b2
int drawable custom_nav_icon_shape 0x7f0800b3
int drawable design_fab_background 0x7f0800b4
int drawable design_ic_visibility 0x7f0800b5
int drawable design_ic_visibility_off 0x7f0800b6
int drawable design_password_eye 0x7f0800b7
int drawable design_snackbar_background 0x7f0800b8
int drawable diamond 0x7f0800b9
int drawable diamond40 0x7f0800ba
int drawable diamond_colored 0x7f0800bb
int drawable diamond_small 0x7f0800bc
int drawable diamond_square_small 0x7f0800bd
int drawable dollar_square 0x7f0800be
int drawable dollar_square_small 0x7f0800bf
int drawable edittext_background 0x7f0800c0
int drawable email 0x7f0800c1
int drawable empty 0x7f0800c2
int drawable exo_edit_mode_logo 0x7f0800c3
int drawable exo_ic_audiotrack 0x7f0800c4
int drawable exo_ic_check 0x7f0800c5
int drawable exo_ic_chevron_left 0x7f0800c6
int drawable exo_ic_chevron_right 0x7f0800c7
int drawable exo_ic_default_album_image 0x7f0800c8
int drawable exo_ic_forward 0x7f0800c9
int drawable exo_ic_fullscreen_enter 0x7f0800ca
int drawable exo_ic_fullscreen_exit 0x7f0800cb
int drawable exo_ic_pause_circle_filled 0x7f0800cc
int drawable exo_ic_play_circle_filled 0x7f0800cd
int drawable exo_ic_rewind 0x7f0800ce
int drawable exo_ic_settings 0x7f0800cf
int drawable exo_ic_skip_next 0x7f0800d0
int drawable exo_ic_skip_previous 0x7f0800d1
int drawable exo_ic_speed 0x7f0800d2
int drawable exo_ic_subtitle_off 0x7f0800d3
int drawable exo_ic_subtitle_on 0x7f0800d4
int drawable exo_icon_circular_play 0x7f0800d5
int drawable exo_icon_fastforward 0x7f0800d6
int drawable exo_icon_fullscreen_enter 0x7f0800d7
int drawable exo_icon_fullscreen_exit 0x7f0800d8
int drawable exo_icon_next 0x7f0800d9
int drawable exo_icon_pause 0x7f0800da
int drawable exo_icon_play 0x7f0800db
int drawable exo_icon_previous 0x7f0800dc
int drawable exo_icon_repeat_all 0x7f0800dd
int drawable exo_icon_repeat_off 0x7f0800de
int drawable exo_icon_repeat_one 0x7f0800df
int drawable exo_icon_rewind 0x7f0800e0
int drawable exo_icon_shuffle_off 0x7f0800e1
int drawable exo_icon_shuffle_on 0x7f0800e2
int drawable exo_icon_stop 0x7f0800e3
int drawable exo_icon_vr 0x7f0800e4
int drawable exo_legacy_controls_fastforward 0x7f0800e5
int drawable exo_legacy_controls_fullscreen_enter 0x7f0800e6
int drawable exo_legacy_controls_fullscreen_exit 0x7f0800e7
int drawable exo_legacy_controls_next 0x7f0800e8
int drawable exo_legacy_controls_pause 0x7f0800e9
int drawable exo_legacy_controls_play 0x7f0800ea
int drawable exo_legacy_controls_previous 0x7f0800eb
int drawable exo_legacy_controls_repeat_all 0x7f0800ec
int drawable exo_legacy_controls_repeat_off 0x7f0800ed
int drawable exo_legacy_controls_repeat_one 0x7f0800ee
int drawable exo_legacy_controls_rewind 0x7f0800ef
int drawable exo_legacy_controls_shuffle_off 0x7f0800f0
int drawable exo_legacy_controls_shuffle_on 0x7f0800f1
int drawable exo_legacy_controls_vr 0x7f0800f2
int drawable exo_notification_fastforward 0x7f0800f3
int drawable exo_notification_next 0x7f0800f4
int drawable exo_notification_pause 0x7f0800f5
int drawable exo_notification_play 0x7f0800f6
int drawable exo_notification_previous 0x7f0800f7
int drawable exo_notification_rewind 0x7f0800f8
int drawable exo_notification_small_icon 0x7f0800f9
int drawable exo_notification_stop 0x7f0800fa
int drawable exo_rounded_rectangle 0x7f0800fb
int drawable exo_styled_controls_audiotrack 0x7f0800fc
int drawable exo_styled_controls_check 0x7f0800fd
int drawable exo_styled_controls_fastforward 0x7f0800fe
int drawable exo_styled_controls_fullscreen_enter 0x7f0800ff
int drawable exo_styled_controls_fullscreen_exit 0x7f080100
int drawable exo_styled_controls_next 0x7f080101
int drawable exo_styled_controls_overflow_hide 0x7f080102
int drawable exo_styled_controls_overflow_show 0x7f080103
int drawable exo_styled_controls_pause 0x7f080104
int drawable exo_styled_controls_play 0x7f080105
int drawable exo_styled_controls_previous 0x7f080106
int drawable exo_styled_controls_repeat_all 0x7f080107
int drawable exo_styled_controls_repeat_off 0x7f080108
int drawable exo_styled_controls_repeat_one 0x7f080109
int drawable exo_styled_controls_rewind 0x7f08010a
int drawable exo_styled_controls_settings 0x7f08010b
int drawable exo_styled_controls_shuffle_off 0x7f08010c
int drawable exo_styled_controls_shuffle_on 0x7f08010d
int drawable exo_styled_controls_speed 0x7f08010e
int drawable exo_styled_controls_subtitle_off 0x7f08010f
int drawable exo_styled_controls_subtitle_on 0x7f080110
int drawable exo_styled_controls_vr 0x7f080111
int drawable eye_crossed 0x7f080112
int drawable focus_marker_fill 0x7f080113
int drawable focus_marker_outline 0x7f080114
int drawable gift 0x7f080115
int drawable gift_n 0x7f080116
int drawable google_color 0x7f080117
int drawable googleg_disabled_color_18 0x7f080118
int drawable googleg_standard_color_18 0x7f080119
int drawable hat_avatar 0x7f08011a
int drawable heart 0x7f08011b
int drawable heart40 0x7f08011c
int drawable heart_colored 0x7f08011d
int drawable heart_filled 0x7f08011e
int drawable heart_outline 0x7f08011f
int drawable heart_small 0x7f080120
int drawable heart_square_small 0x7f080121
int drawable heart_two 0x7f080122
int drawable home 0x7f080123
int drawable ic_alert 0x7f080124
int drawable ic_arrow_back_black_24 0x7f080125
int drawable ic_arrow_left 0x7f080126
int drawable ic_arrow_right 0x7f080127
int drawable ic_back_btn 0x7f080128
int drawable ic_baseline_arrow_downward_24 0x7f080129
int drawable ic_bg_bottom 0x7f08012a
int drawable ic_bg_top 0x7f08012b
int drawable ic_cake 0x7f08012c
int drawable ic_camera_click 0x7f08012d
int drawable ic_clear_black_24 0x7f08012e
int drawable ic_clock_black_24dp 0x7f08012f
int drawable ic_close 0x7f080130
int drawable ic_close_gray 0x7f080131
int drawable ic_congrats 0x7f080132
int drawable ic_crate 0x7f080133
int drawable ic_crate_small 0x7f080134
int drawable ic_cup_tinted 0x7f080135
int drawable ic_daily_luv 0x7f080136
int drawable ic_dollar_small 0x7f080137
int drawable ic_edit 0x7f080138
int drawable ic_edit_blue 0x7f080139
int drawable ic_exclamation_circle 0x7f08013a
int drawable ic_exclamation_mark 0x7f08013b
int drawable ic_filter 0x7f08013c
int drawable ic_gallery 0x7f08013d
int drawable ic_gender 0x7f08013e
int drawable ic_gift_box 0x7f08013f
int drawable ic_giveaway 0x7f080140
int drawable ic_globe 0x7f080141
int drawable ic_h 0x7f080142
int drawable ic_home_tinted 0x7f080143
int drawable ic_info_ex 0x7f080144
int drawable ic_key 0x7f080145
int drawable ic_keyboard_black_24dp 0x7f080146
int drawable ic_lock 0x7f080147
int drawable ic_lock_blue 0x7f080148
int drawable ic_lock_small 0x7f080149
int drawable ic_luv_chest_sent 0x7f08014a
int drawable ic_luv_heart 0x7f08014b
int drawable ic_m3_chip_check 0x7f08014c
int drawable ic_m3_chip_checked_circle 0x7f08014d
int drawable ic_m3_chip_close 0x7f08014e
int drawable ic_money 0x7f08014f
int drawable ic_more 0x7f080150
int drawable ic_msg 0x7f080151
int drawable ic_mtrl_checked_circle 0x7f080152
int drawable ic_mtrl_chip_checked_black 0x7f080153
int drawable ic_mtrl_chip_checked_circle 0x7f080154
int drawable ic_mtrl_chip_close_circle 0x7f080155
int drawable ic_notification_tinted 0x7f080156
int drawable ic_open_chest 0x7f080157
int drawable ic_options 0x7f080158
int drawable ic_plus_circle 0x7f080159
int drawable ic_profile 0x7f08015a
int drawable ic_qr_frame 0x7f08015b
int drawable ic_question 0x7f08015c
int drawable ic_recharge 0x7f08015d
int drawable ic_recharge_arrow 0x7f08015e
int drawable ic_recording 0x7f08015f
int drawable ic_repeat 0x7f080160
int drawable ic_scan_btn 0x7f080161
int drawable ic_scan_tinted 0x7f080162
int drawable ic_search_black_24 0x7f080163
int drawable ic_send 0x7f080164
int drawable ic_send_luv 0x7f080165
int drawable ic_setting 0x7f080166
int drawable ic_share_small 0x7f080167
int drawable ic_star_heart 0x7f080168
int drawable ic_success 0x7f080169
int drawable ic_switch 0x7f08016a
int drawable ic_text_paste 0x7f08016b
int drawable ic_thanks 0x7f08016c
int drawable ic_user_placeholder 0x7f08016d
int drawable ic_winner 0x7f08016e
int drawable ic_winner_gray 0x7f08016f
int drawable ic_withdraw 0x7f080170
int drawable ic_withraw 0x7f080171
int drawable icon_background 0x7f080172
int drawable icon_luv 0x7f080173
int drawable image 0x7f080174
int drawable iv_add 0x7f080175
int drawable iv_check_red 0x7f080176
int drawable iv_close 0x7f080177
int drawable iv_cup 0x7f080178
int drawable iv_followers 0x7f080179
int drawable iv_following 0x7f08017a
int drawable iv_gray_cup 0x7f08017b
int drawable iv_heart 0x7f08017c
int drawable iv_search 0x7f08017d
int drawable iv_world 0x7f08017e
int drawable layered_progress_bar 0x7f08017f
int drawable like_simple 0x7f080180
int drawable luvdrop 0x7f080181
int drawable m3_appbar_background 0x7f080182
int drawable m3_avd_hide_password 0x7f080183
int drawable m3_avd_show_password 0x7f080184
int drawable m3_password_eye 0x7f080185
int drawable m3_popupmenu_background_overlay 0x7f080186
int drawable m3_radiobutton_ripple 0x7f080187
int drawable m3_selection_control_ripple 0x7f080188
int drawable m3_tabs_background 0x7f080189
int drawable m3_tabs_line_indicator 0x7f08018a
int drawable m3_tabs_rounded_line_indicator 0x7f08018b
int drawable m3_tabs_transparent_background 0x7f08018c
int drawable material_cursor_drawable 0x7f08018d
int drawable material_ic_calendar_black_24dp 0x7f08018e
int drawable material_ic_clear_black_24dp 0x7f08018f
int drawable material_ic_edit_black_24dp 0x7f080190
int drawable material_ic_keyboard_arrow_left_black_24dp 0x7f080191
int drawable material_ic_keyboard_arrow_next_black_24dp 0x7f080192
int drawable material_ic_keyboard_arrow_previous_black_24dp 0x7f080193
int drawable material_ic_keyboard_arrow_right_black_24dp 0x7f080194
int drawable material_ic_menu_arrow_down_black_24dp 0x7f080195
int drawable material_ic_menu_arrow_up_black_24dp 0x7f080196
int drawable menu_alert_tint 0x7f080197
int drawable menu_home_tint 0x7f080198
int drawable menu_quest_tint 0x7f080199
int drawable menu_scan_tint 0x7f08019a
int drawable mtrl_bottomsheet_drag_handle 0x7f08019b
int drawable mtrl_checkbox_button 0x7f08019c
int drawable mtrl_checkbox_button_checked_unchecked 0x7f08019d
int drawable mtrl_checkbox_button_icon 0x7f08019e
int drawable mtrl_checkbox_button_icon_checked_indeterminate 0x7f08019f
int drawable mtrl_checkbox_button_icon_checked_unchecked 0x7f0801a0
int drawable mtrl_checkbox_button_icon_indeterminate_checked 0x7f0801a1
int drawable mtrl_checkbox_button_icon_indeterminate_unchecked 0x7f0801a2
int drawable mtrl_checkbox_button_icon_unchecked_checked 0x7f0801a3
int drawable mtrl_checkbox_button_icon_unchecked_indeterminate 0x7f0801a4
int drawable mtrl_checkbox_button_unchecked_checked 0x7f0801a5
int drawable mtrl_dialog_background 0x7f0801a6
int drawable mtrl_dropdown_arrow 0x7f0801a7
int drawable mtrl_ic_arrow_drop_down 0x7f0801a8
int drawable mtrl_ic_arrow_drop_up 0x7f0801a9
int drawable mtrl_ic_cancel 0x7f0801aa
int drawable mtrl_ic_check_mark 0x7f0801ab
int drawable mtrl_ic_checkbox_checked 0x7f0801ac
int drawable mtrl_ic_checkbox_unchecked 0x7f0801ad
int drawable mtrl_ic_error 0x7f0801ae
int drawable mtrl_ic_indeterminate 0x7f0801af
int drawable mtrl_navigation_bar_item_background 0x7f0801b0
int drawable mtrl_popupmenu_background 0x7f0801b1
int drawable mtrl_popupmenu_background_overlay 0x7f0801b2
int drawable mtrl_switch_thumb 0x7f0801b3
int drawable mtrl_switch_thumb_checked 0x7f0801b4
int drawable mtrl_switch_thumb_checked_pressed 0x7f0801b5
int drawable mtrl_switch_thumb_checked_unchecked 0x7f0801b6
int drawable mtrl_switch_thumb_pressed 0x7f0801b7
int drawable mtrl_switch_thumb_pressed_checked 0x7f0801b8
int drawable mtrl_switch_thumb_pressed_unchecked 0x7f0801b9
int drawable mtrl_switch_thumb_unchecked 0x7f0801ba
int drawable mtrl_switch_thumb_unchecked_checked 0x7f0801bb
int drawable mtrl_switch_thumb_unchecked_pressed 0x7f0801bc
int drawable mtrl_switch_track 0x7f0801bd
int drawable mtrl_switch_track_decoration 0x7f0801be
int drawable mtrl_tabs_default_indicator 0x7f0801bf
int drawable navigation_empty_icon 0x7f0801c0
int drawable notification 0x7f0801c1
int drawable notification_action_background 0x7f0801c2
int drawable notification_bg 0x7f0801c3
int drawable notification_bg_low 0x7f0801c4
int drawable notification_bg_low_normal 0x7f0801c5
int drawable notification_bg_low_pressed 0x7f0801c6
int drawable notification_bg_normal 0x7f0801c7
int drawable notification_bg_normal_pressed 0x7f0801c8
int drawable notification_icon_background 0x7f0801c9
int drawable notification_template_icon_bg 0x7f0801ca
int drawable notification_template_icon_low_bg 0x7f0801cb
int drawable notification_tile_bg 0x7f0801cc
int drawable notify_panel_notification_icon_bg 0x7f0801cd
int drawable onboard_1 0x7f0801ce
int drawable onboard_2 0x7f0801cf
int drawable onboard_3 0x7f0801d0
int drawable onboard_4 0x7f0801d1
int drawable person 0x7f0801d2
int drawable person_two 0x7f0801d3
int drawable plus_circle 0x7f0801d4
int drawable profile 0x7f0801d5
int drawable profile_n 0x7f0801d6
int drawable progress_bar_progress 0x7f0801d7
int drawable qr_sample 0x7f0801d8
int drawable quest4 0x7f0801d9
int drawable quest5 0x7f0801da
int drawable quest6 0x7f0801db
int drawable question 0x7f0801dc
int drawable received_luv_image 0x7f0801dd
int drawable rectangle 0x7f0801de
int drawable round_star_24 0x7f0801df
int drawable round_star_border_24 0x7f0801e0
int drawable scan 0x7f0801e1
int drawable search 0x7f0801e2
int drawable search_btn 0x7f0801e3
int drawable search_small 0x7f0801e4
int drawable send 0x7f0801e5
int drawable send_btn 0x7f0801e6
int drawable sendgift 0x7f0801e7
int drawable sent_luv_image 0x7f0801e8
int drawable sheet_shape 0x7f0801e9
int drawable shop 0x7f0801ea
int drawable sms 0x7f0801eb
int drawable splash_bg_gradient 0x7f0801ec
int drawable tab_item_background 0x7f0801ed
int drawable tab_new_background 0x7f0801ee
int drawable tab_selected 0x7f0801ef
int drawable tab_unselected 0x7f0801f0
int drawable test_level_drawable 0x7f0801f1
int drawable text_handle 0x7f0801f2
int drawable tick 0x7f0801f3
int drawable tooltip_frame_dark 0x7f0801f4
int drawable tooltip_frame_light 0x7f0801f5
int drawable turban_avatar 0x7f0801f6
int drawable user 0x7f0801f7
int drawable user_placeholder 0x7f0801f8
int drawable vacter 0x7f0801f9
int drawable vacter_two 0x7f0801fa
int font medium 0x7f090000
int font roboto_medium_numbers 0x7f090001
int font semibold 0x7f090002
int id ALT 0x7f0a0000
int id BOTTOM_END 0x7f0a0001
int id BOTTOM_START 0x7f0a0002
int id CTRL 0x7f0a0003
int id FUNCTION 0x7f0a0004
int id META 0x7f0a0005
int id NO_DEBUG 0x7f0a0006
int id SHIFT 0x7f0a0007
int id SHOW_ALL 0x7f0a0008
int id SHOW_PATH 0x7f0a0009
int id SHOW_PROGRESS 0x7f0a000a
int id SYM 0x7f0a000b
int id TOP_END 0x7f0a000c
int id TOP_START 0x7f0a000d
int id acDays 0x7f0a000e
int id acScans 0x7f0a000f
int id accelerate 0x7f0a0010
int id accessibility_action_clickable_span 0x7f0a0011
int id accessibility_custom_action_0 0x7f0a0012
int id accessibility_custom_action_1 0x7f0a0013
int id accessibility_custom_action_10 0x7f0a0014
int id accessibility_custom_action_11 0x7f0a0015
int id accessibility_custom_action_12 0x7f0a0016
int id accessibility_custom_action_13 0x7f0a0017
int id accessibility_custom_action_14 0x7f0a0018
int id accessibility_custom_action_15 0x7f0a0019
int id accessibility_custom_action_16 0x7f0a001a
int id accessibility_custom_action_17 0x7f0a001b
int id accessibility_custom_action_18 0x7f0a001c
int id accessibility_custom_action_19 0x7f0a001d
int id accessibility_custom_action_2 0x7f0a001e
int id accessibility_custom_action_20 0x7f0a001f
int id accessibility_custom_action_21 0x7f0a0020
int id accessibility_custom_action_22 0x7f0a0021
int id accessibility_custom_action_23 0x7f0a0022
int id accessibility_custom_action_24 0x7f0a0023
int id accessibility_custom_action_25 0x7f0a0024
int id accessibility_custom_action_26 0x7f0a0025
int id accessibility_custom_action_27 0x7f0a0026
int id accessibility_custom_action_28 0x7f0a0027
int id accessibility_custom_action_29 0x7f0a0028
int id accessibility_custom_action_3 0x7f0a0029
int id accessibility_custom_action_30 0x7f0a002a
int id accessibility_custom_action_31 0x7f0a002b
int id accessibility_custom_action_4 0x7f0a002c
int id accessibility_custom_action_5 0x7f0a002d
int id accessibility_custom_action_6 0x7f0a002e
int id accessibility_custom_action_7 0x7f0a002f
int id accessibility_custom_action_8 0x7f0a0030
int id accessibility_custom_action_9 0x7f0a0031
int id action0 0x7f0a0032
int id action_AlertsFragment_to_videoPlayerWithDetailsFragment 0x7f0a0033
int id action_alertFragment_to_myProfileFragment 0x7f0a0034
int id action_alertFragment_to_preRecordingFragment 0x7f0a0035
int id action_allBrandsFragment_to_userProfileFragment 0x7f0a0036
int id action_authSelectFragment_to_homeFragment 0x7f0a0037
int id action_authSelectFragment_to_loginFragment 0x7f0a0038
int id action_authSelectFragment_to_onboardFragment 0x7f0a0039
int id action_authSelectFragment_to_registrationFragment 0x7f0a003a
int id action_bar 0x7f0a003b
int id action_bar_activity_content 0x7f0a003c
int id action_bar_container 0x7f0a003d
int id action_bar_root 0x7f0a003e
int id action_bar_spinner 0x7f0a003f
int id action_bar_subtitle 0x7f0a0040
int id action_bar_title 0x7f0a0041
int id action_chooseFavFragment_to_allBrandsFragment 0x7f0a0042
int id action_chooseFavFragment_to_userProfileFragment 0x7f0a0043
int id action_container 0x7f0a0044
int id action_context_bar 0x7f0a0045
int id action_divider 0x7f0a0046
int id action_followersFragment_to_userProfileFragment 0x7f0a0047
int id action_followingsFragment_to_userProfileFragment 0x7f0a0048
int id action_global_battleFragment 0x7f0a0049
int id action_homeFragment_to_createQrGiftingFragment 0x7f0a004a
int id action_homeFragment_to_giftReceivedDetailsFragment 0x7f0a004b
int id action_homeFragment_to_giftSentDetailsFragment 0x7f0a004c
int id action_homeFragment_to_luvDropFragment 0x7f0a004d
int id action_homeFragment_to_luvDropReceivedDetailsFragment 0x7f0a004e
int id action_homeFragment_to_myChestQrFragment 0x7f0a004f
int id action_homeFragment_to_myProfileFragment 0x7f0a0050
int id action_homeFragment_to_myProfileFragment2 0x7f0a0051
int id action_homeFragment_to_openedLuvChestFragment 0x7f0a0052
int id action_homeFragment_to_openedLuvCrateFragment 0x7f0a0053
int id action_homeFragment_to_qrGiftingFragment 0x7f0a0054
int id action_homeFragment_to_qrReceivingFragment 0x7f0a0055
int id action_homeFragment_to_questCrateFragment 0x7f0a0056
int id action_homeFragment_to_rechargeDetailsFragment 0x7f0a0057
int id action_homeFragment_to_rechargeFragment 0x7f0a0058
int id action_homeFragment_to_referralAwardFragment 0x7f0a0059
int id action_homeFragment_to_sendLuvFragment 0x7f0a005a
int id action_homeFragment_to_sendLuvGetLuvFragment 0x7f0a005b
int id action_homeFragment_to_topLuversFragment 0x7f0a005c
int id action_homeFragment_to_userFollowersFragment 0x7f0a005d
int id action_homeFragment_to_userFollowingsFragment 0x7f0a005e
int id action_homeFragment_to_userProfileFragment 0x7f0a005f
int id action_homeFragment_to_userStoryFragment 0x7f0a0060
int id action_homeFragment_to_withdrawDetailsFragment 0x7f0a0061
int id action_homeFragment_to_withdrawPaymentSelectFragment 0x7f0a0062
int id action_image 0x7f0a0063
int id action_loginFragment_to_homeFragment 0x7f0a0064
int id action_loginFragment_to_onboardFragment 0x7f0a0065
int id action_loginFragment_to_registrationFragment 0x7f0a0066
int id action_loginFragment_to_resetPasswordFragment 0x7f0a0067
int id action_menu_divider 0x7f0a0068
int id action_menu_presenter 0x7f0a0069
int id action_mode_bar 0x7f0a006a
int id action_mode_bar_stub 0x7f0a006b
int id action_mode_close_button 0x7f0a006c
int id action_myProfileFragment_to_chooseFavFragment 0x7f0a006d
int id action_myProfileFragment_to_editProfileFragment 0x7f0a006e
int id action_myProfileFragment_to_followersFragment 0x7f0a006f
int id action_myProfileFragment_to_followingsFragment 0x7f0a0070
int id action_myProfileFragment_to_privacyFragment 0x7f0a0071
int id action_myProfileFragment_to_resetPasswordFragment 0x7f0a0072
int id action_myProfileFragment_to_settingFragment 0x7f0a0073
int id action_myProfileFragment_to_topLuversFragment 0x7f0a0074
int id action_myProfileFragment_to_userFollowersFragment 0x7f0a0075
int id action_myProfileFragment_to_userFollowingsFragment 0x7f0a0076
int id action_myProfileFragment_to_userProfileFragment 0x7f0a0077
int id action_otherUserFollowingsFragment_to_userProfileFragment 0x7f0a0078
int id action_qrGiftingFragment_to_createQrGiftingFragment 0x7f0a0079
int id action_questFragment_to_myProfileFragment 0x7f0a007a
int id action_questFragment_to_sendLuvFragment 0x7f0a007b
int id action_registrationFragment_to_onboardFragment 0x7f0a007c
int id action_reportVideoFragment_to_homeFragment 0x7f0a007d
int id action_scanFragment_to_authorizeFragment 0x7f0a007e
int id action_scanFragment_to_myProfileFragment 0x7f0a007f
int id action_scanFragment_to_userProfileFragment 0x7f0a0080
int id action_searchReceiverFragment_to_selectReceiverFragment 0x7f0a0081
int id action_selectOpponentFragment_to_battleFragment 0x7f0a0082
int id action_selectReceiverFragment_to_searchReceiverFragment 0x7f0a0083
int id action_selectReceiverFragment_to_sendLuvQrScanFragment 0x7f0a0084
int id action_sendLuvFragment_to_selectReceiverFragment 0x7f0a0085
int id action_sendLuvQrScanFragment_to_selectReceiverFragment 0x7f0a0086
int id action_settingFragment_to_editProfileFragment 0x7f0a0087
int id action_settingFragment_to_privacyFragment 0x7f0a0088
int id action_settingFragment_to_resetPasswordFragment 0x7f0a0089
int id action_splashFragment_to_authSelectFragment 0x7f0a008a
int id action_startLuvBattleFragment_to_selectOpponentFragment 0x7f0a008b
int id action_text 0x7f0a008c
int id action_topGifterFilterFragment_to_topLuversFragment 0x7f0a008d
int id action_topLuversFragment_to_allBrandsFragment 0x7f0a008e
int id action_topLuversFragment_to_topGifterFilterFragment 0x7f0a008f
int id action_topLuversFragment_to_userProfileFragment 0x7f0a0090
int id action_userFollowersFragment_to_userProfileFragment 0x7f0a0091
int id action_userFollowingsFragment_to_userProfileFragment 0x7f0a0092
int id action_userProfileFragment_self 0x7f0a0093
int id action_userProfileFragment_to_otherUserFollowingsFragment 0x7f0a0094
int id action_userProfileFragment_to_sendLuvFragment 0x7f0a0095
int id action_userStoryFragment_to_reportVideoFragment 0x7f0a0096
int id action_withdrawPaymentSelectFragment_to_withdrawAmountSelectFragment 0x7f0a0097
int id actions 0x7f0a0098
int id activity_chooser_view_content 0x7f0a0099
int id add 0x7f0a009a
int id adjust_height 0x7f0a009b
int id adjust_width 0x7f0a009c
int id alertFragment 0x7f0a009d
int id alertTitle 0x7f0a009e
int id aligned 0x7f0a009f
int id all 0x7f0a00a0
int id allBrandsFragment 0x7f0a00a1
int id allFields 0x7f0a00a2
int id always 0x7f0a00a3
int id androidx_window_activity_scope 0x7f0a00a4
int id animateToEnd 0x7f0a00a5
int id animateToStart 0x7f0a00a6
int id appCompatButton 0x7f0a00a7
int id arc 0x7f0a00a8
int id asConfigured 0x7f0a00a9
int id async 0x7f0a00aa
int id authSelectFragment 0x7f0a00ab
int id authorizeFragment 0x7f0a00ac
int id auto 0x7f0a00ad
int id autoComplete 0x7f0a00ae
int id autoCompleteToEnd 0x7f0a00af
int id autoCompleteToStart 0x7f0a00b0
int id back 0x7f0a00b1
int id barrier 0x7f0a00b2
int id baseline 0x7f0a00b3
int id battleFragment 0x7f0a00b4
int id beginOnFirstDraw 0x7f0a00b5
int id beginning 0x7f0a00b6
int id blocking 0x7f0a00b7
int id bottom 0x7f0a00b8
int id bottomSheet 0x7f0a00b9
int id bounce 0x7f0a00ba
int id brandIcon 0x7f0a00bb
int id browser_actions_header_text 0x7f0a00bc
int id browser_actions_menu_item_icon 0x7f0a00bd
int id browser_actions_menu_item_text 0x7f0a00be
int id browser_actions_menu_items 0x7f0a00bf
int id browser_actions_menu_view 0x7f0a00c0
int id btnBattle 0x7f0a00c1
int id btnCamera 0x7f0a00c2
int id btnCameraClick 0x7f0a00c3
int id btnCancel 0x7f0a00c4
int id btnCloseMessage 0x7f0a00c5
int id btnCrate 0x7f0a00c6
int id btnDecline 0x7f0a00c7
int id btnDelete 0x7f0a00c8
int id btnDeleteAccount 0x7f0a00c9
int id btnDiscard 0x7f0a00ca
int id btnDrag 0x7f0a00cb
int id btnEarnDrop 0x7f0a00cc
int id btnEdit 0x7f0a00cd
int id btnEnableLocation 0x7f0a00ce
int id btnFilter 0x7f0a00cf
int id btnFollow 0x7f0a00d0
int id btnFollowers 0x7f0a00d1
int id btnFollowing 0x7f0a00d2
int id btnGallery 0x7f0a00d3
int id btnJoin 0x7f0a00d4
int id btnLanguage 0x7f0a00d5
int id btnLogin 0x7f0a00d6
int id btnLogout 0x7f0a00d7
int id btnNear 0x7f0a00d8
int id btnNext 0x7f0a00d9
int id btnOk 0x7f0a00da
int id btnOpen 0x7f0a00db
int id btnPasteText 0x7f0a00dc
int id btnPickNew 0x7f0a00dd
int id btnPlay 0x7f0a00de
int id btnPrivacy 0x7f0a00df
int id btnRandom 0x7f0a00e0
int id btnRecharge 0x7f0a00e1
int id btnRegister 0x7f0a00e2
int id btnRematch 0x7f0a00e3
int id btnRemove 0x7f0a00e4
int id btnReport 0x7f0a00e5
int id btnReportVideo 0x7f0a00e6
int id btnReset 0x7f0a00e7
int id btnSearch 0x7f0a00e8
int id btnSend 0x7f0a00e9
int id btnSendLuv 0x7f0a00ea
int id btnSendVideo 0x7f0a00eb
int id btnSetting 0x7f0a00ec
int id btnStartBattle 0x7f0a00ed
int id btnSubmit 0x7f0a00ee
int id btnSwitchBrand 0x7f0a00ef
int id btnSwitchPersonal 0x7f0a00f0
int id btnWithdraw 0x7f0a00f1
int id btnWorld 0x7f0a00f2
int id buttonPanel 0x7f0a00f3
int id camera 0x7f0a00f4
int id cameraSurfaceView 0x7f0a00f5
int id cancel_action 0x7f0a00f6
int id cancel_button 0x7f0a00f7
int id cardView 0x7f0a00f8
int id cardView2 0x7f0a00f9
int id cardView3 0x7f0a00fa
int id cardView4 0x7f0a00fb
int id cardView5 0x7f0a00fc
int id cardView6 0x7f0a00fd
int id cardView7 0x7f0a00fe
int id cardView8 0x7f0a00ff
int id cardView9 0x7f0a0100
int id cbNeedKey 0x7f0a0101
int id cbTerms 0x7f0a0102
int id cdvDiscard 0x7f0a0103
int id cdvFollow 0x7f0a0104
int id cdvFollowing 0x7f0a0105
int id cdvImage 0x7f0a0106
int id cdvReceiver 0x7f0a0107
int id cdvRepeat 0x7f0a0108
int id cdvUserName 0x7f0a0109
int id center 0x7f0a010a
int id centerCrop 0x7f0a010b
int id centerInside 0x7f0a010c
int id center_horizontal 0x7f0a010d
int id center_vertical 0x7f0a010e
int id chain 0x7f0a010f
int id chains 0x7f0a0110
int id checkbox 0x7f0a0111
int id checked 0x7f0a0112
int id chooseFavFragment 0x7f0a0113
int id chronometer 0x7f0a0114
int id circle_center 0x7f0a0115
int id circularProgressBar 0x7f0a0116
int id clChats 0x7f0a0117
int id clData 0x7f0a0118
int id clDeclined 0x7f0a0119
int id clFavs 0x7f0a011a
int id clSend 0x7f0a011b
int id clVideos 0x7f0a011c
int id clear_text 0x7f0a011d
int id clip_horizontal 0x7f0a011e
int id clip_vertical 0x7f0a011f
int id clockwise 0x7f0a0120
int id collapseActionView 0x7f0a0121
int id completedFields 0x7f0a0122
int id compress 0x7f0a0123
int id confirm_button 0x7f0a0124
int id constraintLayout 0x7f0a0125
int id constraintLayout16 0x7f0a0126
int id constraintLayout2 0x7f0a0127
int id constraintLayout3 0x7f0a0128
int id constraintLayout4 0x7f0a0129
int id constraintLayout5 0x7f0a012a
int id constraintLayout6 0x7f0a012b
int id constraintLayout7 0x7f0a012c
int id constraintLayout8 0x7f0a012d
int id container 0x7f0a012e
int id content 0x7f0a012f
int id contentPanel 0x7f0a0130
int id contiguous 0x7f0a0131
int id continuous 0x7f0a0132
int id coordinator 0x7f0a0133
int id cos 0x7f0a0134
int id counterButton 0x7f0a0135
int id counterclockwise 0x7f0a0136
int id cradle 0x7f0a0137
int id createQrGiftingFragment 0x7f0a0138
int id currentField 0x7f0a0139
int id custom 0x7f0a013a
int id customPanel 0x7f0a013b
int id cut 0x7f0a013c
int id cvClose 0x7f0a013d
int id cvGift 0x7f0a013e
int id cvImageView 0x7f0a013f
int id cvInfo 0x7f0a0140
int id cvInfoOpponent 0x7f0a0141
int id cvOppWins 0x7f0a0142
int id cvOpponent 0x7f0a0143
int id cvOptions 0x7f0a0144
int id cvOwner 0x7f0a0145
int id cvReport 0x7f0a0146
int id cvTabs 0x7f0a0147
int id cvTerms 0x7f0a0148
int id cvUser 0x7f0a0149
int id cvWins 0x7f0a014a
int id dark 0x7f0a014b
int id date_picker_actions 0x7f0a014c
int id decelerate 0x7f0a014d
int id decelerateAndComplete 0x7f0a014e
int id decor_content_parent 0x7f0a014f
int id default_activity_button 0x7f0a0150
int id deltaRelative 0x7f0a0151
int id design_bottom_sheet 0x7f0a0152
int id design_menu_item_action_area 0x7f0a0153
int id design_menu_item_action_area_stub 0x7f0a0154
int id design_menu_item_text 0x7f0a0155
int id design_navigation_view 0x7f0a0156
int id dialogCardView 0x7f0a0157
int id dialog_button 0x7f0a0158
int id dimensions 0x7f0a0159
int id direct 0x7f0a015a
int id disableHome 0x7f0a015b
int id disablePostScroll 0x7f0a015c
int id disableScroll 0x7f0a015d
int id disjoint 0x7f0a015e
int id doneButton 0x7f0a015f
int id dragDown 0x7f0a0160
int id dragEnd 0x7f0a0161
int id dragLeft 0x7f0a0162
int id dragRight 0x7f0a0163
int id dragStart 0x7f0a0164
int id dragUp 0x7f0a0165
int id dropdown_menu 0x7f0a0166
int id easeIn 0x7f0a0167
int id easeInOut 0x7f0a0168
int id easeOut 0x7f0a0169
int id editProfileFragment 0x7f0a016a
int id editText 0x7f0a016b
int id edit_message 0x7f0a016c
int id edit_query 0x7f0a016d
int id edtAge 0x7f0a016e
int id edtAmount 0x7f0a016f
int id edtBio 0x7f0a0170
int id edtCountry 0x7f0a0171
int id edtCurrency 0x7f0a0172
int id edtEmail 0x7f0a0173
int id edtFollow 0x7f0a0174
int id edtFollowers 0x7f0a0175
int id edtGender 0x7f0a0176
int id edtIndustry 0x7f0a0177
int id edtKey 0x7f0a0178
int id edtMethod 0x7f0a0179
int id edtName 0x7f0a017a
int id edtPass 0x7f0a017b
int id edtPassConfirm 0x7f0a017c
int id edtPaypalLink 0x7f0a017d
int id edtPerDay 0x7f0a017e
int id edtReporting 0x7f0a017f
int id edtSearch 0x7f0a0180
int id edtSponsored 0x7f0a0181
int id edtUsername 0x7f0a0182
int id edtWebsite 0x7f0a0183
int id elastic 0x7f0a0184
int id em_bigview_TextView 0x7f0a0185
int id em_bigview_imageView 0x7f0a0186
int id em_notification_content 0x7f0a0187
int id em_notification_date 0x7f0a0188
int id em_notification_icon 0x7f0a0189
int id em_notification_style 0x7f0a018a
int id em_notification_title 0x7f0a018b
int id embed 0x7f0a018c
int id end 0x7f0a018d
int id endToStart 0x7f0a018e
int id end_padder 0x7f0a018f
int id enterAlways 0x7f0a0190
int id enterAlwaysCollapsed 0x7f0a0191
int id exclamation_icon 0x7f0a0192
int id exitUntilCollapsed 0x7f0a0193
int id exo_ad_overlay 0x7f0a0194
int id exo_artwork 0x7f0a0195
int id exo_audio_track 0x7f0a0196
int id exo_basic_controls 0x7f0a0197
int id exo_bottom_bar 0x7f0a0198
int id exo_buffering 0x7f0a0199
int id exo_center_controls 0x7f0a019a
int id exo_check 0x7f0a019b
int id exo_content_frame 0x7f0a019c
int id exo_controller 0x7f0a019d
int id exo_controller_placeholder 0x7f0a019e
int id exo_controls_background 0x7f0a019f
int id exo_duration 0x7f0a01a0
int id exo_error_message 0x7f0a01a1
int id exo_extra_controls 0x7f0a01a2
int id exo_extra_controls_scroll_view 0x7f0a01a3
int id exo_ffwd 0x7f0a01a4
int id exo_ffwd_with_amount 0x7f0a01a5
int id exo_fullscreen 0x7f0a01a6
int id exo_icon 0x7f0a01a7
int id exo_main_text 0x7f0a01a8
int id exo_minimal_controls 0x7f0a01a9
int id exo_minimal_fullscreen 0x7f0a01aa
int id exo_next 0x7f0a01ab
int id exo_overflow_hide 0x7f0a01ac
int id exo_overflow_show 0x7f0a01ad
int id exo_overlay 0x7f0a01ae
int id exo_pause 0x7f0a01af
int id exo_play 0x7f0a01b0
int id exo_play_pause 0x7f0a01b1
int id exo_playback_speed 0x7f0a01b2
int id exo_position 0x7f0a01b3
int id exo_prev 0x7f0a01b4
int id exo_progress 0x7f0a01b5
int id exo_progress_placeholder 0x7f0a01b6
int id exo_repeat_toggle 0x7f0a01b7
int id exo_rew 0x7f0a01b8
int id exo_rew_with_amount 0x7f0a01b9
int id exo_settings 0x7f0a01ba
int id exo_settings_listview 0x7f0a01bb
int id exo_shuffle 0x7f0a01bc
int id exo_shutter 0x7f0a01bd
int id exo_sub_text 0x7f0a01be
int id exo_subtitle 0x7f0a01bf
int id exo_subtitles 0x7f0a01c0
int id exo_text 0x7f0a01c1
int id exo_time 0x7f0a01c2
int id exo_track_selection_view 0x7f0a01c3
int id exo_vr 0x7f0a01c4
int id expand_activities_button 0x7f0a01c5
int id expanded_menu 0x7f0a01c6
int id fade 0x7f0a01c7
int id fill 0x7f0a01c8
int id fill_horizontal 0x7f0a01c9
int id fill_vertical 0x7f0a01ca
int id filled 0x7f0a01cb
int id fit 0x7f0a01cc
int id fitCenter 0x7f0a01cd
int id fitEnd 0x7f0a01ce
int id fitStart 0x7f0a01cf
int id fitToContents 0x7f0a01d0
int id fitXY 0x7f0a01d1
int id fixed 0x7f0a01d2
int id fixed_height 0x7f0a01d3
int id fixed_width 0x7f0a01d4
int id flip 0x7f0a01d5
int id floating 0x7f0a01d6
int id focusMarkerContainer 0x7f0a01d7
int id followersFragment 0x7f0a01d8
int id followingsFragment 0x7f0a01d9
int id forever 0x7f0a01da
int id fragment_container_view_tag 0x7f0a01db
int id front 0x7f0a01dc
int id fullscreen_header 0x7f0a01dd
int id ghost_view 0x7f0a01de
int id ghost_view_holder 0x7f0a01df
int id giftIcon 0x7f0a01e0
int id giftInfo 0x7f0a01e1
int id giftReceivedDetailsFragment 0x7f0a01e2
int id giftSentDetailsFragment 0x7f0a01e3
int id giftValue 0x7f0a01e4
int id give_us_a_review_landmine_button 0x7f0a01e5
int id give_us_a_review_landmine_main_layout 0x7f0a01e6
int id give_us_a_review_landmine_text_1 0x7f0a01e7
int id give_us_a_review_landmine_text_2 0x7f0a01e8
int id glide_custom_view_target_tag 0x7f0a01e9
int id gone 0x7f0a01ea
int id graph 0x7f0a01eb
int id graph_wrap 0x7f0a01ec
int id group_divider 0x7f0a01ed
int id groups 0x7f0a01ee
int id header_title 0x7f0a01ef
int id hideable 0x7f0a01f0
int id highest 0x7f0a01f1
int id home 0x7f0a01f2
int id homeAsUp 0x7f0a01f3
int id homeFragment 0x7f0a01f4
int id honorRequest 0x7f0a01f5
int id horizontal 0x7f0a01f6
int id hsvGifts 0x7f0a01f7
int id icon 0x7f0a01f8
int id icon_group 0x7f0a01f9
int id icon_only 0x7f0a01fa
int id ifRoom 0x7f0a01fb
int id ignore 0x7f0a01fc
int id ignoreRequest 0x7f0a01fd
int id image 0x7f0a01fe
int id imageView 0x7f0a01ff
int id imageView1 0x7f0a0200
int id imageView10 0x7f0a0201
int id imageView11 0x7f0a0202
int id imageView12 0x7f0a0203
int id imageView13 0x7f0a0204
int id imageView15 0x7f0a0205
int id imageView16 0x7f0a0206
int id imageView18 0x7f0a0207
int id imageView19 0x7f0a0208
int id imageView2 0x7f0a0209
int id imageView20 0x7f0a020a
int id imageView3 0x7f0a020b
int id imageView4 0x7f0a020c
int id imageView5 0x7f0a020d
int id imageView6 0x7f0a020e
int id imageView7 0x7f0a020f
int id imageView8 0x7f0a0210
int id imageView9 0x7f0a0211
int id includeLocationShare 0x7f0a0212
int id includeNotification 0x7f0a0213
int id includeNotificationShare 0x7f0a0214
int id indeterminate 0x7f0a0215
int id indicator 0x7f0a0216
int id info 0x7f0a0217
int id innerRadioGroup 0x7f0a0218
int id invisible 0x7f0a0219
int id inward 0x7f0a021a
int id is_pooling_container_tag 0x7f0a021b
int id italic 0x7f0a021c
int id item_touch_helper_previous_elevation 0x7f0a021d
int id ivActionImage 0x7f0a021e
int id ivActionSent 0x7f0a021f
int id ivAdd 0x7f0a0220
int id ivAddCode 0x7f0a0221
int id ivBack 0x7f0a0222
int id ivCheck 0x7f0a0223
int id ivClose 0x7f0a0224
int id ivCurrency 0x7f0a0225
int id ivDollar 0x7f0a0226
int id ivFav 0x7f0a0227
int id ivFrom 0x7f0a0228
int id ivGift 0x7f0a0229
int id ivGifter 0x7f0a022a
int id ivHeart 0x7f0a022b
int id ivImage 0x7f0a022c
int id ivMyImage 0x7f0a022d
int id ivName 0x7f0a022e
int id ivNext 0x7f0a022f
int id ivOptions 0x7f0a0230
int id ivPerson 0x7f0a0231
int id ivQrGifting 0x7f0a0232
int id ivQrReceiving 0x7f0a0233
int id ivRepeat 0x7f0a0234
int id ivSend 0x7f0a0235
int id ivSending 0x7f0a0236
int id ivShare 0x7f0a0237
int id ivTopGiftImage 0x7f0a0238
int id ivUser 0x7f0a0239
int id ivUserImage 0x7f0a023a
int id ivUserReceived 0x7f0a023b
int id ivWinnerOpponent 0x7f0a023c
int id ivWinnerOwner 0x7f0a023d
int id iv_blank 0x7f0a023e
int id iv_followers 0x7f0a023f
int id iv_following 0x7f0a0240
int id iv_gift 0x7f0a0241
int id ivg 0x7f0a0242
int id ivow 0x7f0a0243
int id ivw 0x7f0a0244
int id jumpToEnd 0x7f0a0245
int id jumpToStart 0x7f0a0246
int id labeled 0x7f0a0247
int id layout 0x7f0a0248
int id layoutName 0x7f0a0249
int id layoutStories 0x7f0a024a
int id lazy 0x7f0a024b
int id left 0x7f0a024c
int id leftToRight 0x7f0a024d
int id light 0x7f0a024e
int id line1 0x7f0a024f
int id line3 0x7f0a0250
int id linear 0x7f0a0251
int id linearLayout 0x7f0a0252
int id linearLayout1 0x7f0a0253
int id linearLayout2 0x7f0a0254
int id linearLayout3 0x7f0a0255
int id linearLayout4 0x7f0a0256
int id linearLayout5 0x7f0a0257
int id linearLayout6 0x7f0a0258
int id linearLayout7 0x7f0a0259
int id linearLayoutCompat 0x7f0a025a
int id listMode 0x7f0a025b
int id list_item 0x7f0a025c
int id liveStreamingFragment 0x7f0a025d
int id llBackground 0x7f0a025e
int id llBalance 0x7f0a025f
int id llBrand 0x7f0a0260
int id llBrandViews 0x7f0a0261
int id llButtons 0x7f0a0262
int id llCommunity 0x7f0a0263
int id llFollower 0x7f0a0264
int id llFollowersFollowing 0x7f0a0265
int id llFollowing 0x7f0a0266
int id llInfo 0x7f0a0267
int id llNoAlerts 0x7f0a0268
int id llNoChat 0x7f0a0269
int id llNoFavs 0x7f0a026a
int id llNoItems 0x7f0a026b
int id llPass 0x7f0a026c
int id llPassConfirm 0x7f0a026d
int id llPayment 0x7f0a026e
int id llQr 0x7f0a026f
int id llResultOpponent 0x7f0a0270
int id llResultOwner 0x7f0a0271
int id llSwitchBrand 0x7f0a0272
int id llTheWorld 0x7f0a0273
int id llUserProfile 0x7f0a0274
int id locale 0x7f0a0275
int id loginFragment 0x7f0a0276
int id lowest 0x7f0a0277
int id ltr 0x7f0a0278
int id luvDropFragment 0x7f0a0279
int id luvDropReceivedDetailsFragment 0x7f0a027a
int id m3_side_sheet 0x7f0a027b
int id mainLayout 0x7f0a027c
int id main_nav 0x7f0a027d
int id marquee 0x7f0a027e
int id masked 0x7f0a027f
int id match_parent 0x7f0a0280
int id materialButton 0x7f0a0281
int id materialCardView 0x7f0a0282
int id material_clock_display 0x7f0a0283
int id material_clock_display_and_toggle 0x7f0a0284
int id material_clock_face 0x7f0a0285
int id material_clock_hand 0x7f0a0286
int id material_clock_level 0x7f0a0287
int id material_clock_period_am_button 0x7f0a0288
int id material_clock_period_pm_button 0x7f0a0289
int id material_clock_period_toggle 0x7f0a028a
int id material_hour_text_input 0x7f0a028b
int id material_hour_tv 0x7f0a028c
int id material_label 0x7f0a028d
int id material_minute_text_input 0x7f0a028e
int id material_minute_tv 0x7f0a028f
int id material_textinput_timepicker 0x7f0a0290
int id material_timepicker_cancel_button 0x7f0a0291
int id material_timepicker_container 0x7f0a0292
int id material_timepicker_mode_button 0x7f0a0293
int id material_timepicker_ok_button 0x7f0a0294
int id material_timepicker_view 0x7f0a0295
int id material_value_index 0x7f0a0296
int id matrix 0x7f0a0297
int id max1080p 0x7f0a0298
int id max2160p 0x7f0a0299
int id max480p 0x7f0a029a
int id max720p 0x7f0a029b
int id media_actions 0x7f0a029c
int id media_controller_compat_view_tag 0x7f0a029d
int id message 0x7f0a029e
int id middle 0x7f0a029f
int id mini 0x7f0a02a0
int id month_grid 0x7f0a02a1
int id month_navigation_bar 0x7f0a02a2
int id month_navigation_fragment_toggle 0x7f0a02a3
int id month_navigation_next 0x7f0a02a4
int id month_navigation_previous 0x7f0a02a5
int id month_title 0x7f0a02a6
int id motion_base 0x7f0a02a7
int id mt 0x7f0a02a8
int id mtrl_anchor_parent 0x7f0a02a9
int id mtrl_calendar_day_selector_frame 0x7f0a02aa
int id mtrl_calendar_days_of_week 0x7f0a02ab
int id mtrl_calendar_frame 0x7f0a02ac
int id mtrl_calendar_main_pane 0x7f0a02ad
int id mtrl_calendar_months 0x7f0a02ae
int id mtrl_calendar_selection_frame 0x7f0a02af
int id mtrl_calendar_text_input_frame 0x7f0a02b0
int id mtrl_calendar_year_selector_frame 0x7f0a02b1
int id mtrl_card_checked_layer_id 0x7f0a02b2
int id mtrl_child_content_container 0x7f0a02b3
int id mtrl_internal_children_alpha_tag 0x7f0a02b4
int id mtrl_motion_snapshot_view 0x7f0a02b5
int id mtrl_picker_fullscreen 0x7f0a02b6
int id mtrl_picker_header 0x7f0a02b7
int id mtrl_picker_header_selection_text 0x7f0a02b8
int id mtrl_picker_header_title_and_selection 0x7f0a02b9
int id mtrl_picker_header_toggle 0x7f0a02ba
int id mtrl_picker_text_input_date 0x7f0a02bb
int id mtrl_picker_text_input_range_end 0x7f0a02bc
int id mtrl_picker_text_input_range_start 0x7f0a02bd
int id mtrl_picker_title_text 0x7f0a02be
int id mtrl_view_tag_bottom_padding 0x7f0a02bf
int id multiply 0x7f0a02c0
int id myChestQrFragment 0x7f0a02c1
int id myProfileFragment 0x7f0a02c2
int id nav_controller_view_tag 0x7f0a02c3
int id nav_host_fragment_activity_main 0x7f0a02c4
int id nav_host_fragment_container 0x7f0a02c5
int id nav_view 0x7f0a02c6
int id navigation_bar_item_active_indicator_view 0x7f0a02c7
int id navigation_bar_item_icon_container 0x7f0a02c8
int id navigation_bar_item_icon_view 0x7f0a02c9
int id navigation_bar_item_labels_group 0x7f0a02ca
int id navigation_bar_item_large_label_view 0x7f0a02cb
int id navigation_bar_item_small_label_view 0x7f0a02cc
int id navigation_header_container 0x7f0a02cd
int id nearbyUsersFragment 0x7f0a02ce
int id never 0x7f0a02cf
int id nextButton 0x7f0a02d0
int id noFields 0x7f0a02d1
int id noScroll 0x7f0a02d2
int id none 0x7f0a02d3
int id normal 0x7f0a02d4
int id notification_background 0x7f0a02d5
int id notification_main_column 0x7f0a02d6
int id notification_main_column_container 0x7f0a02d7
int id off 0x7f0a02d8
int id on 0x7f0a02d9
int id onboardFirstPageFrag 0x7f0a02da
int id onboardFourthPageFrag 0x7f0a02db
int id onboardFragment 0x7f0a02dc
int id onboardSecondPageFrag 0x7f0a02dd
int id onboardThirdPageFrag 0x7f0a02de
int id one 0x7f0a02df
int id openedLuvChestFragment 0x7f0a02e0
int id openedLuvCrateFragment 0x7f0a02e1
int id opponentCount 0x7f0a02e2
int id opponentGift 0x7f0a02e3
int id opponentVideoView 0x7f0a02e4
int id opponentWins 0x7f0a02e5
int id otherUserFollowingsFragment 0x7f0a02e6
int id outline 0x7f0a02e7
int id outward 0x7f0a02e8
int id ownerCount 0x7f0a02e9
int id ownerGift 0x7f0a02ea
int id ownerWins 0x7f0a02eb
int id packed 0x7f0a02ec
int id parallax 0x7f0a02ed
int id parent 0x7f0a02ee
int id parentPanel 0x7f0a02ef
int id parentRelative 0x7f0a02f0
int id parent_matrix 0x7f0a02f1
int id password_toggle 0x7f0a02f2
int id path 0x7f0a02f3
int id pathRelative 0x7f0a02f4
int id pbBattle 0x7f0a02f5
int id peekHeight 0x7f0a02f6
int id percent 0x7f0a02f7
int id pgBar 0x7f0a02f8
int id photoLib 0x7f0a02f9
int id picture 0x7f0a02fa
int id pin 0x7f0a02fb
int id pinField 0x7f0a02fc
int id pinch 0x7f0a02fd
int id playerView 0x7f0a02fe
int id pooling_container_listener_holder_tag 0x7f0a02ff
int id position 0x7f0a0300
int id postLayout 0x7f0a0301
int id preRecordingFragment 0x7f0a0302
int id pressed 0x7f0a0303
int id priceText 0x7f0a0304
int id privacyFragment 0x7f0a0305
int id progressBarContainer 0x7f0a0306
int id progress_circular 0x7f0a0307
int id progress_horizontal 0x7f0a0308
int id qrGiftingFragment 0x7f0a0309
int id qrKey 0x7f0a030a
int id qrReceivingFragment 0x7f0a030b
int id qrView 0x7f0a030c
int id questCrateFragment 0x7f0a030d
int id questFragment 0x7f0a030e
int id qvga 0x7f0a030f
int id radio 0x7f0a0310
int id radioButtonSelector 0x7f0a0311
int id ratio 0x7f0a0312
int id rcvBattleChat 0x7f0a0313
int id rcvBrands 0x7f0a0314
int id rcvFav 0x7f0a0315
int id rcvFollowers 0x7f0a0316
int id rcvFollowings 0x7f0a0317
int id rcvGift 0x7f0a0318
int id rcvGifters 0x7f0a0319
int id rcvGifts 0x7f0a031a
int id rcvGiftsReceived 0x7f0a031b
int id rcvHistory 0x7f0a031c
int id rcvLanguage 0x7f0a031d
int id rcvNearby 0x7f0a031e
int id rcvNewAlerts 0x7f0a031f
int id rcvPlans 0x7f0a0320
int id rcvRange 0x7f0a0321
int id rcvReadAlerts 0x7f0a0322
int id rcvStorie 0x7f0a0323
int id rcvTransactions 0x7f0a0324
int id rcvUsers 0x7f0a0325
int id rechargeDetailsFragment 0x7f0a0326
int id rechargeFragment 0x7f0a0327
int id rectangles 0x7f0a0328
int id referralAwardFragment 0x7f0a0329
int id registrationFragment 0x7f0a032a
int id reportVideoFragment 0x7f0a032b
int id resetPasswordFragment 0x7f0a032c
int id reverseSawtooth 0x7f0a032d
int id right 0x7f0a032e
int id rightToLeft 0x7f0a032f
int id right_icon 0x7f0a0330
int id right_side 0x7f0a0331
int id rlView 0x7f0a0332
int id rounded 0x7f0a0333
int id row_index_key 0x7f0a0334
int id rtl 0x7f0a0335
int id saveButton 0x7f0a0336
int id save_non_transition_alpha 0x7f0a0337
int id save_overlay_view 0x7f0a0338
int id sawtooth 0x7f0a0339
int id scale 0x7f0a033a
int id scanFragment 0x7f0a033b
int id screen 0x7f0a033c
int id scroll 0x7f0a033d
int id scrollIndicatorDown 0x7f0a033e
int id scrollIndicatorUp 0x7f0a033f
int id scrollView 0x7f0a0340
int id scrollable 0x7f0a0341
int id searchReceiverFragment 0x7f0a0342
int id search_badge 0x7f0a0343
int id search_bar 0x7f0a0344
int id search_bar_text_view 0x7f0a0345
int id search_button 0x7f0a0346
int id search_close_btn 0x7f0a0347
int id search_edit_frame 0x7f0a0348
int id search_go_btn 0x7f0a0349
int id search_mag_icon 0x7f0a034a
int id search_plate 0x7f0a034b
int id search_src_text 0x7f0a034c
int id search_view_background 0x7f0a034d
int id search_view_clear_button 0x7f0a034e
int id search_view_content_container 0x7f0a034f
int id search_view_divider 0x7f0a0350
int id search_view_dummy_toolbar 0x7f0a0351
int id search_view_edit_text 0x7f0a0352
int id search_view_header_container 0x7f0a0353
int id search_view_root 0x7f0a0354
int id search_view_scrim 0x7f0a0355
int id search_view_search_prefix 0x7f0a0356
int id search_view_status_bar_spacer 0x7f0a0357
int id search_view_toolbar 0x7f0a0358
int id search_view_toolbar_container 0x7f0a0359
int id search_voice_btn 0x7f0a035a
int id selectOpponentFragment 0x7f0a035b
int id selectReceiverFragment 0x7f0a035c
int id select_dialog_listview 0x7f0a035d
int id selected 0x7f0a035e
int id selection_type 0x7f0a035f
int id sendLuvFragment 0x7f0a0360
int id sendLuvGetLuvFragment 0x7f0a0361
int id sendLuvQrScanFragment 0x7f0a0362
int id sendMessage 0x7f0a0363
int id senderText 0x7f0a0364
int id settingFragment 0x7f0a0365
int id shortcut 0x7f0a0366
int id showCustom 0x7f0a0367
int id showHome 0x7f0a0368
int id showTitle 0x7f0a0369
int id sin 0x7f0a036a
int id skipButton 0x7f0a036b
int id skipCollapsed 0x7f0a036c
int id slide 0x7f0a036d
int id sliding_pane_detail_container 0x7f0a036e
int id sliding_pane_layout 0x7f0a036f
int id snackbar_action 0x7f0a0370
int id snackbar_text 0x7f0a0371
int id snap 0x7f0a0372
int id snapMargins 0x7f0a0373
int id spacer 0x7f0a0374
int id special_effects_controller_view_tag 0x7f0a0375
int id speed 0x7f0a0376
int id spherical_gl_surface_view 0x7f0a0377
int id splashFragment 0x7f0a0378
int id splashscreen_icon_view 0x7f0a0379
int id spline 0x7f0a037a
int id split_action_bar 0x7f0a037b
int id spread 0x7f0a037c
int id spread_inside 0x7f0a037d
int id square 0x7f0a037e
int id src_atop 0x7f0a037f
int id src_in 0x7f0a0380
int id src_over 0x7f0a0381
int id standard 0x7f0a0382
int id start 0x7f0a0383
int id startHorizontal 0x7f0a0384
int id startLuvBattleFragment 0x7f0a0385
int id startToEnd 0x7f0a0386
int id startVertical 0x7f0a0387
int id staticLayout 0x7f0a0388
int id staticPostLayout 0x7f0a0389
int id status_bar_latest_event_content 0x7f0a038a
int id still 0x7f0a038b
int id stop 0x7f0a038c
int id stretch 0x7f0a038d
int id strict 0x7f0a038e
int id submenuarrow 0x7f0a038f
int id submit_area 0x7f0a0390
int id surface_view 0x7f0a0391
int id tabLayout 0x7f0a0392
int id tabLayout1 0x7f0a0393
int id tabLayout2 0x7f0a0394
int id tabMode 0x7f0a0395
int id tag_accessibility_actions 0x7f0a0396
int id tag_accessibility_clickable_spans 0x7f0a0397
int id tag_accessibility_heading 0x7f0a0398
int id tag_accessibility_pane_title 0x7f0a0399
int id tag_on_apply_window_listener 0x7f0a039a
int id tag_on_receive_content_listener 0x7f0a039b
int id tag_on_receive_content_mime_types 0x7f0a039c
int id tag_screen_reader_focusable 0x7f0a039d
int id tag_state_description 0x7f0a039e
int id tag_transition_group 0x7f0a039f
int id tag_unhandled_key_event_manager 0x7f0a03a0
int id tag_unhandled_key_listeners 0x7f0a03a1
int id tag_window_insets_animation_callback 0x7f0a03a2
int id tap 0x7f0a03a3
int id tapWithMarker 0x7f0a03a4
int id text 0x7f0a03a5
int id text2 0x7f0a03a6
int id textEnd 0x7f0a03a7
int id textInputLayout 0x7f0a03a8
int id textInputLayout2 0x7f0a03a9
int id textInputLayout3 0x7f0a03aa
int id textSpacerNoButtons 0x7f0a03ab
int id textSpacerNoTitle 0x7f0a03ac
int id textStart 0x7f0a03ad
int id textTop 0x7f0a03ae
int id textView 0x7f0a03af
int id textView10 0x7f0a03b0
int id textView11 0x7f0a03b1
int id textView12 0x7f0a03b2
int id textView126 0x7f0a03b3
int id textView127 0x7f0a03b4
int id textView129 0x7f0a03b5
int id textView13 0x7f0a03b6
int id textView14 0x7f0a03b7
int id textView15 0x7f0a03b8
int id textView16 0x7f0a03b9
int id textView17 0x7f0a03ba
int id textView18 0x7f0a03bb
int id textView19 0x7f0a03bc
int id textView2 0x7f0a03bd
int id textView20 0x7f0a03be
int id textView21 0x7f0a03bf
int id textView22 0x7f0a03c0
int id textView23 0x7f0a03c1
int id textView24 0x7f0a03c2
int id textView25 0x7f0a03c3
int id textView26 0x7f0a03c4
int id textView27 0x7f0a03c5
int id textView28 0x7f0a03c6
int id textView29 0x7f0a03c7
int id textView3 0x7f0a03c8
int id textView30 0x7f0a03c9
int id textView31 0x7f0a03ca
int id textView32 0x7f0a03cb
int id textView33 0x7f0a03cc
int id textView34 0x7f0a03cd
int id textView35 0x7f0a03ce
int id textView36 0x7f0a03cf
int id textView37 0x7f0a03d0
int id textView38 0x7f0a03d1
int id textView39 0x7f0a03d2
int id textView4 0x7f0a03d3
int id textView43 0x7f0a03d4
int id textView44 0x7f0a03d5
int id textView5 0x7f0a03d6
int id textView6 0x7f0a03d7
int id textView7 0x7f0a03d8
int id textView8 0x7f0a03d9
int id textView9 0x7f0a03da
int id text_count 0x7f0a03db
int id text_input_end_icon 0x7f0a03dc
int id text_input_error_icon 0x7f0a03dd
int id text_input_start_icon 0x7f0a03de
int id textinput_counter 0x7f0a03df
int id textinput_error 0x7f0a03e0
int id textinput_helper_text 0x7f0a03e1
int id textinput_placeholder 0x7f0a03e2
int id textinput_prefix_text 0x7f0a03e3
int id textinput_suffix_text 0x7f0a03e4
int id texture_view 0x7f0a03e5
int id time 0x7f0a03e6
int id title 0x7f0a03e7
int id titleDividerNoCustom 0x7f0a03e8
int id title_template 0x7f0a03e9
int id toggle 0x7f0a03ea
int id top 0x7f0a03eb
int id topGifterFilterFragment 0x7f0a03ec
int id topLuversFragment 0x7f0a03ed
int id topPanel 0x7f0a03ee
int id torch 0x7f0a03ef
int id touch_outside 0x7f0a03f0
int id transitionToEnd 0x7f0a03f1
int id transitionToStart 0x7f0a03f2
int id transition_current_scene 0x7f0a03f3
int id transition_layout_save 0x7f0a03f4
int id transition_position 0x7f0a03f5
int id transition_scene_layoutid_cache 0x7f0a03f6
int id transition_transform 0x7f0a03f7
int id triangle 0x7f0a03f8
int id tvAction 0x7f0a03f9
int id tvAmount 0x7f0a03fa
int id tvAmountFirst 0x7f0a03fb
int id tvApple 0x7f0a03fc
int id tvBalance 0x7f0a03fd
int id tvBio 0x7f0a03fe
int id tvBioCount 0x7f0a03ff
int id tvBrand 0x7f0a0400
int id tvBrandName 0x7f0a0401
int id tvCompanyBio 0x7f0a0402
int id tvCountdown 0x7f0a0403
int id tvDailyReward 0x7f0a0404
int id tvDate 0x7f0a0405
int id tvDeclined 0x7f0a0406
int id tvDiamonds 0x7f0a0407
int id tvError 0x7f0a0408
int id tvFollow 0x7f0a0409
int id tvFollowers 0x7f0a040a
int id tvFollowing 0x7f0a040b
int id tvForgot 0x7f0a040c
int id tvHearts 0x7f0a040d
int id tvIndustry 0x7f0a040e
int id tvInfo 0x7f0a040f
int id tvInviteMore 0x7f0a0410
int id tvLimit 0x7f0a0411
int id tvLuv 0x7f0a0412
int id tvMainFirst 0x7f0a0413
int id tvMainSecond 0x7f0a0414
int id tvMyName 0x7f0a0415
int id tvName 0x7f0a0416
int id tvNoCode 0x7f0a0417
int id tvNumber 0x7f0a0418
int id tvPay 0x7f0a0419
int id tvPerson 0x7f0a041a
int id tvRange 0x7f0a041b
int id tvRank 0x7f0a041c
int id tvReceiverUsername 0x7f0a041d
int id tvSeeAll 0x7f0a041e
int id tvSending 0x7f0a041f
int id tvSent 0x7f0a0420
int id tvSentGift 0x7f0a0421
int id tvSponsored 0x7f0a0422
int id tvStatus 0x7f0a0423
int id tvTagNew 0x7f0a0424
int id tvTime 0x7f0a0425
int id tvTimerTitle 0x7f0a0426
int id tvTopDaily 0x7f0a0427
int id tvTopSent 0x7f0a0428
int id tvTopUser 0x7f0a0429
int id tvTransaction 0x7f0a042a
int id tvType 0x7f0a042b
int id tvUsd 0x7f0a042c
int id tvUsedLimit 0x7f0a042d
int id tvUserName 0x7f0a042e
int id tvUsername 0x7f0a042f
int id tvUsernameChat 0x7f0a0430
int id tvUsernameId 0x7f0a0431
int id tvUsernameTime 0x7f0a0432
int id tvWebsite 0x7f0a0433
int id tvWinnerOpponent 0x7f0a0434
int id tvWinnerOwner 0x7f0a0435
int id tv_from 0x7f0a0436
int id tvp 0x7f0a0437
int id unchecked 0x7f0a0438
int id uniform 0x7f0a0439
int id unlabeled 0x7f0a043a
int id up 0x7f0a043b
int id useLogo 0x7f0a043c
int id userFollowersFragment 0x7f0a043d
int id userFollowingsFragment 0x7f0a043e
int id userProfileFragment 0x7f0a043f
int id userStoryFragment 0x7f0a0440
int id vertical 0x7f0a0441
int id videoPlayerWithDetailsFragment 0x7f0a0442
int id videoView 0x7f0a0443
int id videoView12 0x7f0a0444
int id video_decoder_gl_surface_view 0x7f0a0445
int id view 0x7f0a0446
int id view1 0x7f0a0447
int id view2 0x7f0a0448
int id view3 0x7f0a0449
int id view4 0x7f0a044a
int id view5 0x7f0a044b
int id view6 0x7f0a044c
int id view7 0x7f0a044d
int id viewNone 0x7f0a044e
int id viewPager 0x7f0a044f
int id viewSelected 0x7f0a0450
int id viewTerms 0x7f0a0451
int id view_offset_helper 0x7f0a0452
int id view_tree_lifecycle_owner 0x7f0a0453
int id view_tree_on_back_pressed_dispatcher_owner 0x7f0a0454
int id view_tree_saved_state_registry_owner 0x7f0a0455
int id view_tree_view_model_store_owner 0x7f0a0456
int id visible 0x7f0a0457
int id visible_removing_fragment_view_tag 0x7f0a0458
int id when_playing 0x7f0a0459
int id wide 0x7f0a045a
int id withText 0x7f0a045b
int id with_icon 0x7f0a045c
int id withdrawAmountSelectFragment 0x7f0a045d
int id withdrawDetailsFragment 0x7f0a045e
int id withdrawPaymentSelectFragment 0x7f0a045f
int id withinBounds 0x7f0a0460
int id wrap 0x7f0a0461
int id wrap_content 0x7f0a0462
int id zoom 0x7f0a0463
int id zxing_back_button 0x7f0a0464
int id zxing_barcode_scanner 0x7f0a0465
int id zxing_barcode_surface 0x7f0a0466
int id zxing_camera_closed 0x7f0a0467
int id zxing_camera_error 0x7f0a0468
int id zxing_decode 0x7f0a0469
int id zxing_decode_failed 0x7f0a046a
int id zxing_decode_succeeded 0x7f0a046b
int id zxing_possible_result_points 0x7f0a046c
int id zxing_preview_failed 0x7f0a046d
int id zxing_prewiew_size_ready 0x7f0a046e
int id zxing_status_view 0x7f0a046f
int id zxing_viewfinder_view 0x7f0a0470
int integer abc_config_activityDefaultDur 0x7f0b0000
int integer abc_config_activityShortDur 0x7f0b0001
int integer app_bar_elevation_anim_duration 0x7f0b0002
int integer bottom_sheet_slide_duration 0x7f0b0003
int integer cancel_button_image_alpha 0x7f0b0004
int integer config_navAnimTime 0x7f0b0005
int integer config_tooltipAnimTime 0x7f0b0006
int integer default_icon_animation_duration 0x7f0b0007
int integer design_snackbar_text_max_lines 0x7f0b0008
int integer design_tab_indicator_anim_duration_ms 0x7f0b0009
int integer exo_media_button_opacity_percentage_disabled 0x7f0b000a
int integer exo_media_button_opacity_percentage_enabled 0x7f0b000b
int integer google_play_services_version 0x7f0b000c
int integer hide_password_duration 0x7f0b000d
int integer m3_btn_anim_delay_ms 0x7f0b000e
int integer m3_btn_anim_duration_ms 0x7f0b000f
int integer m3_card_anim_delay_ms 0x7f0b0010
int integer m3_card_anim_duration_ms 0x7f0b0011
int integer m3_chip_anim_duration 0x7f0b0012
int integer m3_sys_motion_duration_extra_long1 0x7f0b0013
int integer m3_sys_motion_duration_extra_long2 0x7f0b0014
int integer m3_sys_motion_duration_extra_long3 0x7f0b0015
int integer m3_sys_motion_duration_extra_long4 0x7f0b0016
int integer m3_sys_motion_duration_long1 0x7f0b0017
int integer m3_sys_motion_duration_long2 0x7f0b0018
int integer m3_sys_motion_duration_long3 0x7f0b0019
int integer m3_sys_motion_duration_long4 0x7f0b001a
int integer m3_sys_motion_duration_medium1 0x7f0b001b
int integer m3_sys_motion_duration_medium2 0x7f0b001c
int integer m3_sys_motion_duration_medium3 0x7f0b001d
int integer m3_sys_motion_duration_medium4 0x7f0b001e
int integer m3_sys_motion_duration_short1 0x7f0b001f
int integer m3_sys_motion_duration_short2 0x7f0b0020
int integer m3_sys_motion_duration_short3 0x7f0b0021
int integer m3_sys_motion_duration_short4 0x7f0b0022
int integer material_motion_duration_long_1 0x7f0b0023
int integer material_motion_duration_long_2 0x7f0b0024
int integer material_motion_duration_medium_1 0x7f0b0025
int integer material_motion_duration_medium_2 0x7f0b0026
int integer material_motion_duration_short_1 0x7f0b0027
int integer material_motion_duration_short_2 0x7f0b0028
int integer material_motion_path 0x7f0b0029
int integer mtrl_badge_max_character_count 0x7f0b002a
int integer mtrl_btn_anim_delay_ms 0x7f0b002b
int integer mtrl_btn_anim_duration_ms 0x7f0b002c
int integer mtrl_calendar_header_orientation 0x7f0b002d
int integer mtrl_calendar_selection_text_lines 0x7f0b002e
int integer mtrl_calendar_year_selector_span 0x7f0b002f
int integer mtrl_card_anim_delay_ms 0x7f0b0030
int integer mtrl_card_anim_duration_ms 0x7f0b0031
int integer mtrl_chip_anim_duration 0x7f0b0032
int integer mtrl_switch_thumb_motion_duration 0x7f0b0033
int integer mtrl_switch_thumb_post_morphing_duration 0x7f0b0034
int integer mtrl_switch_thumb_pre_morphing_duration 0x7f0b0035
int integer mtrl_switch_thumb_pressed_duration 0x7f0b0036
int integer mtrl_switch_thumb_viewport_center_coordinate 0x7f0b0037
int integer mtrl_switch_thumb_viewport_size 0x7f0b0038
int integer mtrl_switch_track_viewport_height 0x7f0b0039
int integer mtrl_switch_track_viewport_width 0x7f0b003a
int integer mtrl_tab_indicator_anim_duration_ms 0x7f0b003b
int integer mtrl_view_gone 0x7f0b003c
int integer mtrl_view_invisible 0x7f0b003d
int integer mtrl_view_visible 0x7f0b003e
int integer show_password_duration 0x7f0b003f
int integer status_bar_notification_info_maxnum 0x7f0b0040
int interpolator btn_checkbox_checked_mtrl_animation_interpolator_0 0x7f0c0000
int interpolator btn_checkbox_checked_mtrl_animation_interpolator_1 0x7f0c0001
int interpolator btn_checkbox_unchecked_mtrl_animation_interpolator_0 0x7f0c0002
int interpolator btn_checkbox_unchecked_mtrl_animation_interpolator_1 0x7f0c0003
int interpolator btn_radio_to_off_mtrl_animation_interpolator_0 0x7f0c0004
int interpolator btn_radio_to_on_mtrl_animation_interpolator_0 0x7f0c0005
int interpolator fast_out_slow_in 0x7f0c0006
int interpolator m3_sys_motion_easing_emphasized 0x7f0c0007
int interpolator m3_sys_motion_easing_emphasized_accelerate 0x7f0c0008
int interpolator m3_sys_motion_easing_emphasized_decelerate 0x7f0c0009
int interpolator m3_sys_motion_easing_linear 0x7f0c000a
int interpolator m3_sys_motion_easing_standard 0x7f0c000b
int interpolator m3_sys_motion_easing_standard_accelerate 0x7f0c000c
int interpolator m3_sys_motion_easing_standard_decelerate 0x7f0c000d
int interpolator mtrl_fast_out_linear_in 0x7f0c000e
int interpolator mtrl_fast_out_slow_in 0x7f0c000f
int interpolator mtrl_linear 0x7f0c0010
int interpolator mtrl_linear_out_slow_in 0x7f0c0011
int layout abc_action_bar_title_item 0x7f0d0000
int layout abc_action_bar_up_container 0x7f0d0001
int layout abc_action_menu_item_layout 0x7f0d0002
int layout abc_action_menu_layout 0x7f0d0003
int layout abc_action_mode_bar 0x7f0d0004
int layout abc_action_mode_close_item_material 0x7f0d0005
int layout abc_activity_chooser_view 0x7f0d0006
int layout abc_activity_chooser_view_list_item 0x7f0d0007
int layout abc_alert_dialog_button_bar_material 0x7f0d0008
int layout abc_alert_dialog_material 0x7f0d0009
int layout abc_alert_dialog_title_material 0x7f0d000a
int layout abc_cascading_menu_item_layout 0x7f0d000b
int layout abc_dialog_title_material 0x7f0d000c
int layout abc_expanded_menu_layout 0x7f0d000d
int layout abc_list_menu_item_checkbox 0x7f0d000e
int layout abc_list_menu_item_icon 0x7f0d000f
int layout abc_list_menu_item_layout 0x7f0d0010
int layout abc_list_menu_item_radio 0x7f0d0011
int layout abc_popup_menu_header_item_layout 0x7f0d0012
int layout abc_popup_menu_item_layout 0x7f0d0013
int layout abc_screen_content_include 0x7f0d0014
int layout abc_screen_simple 0x7f0d0015
int layout abc_screen_simple_overlay_action_mode 0x7f0d0016
int layout abc_screen_toolbar 0x7f0d0017
int layout abc_search_dropdown_item_icons_2line 0x7f0d0018
int layout abc_search_view 0x7f0d0019
int layout abc_select_dialog_material 0x7f0d001a
int layout abc_tooltip 0x7f0d001b
int layout activity_main 0x7f0d001c
int layout bottom_sheet_brand_name 0x7f0d001d
int layout bottom_sheet_choose_user 0x7f0d001e
int layout bottom_sheet_custom_brand 0x7f0d001f
int layout bottom_sheet_invite_subscribers 0x7f0d0020
int layout bottom_sheet_your_gift 0x7f0d0021
int layout browser_actions_context_menu_page 0x7f0d0022
int layout browser_actions_context_menu_row 0x7f0d0023
int layout custom_dialog 0x7f0d0024
int layout design_bottom_navigation_item 0x7f0d0025
int layout design_bottom_sheet_dialog 0x7f0d0026
int layout design_layout_snackbar 0x7f0d0027
int layout design_layout_snackbar_include 0x7f0d0028
int layout design_layout_tab_icon 0x7f0d0029
int layout design_layout_tab_text 0x7f0d002a
int layout design_menu_item_action_area 0x7f0d002b
int layout design_navigation_item 0x7f0d002c
int layout design_navigation_item_header 0x7f0d002d
int layout design_navigation_item_separator 0x7f0d002e
int layout design_navigation_item_subheader 0x7f0d002f
int layout design_navigation_menu 0x7f0d0030
int layout design_navigation_menu_item 0x7f0d0031
int layout design_text_input_end_icon 0x7f0d0032
int layout design_text_input_start_icon 0x7f0d0033
int layout dialog_annoucement 0x7f0d0034
int layout dialog_balance_info 0x7f0d0035
int layout dialog_beacon_drop 0x7f0d0036
int layout dialog_congrats 0x7f0d0037
int layout dialog_daily_reward 0x7f0d0038
int layout dialog_gift_luv 0x7f0d0039
int layout dialog_giveaway 0x7f0d003a
int layout dialog_open_crate 0x7f0d003b
int layout dialog_open_crate_key 0x7f0d003c
int layout dialog_unlock_chest 0x7f0d003d
int layout dialog_video_received 0x7f0d003e
int layout dp_example 0x7f0d003f
int layout em_simple_notification 0x7f0d0040
int layout exo_legacy_player_control_view 0x7f0d0041
int layout exo_list_divider 0x7f0d0042
int layout exo_player_control_ffwd_button 0x7f0d0043
int layout exo_player_control_rewind_button 0x7f0d0044
int layout exo_player_control_view 0x7f0d0045
int layout exo_player_view 0x7f0d0046
int layout exo_styled_settings_list 0x7f0d0047
int layout exo_styled_settings_list_item 0x7f0d0048
int layout exo_styled_sub_settings_list_item 0x7f0d0049
int layout exo_track_selection_dialog 0x7f0d004a
int layout fragment_alerts 0x7f0d004b
int layout fragment_all_brands 0x7f0d004c
int layout fragment_auth_select 0x7f0d004d
int layout fragment_authorize 0x7f0d004e
int layout fragment_battle 0x7f0d004f
int layout fragment_beacon_luv_drop 0x7f0d0050
int layout fragment_choose_fav 0x7f0d0051
int layout fragment_create_qr_gifting 0x7f0d0052
int layout fragment_edit_profile 0x7f0d0053
int layout fragment_followers 0x7f0d0054
int layout fragment_followings 0x7f0d0055
int layout fragment_gift_received_details 0x7f0d0056
int layout fragment_gift_sent_details 0x7f0d0057
int layout fragment_home 0x7f0d0058
int layout fragment_live_streaming 0x7f0d0059
int layout fragment_login 0x7f0d005a
int layout fragment_luv_drop 0x7f0d005b
int layout fragment_luv_received 0x7f0d005c
int layout fragment_my_chest_qr 0x7f0d005d
int layout fragment_my_profile 0x7f0d005e
int layout fragment_nearby_users 0x7f0d005f
int layout fragment_onboard 0x7f0d0060
int layout fragment_onboard_first_page 0x7f0d0061
int layout fragment_onboard_fourth_page 0x7f0d0062
int layout fragment_onboard_second_page 0x7f0d0063
int layout fragment_onboard_third_page 0x7f0d0064
int layout fragment_opened_luv_chest 0x7f0d0065
int layout fragment_opened_luv_crate 0x7f0d0066
int layout fragment_other_user_followings 0x7f0d0067
int layout fragment_pre_recording 0x7f0d0068
int layout fragment_privacy 0x7f0d0069
int layout fragment_q_r_scanning 0x7f0d006a
int layout fragment_qr_gifting 0x7f0d006b
int layout fragment_qr_receiving 0x7f0d006c
int layout fragment_quest 0x7f0d006d
int layout fragment_quest_crate 0x7f0d006e
int layout fragment_recharge 0x7f0d006f
int layout fragment_recharge_details 0x7f0d0070
int layout fragment_referral_award 0x7f0d0071
int layout fragment_registration 0x7f0d0072
int layout fragment_report_video 0x7f0d0073
int layout fragment_reset_password 0x7f0d0074
int layout fragment_search_receiver 0x7f0d0075
int layout fragment_select_opponent 0x7f0d0076
int layout fragment_select_receiver 0x7f0d0077
int layout fragment_send_luv 0x7f0d0078
int layout fragment_send_luv_get_luv 0x7f0d0079
int layout fragment_send_luv_qr_scan 0x7f0d007a
int layout fragment_setting 0x7f0d007b
int layout fragment_splash 0x7f0d007c
int layout fragment_start_luv_battle 0x7f0d007d
int layout fragment_top_gifter_filter 0x7f0d007e
int layout fragment_top_luvers 0x7f0d007f
int layout fragment_user_followers 0x7f0d0080
int layout fragment_user_followings 0x7f0d0081
int layout fragment_user_profile 0x7f0d0082
int layout fragment_user_story 0x7f0d0083
int layout fragment_video_player_with_details 0x7f0d0084
int layout fragment_withdraw_amount_select 0x7f0d0085
int layout fragment_withdraw_details 0x7f0d0086
int layout fragment_withdraw_payment_select 0x7f0d0087
int layout item_alert 0x7f0d0088
int layout item_battle_chat 0x7f0d0089
int layout item_brand_name 0x7f0d008a
int layout item_community_transaction 0x7f0d008b
int layout item_custom_brand 0x7f0d008c
int layout item_dropdown 0x7f0d008d
int layout item_fav 0x7f0d008e
int layout item_giftrange 0x7f0d008f
int layout item_home_stats 0x7f0d0090
int layout item_invite 0x7f0d0091
int layout item_language 0x7f0d0092
int layout item_notification_nearby 0x7f0d0093
int layout item_notification_receive 0x7f0d0094
int layout item_person 0x7f0d0095
int layout item_plan 0x7f0d0096
int layout item_search 0x7f0d0097
int layout item_search_fav 0x7f0d0098
int layout item_searchview 0x7f0d0099
int layout item_send_luv 0x7f0d009a
int layout item_share_receive 0x7f0d009b
int layout item_story_view 0x7f0d009c
int layout item_stream_gift 0x7f0d009d
int layout item_title 0x7f0d009e
int layout item_title_regular 0x7f0d009f
int layout item_top_gifter 0x7f0d00a0
int layout item_user_history 0x7f0d00a1
int layout item_your_gift 0x7f0d00a2
int layout layout_focus_marker 0x7f0d00a3
int layout layout_terms 0x7f0d00a4
int layout m3_alert_dialog 0x7f0d00a5
int layout m3_alert_dialog_actions 0x7f0d00a6
int layout m3_alert_dialog_title 0x7f0d00a7
int layout m3_auto_complete_simple_item 0x7f0d00a8
int layout m3_side_sheet_dialog 0x7f0d00a9
int layout material_chip_input_combo 0x7f0d00aa
int layout material_clock_display 0x7f0d00ab
int layout material_clock_display_divider 0x7f0d00ac
int layout material_clock_period_toggle 0x7f0d00ad
int layout material_clock_period_toggle_land 0x7f0d00ae
int layout material_clockface_textview 0x7f0d00af
int layout material_clockface_view 0x7f0d00b0
int layout material_radial_view_group 0x7f0d00b1
int layout material_textinput_timepicker 0x7f0d00b2
int layout material_time_chip 0x7f0d00b3
int layout material_time_input 0x7f0d00b4
int layout material_timepicker 0x7f0d00b5
int layout material_timepicker_dialog 0x7f0d00b6
int layout material_timepicker_textinput_display 0x7f0d00b7
int layout mtrl_alert_dialog 0x7f0d00b8
int layout mtrl_alert_dialog_actions 0x7f0d00b9
int layout mtrl_alert_dialog_title 0x7f0d00ba
int layout mtrl_alert_select_dialog_item 0x7f0d00bb
int layout mtrl_alert_select_dialog_multichoice 0x7f0d00bc
int layout mtrl_alert_select_dialog_singlechoice 0x7f0d00bd
int layout mtrl_auto_complete_simple_item 0x7f0d00be
int layout mtrl_calendar_day 0x7f0d00bf
int layout mtrl_calendar_day_of_week 0x7f0d00c0
int layout mtrl_calendar_days_of_week 0x7f0d00c1
int layout mtrl_calendar_horizontal 0x7f0d00c2
int layout mtrl_calendar_month 0x7f0d00c3
int layout mtrl_calendar_month_labeled 0x7f0d00c4
int layout mtrl_calendar_month_navigation 0x7f0d00c5
int layout mtrl_calendar_months 0x7f0d00c6
int layout mtrl_calendar_vertical 0x7f0d00c7
int layout mtrl_calendar_year 0x7f0d00c8
int layout mtrl_layout_snackbar 0x7f0d00c9
int layout mtrl_layout_snackbar_include 0x7f0d00ca
int layout mtrl_navigation_rail_item 0x7f0d00cb
int layout mtrl_picker_actions 0x7f0d00cc
int layout mtrl_picker_dialog 0x7f0d00cd
int layout mtrl_picker_fullscreen 0x7f0d00ce
int layout mtrl_picker_header_dialog 0x7f0d00cf
int layout mtrl_picker_header_fullscreen 0x7f0d00d0
int layout mtrl_picker_header_selection_text 0x7f0d00d1
int layout mtrl_picker_header_title_text 0x7f0d00d2
int layout mtrl_picker_header_toggle 0x7f0d00d3
int layout mtrl_picker_text_input_date 0x7f0d00d4
int layout mtrl_picker_text_input_date_range 0x7f0d00d5
int layout mtrl_search_bar 0x7f0d00d6
int layout mtrl_search_view 0x7f0d00d7
int layout notification_action 0x7f0d00d8
int layout notification_action_tombstone 0x7f0d00d9
int layout notification_media_action 0x7f0d00da
int layout notification_media_cancel_action 0x7f0d00db
int layout notification_template_big_media 0x7f0d00dc
int layout notification_template_big_media_custom 0x7f0d00dd
int layout notification_template_big_media_narrow 0x7f0d00de
int layout notification_template_big_media_narrow_custom 0x7f0d00df
int layout notification_template_custom_big 0x7f0d00e0
int layout notification_template_icon_group 0x7f0d00e1
int layout notification_template_lines_media 0x7f0d00e2
int layout notification_template_media 0x7f0d00e3
int layout notification_template_media_custom 0x7f0d00e4
int layout notification_template_part_chronometer 0x7f0d00e5
int layout notification_template_part_time 0x7f0d00e6
int layout popup_gift_received 0x7f0d00e7
int layout sdp_example 0x7f0d00e8
int layout select_dialog_item_material 0x7f0d00e9
int layout select_dialog_multichoice_material 0x7f0d00ea
int layout select_dialog_singlechoice_material 0x7f0d00eb
int layout sheet_account_delete 0x7f0d00ec
int layout sheet_discard_media 0x7f0d00ed
int layout sheet_end_battle 0x7f0d00ee
int layout sheet_image_pick 0x7f0d00ef
int layout sheet_logout 0x7f0d00f0
int layout sheet_select_language 0x7f0d00f1
int layout sheet_thanks_received 0x7f0d00f2
int layout sheet_top_gifters 0x7f0d00f3
int layout splash_screen_view 0x7f0d00f4
int layout support_simple_spinner_dropdown_item 0x7f0d00f5
int layout texture_view 0x7f0d00f6
int layout view_animated_tab_layout 0x7f0d00f7
int layout zxing_barcode_scanner 0x7f0d00f8
int layout zxing_capture 0x7f0d00f9
int menu bottom_nav_menu 0x7f0f0000
int mipmap app_icon 0x7f100000
int mipmap app_icon_foreground 0x7f100001
int mipmap app_icon_round 0x7f100002
int navigation mobile_navigation 0x7f110000
int plurals exo_controls_fastforward_by_amount_description 0x7f120000
int plurals exo_controls_rewind_by_amount_description 0x7f120001
int plurals mtrl_badge_content_description 0x7f120002
int raw firebase_common_keep 0x7f130000
int raw keep 0x7f130001
int raw zxing_beep 0x7f130002
int string _100 0x7f140000
int string _10_luv 0x7f140001
int string _1_2 0x7f140002
int string _2_2 0x7f140003
int string _500x_luv_each 0x7f140004
int string abc_action_bar_home_description 0x7f140005
int string abc_action_bar_up_description 0x7f140006
int string abc_action_menu_overflow_description 0x7f140007
int string abc_action_mode_done 0x7f140008
int string abc_activity_chooser_view_see_all 0x7f140009
int string abc_activitychooserview_choose_application 0x7f14000a
int string abc_capital_off 0x7f14000b
int string abc_capital_on 0x7f14000c
int string abc_menu_alt_shortcut_label 0x7f14000d
int string abc_menu_ctrl_shortcut_label 0x7f14000e
int string abc_menu_delete_shortcut_label 0x7f14000f
int string abc_menu_enter_shortcut_label 0x7f140010
int string abc_menu_function_shortcut_label 0x7f140011
int string abc_menu_meta_shortcut_label 0x7f140012
int string abc_menu_shift_shortcut_label 0x7f140013
int string abc_menu_space_shortcut_label 0x7f140014
int string abc_menu_sym_shortcut_label 0x7f140015
int string abc_prepend_shortcut_label 0x7f140016
int string abc_search_hint 0x7f140017
int string abc_searchview_description_clear 0x7f140018
int string abc_searchview_description_query 0x7f140019
int string abc_searchview_description_search 0x7f14001a
int string abc_searchview_description_submit 0x7f14001b
int string abc_searchview_description_voice 0x7f14001c
int string abc_shareactionprovider_share_with 0x7f14001d
int string abc_shareactionprovider_share_with_application 0x7f14001e
int string abc_toolbar_collapse_description 0x7f14001f
int string add_a_message 0x7f140020
int string add_bio 0x7f140021
int string add_your_favorite_brands 0x7f140022
int string afternoon 0x7f140023
int string age 0x7f140024
int string agree_to_terms 0x7f140025
int string alerts 0x7f140026
int string all_the_luv_gifts_that_you_receive_are_converted_into_diamonds 0x7f140027
int string amount 0x7f140028
int string androidx_startup 0x7f140029
int string any_user_who_sends_you_the_specified_gift 0x7f14002a
int string anyone 0x7f14002b
int string anyone_can_view_nfor_24h 0x7f14002c
int string app_name 0x7f14002d
int string app_version 0x7f14002e
int string appbar_scrolling_view_behavior 0x7f14002f
int string apple 0x7f140030
int string are_you_sure_you_nwant_to_log_out 0x7f140031
int string auth_login 0x7f140032
int string authorize_login_to_nluv_network_web 0x7f140033
int string awesome 0x7f140034
int string balance 0x7f140035
int string batte_start 0x7f140036
int string battle 0x7f140037
int string battle_ended 0x7f140038
int string bio 0x7f140039
int string bottom_sheet_behavior 0x7f14003a
int string bottomsheet_action_collapse 0x7f14003b
int string bottomsheet_action_expand 0x7f14003c
int string bottomsheet_action_expand_halfway 0x7f14003d
int string bottomsheet_drag_handle_clicked 0x7f14003e
int string bottomsheet_drag_handle_content_description 0x7f14003f
int string brand 0x7f140040
int string buy 0x7f140041
int string by_clicking_i_agree_to_the 0x7f140042
int string cancel 0x7f140043
int string challenge_sent_waiting_for_reply 0x7f140044
int string change_language 0x7f140045
int string character_counter_content_description 0x7f140046
int string character_counter_overflowed_content_description 0x7f140047
int string character_counter_pattern 0x7f140048
int string checkout_luv_app 0x7f140049
int string clear_text_end_icon_content_description 0x7f14004a
int string collect 0x7f14004b
int string collect_luv_chest 0x7f14004c
int string collect_your_5_luv_for_nsending_luv_daily 0x7f14004d
int string collected 0x7f14004e
int string com_google_firebase_crashlytics_mapping_file_id 0x7f14004f
int string comming_with_luv_from 0x7f140050
int string common_google_play_services_enable_button 0x7f140051
int string common_google_play_services_enable_text 0x7f140052
int string common_google_play_services_enable_title 0x7f140053
int string common_google_play_services_install_button 0x7f140054
int string common_google_play_services_install_text 0x7f140055
int string common_google_play_services_install_title 0x7f140056
int string common_google_play_services_notification_channel_name 0x7f140057
int string common_google_play_services_notification_ticker 0x7f140058
int string common_google_play_services_unknown_issue 0x7f140059
int string common_google_play_services_unsupported_text 0x7f14005a
int string common_google_play_services_update_button 0x7f14005b
int string common_google_play_services_update_text 0x7f14005c
int string common_google_play_services_update_title 0x7f14005d
int string common_google_play_services_updating_text 0x7f14005e
int string common_google_play_services_wear_update_text 0x7f14005f
int string common_open_on_phone 0x7f140060
int string common_signin_button_text 0x7f140061
int string common_signin_button_text_long 0x7f140062
int string community_stats 0x7f140063
int string completed 0x7f140064
int string confirm 0x7f140065
int string confirm_password 0x7f140066
int string congratulations 0x7f140067
int string congratulations_giveaway_winner 0x7f140068
int string copy_toast_msg 0x7f140069
int string country 0x7f14006a
int string create_gifting_qr 0x7f14006b
int string create_luv_chest 0x7f14006c
int string created_crate_bundle 0x7f14006d
int string creating_qr 0x7f14006e
int string currency 0x7f14006f
int string daily 0x7f140070
int string daily_luv 0x7f140071
int string daily_luv_reward 0x7f140072
int string daily_rewards_for_you 0x7f140073
int string daily_top_gift 0x7f140074
int string danger_zone 0x7f140075
int string date_and_time 0x7f140076
int string day_ago 0x7f140077
int string days 0x7f140078
int string days_ago 0x7f140079
int string decline 0x7f14007a
int string default_web_client_id 0x7f14007b
int string define_zxingandroidembedded 0x7f14007c
int string delete 0x7f14007d
int string delete_account 0x7f14007e
int string delete_account_2 0x7f14007f
int string delete_code 0x7f140080
int string diamonds 0x7f140081
int string didn_t_get_code 0x7f140082
int string discard 0x7f140083
int string discard_media 0x7f140084
int string don_t_have_an_account 0x7f140085
int string done 0x7f140086
int string earn_luv_drops 0x7f140087
int string earn_prizes 0x7f140088
int string earn_prizes_by_spreading_luv_send_a_gift_for_a_chance_to_win 0x7f140089
int string edit 0x7f14008a
int string edit_gifting_qr 0x7f14008b
int string edit_luv_chest 0x7f14008c
int string edit_profile 0x7f14008d
int string email 0x7f14008e
int string email_confirm_code 0x7f14008f
int string empty 0x7f140090
int string empty_password 0x7f140091
int string enable 0x7f140092
int string end_live_battle 0x7f140093
int string end_stream 0x7f140094
int string english 0x7f140095
int string error_a11y_label 0x7f140096
int string error_icon_content_description 0x7f140097
int string evening 0x7f140098
int string exo_controls_cc_disabled_description 0x7f140099
int string exo_controls_cc_enabled_description 0x7f14009a
int string exo_controls_custom_playback_speed 0x7f14009b
int string exo_controls_fastforward_description 0x7f14009c
int string exo_controls_fullscreen_enter_description 0x7f14009d
int string exo_controls_fullscreen_exit_description 0x7f14009e
int string exo_controls_hide 0x7f14009f
int string exo_controls_next_description 0x7f1400a0
int string exo_controls_overflow_hide_description 0x7f1400a1
int string exo_controls_overflow_show_description 0x7f1400a2
int string exo_controls_pause_description 0x7f1400a3
int string exo_controls_play_description 0x7f1400a4
int string exo_controls_playback_speed 0x7f1400a5
int string exo_controls_previous_description 0x7f1400a6
int string exo_controls_repeat_all_description 0x7f1400a7
int string exo_controls_repeat_off_description 0x7f1400a8
int string exo_controls_repeat_one_description 0x7f1400a9
int string exo_controls_rewind_description 0x7f1400aa
int string exo_controls_seek_bar_description 0x7f1400ab
int string exo_controls_settings_description 0x7f1400ac
int string exo_controls_show 0x7f1400ad
int string exo_controls_shuffle_off_description 0x7f1400ae
int string exo_controls_shuffle_on_description 0x7f1400af
int string exo_controls_stop_description 0x7f1400b0
int string exo_controls_time_placeholder 0x7f1400b1
int string exo_controls_vr_description 0x7f1400b2
int string exo_download_completed 0x7f1400b3
int string exo_download_description 0x7f1400b4
int string exo_download_downloading 0x7f1400b5
int string exo_download_failed 0x7f1400b6
int string exo_download_notification_channel_name 0x7f1400b7
int string exo_download_paused 0x7f1400b8
int string exo_download_paused_for_network 0x7f1400b9
int string exo_download_paused_for_wifi 0x7f1400ba
int string exo_download_removing 0x7f1400bb
int string exo_item_list 0x7f1400bc
int string exo_track_bitrate 0x7f1400bd
int string exo_track_mono 0x7f1400be
int string exo_track_resolution 0x7f1400bf
int string exo_track_role_alternate 0x7f1400c0
int string exo_track_role_closed_captions 0x7f1400c1
int string exo_track_role_commentary 0x7f1400c2
int string exo_track_role_supplementary 0x7f1400c3
int string exo_track_selection_auto 0x7f1400c4
int string exo_track_selection_none 0x7f1400c5
int string exo_track_selection_title_audio 0x7f1400c6
int string exo_track_selection_title_text 0x7f1400c7
int string exo_track_selection_title_video 0x7f1400c8
int string exo_track_stereo 0x7f1400c9
int string exo_track_surround 0x7f1400ca
int string exo_track_surround_5_point_1 0x7f1400cb
int string exo_track_surround_7_point_1 0x7f1400cc
int string exo_track_unknown 0x7f1400cd
int string exposed_dropdown_menu_content_description 0x7f1400ce
int string fab_transformation_scrim_behavior 0x7f1400cf
int string fab_transformation_sheet_behavior 0x7f1400d0
int string fallback_menu_item_copy_link 0x7f1400d1
int string fallback_menu_item_open_in_browser 0x7f1400d2
int string fallback_menu_item_share_link 0x7f1400d3
int string favorite_brand 0x7f1400d4
int string favorite_brands 0x7f1400d5
int string favorites 0x7f1400d6
int string fcm_fallback_notification_channel_label 0x7f1400d7
int string female 0x7f1400d8
int string fields_empty 0x7f1400d9
int string fill_all_details 0x7f1400da
int string filter 0x7f1400db
int string find_other_users_nopen_luv_crates_nopen_luv_chests 0x7f1400dc
int string finish 0x7f1400dd
int string follow 0x7f1400de
int string followers 0x7f1400df
int string following 0x7f1400e0
int string forgot_password 0x7f1400e1
int string found_nothing_by_your_search 0x7f1400e2
int string from 0x7f1400e3
int string gcm_defaultSenderId 0x7f1400e4
int string gender 0x7f1400e5
int string generate_key_users_will_be_able_nto_use_code_only_with_it 0x7f1400e6
int string get_100_extra_luv 0x7f1400e7
int string gift_range 0x7f1400e8
int string gift_received 0x7f1400e9
int string gift_sent 0x7f1400ea
int string gift_success 0x7f1400eb
int string gift_x_time 0x7f1400ec
int string gifting 0x7f1400ed
int string gifts_sent 0x7f1400ee
int string global_rank 0x7f1400ef
int string going_back_will_delete_already_recorded_video 0x7f1400f0
int string going_back_will_end_the_nlive_stream 0x7f1400f1
int string good_morning 0x7f1400f2
int string google 0x7f1400f3
int string google_api_key 0x7f1400f4
int string google_app_id 0x7f1400f5
int string google_crash_reporting_api_key 0x7f1400f6
int string google_storage_bucket 0x7f1400f7
int string has_left_you_a_luv_crate_nenter_key_to_open 0x7f1400f8
int string has_left_you_no_key 0x7f1400f9
int string has_sent 0x7f1400fa
int string hello_blank_fragment 0x7f1400fb
int string here_s_a_little_welcome_gift 0x7f1400fc
int string hide_bottom_view_on_scroll_behavior 0x7f1400fd
int string home 0x7f1400fe
int string hour_ago 0x7f1400ff
int string hours_ago 0x7f140100
int string icon_content_description 0x7f140101
int string inappropriate 0x7f140102
int string industry 0x7f140103
int string invalid_amount 0x7f140104
int string invalid_email 0x7f140105
int string invalid_email_password 0x7f140106
int string invalid_link 0x7f140107
int string invalid_password 0x7f140108
int string invalid_paypal 0x7f140109
int string invite 0x7f14010a
int string invite_more 0x7f14010b
int string invite_sent 0x7f14010c
int string invite_subscriber 0x7f14010d
int string item_view_role_description 0x7f14010e
int string join 0x7f14010f
int string just_now 0x7f140110
int string key 0x7f140111
int string key_input 0x7f140112
int string language 0x7f140113
int string library_zxingandroidembedded_author 0x7f140114
int string library_zxingandroidembedded_authorWebsite 0x7f140115
int string library_zxingandroidembedded_isOpenSource 0x7f140116
int string library_zxingandroidembedded_libraryDescription 0x7f140117
int string library_zxingandroidembedded_libraryName 0x7f140118
int string library_zxingandroidembedded_libraryVersion 0x7f140119
int string library_zxingandroidembedded_libraryWebsite 0x7f14011a
int string library_zxingandroidembedded_licenseId 0x7f14011b
int string library_zxingandroidembedded_repositoryLink 0x7f14011c
int string log_out 0x7f14011d
int string login_transition 0x7f14011e
int string loser 0x7f14011f
int string luv 0x7f140120
int string luv_chest_qr 0x7f140121
int string luv_chest_received 0x7f140122
int string luv_chest_sent 0x7f140123
int string luv_crate 0x7f140124
int string luv_crates 0x7f140125
int string luv_create_received 0x7f140126
int string luv_create_sent 0x7f140127
int string luv_drop 0x7f140128
int string luv_drop_sent 0x7f140129
int string luv_drop_with_every_visit 0x7f14012a
int string luv_giveaway 0x7f14012b
int string luv_inside_package 0x7f14012c
int string luv_referral 0x7f14012d
int string luv_sent 0x7f14012e
int string luv_shout_out 0x7f14012f
int string luvs 0x7f140130
int string m3_sys_motion_easing_emphasized 0x7f140131
int string m3_sys_motion_easing_emphasized_accelerate 0x7f140132
int string m3_sys_motion_easing_emphasized_decelerate 0x7f140133
int string m3_sys_motion_easing_emphasized_path_data 0x7f140134
int string m3_sys_motion_easing_legacy 0x7f140135
int string m3_sys_motion_easing_legacy_accelerate 0x7f140136
int string m3_sys_motion_easing_legacy_decelerate 0x7f140137
int string m3_sys_motion_easing_linear 0x7f140138
int string m3_sys_motion_easing_standard 0x7f140139
int string m3_sys_motion_easing_standard_accelerate 0x7f14013a
int string m3_sys_motion_easing_standard_decelerate 0x7f14013b
int string male 0x7f14013c
int string material_clock_display_divider 0x7f14013d
int string material_clock_toggle_content_description 0x7f14013e
int string material_hour_24h_suffix 0x7f14013f
int string material_hour_selection 0x7f140140
int string material_hour_suffix 0x7f140141
int string material_minute_selection 0x7f140142
int string material_minute_suffix 0x7f140143
int string material_motion_easing_accelerated 0x7f140144
int string material_motion_easing_decelerated 0x7f140145
int string material_motion_easing_emphasized 0x7f140146
int string material_motion_easing_linear 0x7f140147
int string material_motion_easing_standard 0x7f140148
int string material_slider_range_end 0x7f140149
int string material_slider_range_start 0x7f14014a
int string material_slider_value 0x7f14014b
int string material_timepicker_am 0x7f14014c
int string material_timepicker_clock_mode_description 0x7f14014d
int string material_timepicker_hour 0x7f14014e
int string material_timepicker_minute 0x7f14014f
int string material_timepicker_pm 0x7f140150
int string material_timepicker_select_time 0x7f140151
int string material_timepicker_text_input_mode_description 0x7f140152
int string minutes_ago 0x7f140153
int string month_ago 0x7f140154
int string monthly 0x7f140155
int string months 0x7f140156
int string months_ago 0x7f140157
int string morning 0x7f140158
int string mtrl_badge_numberless_content_description 0x7f140159
int string mtrl_checkbox_button_icon_path_checked 0x7f14015a
int string mtrl_checkbox_button_icon_path_group_name 0x7f14015b
int string mtrl_checkbox_button_icon_path_indeterminate 0x7f14015c
int string mtrl_checkbox_button_icon_path_name 0x7f14015d
int string mtrl_checkbox_button_path_checked 0x7f14015e
int string mtrl_checkbox_button_path_group_name 0x7f14015f
int string mtrl_checkbox_button_path_name 0x7f140160
int string mtrl_checkbox_button_path_unchecked 0x7f140161
int string mtrl_checkbox_state_description_checked 0x7f140162
int string mtrl_checkbox_state_description_indeterminate 0x7f140163
int string mtrl_checkbox_state_description_unchecked 0x7f140164
int string mtrl_chip_close_icon_content_description 0x7f140165
int string mtrl_exceed_max_badge_number_content_description 0x7f140166
int string mtrl_exceed_max_badge_number_suffix 0x7f140167
int string mtrl_picker_a11y_next_month 0x7f140168
int string mtrl_picker_a11y_prev_month 0x7f140169
int string mtrl_picker_announce_current_range_selection 0x7f14016a
int string mtrl_picker_announce_current_selection 0x7f14016b
int string mtrl_picker_announce_current_selection_none 0x7f14016c
int string mtrl_picker_cancel 0x7f14016d
int string mtrl_picker_confirm 0x7f14016e
int string mtrl_picker_date_header_selected 0x7f14016f
int string mtrl_picker_date_header_title 0x7f140170
int string mtrl_picker_date_header_unselected 0x7f140171
int string mtrl_picker_day_of_week_column_header 0x7f140172
int string mtrl_picker_end_date_description 0x7f140173
int string mtrl_picker_invalid_format 0x7f140174
int string mtrl_picker_invalid_format_example 0x7f140175
int string mtrl_picker_invalid_format_use 0x7f140176
int string mtrl_picker_invalid_range 0x7f140177
int string mtrl_picker_navigate_to_current_year_description 0x7f140178
int string mtrl_picker_navigate_to_year_description 0x7f140179
int string mtrl_picker_out_of_range 0x7f14017a
int string mtrl_picker_range_header_only_end_selected 0x7f14017b
int string mtrl_picker_range_header_only_start_selected 0x7f14017c
int string mtrl_picker_range_header_selected 0x7f14017d
int string mtrl_picker_range_header_title 0x7f14017e
int string mtrl_picker_range_header_unselected 0x7f14017f
int string mtrl_picker_save 0x7f140180
int string mtrl_picker_start_date_description 0x7f140181
int string mtrl_picker_text_input_date_hint 0x7f140182
int string mtrl_picker_text_input_date_range_end_hint 0x7f140183
int string mtrl_picker_text_input_date_range_start_hint 0x7f140184
int string mtrl_picker_text_input_day_abbr 0x7f140185
int string mtrl_picker_text_input_month_abbr 0x7f140186
int string mtrl_picker_text_input_year_abbr 0x7f140187
int string mtrl_picker_today_description 0x7f140188
int string mtrl_picker_toggle_to_calendar_input_mode 0x7f140189
int string mtrl_picker_toggle_to_day_selection 0x7f14018a
int string mtrl_picker_toggle_to_text_input_mode 0x7f14018b
int string mtrl_picker_toggle_to_year_selection 0x7f14018c
int string mtrl_switch_thumb_group_name 0x7f14018d
int string mtrl_switch_thumb_path_checked 0x7f14018e
int string mtrl_switch_thumb_path_morphing 0x7f14018f
int string mtrl_switch_thumb_path_name 0x7f140190
int string mtrl_switch_thumb_path_pressed 0x7f140191
int string mtrl_switch_thumb_path_unchecked 0x7f140192
int string mtrl_switch_track_decoration_path 0x7f140193
int string mtrl_switch_track_path 0x7f140194
int string mtrl_timepicker_cancel 0x7f140195
int string mtrl_timepicker_confirm 0x7f140196
int string my_balance 0x7f140197
int string my_codes 0x7f140198
int string my_luv_chest 0x7f140199
int string my_qr_code 0x7f14019a
int string my_qr_codes 0x7f14019b
int string name 0x7f14019c
int string nav_app_bar_navigate_up_description 0x7f14019d
int string nav_app_bar_open_drawer_description 0x7f14019e
int string nearby 0x7f14019f
int string nearby_requires_users_location_and_must_be_enable_in_settings 0x7f1401a0
int string network_notifications 0x7f1401a1
int string new_code_sent 0x7f1401a2
int string next 0x7f1401a3
int string no_balance 0x7f1401a4
int string no_bio 0x7f1401a5
int string no_chat_message 0x7f1401a6
int string no_favorites_brands 0x7f1401a7
int string no_key 0x7f1401a8
int string no_sponsor 0x7f1401a9
int string no_transactions_nyet 0x7f1401aa
int string number_of_scans 0x7f1401ab
int string on_board_Next 0x7f1401ac
int string on_board_first_1 0x7f1401ad
int string on_board_first_2 0x7f1401ae
int string on_board_first_3 0x7f1401af
int string on_board_fourth_1 0x7f1401b0
int string on_board_fourth_2 0x7f1401b1
int string on_board_fourth_3 0x7f1401b2
int string on_board_second_1 0x7f1401b3
int string on_board_second_2 0x7f1401b4
int string on_board_second_3 0x7f1401b5
int string on_board_skip 0x7f1401b6
int string on_board_start 0x7f1401b7
int string on_board_third_1 0x7f1401b8
int string on_board_third_2 0x7f1401b9
int string on_board_third_3 0x7f1401ba
int string once_submitted_your_request_will_be_processed_nvia_paypal_paypal_fees_may_result 0x7f1401bb
int string only_user_name 0x7f1401bc
int string only_you_can_see_it_njust_once 0x7f1401bd
int string onlyme 0x7f1401be
int string open 0x7f1401bf
int string open_camera 0x7f1401c0
int string open_luv_crate 0x7f1401c1
int string opened_chest 0x7f1401c2
int string opened_crate 0x7f1401c3
int string opened_luv_cate 0x7f1401c4
int string other 0x7f1401c5
int string password 0x7f1401c6
int string password_creation 0x7f1401c7
int string password_empty 0x7f1401c8
int string password_mismatch 0x7f1401c9
int string password_must 0x7f1401ca
int string password_nreset 0x7f1401cb
int string password_toggle_content_description 0x7f1401cc
int string path_password_eye 0x7f1401cd
int string path_password_eye_mask_strike_through 0x7f1401ce
int string path_password_eye_mask_visible 0x7f1401cf
int string path_password_strike_through 0x7f1401d0
int string payment_method 0x7f1401d1
int string payout_method 0x7f1401d2
int string paypal_account_must_be_created_with_nsame_email_as_user_email 0x7f1401d3
int string paypal_link 0x7f1401d4
int string pending 0x7f1401d5
int string peopleifollow 0x7f1401d6
int string per 0x7f1401d7
int string period 0x7f1401d8
int string photo_library 0x7f1401d9
int string pick 0x7f1401da
int string pick_new 0x7f1401db
int string place_the_qr_code_in_front_nof_the_square 0x7f1401dc
int string play 0x7f1401dd
int string portuguese 0x7f1401de
int string privacy 0x7f1401df
int string privacy_setting 0x7f1401e0
int string profile 0x7f1401e1
int string profile_image 0x7f1401e2
int string profile_picture_transition 0x7f1401e3
int string project_id 0x7f1401e4
int string qr_gifting 0x7f1401e5
int string qr_not_found 0x7f1401e6
int string qr_receiving 0x7f1401e7
int string qr_scanning 0x7f1401e8
int string quest_reward 0x7f1401e9
int string quest_reward_referral 0x7f1401ea
int string quest_reward_scan_crate 0x7f1401eb
int string quest_reward_send_LUV 0x7f1401ec
int string quests 0x7f1401ed
int string receiver 0x7f1401ee
int string receiving 0x7f1401ef
int string recharge 0x7f1401f0
int string recharge_success 0x7f1401f1
int string refer_friends 0x7f1401f2
int string referral_award 0x7f1401f3
int string referral_reward 0x7f1401f4
int string referral_reward_2 0x7f1401f5
int string rematch 0x7f1401f6
int string remove 0x7f1401f7
int string report 0x7f1401f8
int string report_video 0x7f1401f9
int string resend_code 0x7f1401fa
int string resend_code_again 0x7f1401fb
int string reset_password 0x7f1401fc
int string reset_transition 0x7f1401fd
int string reward 0x7f1401fe
int string save 0x7f1401ff
int string scan 0x7f140200
int string scan_code 0x7f140201
int string search 0x7f140202
int string search_menu_title 0x7f140203
int string searchbar_scrolling_view_behavior 0x7f140204
int string searchview_clear_text_content_description 0x7f140205
int string searchview_navigation_content_description 0x7f140206
int string see_all 0x7f140207
int string see_more 0x7f140208
int string select_from_gallery 0x7f140209
int string select_image 0x7f14020a
int string select_receiver_nearby 0x7f14020b
int string select_receiver_randomly 0x7f14020c
int string select_the_frequency_to_reward_users_when_they_scan_your_qr_for_example_1_per_day 0x7f14020d
int string select_user 0x7f14020e
int string send 0x7f14020f
int string send_a_thank_you_video 0x7f140210
int string send_get_luv 0x7f140211
int string send_luv 0x7f140212
int string send_luv_get_luv 0x7f140213
int string sender_x_receiver 0x7f140214
int string sending_battle_request 0x7f140215
int string sent 0x7f140216
int string set_language 0x7f140217
int string set_new_password 0x7f140218
int string setting 0x7f140219
int string share 0x7f14021a
int string share_this_code_and_tell_your_friends_nto_show_you_some_luv 0x7f14021b
int string shared_live_stream 0x7f14021c
int string show_me_some_love 0x7f14021d
int string side_sheet_accessibility_pane_title 0x7f14021e
int string side_sheet_behavior 0x7f14021f
int string sign_in 0x7f140220
int string sign_in_to_start 0x7f140221
int string sign_up 0x7f140222
int string signup_transition 0x7f140223
int string skip 0x7f140224
int string something_went_wrong 0x7f140225
int string spanish 0x7f140226
int string special_not_allowed 0x7f140227
int string sponsored_by 0x7f140228
int string sponsored_by_2 0x7f140229
int string sponsored_drop 0x7f14022a
int string sponsored_drop_received 0x7f14022b
int string sponsored_drop_sent 0x7f14022c
int string sponsored_luv_drop 0x7f14022d
int string start_battle_instantly 0x7f14022e
int string start_luv_battle 0x7f14022f
int string status 0x7f140230
int string status_bar_notification_info_overflow 0x7f140231
int string submit 0x7f140232
int string suspended 0x7f140233
int string switch_to_brand_account 0x7f140234
int string switch_to_personal_account 0x7f140235
int string system_luv_drop 0x7f140236
int string tap_to_counter 0x7f140237
int string tap_to_type 0x7f140238
int string terms_of_service_amp_privacy_policy 0x7f140239
int string thank_you_video_received 0x7f14023a
int string thanks_received 0x7f14023b
int string thanks_sent_available_to_anyone_for_24h 0x7f14023c
int string thanks_sent_can_view_it_once 0x7f14023d
int string thanks_you_for_your_support 0x7f14023e
int string the_luv_network 0x7f14023f
int string the_report_will_be_reviewed_promptly 0x7f140240
int string the_world_random_user 0x7f140241
int string there_luv_inside_package 0x7f140242
int string this_information 0x7f140243
int string ticket_for_one 0x7f140244
int string ticket_for_one_description 0x7f140245
int string time 0x7f140246
int string time_left 0x7f140247
int string to 0x7f140248
int string top_10_gift_senders 0x7f140249
int string top_brand_gifters 0x7f14024a
int string top_gift 0x7f14024b
int string top_gifte 0x7f14024c
int string top_gifter 0x7f14024d
int string top_luvers 0x7f14024e
int string top_regular_gifters 0x7f14024f
int string top_sent_gift 0x7f140250
int string transaction_id 0x7f140251
int string try_again 0x7f140252
int string usd 0x7f140253
int string use_luvs_to_buy_and_send_gifts_to_anyone_with_the_luv_app 0x7f140254
int string user 0x7f140255
int string user_join 0x7f140256
int string user_profile_transition 0x7f140257
int string user_type 0x7f140258
int string username 0x7f140259
int string username_s_luv_chest 0x7f14025a
int string username_x 0x7f14025b
int string username_x_time 0x7f14025c
int string users_who_scan_your_code_will_nreceive_a_luv_gift_from_you 0x7f14025d
int string victor_challenged_you_to_a_battle 0x7f14025e
int string victor_declined_your_invite 0x7f14025f
int string visit_particopating_stores 0x7f140260
int string void_trans 0x7f140261
int string waiting_for_participants 0x7f140262
int string we_couldn_t_find_a_rival_for_you_please_try_later 0x7f140263
int string we_have_winner 0x7f140264
int string website 0x7f140265
int string week_ago 0x7f140266
int string weekly_limit_used 0x7f140267
int string weeks 0x7f140268
int string weeks_ago 0x7f140269
int string welcome_to_n_brand_name 0x7f14026a
int string who_can_see_my_followers 0x7f14026b
int string who_can_see_who_i_follow 0x7f14026c
int string who_would_you_like_to_nchallenge_to_battle 0x7f14026d
int string why_are_you_reporting_this_video 0x7f14026e
int string winner 0x7f14026f
int string with_luv_from 0x7f140270
int string withdraw 0x7f140271
int string withdraw_success 0x7f140272
int string withdraw_your_diamonds_to_your_paypal_in_usd 0x7f140273
int string write_your_email 0x7f140274
int string year_ago 0x7f140275
int string years 0x7f140276
int string years_ago 0x7f140277
int string you 0x7f140278
int string you_can_now_receive_a_luv_drop_from_people_bear_you_enable_in_settings 0x7f140279
int string you_don_t_have_enough_diamonds 0x7f14027a
int string you_dont_enough_balance 0x7f14027b
int string you_get_20_luv_when_you_spread_luv_by_inviting_others 0x7f14027c
int string you_have_been_selected 0x7f14027d
int string you_have_no_notifications_nso_far 0x7f14027e
int string you_lost 0x7f14027f
int string you_ve_received_a_thank_you_video_only_you_can_see_it_once 0x7f140280
int string you_won 0x7f140281
int string your_comment 0x7f140282
int string your_email 0x7f140283
int string your_gift 0x7f140284
int string your_name 0x7f140285
int string zxing_app_name 0x7f140286
int string zxing_button_ok 0x7f140287
int string zxing_msg_camera_framework_bug 0x7f140288
int string zxing_msg_default_status 0x7f140289
int style AlertDialog_AppCompat 0x7f150000
int style AlertDialog_AppCompat_Light 0x7f150001
int style Animation_AppCompat_Dialog 0x7f150002
int style Animation_AppCompat_DropDownUp 0x7f150003
int style Animation_AppCompat_Tooltip 0x7f150004
int style Animation_Design_BottomSheetDialog 0x7f150005
int style Animation_Material3_BottomSheetDialog 0x7f150006
int style Animation_Material3_SideSheetDialog 0x7f150007
int style Animation_MaterialComponents_BottomSheetDialog 0x7f150008
int style AppTheme 0x7f150009
int style Base_AlertDialog_AppCompat 0x7f15000a
int style Base_AlertDialog_AppCompat_Light 0x7f15000b
int style Base_Animation_AppCompat_Dialog 0x7f15000c
int style Base_Animation_AppCompat_DropDownUp 0x7f15000d
int style Base_Animation_AppCompat_Tooltip 0x7f15000e
int style Base_CardView 0x7f15000f
int style Base_DialogWindowTitle_AppCompat 0x7f150010
int style Base_DialogWindowTitleBackground_AppCompat 0x7f150011
int style Base_MaterialAlertDialog_MaterialComponents_Title_Icon 0x7f150012
int style Base_MaterialAlertDialog_MaterialComponents_Title_Panel 0x7f150013
int style Base_MaterialAlertDialog_MaterialComponents_Title_Text 0x7f150014
int style Base_TextAppearance_AppCompat 0x7f150015
int style Base_TextAppearance_AppCompat_Body1 0x7f150016
int style Base_TextAppearance_AppCompat_Body2 0x7f150017
int style Base_TextAppearance_AppCompat_Button 0x7f150018
int style Base_TextAppearance_AppCompat_Caption 0x7f150019
int style Base_TextAppearance_AppCompat_Display1 0x7f15001a
int style Base_TextAppearance_AppCompat_Display2 0x7f15001b
int style Base_TextAppearance_AppCompat_Display3 0x7f15001c
int style Base_TextAppearance_AppCompat_Display4 0x7f15001d
int style Base_TextAppearance_AppCompat_Headline 0x7f15001e
int style Base_TextAppearance_AppCompat_Inverse 0x7f15001f
int style Base_TextAppearance_AppCompat_Large 0x7f150020
int style Base_TextAppearance_AppCompat_Large_Inverse 0x7f150021
int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x7f150022
int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x7f150023
int style Base_TextAppearance_AppCompat_Medium 0x7f150024
int style Base_TextAppearance_AppCompat_Medium_Inverse 0x7f150025
int style Base_TextAppearance_AppCompat_Menu 0x7f150026
int style Base_TextAppearance_AppCompat_SearchResult 0x7f150027
int style Base_TextAppearance_AppCompat_SearchResult_Subtitle 0x7f150028
int style Base_TextAppearance_AppCompat_SearchResult_Title 0x7f150029
int style Base_TextAppearance_AppCompat_Small 0x7f15002a
int style Base_TextAppearance_AppCompat_Small_Inverse 0x7f15002b
int style Base_TextAppearance_AppCompat_Subhead 0x7f15002c
int style Base_TextAppearance_AppCompat_Subhead_Inverse 0x7f15002d
int style Base_TextAppearance_AppCompat_Title 0x7f15002e
int style Base_TextAppearance_AppCompat_Title_Inverse 0x7f15002f
int style Base_TextAppearance_AppCompat_Tooltip 0x7f150030
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Menu 0x7f150031
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x7f150032
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x7f150033
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title 0x7f150034
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x7f150035
int style Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x7f150036
int style Base_TextAppearance_AppCompat_Widget_ActionMode_Title 0x7f150037
int style Base_TextAppearance_AppCompat_Widget_Button 0x7f150038
int style Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x7f150039
int style Base_TextAppearance_AppCompat_Widget_Button_Colored 0x7f15003a
int style Base_TextAppearance_AppCompat_Widget_Button_Inverse 0x7f15003b
int style Base_TextAppearance_AppCompat_Widget_DropDownItem 0x7f15003c
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Header 0x7f15003d
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Large 0x7f15003e
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Small 0x7f15003f
int style Base_TextAppearance_AppCompat_Widget_Switch 0x7f150040
int style Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x7f150041
int style Base_TextAppearance_Material3_Search 0x7f150042
int style Base_TextAppearance_MaterialComponents_Badge 0x7f150043
int style Base_TextAppearance_MaterialComponents_Button 0x7f150044
int style Base_TextAppearance_MaterialComponents_Headline6 0x7f150045
int style Base_TextAppearance_MaterialComponents_Subtitle2 0x7f150046
int style Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x7f150047
int style Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x7f150048
int style Base_TextAppearance_Widget_AppCompat_Toolbar_Title 0x7f150049
int style Base_Theme_AppCompat 0x7f15004a
int style Base_Theme_AppCompat_CompactMenu 0x7f15004b
int style Base_Theme_AppCompat_Dialog 0x7f15004c
int style Base_Theme_AppCompat_Dialog_Alert 0x7f15004d
int style Base_Theme_AppCompat_Dialog_FixedSize 0x7f15004e
int style Base_Theme_AppCompat_Dialog_MinWidth 0x7f15004f
int style Base_Theme_AppCompat_DialogWhenLarge 0x7f150050
int style Base_Theme_AppCompat_Light 0x7f150051
int style Base_Theme_AppCompat_Light_DarkActionBar 0x7f150052
int style Base_Theme_AppCompat_Light_Dialog 0x7f150053
int style Base_Theme_AppCompat_Light_Dialog_Alert 0x7f150054
int style Base_Theme_AppCompat_Light_Dialog_FixedSize 0x7f150055
int style Base_Theme_AppCompat_Light_Dialog_MinWidth 0x7f150056
int style Base_Theme_AppCompat_Light_DialogWhenLarge 0x7f150057
int style Base_Theme_Material3_Dark 0x7f150058
int style Base_Theme_Material3_Dark_BottomSheetDialog 0x7f150059
int style Base_Theme_Material3_Dark_Dialog 0x7f15005a
int style Base_Theme_Material3_Dark_SideSheetDialog 0x7f15005b
int style Base_Theme_Material3_Light 0x7f15005c
int style Base_Theme_Material3_Light_BottomSheetDialog 0x7f15005d
int style Base_Theme_Material3_Light_Dialog 0x7f15005e
int style Base_Theme_Material3_Light_SideSheetDialog 0x7f15005f
int style Base_Theme_MaterialComponents 0x7f150060
int style Base_Theme_MaterialComponents_Bridge 0x7f150061
int style Base_Theme_MaterialComponents_CompactMenu 0x7f150062
int style Base_Theme_MaterialComponents_Dialog 0x7f150063
int style Base_Theme_MaterialComponents_Dialog_Alert 0x7f150064
int style Base_Theme_MaterialComponents_Dialog_Bridge 0x7f150065
int style Base_Theme_MaterialComponents_Dialog_FixedSize 0x7f150066
int style Base_Theme_MaterialComponents_Dialog_MinWidth 0x7f150067
int style Base_Theme_MaterialComponents_DialogWhenLarge 0x7f150068
int style Base_Theme_MaterialComponents_Light 0x7f150069
int style Base_Theme_MaterialComponents_Light_Bridge 0x7f15006a
int style Base_Theme_MaterialComponents_Light_DarkActionBar 0x7f15006b
int style Base_Theme_MaterialComponents_Light_DarkActionBar_Bridge 0x7f15006c
int style Base_Theme_MaterialComponents_Light_Dialog 0x7f15006d
int style Base_Theme_MaterialComponents_Light_Dialog_Alert 0x7f15006e
int style Base_Theme_MaterialComponents_Light_Dialog_Bridge 0x7f15006f
int style Base_Theme_MaterialComponents_Light_Dialog_FixedSize 0x7f150070
int style Base_Theme_MaterialComponents_Light_Dialog_MinWidth 0x7f150071
int style Base_Theme_MaterialComponents_Light_DialogWhenLarge 0x7f150072
int style Base_Theme_SplashScreen 0x7f150073
int style Base_Theme_SplashScreen_DayNight 0x7f150074
int style Base_Theme_SplashScreen_Light 0x7f150075
int style Base_ThemeOverlay_AppCompat 0x7f150076
int style Base_ThemeOverlay_AppCompat_ActionBar 0x7f150077
int style Base_ThemeOverlay_AppCompat_Dark 0x7f150078
int style Base_ThemeOverlay_AppCompat_Dark_ActionBar 0x7f150079
int style Base_ThemeOverlay_AppCompat_Dialog 0x7f15007a
int style Base_ThemeOverlay_AppCompat_Dialog_Alert 0x7f15007b
int style Base_ThemeOverlay_AppCompat_Light 0x7f15007c
int style Base_ThemeOverlay_Material3_AutoCompleteTextView 0x7f15007d
int style Base_ThemeOverlay_Material3_BottomSheetDialog 0x7f15007e
int style Base_ThemeOverlay_Material3_Dialog 0x7f15007f
int style Base_ThemeOverlay_Material3_SideSheetDialog 0x7f150080
int style Base_ThemeOverlay_Material3_TextInputEditText 0x7f150081
int style Base_ThemeOverlay_MaterialComponents_Dialog 0x7f150082
int style Base_ThemeOverlay_MaterialComponents_Dialog_Alert 0x7f150083
int style Base_ThemeOverlay_MaterialComponents_Dialog_Alert_Framework 0x7f150084
int style Base_ThemeOverlay_MaterialComponents_Light_Dialog_Alert_Framework 0x7f150085
int style Base_ThemeOverlay_MaterialComponents_MaterialAlertDialog 0x7f150086
int style Base_V14_Theme_Material3_Dark 0x7f150087
int style Base_V14_Theme_Material3_Dark_BottomSheetDialog 0x7f150088
int style Base_V14_Theme_Material3_Dark_Dialog 0x7f150089
int style Base_V14_Theme_Material3_Dark_SideSheetDialog 0x7f15008a
int style Base_V14_Theme_Material3_Light 0x7f15008b
int style Base_V14_Theme_Material3_Light_BottomSheetDialog 0x7f15008c
int style Base_V14_Theme_Material3_Light_Dialog 0x7f15008d
int style Base_V14_Theme_Material3_Light_SideSheetDialog 0x7f15008e
int style Base_V14_Theme_MaterialComponents 0x7f15008f
int style Base_V14_Theme_MaterialComponents_Bridge 0x7f150090
int style Base_V14_Theme_MaterialComponents_Dialog 0x7f150091
int style Base_V14_Theme_MaterialComponents_Dialog_Bridge 0x7f150092
int style Base_V14_Theme_MaterialComponents_Light 0x7f150093
int style Base_V14_Theme_MaterialComponents_Light_Bridge 0x7f150094
int style Base_V14_Theme_MaterialComponents_Light_DarkActionBar_Bridge 0x7f150095
int style Base_V14_Theme_MaterialComponents_Light_Dialog 0x7f150096
int style Base_V14_Theme_MaterialComponents_Light_Dialog_Bridge 0x7f150097
int style Base_V14_ThemeOverlay_Material3_BottomSheetDialog 0x7f150098
int style Base_V14_ThemeOverlay_Material3_SideSheetDialog 0x7f150099
int style Base_V14_ThemeOverlay_MaterialComponents_BottomSheetDialog 0x7f15009a
int style Base_V14_ThemeOverlay_MaterialComponents_Dialog 0x7f15009b
int style Base_V14_ThemeOverlay_MaterialComponents_Dialog_Alert 0x7f15009c
int style Base_V14_ThemeOverlay_MaterialComponents_MaterialAlertDialog 0x7f15009d
int style Base_V14_Widget_MaterialComponents_AutoCompleteTextView 0x7f15009e
int style Base_V21_Theme_AppCompat 0x7f15009f
int style Base_V21_Theme_AppCompat_Dialog 0x7f1500a0
int style Base_V21_Theme_AppCompat_Light 0x7f1500a1
int style Base_V21_Theme_AppCompat_Light_Dialog 0x7f1500a2
int style Base_V21_Theme_MaterialComponents 0x7f1500a3
int style Base_V21_Theme_MaterialComponents_Dialog 0x7f1500a4
int style Base_V21_Theme_MaterialComponents_Light 0x7f1500a5
int style Base_V21_Theme_MaterialComponents_Light_Dialog 0x7f1500a6
int style Base_V21_ThemeOverlay_AppCompat_Dialog 0x7f1500a7
int style Base_V21_ThemeOverlay_Material3_BottomSheetDialog 0x7f1500a8
int style Base_V21_ThemeOverlay_Material3_SideSheetDialog 0x7f1500a9
int style Base_V21_ThemeOverlay_MaterialComponents_BottomSheetDialog 0x7f1500aa
int style Base_V22_Theme_AppCompat 0x7f1500ab
int style Base_V22_Theme_AppCompat_Light 0x7f1500ac
int style Base_V23_Theme_AppCompat 0x7f1500ad
int style Base_V23_Theme_AppCompat_Light 0x7f1500ae
int style Base_V24_Theme_Material3_Dark 0x7f1500af
int style Base_V24_Theme_Material3_Dark_Dialog 0x7f1500b0
int style Base_V24_Theme_Material3_Light 0x7f1500b1
int style Base_V24_Theme_Material3_Light_Dialog 0x7f1500b2
int style Base_V26_Theme_AppCompat 0x7f1500b3
int style Base_V26_Theme_AppCompat_Light 0x7f1500b4
int style Base_V26_Widget_AppCompat_Toolbar 0x7f1500b5
int style Base_V28_Theme_AppCompat 0x7f1500b6
int style Base_V28_Theme_AppCompat_Light 0x7f1500b7
int style Base_V7_Theme_AppCompat 0x7f1500b8
int style Base_V7_Theme_AppCompat_Dialog 0x7f1500b9
int style Base_V7_Theme_AppCompat_Light 0x7f1500ba
int style Base_V7_Theme_AppCompat_Light_Dialog 0x7f1500bb
int style Base_V7_ThemeOverlay_AppCompat_Dialog 0x7f1500bc
int style Base_V7_Widget_AppCompat_AutoCompleteTextView 0x7f1500bd
int style Base_V7_Widget_AppCompat_EditText 0x7f1500be
int style Base_V7_Widget_AppCompat_Toolbar 0x7f1500bf
int style Base_Widget_AppCompat_ActionBar 0x7f1500c0
int style Base_Widget_AppCompat_ActionBar_Solid 0x7f1500c1
int style Base_Widget_AppCompat_ActionBar_TabBar 0x7f1500c2
int style Base_Widget_AppCompat_ActionBar_TabText 0x7f1500c3
int style Base_Widget_AppCompat_ActionBar_TabView 0x7f1500c4
int style Base_Widget_AppCompat_ActionButton 0x7f1500c5
int style Base_Widget_AppCompat_ActionButton_CloseMode 0x7f1500c6
int style Base_Widget_AppCompat_ActionButton_Overflow 0x7f1500c7
int style Base_Widget_AppCompat_ActionMode 0x7f1500c8
int style Base_Widget_AppCompat_ActivityChooserView 0x7f1500c9
int style Base_Widget_AppCompat_AutoCompleteTextView 0x7f1500ca
int style Base_Widget_AppCompat_Button 0x7f1500cb
int style Base_Widget_AppCompat_Button_Borderless 0x7f1500cc
int style Base_Widget_AppCompat_Button_Borderless_Colored 0x7f1500cd
int style Base_Widget_AppCompat_Button_ButtonBar_AlertDialog 0x7f1500ce
int style Base_Widget_AppCompat_Button_Colored 0x7f1500cf
int style Base_Widget_AppCompat_Button_Small 0x7f1500d0
int style Base_Widget_AppCompat_ButtonBar 0x7f1500d1
int style Base_Widget_AppCompat_ButtonBar_AlertDialog 0x7f1500d2
int style Base_Widget_AppCompat_CompoundButton_CheckBox 0x7f1500d3
int style Base_Widget_AppCompat_CompoundButton_RadioButton 0x7f1500d4
int style Base_Widget_AppCompat_CompoundButton_Switch 0x7f1500d5
int style Base_Widget_AppCompat_DrawerArrowToggle 0x7f1500d6
int style Base_Widget_AppCompat_DrawerArrowToggle_Common 0x7f1500d7
int style Base_Widget_AppCompat_DropDownItem_Spinner 0x7f1500d8
int style Base_Widget_AppCompat_EditText 0x7f1500d9
int style Base_Widget_AppCompat_ImageButton 0x7f1500da
int style Base_Widget_AppCompat_Light_ActionBar 0x7f1500db
int style Base_Widget_AppCompat_Light_ActionBar_Solid 0x7f1500dc
int style Base_Widget_AppCompat_Light_ActionBar_TabBar 0x7f1500dd
int style Base_Widget_AppCompat_Light_ActionBar_TabText 0x7f1500de
int style Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x7f1500df
int style Base_Widget_AppCompat_Light_ActionBar_TabView 0x7f1500e0
int style Base_Widget_AppCompat_Light_PopupMenu 0x7f1500e1
int style Base_Widget_AppCompat_Light_PopupMenu_Overflow 0x7f1500e2
int style Base_Widget_AppCompat_ListMenuView 0x7f1500e3
int style Base_Widget_AppCompat_ListPopupWindow 0x7f1500e4
int style Base_Widget_AppCompat_ListView 0x7f1500e5
int style Base_Widget_AppCompat_ListView_DropDown 0x7f1500e6
int style Base_Widget_AppCompat_ListView_Menu 0x7f1500e7
int style Base_Widget_AppCompat_PopupMenu 0x7f1500e8
int style Base_Widget_AppCompat_PopupMenu_Overflow 0x7f1500e9
int style Base_Widget_AppCompat_PopupWindow 0x7f1500ea
int style Base_Widget_AppCompat_ProgressBar 0x7f1500eb
int style Base_Widget_AppCompat_ProgressBar_Horizontal 0x7f1500ec
int style Base_Widget_AppCompat_RatingBar 0x7f1500ed
int style Base_Widget_AppCompat_RatingBar_Indicator 0x7f1500ee
int style Base_Widget_AppCompat_RatingBar_Small 0x7f1500ef
int style Base_Widget_AppCompat_SearchView 0x7f1500f0
int style Base_Widget_AppCompat_SearchView_ActionBar 0x7f1500f1
int style Base_Widget_AppCompat_SeekBar 0x7f1500f2
int style Base_Widget_AppCompat_SeekBar_Discrete 0x7f1500f3
int style Base_Widget_AppCompat_Spinner 0x7f1500f4
int style Base_Widget_AppCompat_Spinner_Underlined 0x7f1500f5
int style Base_Widget_AppCompat_TextView 0x7f1500f6
int style Base_Widget_AppCompat_TextView_SpinnerItem 0x7f1500f7
int style Base_Widget_AppCompat_Toolbar 0x7f1500f8
int style Base_Widget_AppCompat_Toolbar_Button_Navigation 0x7f1500f9
int style Base_Widget_Design_TabLayout 0x7f1500fa
int style Base_Widget_Material3_ActionBar_Solid 0x7f1500fb
int style Base_Widget_Material3_ActionMode 0x7f1500fc
int style Base_Widget_Material3_BottomNavigationView 0x7f1500fd
int style Base_Widget_Material3_CardView 0x7f1500fe
int style Base_Widget_Material3_Chip 0x7f1500ff
int style Base_Widget_Material3_CollapsingToolbar 0x7f150100
int style Base_Widget_Material3_CompoundButton_CheckBox 0x7f150101
int style Base_Widget_Material3_CompoundButton_RadioButton 0x7f150102
int style Base_Widget_Material3_CompoundButton_Switch 0x7f150103
int style Base_Widget_Material3_ExtendedFloatingActionButton 0x7f150104
int style Base_Widget_Material3_ExtendedFloatingActionButton_Icon 0x7f150105
int style Base_Widget_Material3_FloatingActionButton 0x7f150106
int style Base_Widget_Material3_FloatingActionButton_Large 0x7f150107
int style Base_Widget_Material3_FloatingActionButton_Small 0x7f150108
int style Base_Widget_Material3_Light_ActionBar_Solid 0x7f150109
int style Base_Widget_Material3_MaterialCalendar_NavigationButton 0x7f15010a
int style Base_Widget_Material3_Snackbar 0x7f15010b
int style Base_Widget_Material3_TabLayout 0x7f15010c
int style Base_Widget_Material3_TabLayout_OnSurface 0x7f15010d
int style Base_Widget_Material3_TabLayout_Secondary 0x7f15010e
int style Base_Widget_MaterialComponents_AutoCompleteTextView 0x7f15010f
int style Base_Widget_MaterialComponents_CheckedTextView 0x7f150110
int style Base_Widget_MaterialComponents_Chip 0x7f150111
int style Base_Widget_MaterialComponents_MaterialCalendar_HeaderToggleButton 0x7f150112
int style Base_Widget_MaterialComponents_MaterialCalendar_NavigationButton 0x7f150113
int style Base_Widget_MaterialComponents_PopupMenu 0x7f150114
int style Base_Widget_MaterialComponents_PopupMenu_ContextMenu 0x7f150115
int style Base_Widget_MaterialComponents_PopupMenu_ListPopupWindow 0x7f150116
int style Base_Widget_MaterialComponents_PopupMenu_Overflow 0x7f150117
int style Base_Widget_MaterialComponents_Slider 0x7f150118
int style Base_Widget_MaterialComponents_Snackbar 0x7f150119
int style Base_Widget_MaterialComponents_TextInputEditText 0x7f15011a
int style Base_Widget_MaterialComponents_TextInputLayout 0x7f15011b
int style Base_Widget_MaterialComponents_TextView 0x7f15011c
int style Base_v21_Theme_SplashScreen 0x7f15011d
int style Base_v21_Theme_SplashScreen_Light 0x7f15011e
int style Base_v27_Theme_SplashScreen 0x7f15011f
int style Base_v27_Theme_SplashScreen_Light 0x7f150120
int style CardView 0x7f150121
int style CardView_Dark 0x7f150122
int style CardView_Light 0x7f150123
int style CustomActiveIndicatorShape 0x7f150124
int style CustomActiveIndicatorStyle 0x7f150125
int style Dexter_Internal_Theme_Transparent 0x7f150126
int style ExoMediaButton 0x7f150127
int style ExoMediaButton_FastForward 0x7f150128
int style ExoMediaButton_Next 0x7f150129
int style ExoMediaButton_Pause 0x7f15012a
int style ExoMediaButton_Play 0x7f15012b
int style ExoMediaButton_Previous 0x7f15012c
int style ExoMediaButton_Rewind 0x7f15012d
int style ExoMediaButton_VR 0x7f15012e
int style ExoStyledControls 0x7f15012f
int style ExoStyledControls_Button 0x7f150130
int style ExoStyledControls_Button_Bottom 0x7f150131
int style ExoStyledControls_Button_Bottom_AudioTrack 0x7f150132
int style ExoStyledControls_Button_Bottom_CC 0x7f150133
int style ExoStyledControls_Button_Bottom_FullScreen 0x7f150134
int style ExoStyledControls_Button_Bottom_OverflowHide 0x7f150135
int style ExoStyledControls_Button_Bottom_OverflowShow 0x7f150136
int style ExoStyledControls_Button_Bottom_PlaybackSpeed 0x7f150137
int style ExoStyledControls_Button_Bottom_RepeatToggle 0x7f150138
int style ExoStyledControls_Button_Bottom_Settings 0x7f150139
int style ExoStyledControls_Button_Bottom_Shuffle 0x7f15013a
int style ExoStyledControls_Button_Bottom_VR 0x7f15013b
int style ExoStyledControls_Button_Center 0x7f15013c
int style ExoStyledControls_Button_Center_FfwdWithAmount 0x7f15013d
int style ExoStyledControls_Button_Center_Next 0x7f15013e
int style ExoStyledControls_Button_Center_PlayPause 0x7f15013f
int style ExoStyledControls_Button_Center_Previous 0x7f150140
int style ExoStyledControls_Button_Center_RewWithAmount 0x7f150141
int style ExoStyledControls_TimeBar 0x7f150142
int style ExoStyledControls_TimeText 0x7f150143
int style ExoStyledControls_TimeText_Duration 0x7f150144
int style ExoStyledControls_TimeText_Position 0x7f150145
int style ExoStyledControls_TimeText_Separator 0x7f150146
int style MaterialAlertDialog_Material3 0x7f150147
int style MaterialAlertDialog_Material3_Animation 0x7f150148
int style MaterialAlertDialog_Material3_Body_Text 0x7f150149
int style MaterialAlertDialog_Material3_Body_Text_CenterStacked 0x7f15014a
int style MaterialAlertDialog_Material3_Title_Icon 0x7f15014b
int style MaterialAlertDialog_Material3_Title_Icon_CenterStacked 0x7f15014c
int style MaterialAlertDialog_Material3_Title_Panel 0x7f15014d
int style MaterialAlertDialog_Material3_Title_Panel_CenterStacked 0x7f15014e
int style MaterialAlertDialog_Material3_Title_Text 0x7f15014f
int style MaterialAlertDialog_Material3_Title_Text_CenterStacked 0x7f150150
int style MaterialAlertDialog_MaterialComponents 0x7f150151
int style MaterialAlertDialog_MaterialComponents_Body_Text 0x7f150152
int style MaterialAlertDialog_MaterialComponents_Picker_Date_Calendar 0x7f150153
int style MaterialAlertDialog_MaterialComponents_Picker_Date_Spinner 0x7f150154
int style MaterialAlertDialog_MaterialComponents_Title_Icon 0x7f150155
int style MaterialAlertDialog_MaterialComponents_Title_Icon_CenterStacked 0x7f150156
int style MaterialAlertDialog_MaterialComponents_Title_Panel 0x7f150157
int style MaterialAlertDialog_MaterialComponents_Title_Panel_CenterStacked 0x7f150158
int style MaterialAlertDialog_MaterialComponents_Title_Text 0x7f150159
int style MaterialAlertDialog_MaterialComponents_Title_Text_CenterStacked 0x7f15015a
int style Platform_AppCompat 0x7f15015b
int style Platform_AppCompat_Light 0x7f15015c
int style Platform_MaterialComponents 0x7f15015d
int style Platform_MaterialComponents_Dialog 0x7f15015e
int style Platform_MaterialComponents_Light 0x7f15015f
int style Platform_MaterialComponents_Light_Dialog 0x7f150160
int style Platform_ThemeOverlay_AppCompat 0x7f150161
int style Platform_ThemeOverlay_AppCompat_Dark 0x7f150162
int style Platform_ThemeOverlay_AppCompat_Light 0x7f150163
int style Platform_V21_AppCompat 0x7f150164
int style Platform_V21_AppCompat_Light 0x7f150165
int style Platform_V25_AppCompat 0x7f150166
int style Platform_V25_AppCompat_Light 0x7f150167
int style Platform_Widget_AppCompat_Spinner 0x7f150168
int style RtlOverlay_DialogWindowTitle_AppCompat 0x7f150169
int style RtlOverlay_Widget_AppCompat_ActionBar_TitleItem 0x7f15016a
int style RtlOverlay_Widget_AppCompat_DialogTitle_Icon 0x7f15016b
int style RtlOverlay_Widget_AppCompat_PopupMenuItem 0x7f15016c
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup 0x7f15016d
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Shortcut 0x7f15016e
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow 0x7f15016f
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Text 0x7f150170
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Title 0x7f150171
int style RtlOverlay_Widget_AppCompat_Search_DropDown 0x7f150172
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1 0x7f150173
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2 0x7f150174
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Query 0x7f150175
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Text 0x7f150176
int style RtlOverlay_Widget_AppCompat_SearchView_MagIcon 0x7f150177
int style RtlUnderlay_Widget_AppCompat_ActionButton 0x7f150178
int style RtlUnderlay_Widget_AppCompat_ActionButton_Overflow 0x7f150179
int style ScrollingPagerIndicator 0x7f15017a
int style ShapeAppearance_M3_Comp_BottomAppBar_Container_Shape 0x7f15017b
int style ShapeAppearance_M3_Comp_FilledButton_Container_Shape 0x7f15017c
int style ShapeAppearance_M3_Comp_NavigationRail_ActiveIndicator_Shape 0x7f15017d
int style ShapeAppearance_M3_Comp_SearchBar_Avatar_Shape 0x7f15017e
int style ShapeAppearance_M3_Comp_SearchBar_Container_Shape 0x7f15017f
int style ShapeAppearance_M3_Comp_SearchView_FullScreen_Container_Shape 0x7f150180
int style ShapeAppearance_M3_Comp_Switch_Handle_Shape 0x7f150181
int style ShapeAppearance_M3_Comp_Switch_StateLayer_Shape 0x7f150182
int style ShapeAppearance_M3_Comp_Switch_Track_Shape 0x7f150183
int style ShapeAppearance_M3_Comp_TextButton_Container_Shape 0x7f150184
int style ShapeAppearance_M3_Sys_Shape_Corner_ExtraLarge 0x7f150185
int style ShapeAppearance_M3_Sys_Shape_Corner_ExtraSmall 0x7f150186
int style ShapeAppearance_M3_Sys_Shape_Corner_Full 0x7f150187
int style ShapeAppearance_M3_Sys_Shape_Corner_Large 0x7f150188
int style ShapeAppearance_M3_Sys_Shape_Corner_Medium 0x7f150189
int style ShapeAppearance_M3_Sys_Shape_Corner_None 0x7f15018a
int style ShapeAppearance_M3_Sys_Shape_Corner_Small 0x7f15018b
int style ShapeAppearance_Material3_Corner_ExtraLarge 0x7f15018c
int style ShapeAppearance_Material3_Corner_ExtraSmall 0x7f15018d
int style ShapeAppearance_Material3_Corner_Full 0x7f15018e
int style ShapeAppearance_Material3_Corner_Large 0x7f15018f
int style ShapeAppearance_Material3_Corner_Medium 0x7f150190
int style ShapeAppearance_Material3_Corner_None 0x7f150191
int style ShapeAppearance_Material3_Corner_Small 0x7f150192
int style ShapeAppearance_Material3_LargeComponent 0x7f150193
int style ShapeAppearance_Material3_MediumComponent 0x7f150194
int style ShapeAppearance_Material3_NavigationBarView_ActiveIndicator 0x7f150195
int style ShapeAppearance_Material3_SmallComponent 0x7f150196
int style ShapeAppearance_Material3_Tooltip 0x7f150197
int style ShapeAppearance_MaterialComponents 0x7f150198
int style ShapeAppearance_MaterialComponents_LargeComponent 0x7f150199
int style ShapeAppearance_MaterialComponents_MediumComponent 0x7f15019a
int style ShapeAppearance_MaterialComponents_SmallComponent 0x7f15019b
int style ShapeAppearance_MaterialComponents_Tooltip 0x7f15019c
int style ShapeAppearanceOverlay_Material3_Button 0x7f15019d
int style ShapeAppearanceOverlay_Material3_Chip 0x7f15019e
int style ShapeAppearanceOverlay_Material3_Corner_Bottom 0x7f15019f
int style ShapeAppearanceOverlay_Material3_Corner_Left 0x7f1501a0
int style ShapeAppearanceOverlay_Material3_Corner_Right 0x7f1501a1
int style ShapeAppearanceOverlay_Material3_Corner_Top 0x7f1501a2
int style ShapeAppearanceOverlay_Material3_FloatingActionButton 0x7f1501a3
int style ShapeAppearanceOverlay_Material3_NavigationView_Item 0x7f1501a4
int style ShapeAppearanceOverlay_Material3_SearchBar 0x7f1501a5
int style ShapeAppearanceOverlay_Material3_SearchView 0x7f1501a6
int style ShapeAppearanceOverlay_MaterialAlertDialog_Material3 0x7f1501a7
int style ShapeAppearanceOverlay_MaterialComponents_BottomSheet 0x7f1501a8
int style ShapeAppearanceOverlay_MaterialComponents_Chip 0x7f1501a9
int style ShapeAppearanceOverlay_MaterialComponents_ExtendedFloatingActionButton 0x7f1501aa
int style ShapeAppearanceOverlay_MaterialComponents_FloatingActionButton 0x7f1501ab
int style ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Day 0x7f1501ac
int style ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Window_Fullscreen 0x7f1501ad
int style ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Year 0x7f1501ae
int style ShapeAppearanceOverlay_MaterialComponents_TextInputLayout_FilledBox 0x7f1501af
int style TextAppearance_AppCompat 0x7f1501b0
int style TextAppearance_AppCompat_Body1 0x7f1501b1
int style TextAppearance_AppCompat_Body2 0x7f1501b2
int style TextAppearance_AppCompat_Button 0x7f1501b3
int style TextAppearance_AppCompat_Caption 0x7f1501b4
int style TextAppearance_AppCompat_Display1 0x7f1501b5
int style TextAppearance_AppCompat_Display2 0x7f1501b6
int style TextAppearance_AppCompat_Display3 0x7f1501b7
int style TextAppearance_AppCompat_Display4 0x7f1501b8
int style TextAppearance_AppCompat_Headline 0x7f1501b9
int style TextAppearance_AppCompat_Inverse 0x7f1501ba
int style TextAppearance_AppCompat_Large 0x7f1501bb
int style TextAppearance_AppCompat_Large_Inverse 0x7f1501bc
int style TextAppearance_AppCompat_Light_SearchResult_Subtitle 0x7f1501bd
int style TextAppearance_AppCompat_Light_SearchResult_Title 0x7f1501be
int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x7f1501bf
int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x7f1501c0
int style TextAppearance_AppCompat_Medium 0x7f1501c1
int style TextAppearance_AppCompat_Medium_Inverse 0x7f1501c2
int style TextAppearance_AppCompat_Menu 0x7f1501c3
int style TextAppearance_AppCompat_SearchResult_Subtitle 0x7f1501c4
int style TextAppearance_AppCompat_SearchResult_Title 0x7f1501c5
int style TextAppearance_AppCompat_Small 0x7f1501c6
int style TextAppearance_AppCompat_Small_Inverse 0x7f1501c7
int style TextAppearance_AppCompat_Subhead 0x7f1501c8
int style TextAppearance_AppCompat_Subhead_Inverse 0x7f1501c9
int style TextAppearance_AppCompat_Title 0x7f1501ca
int style TextAppearance_AppCompat_Title_Inverse 0x7f1501cb
int style TextAppearance_AppCompat_Tooltip 0x7f1501cc
int style TextAppearance_AppCompat_Widget_ActionBar_Menu 0x7f1501cd
int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x7f1501ce
int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x7f1501cf
int style TextAppearance_AppCompat_Widget_ActionBar_Title 0x7f1501d0
int style TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x7f1501d1
int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x7f1501d2
int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse 0x7f1501d3
int style TextAppearance_AppCompat_Widget_ActionMode_Title 0x7f1501d4
int style TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse 0x7f1501d5
int style TextAppearance_AppCompat_Widget_Button 0x7f1501d6
int style TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x7f1501d7
int style TextAppearance_AppCompat_Widget_Button_Colored 0x7f1501d8
int style TextAppearance_AppCompat_Widget_Button_Inverse 0x7f1501d9
int style TextAppearance_AppCompat_Widget_DropDownItem 0x7f1501da
int style TextAppearance_AppCompat_Widget_PopupMenu_Header 0x7f1501db
int style TextAppearance_AppCompat_Widget_PopupMenu_Large 0x7f1501dc
int style TextAppearance_AppCompat_Widget_PopupMenu_Small 0x7f1501dd
int style TextAppearance_AppCompat_Widget_Switch 0x7f1501de
int style TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x7f1501df
int style TextAppearance_Compat_Notification 0x7f1501e0
int style TextAppearance_Compat_Notification_Info 0x7f1501e1
int style TextAppearance_Compat_Notification_Info_Media 0x7f1501e2
int style TextAppearance_Compat_Notification_Line2 0x7f1501e3
int style TextAppearance_Compat_Notification_Line2_Media 0x7f1501e4
int style TextAppearance_Compat_Notification_Media 0x7f1501e5
int style TextAppearance_Compat_Notification_Time 0x7f1501e6
int style TextAppearance_Compat_Notification_Time_Media 0x7f1501e7
int style TextAppearance_Compat_Notification_Title 0x7f1501e8
int style TextAppearance_Compat_Notification_Title_Media 0x7f1501e9
int style TextAppearance_Design_CollapsingToolbar_Expanded 0x7f1501ea
int style TextAppearance_Design_Counter 0x7f1501eb
int style TextAppearance_Design_Counter_Overflow 0x7f1501ec
int style TextAppearance_Design_Error 0x7f1501ed
int style TextAppearance_Design_HelperText 0x7f1501ee
int style TextAppearance_Design_Hint 0x7f1501ef
int style TextAppearance_Design_Placeholder 0x7f1501f0
int style TextAppearance_Design_Prefix 0x7f1501f1
int style TextAppearance_Design_Snackbar_Message 0x7f1501f2
int style TextAppearance_Design_Suffix 0x7f1501f3
int style TextAppearance_Design_Tab 0x7f1501f4
int style TextAppearance_M3_Sys_Typescale_BodyLarge 0x7f1501f5
int style TextAppearance_M3_Sys_Typescale_BodyMedium 0x7f1501f6
int style TextAppearance_M3_Sys_Typescale_BodySmall 0x7f1501f7
int style TextAppearance_M3_Sys_Typescale_DisplayLarge 0x7f1501f8
int style TextAppearance_M3_Sys_Typescale_DisplayMedium 0x7f1501f9
int style TextAppearance_M3_Sys_Typescale_DisplaySmall 0x7f1501fa
int style TextAppearance_M3_Sys_Typescale_HeadlineLarge 0x7f1501fb
int style TextAppearance_M3_Sys_Typescale_HeadlineMedium 0x7f1501fc
int style TextAppearance_M3_Sys_Typescale_HeadlineSmall 0x7f1501fd
int style TextAppearance_M3_Sys_Typescale_LabelLarge 0x7f1501fe
int style TextAppearance_M3_Sys_Typescale_LabelMedium 0x7f1501ff
int style TextAppearance_M3_Sys_Typescale_LabelSmall 0x7f150200
int style TextAppearance_M3_Sys_Typescale_TitleLarge 0x7f150201
int style TextAppearance_M3_Sys_Typescale_TitleMedium 0x7f150202
int style TextAppearance_M3_Sys_Typescale_TitleSmall 0x7f150203
int style TextAppearance_Material3_ActionBar_Subtitle 0x7f150204
int style TextAppearance_Material3_ActionBar_Title 0x7f150205
int style TextAppearance_Material3_BodyLarge 0x7f150206
int style TextAppearance_Material3_BodyMedium 0x7f150207
int style TextAppearance_Material3_BodySmall 0x7f150208
int style TextAppearance_Material3_DisplayLarge 0x7f150209
int style TextAppearance_Material3_DisplayMedium 0x7f15020a
int style TextAppearance_Material3_DisplaySmall 0x7f15020b
int style TextAppearance_Material3_HeadlineLarge 0x7f15020c
int style TextAppearance_Material3_HeadlineMedium 0x7f15020d
int style TextAppearance_Material3_HeadlineSmall 0x7f15020e
int style TextAppearance_Material3_LabelLarge 0x7f15020f
int style TextAppearance_Material3_LabelMedium 0x7f150210
int style TextAppearance_Material3_LabelSmall 0x7f150211
int style TextAppearance_Material3_MaterialTimePicker_Title 0x7f150212
int style TextAppearance_Material3_SearchBar 0x7f150213
int style TextAppearance_Material3_SearchView 0x7f150214
int style TextAppearance_Material3_SearchView_Prefix 0x7f150215
int style TextAppearance_Material3_TitleLarge 0x7f150216
int style TextAppearance_Material3_TitleMedium 0x7f150217
int style TextAppearance_Material3_TitleSmall 0x7f150218
int style TextAppearance_MaterialComponents_Badge 0x7f150219
int style TextAppearance_MaterialComponents_Body1 0x7f15021a
int style TextAppearance_MaterialComponents_Body2 0x7f15021b
int style TextAppearance_MaterialComponents_Button 0x7f15021c
int style TextAppearance_MaterialComponents_Caption 0x7f15021d
int style TextAppearance_MaterialComponents_Chip 0x7f15021e
int style TextAppearance_MaterialComponents_Headline1 0x7f15021f
int style TextAppearance_MaterialComponents_Headline2 0x7f150220
int style TextAppearance_MaterialComponents_Headline3 0x7f150221
int style TextAppearance_MaterialComponents_Headline4 0x7f150222
int style TextAppearance_MaterialComponents_Headline5 0x7f150223
int style TextAppearance_MaterialComponents_Headline6 0x7f150224
int style TextAppearance_MaterialComponents_Overline 0x7f150225
int style TextAppearance_MaterialComponents_Subtitle1 0x7f150226
int style TextAppearance_MaterialComponents_Subtitle2 0x7f150227
int style TextAppearance_MaterialComponents_TimePicker_Title 0x7f150228
int style TextAppearance_MaterialComponents_Tooltip 0x7f150229
int style TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x7f15022a
int style TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x7f15022b
int style TextAppearance_Widget_AppCompat_Toolbar_Title 0x7f15022c
int style Theme_AppCompat 0x7f15022d
int style Theme_AppCompat_CompactMenu 0x7f15022e
int style Theme_AppCompat_DayNight 0x7f15022f
int style Theme_AppCompat_DayNight_DarkActionBar 0x7f150230
int style Theme_AppCompat_DayNight_Dialog 0x7f150231
int style Theme_AppCompat_DayNight_Dialog_Alert 0x7f150232
int style Theme_AppCompat_DayNight_Dialog_MinWidth 0x7f150233
int style Theme_AppCompat_DayNight_DialogWhenLarge 0x7f150234
int style Theme_AppCompat_DayNight_NoActionBar 0x7f150235
int style Theme_AppCompat_Dialog 0x7f150236
int style Theme_AppCompat_Dialog_Alert 0x7f150237
int style Theme_AppCompat_Dialog_MinWidth 0x7f150238
int style Theme_AppCompat_DialogWhenLarge 0x7f150239
int style Theme_AppCompat_Empty 0x7f15023a
int style Theme_AppCompat_Light 0x7f15023b
int style Theme_AppCompat_Light_DarkActionBar 0x7f15023c
int style Theme_AppCompat_Light_Dialog 0x7f15023d
int style Theme_AppCompat_Light_Dialog_Alert 0x7f15023e
int style Theme_AppCompat_Light_Dialog_MinWidth 0x7f15023f
int style Theme_AppCompat_Light_DialogWhenLarge 0x7f150240
int style Theme_AppCompat_Light_NoActionBar 0x7f150241
int style Theme_AppCompat_NoActionBar 0x7f150242
int style Theme_Design 0x7f150243
int style Theme_Design_BottomSheetDialog 0x7f150244
int style Theme_Design_Light 0x7f150245
int style Theme_Design_Light_BottomSheetDialog 0x7f150246
int style Theme_Design_Light_NoActionBar 0x7f150247
int style Theme_Design_NoActionBar 0x7f150248
int style Theme_Material3_Dark 0x7f150249
int style Theme_Material3_Dark_BottomSheetDialog 0x7f15024a
int style Theme_Material3_Dark_Dialog 0x7f15024b
int style Theme_Material3_Dark_Dialog_Alert 0x7f15024c
int style Theme_Material3_Dark_Dialog_MinWidth 0x7f15024d
int style Theme_Material3_Dark_DialogWhenLarge 0x7f15024e
int style Theme_Material3_Dark_NoActionBar 0x7f15024f
int style Theme_Material3_Dark_SideSheetDialog 0x7f150250
int style Theme_Material3_DayNight 0x7f150251
int style Theme_Material3_DayNight_BottomSheetDialog 0x7f150252
int style Theme_Material3_DayNight_Dialog 0x7f150253
int style Theme_Material3_DayNight_Dialog_Alert 0x7f150254
int style Theme_Material3_DayNight_Dialog_MinWidth 0x7f150255
int style Theme_Material3_DayNight_DialogWhenLarge 0x7f150256
int style Theme_Material3_DayNight_NoActionBar 0x7f150257
int style Theme_Material3_DayNight_SideSheetDialog 0x7f150258
int style Theme_Material3_DynamicColors_Dark 0x7f150259
int style Theme_Material3_DynamicColors_DayNight 0x7f15025a
int style Theme_Material3_DynamicColors_Light 0x7f15025b
int style Theme_Material3_Light 0x7f15025c
int style Theme_Material3_Light_BottomSheetDialog 0x7f15025d
int style Theme_Material3_Light_Dialog 0x7f15025e
int style Theme_Material3_Light_Dialog_Alert 0x7f15025f
int style Theme_Material3_Light_Dialog_MinWidth 0x7f150260
int style Theme_Material3_Light_DialogWhenLarge 0x7f150261
int style Theme_Material3_Light_NoActionBar 0x7f150262
int style Theme_Material3_Light_SideSheetDialog 0x7f150263
int style Theme_MaterialComponents 0x7f150264
int style Theme_MaterialComponents_BottomSheetDialog 0x7f150265
int style Theme_MaterialComponents_Bridge 0x7f150266
int style Theme_MaterialComponents_CompactMenu 0x7f150267
int style Theme_MaterialComponents_DayNight 0x7f150268
int style Theme_MaterialComponents_DayNight_BottomSheetDialog 0x7f150269
int style Theme_MaterialComponents_DayNight_Bridge 0x7f15026a
int style Theme_MaterialComponents_DayNight_DarkActionBar 0x7f15026b
int style Theme_MaterialComponents_DayNight_DarkActionBar_Bridge 0x7f15026c
int style Theme_MaterialComponents_DayNight_Dialog 0x7f15026d
int style Theme_MaterialComponents_DayNight_Dialog_Alert 0x7f15026e
int style Theme_MaterialComponents_DayNight_Dialog_Alert_Bridge 0x7f15026f
int style Theme_MaterialComponents_DayNight_Dialog_Bridge 0x7f150270
int style Theme_MaterialComponents_DayNight_Dialog_FixedSize 0x7f150271
int style Theme_MaterialComponents_DayNight_Dialog_FixedSize_Bridge 0x7f150272
int style Theme_MaterialComponents_DayNight_Dialog_MinWidth 0x7f150273
int style Theme_MaterialComponents_DayNight_Dialog_MinWidth_Bridge 0x7f150274
int style Theme_MaterialComponents_DayNight_DialogWhenLarge 0x7f150275
int style Theme_MaterialComponents_DayNight_NoActionBar 0x7f150276
int style Theme_MaterialComponents_DayNight_NoActionBar_Bridge 0x7f150277
int style Theme_MaterialComponents_Dialog 0x7f150278
int style Theme_MaterialComponents_Dialog_Alert 0x7f150279
int style Theme_MaterialComponents_Dialog_Alert_Bridge 0x7f15027a
int style Theme_MaterialComponents_Dialog_Bridge 0x7f15027b
int style Theme_MaterialComponents_Dialog_FixedSize 0x7f15027c
int style Theme_MaterialComponents_Dialog_FixedSize_Bridge 0x7f15027d
int style Theme_MaterialComponents_Dialog_MinWidth 0x7f15027e
int style Theme_MaterialComponents_Dialog_MinWidth_Bridge 0x7f15027f
int style Theme_MaterialComponents_DialogWhenLarge 0x7f150280
int style Theme_MaterialComponents_Light 0x7f150281
int style Theme_MaterialComponents_Light_BottomSheetDialog 0x7f150282
int style Theme_MaterialComponents_Light_Bridge 0x7f150283
int style Theme_MaterialComponents_Light_DarkActionBar 0x7f150284
int style Theme_MaterialComponents_Light_DarkActionBar_Bridge 0x7f150285
int style Theme_MaterialComponents_Light_Dialog 0x7f150286
int style Theme_MaterialComponents_Light_Dialog_Alert 0x7f150287
int style Theme_MaterialComponents_Light_Dialog_Alert_Bridge 0x7f150288
int style Theme_MaterialComponents_Light_Dialog_Bridge 0x7f150289
int style Theme_MaterialComponents_Light_Dialog_FixedSize 0x7f15028a
int style Theme_MaterialComponents_Light_Dialog_FixedSize_Bridge 0x7f15028b
int style Theme_MaterialComponents_Light_Dialog_MinWidth 0x7f15028c
int style Theme_MaterialComponents_Light_Dialog_MinWidth_Bridge 0x7f15028d
int style Theme_MaterialComponents_Light_DialogWhenLarge 0x7f15028e
int style Theme_MaterialComponents_Light_NoActionBar 0x7f15028f
int style Theme_MaterialComponents_Light_NoActionBar_Bridge 0x7f150290
int style Theme_MaterialComponents_NoActionBar 0x7f150291
int style Theme_MaterialComponents_NoActionBar_Bridge 0x7f150292
int style Theme_SplashScreen 0x7f150293
int style Theme_SplashScreen_Common 0x7f150294
int style Theme_SplashScreen_IconBackground 0x7f150295
int style ThemeOverlay_AppCompat 0x7f150296
int style ThemeOverlay_AppCompat_ActionBar 0x7f150297
int style ThemeOverlay_AppCompat_Dark 0x7f150298
int style ThemeOverlay_AppCompat_Dark_ActionBar 0x7f150299
int style ThemeOverlay_AppCompat_DayNight 0x7f15029a
int style ThemeOverlay_AppCompat_DayNight_ActionBar 0x7f15029b
int style ThemeOverlay_AppCompat_Dialog 0x7f15029c
int style ThemeOverlay_AppCompat_Dialog_Alert 0x7f15029d
int style ThemeOverlay_AppCompat_Light 0x7f15029e
int style ThemeOverlay_Design_TextInputEditText 0x7f15029f
int style ThemeOverlay_Material3 0x7f1502a0
int style ThemeOverlay_Material3_ActionBar 0x7f1502a1
int style ThemeOverlay_Material3_AutoCompleteTextView 0x7f1502a2
int style ThemeOverlay_Material3_AutoCompleteTextView_FilledBox 0x7f1502a3
int style ThemeOverlay_Material3_AutoCompleteTextView_FilledBox_Dense 0x7f1502a4
int style ThemeOverlay_Material3_AutoCompleteTextView_OutlinedBox 0x7f1502a5
int style ThemeOverlay_Material3_AutoCompleteTextView_OutlinedBox_Dense 0x7f1502a6
int style ThemeOverlay_Material3_BottomAppBar 0x7f1502a7
int style ThemeOverlay_Material3_BottomAppBar_Legacy 0x7f1502a8
int style ThemeOverlay_Material3_BottomSheetDialog 0x7f1502a9
int style ThemeOverlay_Material3_Button 0x7f1502aa
int style ThemeOverlay_Material3_Button_ElevatedButton 0x7f1502ab
int style ThemeOverlay_Material3_Button_IconButton 0x7f1502ac
int style ThemeOverlay_Material3_Button_IconButton_Filled 0x7f1502ad
int style ThemeOverlay_Material3_Button_IconButton_Filled_Tonal 0x7f1502ae
int style ThemeOverlay_Material3_Button_TextButton 0x7f1502af
int style ThemeOverlay_Material3_Button_TextButton_Snackbar 0x7f1502b0
int style ThemeOverlay_Material3_Button_TonalButton 0x7f1502b1
int style ThemeOverlay_Material3_Chip 0x7f1502b2
int style ThemeOverlay_Material3_Chip_Assist 0x7f1502b3
int style ThemeOverlay_Material3_Dark 0x7f1502b4
int style ThemeOverlay_Material3_Dark_ActionBar 0x7f1502b5
int style ThemeOverlay_Material3_DayNight_BottomSheetDialog 0x7f1502b6
int style ThemeOverlay_Material3_DayNight_SideSheetDialog 0x7f1502b7
int style ThemeOverlay_Material3_Dialog 0x7f1502b8
int style ThemeOverlay_Material3_Dialog_Alert 0x7f1502b9
int style ThemeOverlay_Material3_Dialog_Alert_Framework 0x7f1502ba
int style ThemeOverlay_Material3_DynamicColors_Dark 0x7f1502bb
int style ThemeOverlay_Material3_DynamicColors_DayNight 0x7f1502bc
int style ThemeOverlay_Material3_DynamicColors_Light 0x7f1502bd
int style ThemeOverlay_Material3_ExtendedFloatingActionButton_Primary 0x7f1502be
int style ThemeOverlay_Material3_ExtendedFloatingActionButton_Secondary 0x7f1502bf
int style ThemeOverlay_Material3_ExtendedFloatingActionButton_Surface 0x7f1502c0
int style ThemeOverlay_Material3_ExtendedFloatingActionButton_Tertiary 0x7f1502c1
int style ThemeOverlay_Material3_FloatingActionButton_Primary 0x7f1502c2
int style ThemeOverlay_Material3_FloatingActionButton_Secondary 0x7f1502c3
int style ThemeOverlay_Material3_FloatingActionButton_Surface 0x7f1502c4
int style ThemeOverlay_Material3_FloatingActionButton_Tertiary 0x7f1502c5
int style ThemeOverlay_Material3_HarmonizedColors 0x7f1502c6
int style ThemeOverlay_Material3_HarmonizedColors_Empty 0x7f1502c7
int style ThemeOverlay_Material3_Light 0x7f1502c8
int style ThemeOverlay_Material3_Light_Dialog_Alert_Framework 0x7f1502c9
int style ThemeOverlay_Material3_MaterialAlertDialog 0x7f1502ca
int style ThemeOverlay_Material3_MaterialAlertDialog_Centered 0x7f1502cb
int style ThemeOverlay_Material3_MaterialCalendar 0x7f1502cc
int style ThemeOverlay_Material3_MaterialCalendar_Fullscreen 0x7f1502cd
int style ThemeOverlay_Material3_MaterialCalendar_HeaderCancelButton 0x7f1502ce
int style ThemeOverlay_Material3_MaterialTimePicker 0x7f1502cf
int style ThemeOverlay_Material3_MaterialTimePicker_Display_TextInputEditText 0x7f1502d0
int style ThemeOverlay_Material3_NavigationView 0x7f1502d1
int style ThemeOverlay_Material3_PersonalizedColors 0x7f1502d2
int style ThemeOverlay_Material3_Search 0x7f1502d3
int style ThemeOverlay_Material3_SideSheetDialog 0x7f1502d4
int style ThemeOverlay_Material3_Snackbar 0x7f1502d5
int style ThemeOverlay_Material3_TextInputEditText 0x7f1502d6
int style ThemeOverlay_Material3_TextInputEditText_FilledBox 0x7f1502d7
int style ThemeOverlay_Material3_TextInputEditText_FilledBox_Dense 0x7f1502d8
int style ThemeOverlay_Material3_TextInputEditText_OutlinedBox 0x7f1502d9
int style ThemeOverlay_Material3_TextInputEditText_OutlinedBox_Dense 0x7f1502da
int style ThemeOverlay_Material3_Toolbar_Surface 0x7f1502db
int style ThemeOverlay_MaterialAlertDialog_Material3_Title_Icon 0x7f1502dc
int style ThemeOverlay_MaterialComponents 0x7f1502dd
int style ThemeOverlay_MaterialComponents_ActionBar 0x7f1502de
int style ThemeOverlay_MaterialComponents_ActionBar_Primary 0x7f1502df
int style ThemeOverlay_MaterialComponents_ActionBar_Surface 0x7f1502e0
int style ThemeOverlay_MaterialComponents_AutoCompleteTextView 0x7f1502e1
int style ThemeOverlay_MaterialComponents_AutoCompleteTextView_FilledBox 0x7f1502e2
int style ThemeOverlay_MaterialComponents_AutoCompleteTextView_FilledBox_Dense 0x7f1502e3
int style ThemeOverlay_MaterialComponents_AutoCompleteTextView_OutlinedBox 0x7f1502e4
int style ThemeOverlay_MaterialComponents_AutoCompleteTextView_OutlinedBox_Dense 0x7f1502e5
int style ThemeOverlay_MaterialComponents_BottomAppBar_Primary 0x7f1502e6
int style ThemeOverlay_MaterialComponents_BottomAppBar_Surface 0x7f1502e7
int style ThemeOverlay_MaterialComponents_BottomSheetDialog 0x7f1502e8
int style ThemeOverlay_MaterialComponents_Dark 0x7f1502e9
int style ThemeOverlay_MaterialComponents_Dark_ActionBar 0x7f1502ea
int style ThemeOverlay_MaterialComponents_DayNight_BottomSheetDialog 0x7f1502eb
int style ThemeOverlay_MaterialComponents_Dialog 0x7f1502ec
int style ThemeOverlay_MaterialComponents_Dialog_Alert 0x7f1502ed
int style ThemeOverlay_MaterialComponents_Dialog_Alert_Framework 0x7f1502ee
int style ThemeOverlay_MaterialComponents_Light 0x7f1502ef
int style ThemeOverlay_MaterialComponents_Light_Dialog_Alert_Framework 0x7f1502f0
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog 0x7f1502f1
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Centered 0x7f1502f2
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date 0x7f1502f3
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Calendar 0x7f1502f4
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Header_Text 0x7f1502f5
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Header_Text_Day 0x7f1502f6
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Spinner 0x7f1502f7
int style ThemeOverlay_MaterialComponents_MaterialCalendar 0x7f1502f8
int style ThemeOverlay_MaterialComponents_MaterialCalendar_Fullscreen 0x7f1502f9
int style ThemeOverlay_MaterialComponents_TextInputEditText 0x7f1502fa
int style ThemeOverlay_MaterialComponents_TextInputEditText_FilledBox 0x7f1502fb
int style ThemeOverlay_MaterialComponents_TextInputEditText_FilledBox_Dense 0x7f1502fc
int style ThemeOverlay_MaterialComponents_TextInputEditText_OutlinedBox 0x7f1502fd
int style ThemeOverlay_MaterialComponents_TextInputEditText_OutlinedBox_Dense 0x7f1502fe
int style ThemeOverlay_MaterialComponents_TimePicker 0x7f1502ff
int style ThemeOverlay_MaterialComponents_TimePicker_Display 0x7f150300
int style ThemeOverlay_MaterialComponents_TimePicker_Display_TextInputEditText 0x7f150301
int style ThemeOverlay_MaterialComponents_Toolbar_Popup_Primary 0x7f150302
int style ThemeOverlay_MaterialComponents_Toolbar_Primary 0x7f150303
int style ThemeOverlay_MaterialComponents_Toolbar_Surface 0x7f150304
int style Widget_AppCompat_ActionBar 0x7f150305
int style Widget_AppCompat_ActionBar_Solid 0x7f150306
int style Widget_AppCompat_ActionBar_TabBar 0x7f150307
int style Widget_AppCompat_ActionBar_TabText 0x7f150308
int style Widget_AppCompat_ActionBar_TabView 0x7f150309
int style Widget_AppCompat_ActionButton 0x7f15030a
int style Widget_AppCompat_ActionButton_CloseMode 0x7f15030b
int style Widget_AppCompat_ActionButton_Overflow 0x7f15030c
int style Widget_AppCompat_ActionMode 0x7f15030d
int style Widget_AppCompat_ActivityChooserView 0x7f15030e
int style Widget_AppCompat_AutoCompleteTextView 0x7f15030f
int style Widget_AppCompat_Button 0x7f150310
int style Widget_AppCompat_Button_Borderless 0x7f150311
int style Widget_AppCompat_Button_Borderless_Colored 0x7f150312
int style Widget_AppCompat_Button_ButtonBar_AlertDialog 0x7f150313
int style Widget_AppCompat_Button_Colored 0x7f150314
int style Widget_AppCompat_Button_Small 0x7f150315
int style Widget_AppCompat_ButtonBar 0x7f150316
int style Widget_AppCompat_ButtonBar_AlertDialog 0x7f150317
int style Widget_AppCompat_CompoundButton_CheckBox 0x7f150318
int style Widget_AppCompat_CompoundButton_RadioButton 0x7f150319
int style Widget_AppCompat_CompoundButton_Switch 0x7f15031a
int style Widget_AppCompat_DrawerArrowToggle 0x7f15031b
int style Widget_AppCompat_DropDownItem_Spinner 0x7f15031c
int style Widget_AppCompat_EditText 0x7f15031d
int style Widget_AppCompat_ImageButton 0x7f15031e
int style Widget_AppCompat_Light_ActionBar 0x7f15031f
int style Widget_AppCompat_Light_ActionBar_Solid 0x7f150320
int style Widget_AppCompat_Light_ActionBar_Solid_Inverse 0x7f150321
int style Widget_AppCompat_Light_ActionBar_TabBar 0x7f150322
int style Widget_AppCompat_Light_ActionBar_TabBar_Inverse 0x7f150323
int style Widget_AppCompat_Light_ActionBar_TabText 0x7f150324
int style Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x7f150325
int style Widget_AppCompat_Light_ActionBar_TabView 0x7f150326
int style Widget_AppCompat_Light_ActionBar_TabView_Inverse 0x7f150327
int style Widget_AppCompat_Light_ActionButton 0x7f150328
int style Widget_AppCompat_Light_ActionButton_CloseMode 0x7f150329
int style Widget_AppCompat_Light_ActionButton_Overflow 0x7f15032a
int style Widget_AppCompat_Light_ActionMode_Inverse 0x7f15032b
int style Widget_AppCompat_Light_ActivityChooserView 0x7f15032c
int style Widget_AppCompat_Light_AutoCompleteTextView 0x7f15032d
int style Widget_AppCompat_Light_DropDownItem_Spinner 0x7f15032e
int style Widget_AppCompat_Light_ListPopupWindow 0x7f15032f
int style Widget_AppCompat_Light_ListView_DropDown 0x7f150330
int style Widget_AppCompat_Light_PopupMenu 0x7f150331
int style Widget_AppCompat_Light_PopupMenu_Overflow 0x7f150332
int style Widget_AppCompat_Light_SearchView 0x7f150333
int style Widget_AppCompat_Light_Spinner_DropDown_ActionBar 0x7f150334
int style Widget_AppCompat_ListMenuView 0x7f150335
int style Widget_AppCompat_ListPopupWindow 0x7f150336
int style Widget_AppCompat_ListView 0x7f150337
int style Widget_AppCompat_ListView_DropDown 0x7f150338
int style Widget_AppCompat_ListView_Menu 0x7f150339
int style Widget_AppCompat_PopupMenu 0x7f15033a
int style Widget_AppCompat_PopupMenu_Overflow 0x7f15033b
int style Widget_AppCompat_PopupWindow 0x7f15033c
int style Widget_AppCompat_ProgressBar 0x7f15033d
int style Widget_AppCompat_ProgressBar_Horizontal 0x7f15033e
int style Widget_AppCompat_RatingBar 0x7f15033f
int style Widget_AppCompat_RatingBar_Indicator 0x7f150340
int style Widget_AppCompat_RatingBar_Small 0x7f150341
int style Widget_AppCompat_SearchView 0x7f150342
int style Widget_AppCompat_SearchView_ActionBar 0x7f150343
int style Widget_AppCompat_SeekBar 0x7f150344
int style Widget_AppCompat_SeekBar_Discrete 0x7f150345
int style Widget_AppCompat_Spinner 0x7f150346
int style Widget_AppCompat_Spinner_DropDown 0x7f150347
int style Widget_AppCompat_Spinner_DropDown_ActionBar 0x7f150348
int style Widget_AppCompat_Spinner_Underlined 0x7f150349
int style Widget_AppCompat_TextView 0x7f15034a
int style Widget_AppCompat_TextView_SpinnerItem 0x7f15034b
int style Widget_AppCompat_Toolbar 0x7f15034c
int style Widget_AppCompat_Toolbar_Button_Navigation 0x7f15034d
int style Widget_Compat_NotificationActionContainer 0x7f15034e
int style Widget_Compat_NotificationActionText 0x7f15034f
int style Widget_Design_AppBarLayout 0x7f150350
int style Widget_Design_BottomNavigationView 0x7f150351
int style Widget_Design_BottomSheet_Modal 0x7f150352
int style Widget_Design_CollapsingToolbar 0x7f150353
int style Widget_Design_FloatingActionButton 0x7f150354
int style Widget_Design_NavigationView 0x7f150355
int style Widget_Design_ScrimInsetsFrameLayout 0x7f150356
int style Widget_Design_Snackbar 0x7f150357
int style Widget_Design_TabLayout 0x7f150358
int style Widget_Design_TextInputEditText 0x7f150359
int style Widget_Design_TextInputLayout 0x7f15035a
int style Widget_Material3_ActionBar_Solid 0x7f15035b
int style Widget_Material3_ActionMode 0x7f15035c
int style Widget_Material3_AppBarLayout 0x7f15035d
int style Widget_Material3_AutoCompleteTextView_FilledBox 0x7f15035e
int style Widget_Material3_AutoCompleteTextView_FilledBox_Dense 0x7f15035f
int style Widget_Material3_AutoCompleteTextView_OutlinedBox 0x7f150360
int style Widget_Material3_AutoCompleteTextView_OutlinedBox_Dense 0x7f150361
int style Widget_Material3_Badge 0x7f150362
int style Widget_Material3_BottomAppBar 0x7f150363
int style Widget_Material3_BottomAppBar_Button_Navigation 0x7f150364
int style Widget_Material3_BottomAppBar_Legacy 0x7f150365
int style Widget_Material3_BottomNavigationView 0x7f150366
int style Widget_Material3_BottomNavigationView_ActiveIndicator 0x7f150367
int style Widget_Material3_BottomSheet 0x7f150368
int style Widget_Material3_BottomSheet_DragHandle 0x7f150369
int style Widget_Material3_BottomSheet_Modal 0x7f15036a
int style Widget_Material3_Button 0x7f15036b
int style Widget_Material3_Button_ElevatedButton 0x7f15036c
int style Widget_Material3_Button_ElevatedButton_Icon 0x7f15036d
int style Widget_Material3_Button_Icon 0x7f15036e
int style Widget_Material3_Button_IconButton 0x7f15036f
int style Widget_Material3_Button_IconButton_Filled 0x7f150370
int style Widget_Material3_Button_IconButton_Filled_Tonal 0x7f150371
int style Widget_Material3_Button_IconButton_Outlined 0x7f150372
int style Widget_Material3_Button_OutlinedButton 0x7f150373
int style Widget_Material3_Button_OutlinedButton_Icon 0x7f150374
int style Widget_Material3_Button_TextButton 0x7f150375
int style Widget_Material3_Button_TextButton_Dialog 0x7f150376
int style Widget_Material3_Button_TextButton_Dialog_Flush 0x7f150377
int style Widget_Material3_Button_TextButton_Dialog_Icon 0x7f150378
int style Widget_Material3_Button_TextButton_Icon 0x7f150379
int style Widget_Material3_Button_TextButton_Snackbar 0x7f15037a
int style Widget_Material3_Button_TonalButton 0x7f15037b
int style Widget_Material3_Button_TonalButton_Icon 0x7f15037c
int style Widget_Material3_Button_UnelevatedButton 0x7f15037d
int style Widget_Material3_CardView_Elevated 0x7f15037e
int style Widget_Material3_CardView_Filled 0x7f15037f
int style Widget_Material3_CardView_Outlined 0x7f150380
int style Widget_Material3_CheckedTextView 0x7f150381
int style Widget_Material3_Chip_Assist 0x7f150382
int style Widget_Material3_Chip_Assist_Elevated 0x7f150383
int style Widget_Material3_Chip_Filter 0x7f150384
int style Widget_Material3_Chip_Filter_Elevated 0x7f150385
int style Widget_Material3_Chip_Input 0x7f150386
int style Widget_Material3_Chip_Input_Elevated 0x7f150387
int style Widget_Material3_Chip_Input_Icon 0x7f150388
int style Widget_Material3_Chip_Input_Icon_Elevated 0x7f150389
int style Widget_Material3_Chip_Suggestion 0x7f15038a
int style Widget_Material3_Chip_Suggestion_Elevated 0x7f15038b
int style Widget_Material3_ChipGroup 0x7f15038c
int style Widget_Material3_CircularProgressIndicator 0x7f15038d
int style Widget_Material3_CircularProgressIndicator_ExtraSmall 0x7f15038e
int style Widget_Material3_CircularProgressIndicator_Medium 0x7f15038f
int style Widget_Material3_CircularProgressIndicator_Small 0x7f150390
int style Widget_Material3_CollapsingToolbar 0x7f150391
int style Widget_Material3_CollapsingToolbar_Large 0x7f150392
int style Widget_Material3_CollapsingToolbar_Medium 0x7f150393
int style Widget_Material3_CompoundButton_CheckBox 0x7f150394
int style Widget_Material3_CompoundButton_MaterialSwitch 0x7f150395
int style Widget_Material3_CompoundButton_RadioButton 0x7f150396
int style Widget_Material3_CompoundButton_Switch 0x7f150397
int style Widget_Material3_DrawerLayout 0x7f150398
int style Widget_Material3_ExtendedFloatingActionButton_Icon_Primary 0x7f150399
int style Widget_Material3_ExtendedFloatingActionButton_Icon_Secondary 0x7f15039a
int style Widget_Material3_ExtendedFloatingActionButton_Icon_Surface 0x7f15039b
int style Widget_Material3_ExtendedFloatingActionButton_Icon_Tertiary 0x7f15039c
int style Widget_Material3_ExtendedFloatingActionButton_Primary 0x7f15039d
int style Widget_Material3_ExtendedFloatingActionButton_Secondary 0x7f15039e
int style Widget_Material3_ExtendedFloatingActionButton_Surface 0x7f15039f
int style Widget_Material3_ExtendedFloatingActionButton_Tertiary 0x7f1503a0
int style Widget_Material3_FloatingActionButton_Large_Primary 0x7f1503a1
int style Widget_Material3_FloatingActionButton_Large_Secondary 0x7f1503a2
int style Widget_Material3_FloatingActionButton_Large_Surface 0x7f1503a3
int style Widget_Material3_FloatingActionButton_Large_Tertiary 0x7f1503a4
int style Widget_Material3_FloatingActionButton_Primary 0x7f1503a5
int style Widget_Material3_FloatingActionButton_Secondary 0x7f1503a6
int style Widget_Material3_FloatingActionButton_Small_Primary 0x7f1503a7
int style Widget_Material3_FloatingActionButton_Small_Secondary 0x7f1503a8
int style Widget_Material3_FloatingActionButton_Small_Surface 0x7f1503a9
int style Widget_Material3_FloatingActionButton_Small_Tertiary 0x7f1503aa
int style Widget_Material3_FloatingActionButton_Surface 0x7f1503ab
int style Widget_Material3_FloatingActionButton_Tertiary 0x7f1503ac
int style Widget_Material3_Light_ActionBar_Solid 0x7f1503ad
int style Widget_Material3_LinearProgressIndicator 0x7f1503ae
int style Widget_Material3_MaterialButtonToggleGroup 0x7f1503af
int style Widget_Material3_MaterialCalendar 0x7f1503b0
int style Widget_Material3_MaterialCalendar_Day 0x7f1503b1
int style Widget_Material3_MaterialCalendar_Day_Invalid 0x7f1503b2
int style Widget_Material3_MaterialCalendar_Day_Selected 0x7f1503b3
int style Widget_Material3_MaterialCalendar_Day_Today 0x7f1503b4
int style Widget_Material3_MaterialCalendar_DayOfWeekLabel 0x7f1503b5
int style Widget_Material3_MaterialCalendar_DayTextView 0x7f1503b6
int style Widget_Material3_MaterialCalendar_Fullscreen 0x7f1503b7
int style Widget_Material3_MaterialCalendar_HeaderCancelButton 0x7f1503b8
int style Widget_Material3_MaterialCalendar_HeaderDivider 0x7f1503b9
int style Widget_Material3_MaterialCalendar_HeaderLayout 0x7f1503ba
int style Widget_Material3_MaterialCalendar_HeaderLayout_Fullscreen 0x7f1503bb
int style Widget_Material3_MaterialCalendar_HeaderSelection 0x7f1503bc
int style Widget_Material3_MaterialCalendar_HeaderSelection_Fullscreen 0x7f1503bd
int style Widget_Material3_MaterialCalendar_HeaderTitle 0x7f1503be
int style Widget_Material3_MaterialCalendar_HeaderToggleButton 0x7f1503bf
int style Widget_Material3_MaterialCalendar_Item 0x7f1503c0
int style Widget_Material3_MaterialCalendar_MonthNavigationButton 0x7f1503c1
int style Widget_Material3_MaterialCalendar_MonthTextView 0x7f1503c2
int style Widget_Material3_MaterialCalendar_Year 0x7f1503c3
int style Widget_Material3_MaterialCalendar_Year_Selected 0x7f1503c4
int style Widget_Material3_MaterialCalendar_Year_Today 0x7f1503c5
int style Widget_Material3_MaterialCalendar_YearNavigationButton 0x7f1503c6
int style Widget_Material3_MaterialDivider 0x7f1503c7
int style Widget_Material3_MaterialDivider_Heavy 0x7f1503c8
int style Widget_Material3_MaterialTimePicker 0x7f1503c9
int style Widget_Material3_MaterialTimePicker_Button 0x7f1503ca
int style Widget_Material3_MaterialTimePicker_Clock 0x7f1503cb
int style Widget_Material3_MaterialTimePicker_Display 0x7f1503cc
int style Widget_Material3_MaterialTimePicker_Display_Divider 0x7f1503cd
int style Widget_Material3_MaterialTimePicker_Display_HelperText 0x7f1503ce
int style Widget_Material3_MaterialTimePicker_Display_TextInputEditText 0x7f1503cf
int style Widget_Material3_MaterialTimePicker_Display_TextInputLayout 0x7f1503d0
int style Widget_Material3_MaterialTimePicker_ImageButton 0x7f1503d1
int style Widget_Material3_NavigationRailView 0x7f1503d2
int style Widget_Material3_NavigationRailView_ActiveIndicator 0x7f1503d3
int style Widget_Material3_NavigationView 0x7f1503d4
int style Widget_Material3_PopupMenu 0x7f1503d5
int style Widget_Material3_PopupMenu_ContextMenu 0x7f1503d6
int style Widget_Material3_PopupMenu_ListPopupWindow 0x7f1503d7
int style Widget_Material3_PopupMenu_Overflow 0x7f1503d8
int style Widget_Material3_Search_ActionButton_Overflow 0x7f1503d9
int style Widget_Material3_Search_Toolbar_Button_Navigation 0x7f1503da
int style Widget_Material3_SearchBar 0x7f1503db
int style Widget_Material3_SearchBar_Outlined 0x7f1503dc
int style Widget_Material3_SearchView 0x7f1503dd
int style Widget_Material3_SearchView_Prefix 0x7f1503de
int style Widget_Material3_SearchView_Toolbar 0x7f1503df
int style Widget_Material3_SideSheet 0x7f1503e0
int style Widget_Material3_SideSheet_Modal 0x7f1503e1
int style Widget_Material3_Slider 0x7f1503e2
int style Widget_Material3_Slider_Label 0x7f1503e3
int style Widget_Material3_Snackbar 0x7f1503e4
int style Widget_Material3_Snackbar_FullWidth 0x7f1503e5
int style Widget_Material3_Snackbar_TextView 0x7f1503e6
int style Widget_Material3_TabLayout 0x7f1503e7
int style Widget_Material3_TabLayout_OnSurface 0x7f1503e8
int style Widget_Material3_TabLayout_Secondary 0x7f1503e9
int style Widget_Material3_TextInputEditText_FilledBox 0x7f1503ea
int style Widget_Material3_TextInputEditText_FilledBox_Dense 0x7f1503eb
int style Widget_Material3_TextInputEditText_OutlinedBox 0x7f1503ec
int style Widget_Material3_TextInputEditText_OutlinedBox_Dense 0x7f1503ed
int style Widget_Material3_TextInputLayout_FilledBox 0x7f1503ee
int style Widget_Material3_TextInputLayout_FilledBox_Dense 0x7f1503ef
int style Widget_Material3_TextInputLayout_FilledBox_Dense_ExposedDropdownMenu 0x7f1503f0
int style Widget_Material3_TextInputLayout_FilledBox_ExposedDropdownMenu 0x7f1503f1
int style Widget_Material3_TextInputLayout_OutlinedBox 0x7f1503f2
int style Widget_Material3_TextInputLayout_OutlinedBox_Dense 0x7f1503f3
int style Widget_Material3_TextInputLayout_OutlinedBox_Dense_ExposedDropdownMenu 0x7f1503f4
int style Widget_Material3_TextInputLayout_OutlinedBox_ExposedDropdownMenu 0x7f1503f5
int style Widget_Material3_Toolbar 0x7f1503f6
int style Widget_Material3_Toolbar_OnSurface 0x7f1503f7
int style Widget_Material3_Toolbar_Surface 0x7f1503f8
int style Widget_Material3_Tooltip 0x7f1503f9
int style Widget_MaterialComponents_ActionBar_Primary 0x7f1503fa
int style Widget_MaterialComponents_ActionBar_PrimarySurface 0x7f1503fb
int style Widget_MaterialComponents_ActionBar_Solid 0x7f1503fc
int style Widget_MaterialComponents_ActionBar_Surface 0x7f1503fd
int style Widget_MaterialComponents_ActionMode 0x7f1503fe
int style Widget_MaterialComponents_AppBarLayout_Primary 0x7f1503ff
int style Widget_MaterialComponents_AppBarLayout_PrimarySurface 0x7f150400
int style Widget_MaterialComponents_AppBarLayout_Surface 0x7f150401
int style Widget_MaterialComponents_AutoCompleteTextView_FilledBox 0x7f150402
int style Widget_MaterialComponents_AutoCompleteTextView_FilledBox_Dense 0x7f150403
int style Widget_MaterialComponents_AutoCompleteTextView_OutlinedBox 0x7f150404
int style Widget_MaterialComponents_AutoCompleteTextView_OutlinedBox_Dense 0x7f150405
int style Widget_MaterialComponents_Badge 0x7f150406
int style Widget_MaterialComponents_BottomAppBar 0x7f150407
int style Widget_MaterialComponents_BottomAppBar_Colored 0x7f150408
int style Widget_MaterialComponents_BottomAppBar_PrimarySurface 0x7f150409
int style Widget_MaterialComponents_BottomNavigationView 0x7f15040a
int style Widget_MaterialComponents_BottomNavigationView_Colored 0x7f15040b
int style Widget_MaterialComponents_BottomNavigationView_PrimarySurface 0x7f15040c
int style Widget_MaterialComponents_BottomSheet 0x7f15040d
int style Widget_MaterialComponents_BottomSheet_Modal 0x7f15040e
int style Widget_MaterialComponents_Button 0x7f15040f
int style Widget_MaterialComponents_Button_Icon 0x7f150410
int style Widget_MaterialComponents_Button_OutlinedButton 0x7f150411
int style Widget_MaterialComponents_Button_OutlinedButton_Icon 0x7f150412
int style Widget_MaterialComponents_Button_TextButton 0x7f150413
int style Widget_MaterialComponents_Button_TextButton_Dialog 0x7f150414
int style Widget_MaterialComponents_Button_TextButton_Dialog_Flush 0x7f150415
int style Widget_MaterialComponents_Button_TextButton_Dialog_Icon 0x7f150416
int style Widget_MaterialComponents_Button_TextButton_Icon 0x7f150417
int style Widget_MaterialComponents_Button_TextButton_Snackbar 0x7f150418
int style Widget_MaterialComponents_Button_UnelevatedButton 0x7f150419
int style Widget_MaterialComponents_Button_UnelevatedButton_Icon 0x7f15041a
int style Widget_MaterialComponents_CardView 0x7f15041b
int style Widget_MaterialComponents_CheckedTextView 0x7f15041c
int style Widget_MaterialComponents_Chip_Action 0x7f15041d
int style Widget_MaterialComponents_Chip_Choice 0x7f15041e
int style Widget_MaterialComponents_Chip_Entry 0x7f15041f
int style Widget_MaterialComponents_Chip_Filter 0x7f150420
int style Widget_MaterialComponents_ChipGroup 0x7f150421
int style Widget_MaterialComponents_CircularProgressIndicator 0x7f150422
int style Widget_MaterialComponents_CircularProgressIndicator_ExtraSmall 0x7f150423
int style Widget_MaterialComponents_CircularProgressIndicator_Medium 0x7f150424
int style Widget_MaterialComponents_CircularProgressIndicator_Small 0x7f150425
int style Widget_MaterialComponents_CollapsingToolbar 0x7f150426
int style Widget_MaterialComponents_CompoundButton_CheckBox 0x7f150427
int style Widget_MaterialComponents_CompoundButton_RadioButton 0x7f150428
int style Widget_MaterialComponents_CompoundButton_Switch 0x7f150429
int style Widget_MaterialComponents_ExtendedFloatingActionButton 0x7f15042a
int style Widget_MaterialComponents_ExtendedFloatingActionButton_Icon 0x7f15042b
int style Widget_MaterialComponents_FloatingActionButton 0x7f15042c
int style Widget_MaterialComponents_Light_ActionBar_Solid 0x7f15042d
int style Widget_MaterialComponents_LinearProgressIndicator 0x7f15042e
int style Widget_MaterialComponents_MaterialButtonToggleGroup 0x7f15042f
int style Widget_MaterialComponents_MaterialCalendar 0x7f150430
int style Widget_MaterialComponents_MaterialCalendar_Day 0x7f150431
int style Widget_MaterialComponents_MaterialCalendar_Day_Invalid 0x7f150432
int style Widget_MaterialComponents_MaterialCalendar_Day_Selected 0x7f150433
int style Widget_MaterialComponents_MaterialCalendar_Day_Today 0x7f150434
int style Widget_MaterialComponents_MaterialCalendar_DayOfWeekLabel 0x7f150435
int style Widget_MaterialComponents_MaterialCalendar_DayTextView 0x7f150436
int style Widget_MaterialComponents_MaterialCalendar_Fullscreen 0x7f150437
int style Widget_MaterialComponents_MaterialCalendar_HeaderCancelButton 0x7f150438
int style Widget_MaterialComponents_MaterialCalendar_HeaderConfirmButton 0x7f150439
int style Widget_MaterialComponents_MaterialCalendar_HeaderDivider 0x7f15043a
int style Widget_MaterialComponents_MaterialCalendar_HeaderLayout 0x7f15043b
int style Widget_MaterialComponents_MaterialCalendar_HeaderLayout_Fullscreen 0x7f15043c
int style Widget_MaterialComponents_MaterialCalendar_HeaderSelection 0x7f15043d
int style Widget_MaterialComponents_MaterialCalendar_HeaderSelection_Fullscreen 0x7f15043e
int style Widget_MaterialComponents_MaterialCalendar_HeaderTitle 0x7f15043f
int style Widget_MaterialComponents_MaterialCalendar_HeaderToggleButton 0x7f150440
int style Widget_MaterialComponents_MaterialCalendar_Item 0x7f150441
int style Widget_MaterialComponents_MaterialCalendar_MonthNavigationButton 0x7f150442
int style Widget_MaterialComponents_MaterialCalendar_MonthTextView 0x7f150443
int style Widget_MaterialComponents_MaterialCalendar_Year 0x7f150444
int style Widget_MaterialComponents_MaterialCalendar_Year_Selected 0x7f150445
int style Widget_MaterialComponents_MaterialCalendar_Year_Today 0x7f150446
int style Widget_MaterialComponents_MaterialCalendar_YearNavigationButton 0x7f150447
int style Widget_MaterialComponents_MaterialDivider 0x7f150448
int style Widget_MaterialComponents_NavigationRailView 0x7f150449
int style Widget_MaterialComponents_NavigationRailView_Colored 0x7f15044a
int style Widget_MaterialComponents_NavigationRailView_Colored_Compact 0x7f15044b
int style Widget_MaterialComponents_NavigationRailView_Compact 0x7f15044c
int style Widget_MaterialComponents_NavigationRailView_PrimarySurface 0x7f15044d
int style Widget_MaterialComponents_NavigationView 0x7f15044e
int style Widget_MaterialComponents_PopupMenu 0x7f15044f
int style Widget_MaterialComponents_PopupMenu_ContextMenu 0x7f150450
int style Widget_MaterialComponents_PopupMenu_ListPopupWindow 0x7f150451
int style Widget_MaterialComponents_PopupMenu_Overflow 0x7f150452
int style Widget_MaterialComponents_ProgressIndicator 0x7f150453
int style Widget_MaterialComponents_ShapeableImageView 0x7f150454
int style Widget_MaterialComponents_Slider 0x7f150455
int style Widget_MaterialComponents_Snackbar 0x7f150456
int style Widget_MaterialComponents_Snackbar_FullWidth 0x7f150457
int style Widget_MaterialComponents_Snackbar_TextView 0x7f150458
int style Widget_MaterialComponents_TabLayout 0x7f150459
int style Widget_MaterialComponents_TabLayout_Colored 0x7f15045a
int style Widget_MaterialComponents_TabLayout_PrimarySurface 0x7f15045b
int style Widget_MaterialComponents_TextInputEditText_FilledBox 0x7f15045c
int style Widget_MaterialComponents_TextInputEditText_FilledBox_Dense 0x7f15045d
int style Widget_MaterialComponents_TextInputEditText_OutlinedBox 0x7f15045e
int style Widget_MaterialComponents_TextInputEditText_OutlinedBox_Dense 0x7f15045f
int style Widget_MaterialComponents_TextInputLayout_FilledBox 0x7f150460
int style Widget_MaterialComponents_TextInputLayout_FilledBox_Dense 0x7f150461
int style Widget_MaterialComponents_TextInputLayout_FilledBox_Dense_ExposedDropdownMenu 0x7f150462
int style Widget_MaterialComponents_TextInputLayout_FilledBox_ExposedDropdownMenu 0x7f150463
int style Widget_MaterialComponents_TextInputLayout_OutlinedBox 0x7f150464
int style Widget_MaterialComponents_TextInputLayout_OutlinedBox_Dense 0x7f150465
int style Widget_MaterialComponents_TextInputLayout_OutlinedBox_Dense_ExposedDropdownMenu 0x7f150466
int style Widget_MaterialComponents_TextInputLayout_OutlinedBox_ExposedDropdownMenu 0x7f150467
int style Widget_MaterialComponents_TextView 0x7f150468
int style Widget_MaterialComponents_TimePicker 0x7f150469
int style Widget_MaterialComponents_TimePicker_Button 0x7f15046a
int style Widget_MaterialComponents_TimePicker_Clock 0x7f15046b
int style Widget_MaterialComponents_TimePicker_Display 0x7f15046c
int style Widget_MaterialComponents_TimePicker_Display_Divider 0x7f15046d
int style Widget_MaterialComponents_TimePicker_Display_HelperText 0x7f15046e
int style Widget_MaterialComponents_TimePicker_Display_TextInputEditText 0x7f15046f
int style Widget_MaterialComponents_TimePicker_Display_TextInputLayout 0x7f150470
int style Widget_MaterialComponents_TimePicker_ImageButton 0x7f150471
int style Widget_MaterialComponents_TimePicker_ImageButton_ShapeAppearance 0x7f150472
int style Widget_MaterialComponents_Toolbar 0x7f150473
int style Widget_MaterialComponents_Toolbar_Primary 0x7f150474
int style Widget_MaterialComponents_Toolbar_PrimarySurface 0x7f150475
int style Widget_MaterialComponents_Toolbar_Surface 0x7f150476
int style Widget_MaterialComponents_Tooltip 0x7f150477
int style Widget_Support_CoordinatorLayout 0x7f150478
int style blueButton 0x7f150479
int style button 0x7f15047a
int style grayButton 0x7f15047b
int style h1 0x7f15047c
int style h2 0x7f15047d
int style h3 0x7f15047e
int style outlinedButton 0x7f15047f
int style small 0x7f150480
int style subtitle 0x7f150481
int style text 0x7f150482
int style zxing_CaptureTheme 0x7f150483
int[] styleable ActionBar { 0x7f04004a, 0x7f040051, 0x7f040052, 0x7f04013e, 0x7f04013f, 0x7f040140, 0x7f040141, 0x7f040142, 0x7f040143, 0x7f04016b, 0x7f040182, 0x7f040184, 0x7f0401a3, 0x7f04022a, 0x7f040232, 0x7f04023e, 0x7f04023f, 0x7f040243, 0x7f040250, 0x7f040268, 0x7f0402df, 0x7f040359, 0x7f040392, 0x7f04039c, 0x7f04039d, 0x7f04042d, 0x7f040431, 0x7f0404a9, 0x7f0404b7 }
int styleable ActionBar_background 0
int styleable ActionBar_backgroundSplit 1
int styleable ActionBar_backgroundStacked 2
int styleable ActionBar_contentInsetEnd 3
int styleable ActionBar_contentInsetEndWithActions 4
int styleable ActionBar_contentInsetLeft 5
int styleable ActionBar_contentInsetRight 6
int styleable ActionBar_contentInsetStart 7
int styleable ActionBar_contentInsetStartWithNavigation 8
int styleable ActionBar_customNavigationLayout 9
int styleable ActionBar_displayOptions 10
int styleable ActionBar_divider 11
int styleable ActionBar_elevation 12
int styleable ActionBar_height 13
int styleable ActionBar_hideOnContentScroll 14
int styleable ActionBar_homeAsUpIndicator 15
int styleable ActionBar_homeLayout 16
int styleable ActionBar_icon 17
int styleable ActionBar_indeterminateProgressStyle 18
int styleable ActionBar_itemPadding 19
int styleable ActionBar_logo 20
int styleable ActionBar_navigationMode 21
int styleable ActionBar_popupTheme 22
int styleable ActionBar_progressBarPadding 23
int styleable ActionBar_progressBarStyle 24
int styleable ActionBar_subtitle 25
int styleable ActionBar_subtitleTextStyle 26
int styleable ActionBar_title 27
int styleable ActionBar_titleTextStyle 28
int[] styleable ActionBarLayout { 0x010100b3 }
int styleable ActionBarLayout_android_layout_gravity 0
int[] styleable ActionMenuItemView { 0x0101013f }
int styleable ActionMenuItemView_android_minWidth 0
int[] styleable ActionMenuView { }
int[] styleable ActionMode { 0x7f04004a, 0x7f040051, 0x7f0400da, 0x7f04022a, 0x7f040431, 0x7f0404b7 }
int styleable ActionMode_background 0
int styleable ActionMode_backgroundSplit 1
int styleable ActionMode_closeItemLayout 2
int styleable ActionMode_height 3
int styleable ActionMode_subtitleTextStyle 4
int styleable ActionMode_titleTextStyle 5
int[] styleable ActivityChooserView { 0x7f0401c0, 0x7f040256 }
int styleable ActivityChooserView_expandActivityOverflowButtonDrawable 0
int styleable ActivityChooserView_initialActivityCount 1
int[] styleable ActivityFilter { 0x7f040025, 0x7f040027 }
int styleable ActivityFilter_activityAction 0
int styleable ActivityFilter_activityName 1
int[] styleable ActivityNavigator { 0x01010003, 0x7f040000, 0x7f04016e, 0x7f04016f, 0x7f04045a }
int styleable ActivityNavigator_android_name 0
int styleable ActivityNavigator_action 1
int styleable ActivityNavigator_data 2
int styleable ActivityNavigator_dataPattern 3
int styleable ActivityNavigator_targetPackage 4
int[] styleable ActivityRule { 0x7f040033 }
int styleable ActivityRule_alwaysExpand 0
int[] styleable AlertDialog { 0x010100f2, 0x7f04008b, 0x7f04008e, 0x7f0402d4, 0x7f0402d5, 0x7f040354, 0x7f0403df, 0x7f0403f1 }
int styleable AlertDialog_android_layout 0
int styleable AlertDialog_buttonIconDimen 1
int styleable AlertDialog_buttonPanelSideLayout 2
int styleable AlertDialog_listItemLayout 3
int styleable AlertDialog_listLayout 4
int styleable AlertDialog_multiChoiceItemLayout 5
int styleable AlertDialog_showTitle 6
int styleable AlertDialog_singleChoiceItemLayout 7
int[] styleable AnimatedStateListDrawableCompat { 0x0101011c, 0x01010194, 0x01010195, 0x01010196, 0x0101030c, 0x0101030d }
int styleable AnimatedStateListDrawableCompat_android_dither 0
int styleable AnimatedStateListDrawableCompat_android_visible 1
int styleable AnimatedStateListDrawableCompat_android_variablePadding 2
int styleable AnimatedStateListDrawableCompat_android_constantSize 3
int styleable AnimatedStateListDrawableCompat_android_enterFadeDuration 4
int styleable AnimatedStateListDrawableCompat_android_exitFadeDuration 5
int[] styleable AnimatedStateListDrawableItem { 0x010100d0, 0x01010199 }
int styleable AnimatedStateListDrawableItem_android_id 0
int styleable AnimatedStateListDrawableItem_android_drawable 1
int[] styleable AnimatedStateListDrawableTransition { 0x01010199, 0x01010449, 0x0101044a, 0x0101044b }
int styleable AnimatedStateListDrawableTransition_android_drawable 0
int styleable AnimatedStateListDrawableTransition_android_toId 1
int styleable AnimatedStateListDrawableTransition_android_fromId 2
int styleable AnimatedStateListDrawableTransition_android_reversible 3
int[] styleable AppBarLayout { 0x010100d4, 0x0101048f, 0x01010540, 0x7f0401a3, 0x7f0401c1, 0x7f0402c8, 0x7f0402c9, 0x7f0402ca, 0x7f040423 }
int styleable AppBarLayout_android_background 0
int styleable AppBarLayout_android_touchscreenBlocksFocus 1
int styleable AppBarLayout_android_keyboardNavigationCluster 2
int styleable AppBarLayout_elevation 3
int styleable AppBarLayout_expanded 4
int styleable AppBarLayout_liftOnScroll 5
int styleable AppBarLayout_liftOnScrollColor 6
int styleable AppBarLayout_liftOnScrollTargetViewId 7
int styleable AppBarLayout_statusBarForeground 8
int[] styleable AppBarLayoutStates { 0x7f04041a, 0x7f04041b, 0x7f04041f, 0x7f040420 }
int styleable AppBarLayoutStates_state_collapsed 0
int styleable AppBarLayoutStates_state_collapsible 1
int styleable AppBarLayoutStates_state_liftable 2
int styleable AppBarLayoutStates_state_lifted 3
int[] styleable AppBarLayout_Layout { 0x7f0402c5, 0x7f0402c6, 0x7f0402c7 }
int styleable AppBarLayout_Layout_layout_scrollEffect 0
int styleable AppBarLayout_Layout_layout_scrollFlags 1
int styleable AppBarLayout_Layout_layout_scrollInterpolator 2
int[] styleable AppCompatEmojiHelper { }
int[] styleable AppCompatImageView { 0x01010119, 0x7f04040e, 0x7f0404a6, 0x7f0404a7 }
int styleable AppCompatImageView_android_src 0
int styleable AppCompatImageView_srcCompat 1
int styleable AppCompatImageView_tint 2
int styleable AppCompatImageView_tintMode 3
int[] styleable AppCompatSeekBar { 0x01010142, 0x7f0404a1, 0x7f0404a2, 0x7f0404a3 }
int styleable AppCompatSeekBar_android_thumb 0
int styleable AppCompatSeekBar_tickMark 1
int styleable AppCompatSeekBar_tickMarkTint 2
int styleable AppCompatSeekBar_tickMarkTintMode 3
int[] styleable AppCompatTextHelper { 0x01010034, 0x0101016d, 0x0101016e, 0x0101016f, 0x01010170, 0x01010392, 0x01010393 }
int styleable AppCompatTextHelper_android_textAppearance 0
int styleable AppCompatTextHelper_android_drawableTop 1
int styleable AppCompatTextHelper_android_drawableBottom 2
int styleable AppCompatTextHelper_android_drawableLeft 3
int styleable AppCompatTextHelper_android_drawableRight 4
int styleable AppCompatTextHelper_android_drawableStart 5
int styleable AppCompatTextHelper_android_drawableEnd 6
int[] styleable AppCompatTextView { 0x01010034, 0x7f040043, 0x7f040044, 0x7f040045, 0x7f040046, 0x7f040047, 0x7f040190, 0x7f040191, 0x7f040192, 0x7f040193, 0x7f040195, 0x7f040196, 0x7f040197, 0x7f040198, 0x7f0401a7, 0x7f0401e7, 0x7f04020b, 0x7f040214, 0x7f040283, 0x7f0402cc, 0x7f04045e, 0x7f04048f }
int styleable AppCompatTextView_android_textAppearance 0
int styleable AppCompatTextView_autoSizeMaxTextSize 1
int styleable AppCompatTextView_autoSizeMinTextSize 2
int styleable AppCompatTextView_autoSizePresetSizes 3
int styleable AppCompatTextView_autoSizeStepGranularity 4
int styleable AppCompatTextView_autoSizeTextType 5
int styleable AppCompatTextView_drawableBottomCompat 6
int styleable AppCompatTextView_drawableEndCompat 7
int styleable AppCompatTextView_drawableLeftCompat 8
int styleable AppCompatTextView_drawableRightCompat 9
int styleable AppCompatTextView_drawableStartCompat 10
int styleable AppCompatTextView_drawableTint 11
int styleable AppCompatTextView_drawableTintMode 12
int styleable AppCompatTextView_drawableTopCompat 13
int styleable AppCompatTextView_emojiCompatEnabled 14
int styleable AppCompatTextView_firstBaselineToTopHeight 15
int styleable AppCompatTextView_fontFamily 16
int styleable AppCompatTextView_fontVariationSettings 17
int styleable AppCompatTextView_lastBaselineToBottomHeight 18
int styleable AppCompatTextView_lineHeight 19
int styleable AppCompatTextView_textAllCaps 20
int styleable AppCompatTextView_textLocale 21
int[] styleable AppCompatTheme { 0x01010057, 0x010100ae, 0x7f040001, 0x7f040002, 0x7f040003, 0x7f040004, 0x7f040005, 0x7f040006, 0x7f040007, 0x7f040008, 0x7f040009, 0x7f04000a, 0x7f04000b, 0x7f04000c, 0x7f04000d, 0x7f04000f, 0x7f040010, 0x7f040011, 0x7f040012, 0x7f040013, 0x7f040014, 0x7f040015, 0x7f040016, 0x7f040017, 0x7f040018, 0x7f040019, 0x7f04001a, 0x7f04001b, 0x7f04001c, 0x7f04001d, 0x7f04001e, 0x7f04001f, 0x7f040020, 0x7f040021, 0x7f040026, 0x7f04002b, 0x7f04002c, 0x7f04002d, 0x7f04002e, 0x7f040041, 0x7f04006e, 0x7f040083, 0x7f040084, 0x7f040085, 0x7f040086, 0x7f040087, 0x7f040090, 0x7f040091, 0x7f0400a1, 0x7f0400ac, 0x7f0400e7, 0x7f0400e8, 0x7f0400eb, 0x7f0400ed, 0x7f0400ee, 0x7f0400ef, 0x7f0400f0, 0x7f040119, 0x7f04011b, 0x7f040132, 0x7f04014d, 0x7f04017f, 0x7f040180, 0x7f040181, 0x7f040186, 0x7f04018b, 0x7f04019c, 0x7f04019d, 0x7f0401a0, 0x7f0401a1, 0x7f0401a2, 0x7f04023e, 0x7f04024e, 0x7f0402d0, 0x7f0402d1, 0x7f0402d2, 0x7f0402d3, 0x7f0402d6, 0x7f0402d7, 0x7f0402d8, 0x7f0402d9, 0x7f0402da, 0x7f0402db, 0x7f0402dc, 0x7f0402dd, 0x7f0402de, 0x7f040373, 0x7f040374, 0x7f040375, 0x7f040391, 0x7f040393, 0x7f0403a1, 0x7f0403a3, 0x7f0403a4, 0x7f0403a5, 0x7f0403c2, 0x7f0403c5, 0x7f0403c6, 0x7f0403c7, 0x7f040406, 0x7f040407, 0x7f04043a, 0x7f040475, 0x7f040477, 0x7f040478, 0x7f040479, 0x7f04047b, 0x7f04047c, 0x7f04047d, 0x7f04047e, 0x7f040484, 0x7f040485, 0x7f0404ba, 0x7f0404bb, 0x7f0404bd, 0x7f0404be, 0x7f0404e5, 0x7f0404ee, 0x7f0404ef, 0x7f0404f0, 0x7f0404f1, 0x7f0404f2, 0x7f0404f3, 0x7f0404f4, 0x7f0404f5, 0x7f0404f6, 0x7f0404f7 }
int styleable AppCompatTheme_android_windowIsFloating 0
int styleable AppCompatTheme_android_windowAnimationStyle 1
int styleable AppCompatTheme_actionBarDivider 2
int styleable AppCompatTheme_actionBarItemBackground 3
int styleable AppCompatTheme_actionBarPopupTheme 4
int styleable AppCompatTheme_actionBarSize 5
int styleable AppCompatTheme_actionBarSplitStyle 6
int styleable AppCompatTheme_actionBarStyle 7
int styleable AppCompatTheme_actionBarTabBarStyle 8
int styleable AppCompatTheme_actionBarTabStyle 9
int styleable AppCompatTheme_actionBarTabTextStyle 10
int styleable AppCompatTheme_actionBarTheme 11
int styleable AppCompatTheme_actionBarWidgetTheme 12
int styleable AppCompatTheme_actionButtonStyle 13
int styleable AppCompatTheme_actionDropDownStyle 14
int styleable AppCompatTheme_actionMenuTextAppearance 15
int styleable AppCompatTheme_actionMenuTextColor 16
int styleable AppCompatTheme_actionModeBackground 17
int styleable AppCompatTheme_actionModeCloseButtonStyle 18
int styleable AppCompatTheme_actionModeCloseContentDescription 19
int styleable AppCompatTheme_actionModeCloseDrawable 20
int styleable AppCompatTheme_actionModeCopyDrawable 21
int styleable AppCompatTheme_actionModeCutDrawable 22
int styleable AppCompatTheme_actionModeFindDrawable 23
int styleable AppCompatTheme_actionModePasteDrawable 24
int styleable AppCompatTheme_actionModePopupWindowStyle 25
int styleable AppCompatTheme_actionModeSelectAllDrawable 26
int styleable AppCompatTheme_actionModeShareDrawable 27
int styleable AppCompatTheme_actionModeSplitBackground 28
int styleable AppCompatTheme_actionModeStyle 29
int styleable AppCompatTheme_actionModeTheme 30
int styleable AppCompatTheme_actionModeWebSearchDrawable 31
int styleable AppCompatTheme_actionOverflowButtonStyle 32
int styleable AppCompatTheme_actionOverflowMenuStyle 33
int styleable AppCompatTheme_activityChooserViewStyle 34
int styleable AppCompatTheme_alertDialogButtonGroupStyle 35
int styleable AppCompatTheme_alertDialogCenterButtons 36
int styleable AppCompatTheme_alertDialogStyle 37
int styleable AppCompatTheme_alertDialogTheme 38
int styleable AppCompatTheme_autoCompleteTextViewStyle 39
int styleable AppCompatTheme_borderlessButtonStyle 40
int styleable AppCompatTheme_buttonBarButtonStyle 41
int styleable AppCompatTheme_buttonBarNegativeButtonStyle 42
int styleable AppCompatTheme_buttonBarNeutralButtonStyle 43
int styleable AppCompatTheme_buttonBarPositiveButtonStyle 44
int styleable AppCompatTheme_buttonBarStyle 45
int styleable AppCompatTheme_buttonStyle 46
int styleable AppCompatTheme_buttonStyleSmall 47
int styleable AppCompatTheme_checkboxStyle 48
int styleable AppCompatTheme_checkedTextViewStyle 49
int styleable AppCompatTheme_colorAccent 50
int styleable AppCompatTheme_colorBackgroundFloating 51
int styleable AppCompatTheme_colorButtonNormal 52
int styleable AppCompatTheme_colorControlActivated 53
int styleable AppCompatTheme_colorControlHighlight 54
int styleable AppCompatTheme_colorControlNormal 55
int styleable AppCompatTheme_colorError 56
int styleable AppCompatTheme_colorPrimary 57
int styleable AppCompatTheme_colorPrimaryDark 58
int styleable AppCompatTheme_colorSwitchThumbNormal 59
int styleable AppCompatTheme_controlBackground 60
int styleable AppCompatTheme_dialogCornerRadius 61
int styleable AppCompatTheme_dialogPreferredPadding 62
int styleable AppCompatTheme_dialogTheme 63
int styleable AppCompatTheme_dividerHorizontal 64
int styleable AppCompatTheme_dividerVertical 65
int styleable AppCompatTheme_dropDownListViewStyle 66
int styleable AppCompatTheme_dropdownListPreferredItemHeight 67
int styleable AppCompatTheme_editTextBackground 68
int styleable AppCompatTheme_editTextColor 69
int styleable AppCompatTheme_editTextStyle 70
int styleable AppCompatTheme_homeAsUpIndicator 71
int styleable AppCompatTheme_imageButtonStyle 72
int styleable AppCompatTheme_listChoiceBackgroundIndicator 73
int styleable AppCompatTheme_listChoiceIndicatorMultipleAnimated 74
int styleable AppCompatTheme_listChoiceIndicatorSingleAnimated 75
int styleable AppCompatTheme_listDividerAlertDialog 76
int styleable AppCompatTheme_listMenuViewStyle 77
int styleable AppCompatTheme_listPopupWindowStyle 78
int styleable AppCompatTheme_listPreferredItemHeight 79
int styleable AppCompatTheme_listPreferredItemHeightLarge 80
int styleable AppCompatTheme_listPreferredItemHeightSmall 81
int styleable AppCompatTheme_listPreferredItemPaddingEnd 82
int styleable AppCompatTheme_listPreferredItemPaddingLeft 83
int styleable AppCompatTheme_listPreferredItemPaddingRight 84
int styleable AppCompatTheme_listPreferredItemPaddingStart 85
int styleable AppCompatTheme_panelBackground 86
int styleable AppCompatTheme_panelMenuListTheme 87
int styleable AppCompatTheme_panelMenuListWidth 88
int styleable AppCompatTheme_popupMenuStyle 89
int styleable AppCompatTheme_popupWindowStyle 90
int styleable AppCompatTheme_radioButtonStyle 91
int styleable AppCompatTheme_ratingBarStyle 92
int styleable AppCompatTheme_ratingBarStyleIndicator 93
int styleable AppCompatTheme_ratingBarStyleSmall 94
int styleable AppCompatTheme_searchViewStyle 95
int styleable AppCompatTheme_seekBarStyle 96
int styleable AppCompatTheme_selectableItemBackground 97
int styleable AppCompatTheme_selectableItemBackgroundBorderless 98
int styleable AppCompatTheme_spinnerDropDownItemStyle 99
int styleable AppCompatTheme_spinnerStyle 100
int styleable AppCompatTheme_switchStyle 101
int styleable AppCompatTheme_textAppearanceLargePopupMenu 102
int styleable AppCompatTheme_textAppearanceListItem 103
int styleable AppCompatTheme_textAppearanceListItemSecondary 104
int styleable AppCompatTheme_textAppearanceListItemSmall 105
int styleable AppCompatTheme_textAppearancePopupMenuHeader 106
int styleable AppCompatTheme_textAppearanceSearchResultSubtitle 107
int styleable AppCompatTheme_textAppearanceSearchResultTitle 108
int styleable AppCompatTheme_textAppearanceSmallPopupMenu 109
int styleable AppCompatTheme_textColorAlertDialogListItem 110
int styleable AppCompatTheme_textColorSearchUrl 111
int styleable AppCompatTheme_toolbarNavigationButtonStyle 112
int styleable AppCompatTheme_toolbarStyle 113
int styleable AppCompatTheme_tooltipForegroundColor 114
int styleable AppCompatTheme_tooltipFrameBackground 115
int styleable AppCompatTheme_viewInflaterClass 116
int styleable AppCompatTheme_windowActionBar 117
int styleable AppCompatTheme_windowActionBarOverlay 118
int styleable AppCompatTheme_windowActionModeOverlay 119
int styleable AppCompatTheme_windowFixedHeightMajor 120
int styleable AppCompatTheme_windowFixedHeightMinor 121
int styleable AppCompatTheme_windowFixedWidthMajor 122
int styleable AppCompatTheme_windowFixedWidthMinor 123
int styleable AppCompatTheme_windowMinWidthMajor 124
int styleable AppCompatTheme_windowMinWidthMinor 125
int styleable AppCompatTheme_windowNoTitle 126
int[] styleable AspectRatioFrameLayout { 0x7f0403ad }
int styleable AspectRatioFrameLayout_resize_mode 0
int[] styleable AutofitTextView { 0x7f040325, 0x7f040395, 0x7f0403f5 }
int styleable AutofitTextView_minTextSize 0
int styleable AutofitTextView_precision 1
int styleable AutofitTextView_sizeToFit 2
int[] styleable Badge { 0x7f04004b, 0x7f040055, 0x7f040056, 0x7f040058, 0x7f040059, 0x7f04005a, 0x7f040240, 0x7f040241, 0x7f040317, 0x7f040361, 0x7f0404e3, 0x7f0404e4 }
int styleable Badge_backgroundColor 0
int styleable Badge_badgeGravity 1
int styleable Badge_badgeRadius 2
int styleable Badge_badgeTextColor 3
int styleable Badge_badgeWidePadding 4
int styleable Badge_badgeWithTextRadius 5
int styleable Badge_horizontalOffset 6
int styleable Badge_horizontalOffsetWithText 7
int styleable Badge_maxCharacterCount 8
int styleable Badge_number 9
int styleable Badge_verticalOffset 10
int styleable Badge_verticalOffsetWithText 11
int[] styleable BaseProgressIndicator { 0x01010139, 0x7f04022f, 0x7f040251, 0x7f040323, 0x7f0403d8, 0x7f0403da, 0x7f0404c7, 0x7f0404ca, 0x7f0404cf }
int styleable BaseProgressIndicator_android_indeterminate 0
int styleable BaseProgressIndicator_hideAnimationBehavior 1
int styleable BaseProgressIndicator_indicatorColor 2
int styleable BaseProgressIndicator_minHideDelay 3
int styleable BaseProgressIndicator_showAnimationBehavior 4
int styleable BaseProgressIndicator_showDelay 5
int styleable BaseProgressIndicator_trackColor 6
int styleable BaseProgressIndicator_trackCornerRadius 7
int styleable BaseProgressIndicator_trackThickness 8
int[] styleable BottomAppBar { 0x7f04002a, 0x7f040053, 0x7f0401a3, 0x7f0401d3, 0x7f0401d4, 0x7f0401d5, 0x7f0401d6, 0x7f0401d7, 0x7f0401d8, 0x7f0401d9, 0x7f040233, 0x7f04031f, 0x7f040358, 0x7f04036c, 0x7f04036e, 0x7f04036f, 0x7f0403ab }
int styleable BottomAppBar_addElevationShadow 0
int styleable BottomAppBar_backgroundTint 1
int styleable BottomAppBar_elevation 2
int styleable BottomAppBar_fabAlignmentMode 3
int styleable BottomAppBar_fabAlignmentModeEndMargin 4
int styleable BottomAppBar_fabAnchorMode 5
int styleable BottomAppBar_fabAnimationMode 6
int styleable BottomAppBar_fabCradleMargin 7
int styleable BottomAppBar_fabCradleRoundedCornerRadius 8
int styleable BottomAppBar_fabCradleVerticalOffset 9
int styleable BottomAppBar_hideOnScroll 10
int styleable BottomAppBar_menuAlignmentMode 11
int styleable BottomAppBar_navigationIconTint 12
int styleable BottomAppBar_paddingBottomSystemWindowInsets 13
int styleable BottomAppBar_paddingLeftSystemWindowInsets 14
int styleable BottomAppBar_paddingRightSystemWindowInsets 15
int styleable BottomAppBar_removeEmbeddedFabElevation 16
int[] styleable BottomNavigationView { 0x01010140, 0x7f040136, 0x7f040262 }
int styleable BottomNavigationView_android_minHeight 0
int styleable BottomNavigationView_compatShadowEnabled 1
int styleable BottomNavigationView_itemHorizontalTranslationEnabled 2
int[] styleable BottomSheetBehavior_Layout { 0x0101011f, 0x01010120, 0x01010440, 0x7f040053, 0x7f040063, 0x7f040064, 0x7f040065, 0x7f040066, 0x7f040067, 0x7f040069, 0x7f04006a, 0x7f04006b, 0x7f04006c, 0x7f04021b, 0x7f0402e4, 0x7f0402e5, 0x7f0402e6, 0x7f04036c, 0x7f04036e, 0x7f04036f, 0x7f040372, 0x7f0403cb, 0x7f0403d3, 0x7f0403d7 }
int styleable BottomSheetBehavior_Layout_android_maxWidth 0
int styleable BottomSheetBehavior_Layout_android_maxHeight 1
int styleable BottomSheetBehavior_Layout_android_elevation 2
int styleable BottomSheetBehavior_Layout_backgroundTint 3
int styleable BottomSheetBehavior_Layout_behavior_draggable 4
int styleable BottomSheetBehavior_Layout_behavior_expandedOffset 5
int styleable BottomSheetBehavior_Layout_behavior_fitToContents 6
int styleable BottomSheetBehavior_Layout_behavior_halfExpandedRatio 7
int styleable BottomSheetBehavior_Layout_behavior_hideable 8
int styleable BottomSheetBehavior_Layout_behavior_peekHeight 9
int styleable BottomSheetBehavior_Layout_behavior_saveFlags 10
int styleable BottomSheetBehavior_Layout_behavior_significantVelocityThreshold 11
int styleable BottomSheetBehavior_Layout_behavior_skipCollapsed 12
int styleable BottomSheetBehavior_Layout_gestureInsetBottomIgnored 13
int styleable BottomSheetBehavior_Layout_marginLeftSystemWindowInsets 14
int styleable BottomSheetBehavior_Layout_marginRightSystemWindowInsets 15
int styleable BottomSheetBehavior_Layout_marginTopSystemWindowInsets 16
int styleable BottomSheetBehavior_Layout_paddingBottomSystemWindowInsets 17
int styleable BottomSheetBehavior_Layout_paddingLeftSystemWindowInsets 18
int styleable BottomSheetBehavior_Layout_paddingRightSystemWindowInsets 19
int styleable BottomSheetBehavior_Layout_paddingTopSystemWindowInsets 20
int styleable BottomSheetBehavior_Layout_shapeAppearance 21
int styleable BottomSheetBehavior_Layout_shapeAppearanceOverlay 22
int styleable BottomSheetBehavior_Layout_shouldRemoveExpandedCorners 23
int[] styleable ButtonBarLayout { 0x7f04002f }
int styleable ButtonBarLayout_allowStacking 0
int[] styleable CameraView { 0x0101011e, 0x7f0400c4, 0x7f0400c5, 0x7f0400c6, 0x7f0400c7, 0x7f0400c8, 0x7f0400c9, 0x7f0400ca, 0x7f0400cb, 0x7f0400cc }
int styleable CameraView_android_adjustViewBounds 0
int styleable CameraView_ckCropOutput 1
int styleable CameraView_ckFacing 2
int styleable CameraView_ckFlash 3
int styleable CameraView_ckFocus 4
int styleable CameraView_ckJpegQuality 5
int styleable CameraView_ckMethod 6
int styleable CameraView_ckPermissions 7
int styleable CameraView_ckVideoQuality 8
int styleable CameraView_ckZoom 9
int[] styleable Capability { 0x7f0403a0, 0x7f0403d6 }
int styleable Capability_queryPatterns 0
int styleable Capability_shortcutMatchRequired 1
int[] styleable CardView { 0x0101013f, 0x01010140, 0x7f040094, 0x7f040095, 0x7f040096, 0x7f040098, 0x7f040099, 0x7f04009a, 0x7f040144, 0x7f040145, 0x7f040147, 0x7f040148, 0x7f04014a }
int styleable CardView_android_minWidth 0
int styleable CardView_android_minHeight 1
int styleable CardView_cardBackgroundColor 2
int styleable CardView_cardCornerRadius 3
int styleable CardView_cardElevation 4
int styleable CardView_cardMaxElevation 5
int styleable CardView_cardPreventCornerOverlap 6
int styleable CardView_cardUseCompatPadding 7
int styleable CardView_contentPadding 8
int styleable CardView_contentPaddingBottom 9
int styleable CardView_contentPaddingLeft 10
int styleable CardView_contentPaddingRight 11
int styleable CardView_contentPaddingTop 12
int[] styleable CheckedTextView { 0x01010108, 0x7f04009e, 0x7f04009f, 0x7f0400a0 }
int styleable CheckedTextView_android_checkMark 0
int styleable CheckedTextView_checkMarkCompat 1
int styleable CheckedTextView_checkMarkTint 2
int styleable CheckedTextView_checkMarkTintMode 3
int[] styleable Chip { 0x01010034, 0x01010095, 0x01010098, 0x010100ab, 0x0101011f, 0x0101014f, 0x010101e5, 0x7f0400a4, 0x7f0400a5, 0x7f0400a9, 0x7f0400aa, 0x7f0400ad, 0x7f0400ae, 0x7f0400af, 0x7f0400b1, 0x7f0400b2, 0x7f0400b3, 0x7f0400b4, 0x7f0400b5, 0x7f0400b6, 0x7f0400b7, 0x7f0400bc, 0x7f0400bd, 0x7f0400be, 0x7f0400c0, 0x7f0400d3, 0x7f0400d4, 0x7f0400d5, 0x7f0400d6, 0x7f0400d7, 0x7f0400d8, 0x7f0400d9, 0x7f0401b3, 0x7f040230, 0x7f040244, 0x7f040248, 0x7f0403b0, 0x7f0403cb, 0x7f0403d3, 0x7f0403dc, 0x7f040486, 0x7f040490 }
int styleable Chip_android_textAppearance 0
int styleable Chip_android_textSize 1
int styleable Chip_android_textColor 2
int styleable Chip_android_ellipsize 3
int styleable Chip_android_maxWidth 4
int styleable Chip_android_text 5
int styleable Chip_android_checkable 6
int styleable Chip_checkedIcon 7
int styleable Chip_checkedIconEnabled 8
int styleable Chip_checkedIconTint 9
int styleable Chip_checkedIconVisible 10
int styleable Chip_chipBackgroundColor 11
int styleable Chip_chipCornerRadius 12
int styleable Chip_chipEndPadding 13
int styleable Chip_chipIcon 14
int styleable Chip_chipIconEnabled 15
int styleable Chip_chipIconSize 16
int styleable Chip_chipIconTint 17
int styleable Chip_chipIconVisible 18
int styleable Chip_chipMinHeight 19
int styleable Chip_chipMinTouchTargetSize 20
int styleable Chip_chipStartPadding 21
int styleable Chip_chipStrokeColor 22
int styleable Chip_chipStrokeWidth 23
int styleable Chip_chipSurfaceColor 24
int styleable Chip_closeIcon 25
int styleable Chip_closeIconEnabled 26
int styleable Chip_closeIconEndPadding 27
int styleable Chip_closeIconSize 28
int styleable Chip_closeIconStartPadding 29
int styleable Chip_closeIconTint 30
int styleable Chip_closeIconVisible 31
int styleable Chip_ensureMinTouchTargetSize 32
int styleable Chip_hideMotionSpec 33
int styleable Chip_iconEndPadding 34
int styleable Chip_iconStartPadding 35
int styleable Chip_rippleColor 36
int styleable Chip_shapeAppearance 37
int styleable Chip_shapeAppearanceOverlay 38
int styleable Chip_showMotionSpec 39
int styleable Chip_textEndPadding 40
int styleable Chip_textStartPadding 41
int[] styleable ChipGroup { 0x7f0400a3, 0x7f0400b8, 0x7f0400b9, 0x7f0400ba, 0x7f0403c8, 0x7f0403f2, 0x7f0403f3 }
int styleable ChipGroup_checkedChip 0
int styleable ChipGroup_chipSpacing 1
int styleable ChipGroup_chipSpacingHorizontal 2
int styleable ChipGroup_chipSpacingVertical 3
int styleable ChipGroup_selectionRequired 4
int styleable ChipGroup_singleLine 5
int styleable ChipGroup_singleSelection 6
int[] styleable CirclePinField { 0x7f0400c2, 0x7f0401e3, 0x7f0401e4 }
int styleable CirclePinField_circleRadius 0
int styleable CirclePinField_fillerColor 1
int styleable CirclePinField_fillerRadius 2
int[] styleable CircularProgressIndicator { 0x7f040252, 0x7f040254, 0x7f040255 }
int styleable CircularProgressIndicator_indicatorDirectionCircular 0
int styleable CircularProgressIndicator_indicatorInset 1
int styleable CircularProgressIndicator_indicatorSize 2
int[] styleable ClockFaceView { 0x7f0400cf, 0x7f0400d2 }
int styleable ClockFaceView_clockFaceBackgroundColor 0
int styleable ClockFaceView_clockNumberTextColor 1
int[] styleable ClockHandView { 0x7f0400d0, 0x7f040303, 0x7f0403ca }
int styleable ClockHandView_clockHandColor 0
int styleable ClockHandView_materialCircleRadius 1
int styleable ClockHandView_selectorSize 2
int[] styleable CollapsingToolbarLayout { 0x7f0400de, 0x7f0400df, 0x7f0400e0, 0x7f04014b, 0x7f0401c3, 0x7f0401c4, 0x7f0401c5, 0x7f0401c6, 0x7f0401c7, 0x7f0401c8, 0x7f0401c9, 0x7f0401ca, 0x7f0401d2, 0x7f040216, 0x7f04031a, 0x7f0403b6, 0x7f0403b8, 0x7f040424, 0x7f0404a9, 0x7f0404ab, 0x7f0404ac, 0x7f0404b3, 0x7f0404b6, 0x7f0404b9 }
int styleable CollapsingToolbarLayout_collapsedTitleGravity 0
int styleable CollapsingToolbarLayout_collapsedTitleTextAppearance 1
int styleable CollapsingToolbarLayout_collapsedTitleTextColor 2
int styleable CollapsingToolbarLayout_contentScrim 3
int styleable CollapsingToolbarLayout_expandedTitleGravity 4
int styleable CollapsingToolbarLayout_expandedTitleMargin 5
int styleable CollapsingToolbarLayout_expandedTitleMarginBottom 6
int styleable CollapsingToolbarLayout_expandedTitleMarginEnd 7
int styleable CollapsingToolbarLayout_expandedTitleMarginStart 8
int styleable CollapsingToolbarLayout_expandedTitleMarginTop 9
int styleable CollapsingToolbarLayout_expandedTitleTextAppearance 10
int styleable CollapsingToolbarLayout_expandedTitleTextColor 11
int styleable CollapsingToolbarLayout_extraMultilineHeightEnabled 12
int styleable CollapsingToolbarLayout_forceApplySystemWindowInsetTop 13
int styleable CollapsingToolbarLayout_maxLines 14
int styleable CollapsingToolbarLayout_scrimAnimationDuration 15
int styleable CollapsingToolbarLayout_scrimVisibleHeightTrigger 16
int styleable CollapsingToolbarLayout_statusBarScrim 17
int styleable CollapsingToolbarLayout_title 18
int styleable CollapsingToolbarLayout_titleCollapseMode 19
int styleable CollapsingToolbarLayout_titleEnabled 20
int styleable CollapsingToolbarLayout_titlePositionInterpolator 21
int styleable CollapsingToolbarLayout_titleTextEllipsize 22
int styleable CollapsingToolbarLayout_toolbarId 23
int[] styleable CollapsingToolbarLayout_Layout { 0x7f04028d, 0x7f04028e }
int styleable CollapsingToolbarLayout_Layout_layout_collapseMode 0
int styleable CollapsingToolbarLayout_Layout_layout_collapseParallaxMultiplier 1
int[] styleable ColorStateListItem { 0x010101a5, 0x0101031f, 0x01010647, 0x7f040030, 0x7f04027f }
int styleable ColorStateListItem_android_color 0
int styleable ColorStateListItem_android_alpha 1
int styleable ColorStateListItem_android_lStar 2
int styleable ColorStateListItem_alpha 3
int styleable ColorStateListItem_lStar 4
int[] styleable CompoundButton { 0x01010107, 0x7f040088, 0x7f040092, 0x7f040093 }
int styleable CompoundButton_android_button 0
int styleable CompoundButton_buttonCompat 1
int styleable CompoundButton_buttonTint 2
int styleable CompoundButton_buttonTintMode 3
int[] styleable Constraint { 0x010100c4, 0x010100d0, 0x010100dc, 0x010100f4, 0x010100f5, 0x010100f7, 0x010100f8, 0x010100f9, 0x010100fa, 0x0101011f, 0x01010120, 0x0101013f, 0x01010140, 0x0101031f, 0x01010320, 0x01010321, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103b5, 0x010103b6, 0x010103fa, 0x01010440, 0x7f040036, 0x7f04005e, 0x7f04005f, 0x7f040060, 0x7f04009d, 0x7f04013a, 0x7f04018f, 0x7f0401f7, 0x7f0401f8, 0x7f0401f9, 0x7f0401fa, 0x7f0401fb, 0x7f0401fc, 0x7f0401fd, 0x7f0401fe, 0x7f0401ff, 0x7f040200, 0x7f040201, 0x7f040202, 0x7f040203, 0x7f040205, 0x7f040206, 0x7f040207, 0x7f040208, 0x7f040209, 0x7f04028f, 0x7f040290, 0x7f040291, 0x7f040292, 0x7f040293, 0x7f040294, 0x7f040295, 0x7f040296, 0x7f040297, 0x7f040298, 0x7f040299, 0x7f04029a, 0x7f04029b, 0x7f04029c, 0x7f04029d, 0x7f04029e, 0x7f04029f, 0x7f0402a0, 0x7f0402a1, 0x7f0402a2, 0x7f0402a3, 0x7f0402a4, 0x7f0402a5, 0x7f0402a6, 0x7f0402a7, 0x7f0402a8, 0x7f0402a9, 0x7f0402aa, 0x7f0402ab, 0x7f0402ac, 0x7f0402ad, 0x7f0402ae, 0x7f0402af, 0x7f0402b0, 0x7f0402b1, 0x7f0402b2, 0x7f0402b3, 0x7f0402b4, 0x7f0402b5, 0x7f0402b6, 0x7f0402b7, 0x7f0402b8, 0x7f0402ba, 0x7f0402bb, 0x7f0402bc, 0x7f0402bd, 0x7f0402be, 0x7f0402bf, 0x7f0402c0, 0x7f0402c1, 0x7f04034e, 0x7f04034f, 0x7f04037b, 0x7f040382, 0x7f0404d3, 0x7f0404d5, 0x7f0404e6 }
int styleable Constraint_android_orientation 0
int styleable Constraint_android_id 1
int styleable Constraint_android_visibility 2
int styleable Constraint_android_layout_width 3
int styleable Constraint_android_layout_height 4
int styleable Constraint_android_layout_marginLeft 5
int styleable Constraint_android_layout_marginTop 6
int styleable Constraint_android_layout_marginRight 7
int styleable Constraint_android_layout_marginBottom 8
int styleable Constraint_android_maxWidth 9
int styleable Constraint_android_maxHeight 10
int styleable Constraint_android_minWidth 11
int styleable Constraint_android_minHeight 12
int styleable Constraint_android_alpha 13
int styleable Constraint_android_transformPivotX 14
int styleable Constraint_android_transformPivotY 15
int styleable Constraint_android_translationX 16
int styleable Constraint_android_translationY 17
int styleable Constraint_android_scaleX 18
int styleable Constraint_android_scaleY 19
int styleable Constraint_android_rotation 20
int styleable Constraint_android_rotationX 21
int styleable Constraint_android_rotationY 22
int styleable Constraint_android_layout_marginStart 23
int styleable Constraint_android_layout_marginEnd 24
int styleable Constraint_android_translationZ 25
int styleable Constraint_android_elevation 26
int styleable Constraint_animate_relativeTo 27
int styleable Constraint_barrierAllowsGoneWidgets 28
int styleable Constraint_barrierDirection 29
int styleable Constraint_barrierMargin 30
int styleable Constraint_chainUseRtl 31
int styleable Constraint_constraint_referenced_ids 32
int styleable Constraint_drawPath 33
int styleable Constraint_flow_firstHorizontalBias 34
int styleable Constraint_flow_firstHorizontalStyle 35
int styleable Constraint_flow_firstVerticalBias 36
int styleable Constraint_flow_firstVerticalStyle 37
int styleable Constraint_flow_horizontalAlign 38
int styleable Constraint_flow_horizontalBias 39
int styleable Constraint_flow_horizontalGap 40
int styleable Constraint_flow_horizontalStyle 41
int styleable Constraint_flow_lastHorizontalBias 42
int styleable Constraint_flow_lastHorizontalStyle 43
int styleable Constraint_flow_lastVerticalBias 44
int styleable Constraint_flow_lastVerticalStyle 45
int styleable Constraint_flow_maxElementsWrap 46
int styleable Constraint_flow_verticalAlign 47
int styleable Constraint_flow_verticalBias 48
int styleable Constraint_flow_verticalGap 49
int styleable Constraint_flow_verticalStyle 50
int styleable Constraint_flow_wrapMode 51
int styleable Constraint_layout_constrainedHeight 52
int styleable Constraint_layout_constrainedWidth 53
int styleable Constraint_layout_constraintBaseline_creator 54
int styleable Constraint_layout_constraintBaseline_toBaselineOf 55
int styleable Constraint_layout_constraintBottom_creator 56
int styleable Constraint_layout_constraintBottom_toBottomOf 57
int styleable Constraint_layout_constraintBottom_toTopOf 58
int styleable Constraint_layout_constraintCircle 59
int styleable Constraint_layout_constraintCircleAngle 60
int styleable Constraint_layout_constraintCircleRadius 61
int styleable Constraint_layout_constraintDimensionRatio 62
int styleable Constraint_layout_constraintEnd_toEndOf 63
int styleable Constraint_layout_constraintEnd_toStartOf 64
int styleable Constraint_layout_constraintGuide_begin 65
int styleable Constraint_layout_constraintGuide_end 66
int styleable Constraint_layout_constraintGuide_percent 67
int styleable Constraint_layout_constraintHeight_default 68
int styleable Constraint_layout_constraintHeight_max 69
int styleable Constraint_layout_constraintHeight_min 70
int styleable Constraint_layout_constraintHeight_percent 71
int styleable Constraint_layout_constraintHorizontal_bias 72
int styleable Constraint_layout_constraintHorizontal_chainStyle 73
int styleable Constraint_layout_constraintHorizontal_weight 74
int styleable Constraint_layout_constraintLeft_creator 75
int styleable Constraint_layout_constraintLeft_toLeftOf 76
int styleable Constraint_layout_constraintLeft_toRightOf 77
int styleable Constraint_layout_constraintRight_creator 78
int styleable Constraint_layout_constraintRight_toLeftOf 79
int styleable Constraint_layout_constraintRight_toRightOf 80
int styleable Constraint_layout_constraintStart_toEndOf 81
int styleable Constraint_layout_constraintStart_toStartOf 82
int styleable Constraint_layout_constraintTag 83
int styleable Constraint_layout_constraintTop_creator 84
int styleable Constraint_layout_constraintTop_toBottomOf 85
int styleable Constraint_layout_constraintTop_toTopOf 86
int styleable Constraint_layout_constraintVertical_bias 87
int styleable Constraint_layout_constraintVertical_chainStyle 88
int styleable Constraint_layout_constraintVertical_weight 89
int styleable Constraint_layout_constraintWidth_default 90
int styleable Constraint_layout_constraintWidth_max 91
int styleable Constraint_layout_constraintWidth_min 92
int styleable Constraint_layout_constraintWidth_percent 93
int styleable Constraint_layout_editor_absoluteX 94
int styleable Constraint_layout_editor_absoluteY 95
int styleable Constraint_layout_goneMarginBottom 96
int styleable Constraint_layout_goneMarginEnd 97
int styleable Constraint_layout_goneMarginLeft 98
int styleable Constraint_layout_goneMarginRight 99
int styleable Constraint_layout_goneMarginStart 100
int styleable Constraint_layout_goneMarginTop 101
int styleable Constraint_motionProgress 102
int styleable Constraint_motionStagger 103
int styleable Constraint_pathMotionArc 104
int styleable Constraint_pivotAnchor 105
int styleable Constraint_transitionEasing 106
int styleable Constraint_transitionPathRotate 107
int styleable Constraint_visibilityMode 108
int[] styleable ConstraintLayout_Layout { 0x010100c4, 0x010100d5, 0x010100d6, 0x010100d7, 0x010100d8, 0x010100d9, 0x010100dc, 0x0101011f, 0x01010120, 0x0101013f, 0x01010140, 0x010103b3, 0x010103b4, 0x01010440, 0x7f04005e, 0x7f04005f, 0x7f040060, 0x7f04009d, 0x7f040137, 0x7f04013a, 0x7f0401f7, 0x7f0401f8, 0x7f0401f9, 0x7f0401fa, 0x7f0401fb, 0x7f0401fc, 0x7f0401fd, 0x7f0401fe, 0x7f0401ff, 0x7f040200, 0x7f040201, 0x7f040202, 0x7f040203, 0x7f040205, 0x7f040206, 0x7f040207, 0x7f040208, 0x7f040209, 0x7f040287, 0x7f04028f, 0x7f040290, 0x7f040291, 0x7f040292, 0x7f040293, 0x7f040294, 0x7f040295, 0x7f040296, 0x7f040297, 0x7f040298, 0x7f040299, 0x7f04029a, 0x7f04029b, 0x7f04029c, 0x7f04029d, 0x7f04029e, 0x7f04029f, 0x7f0402a0, 0x7f0402a1, 0x7f0402a2, 0x7f0402a3, 0x7f0402a4, 0x7f0402a5, 0x7f0402a6, 0x7f0402a7, 0x7f0402a8, 0x7f0402a9, 0x7f0402aa, 0x7f0402ab, 0x7f0402ac, 0x7f0402ad, 0x7f0402ae, 0x7f0402af, 0x7f0402b0, 0x7f0402b1, 0x7f0402b2, 0x7f0402b3, 0x7f0402b4, 0x7f0402b5, 0x7f0402b6, 0x7f0402b7, 0x7f0402b8, 0x7f0402ba, 0x7f0402bb, 0x7f0402bc, 0x7f0402bd, 0x7f0402be, 0x7f0402bf, 0x7f0402c0, 0x7f0402c1, 0x7f0402c4 }
int styleable ConstraintLayout_Layout_android_orientation 0
int styleable ConstraintLayout_Layout_android_padding 1
int styleable ConstraintLayout_Layout_android_paddingLeft 2
int styleable ConstraintLayout_Layout_android_paddingTop 3
int styleable ConstraintLayout_Layout_android_paddingRight 4
int styleable ConstraintLayout_Layout_android_paddingBottom 5
int styleable ConstraintLayout_Layout_android_visibility 6
int styleable ConstraintLayout_Layout_android_maxWidth 7
int styleable ConstraintLayout_Layout_android_maxHeight 8
int styleable ConstraintLayout_Layout_android_minWidth 9
int styleable ConstraintLayout_Layout_android_minHeight 10
int styleable ConstraintLayout_Layout_android_paddingStart 11
int styleable ConstraintLayout_Layout_android_paddingEnd 12
int styleable ConstraintLayout_Layout_android_elevation 13
int styleable ConstraintLayout_Layout_barrierAllowsGoneWidgets 14
int styleable ConstraintLayout_Layout_barrierDirection 15
int styleable ConstraintLayout_Layout_barrierMargin 16
int styleable ConstraintLayout_Layout_chainUseRtl 17
int styleable ConstraintLayout_Layout_constraintSet 18
int styleable ConstraintLayout_Layout_constraint_referenced_ids 19
int styleable ConstraintLayout_Layout_flow_firstHorizontalBias 20
int styleable ConstraintLayout_Layout_flow_firstHorizontalStyle 21
int styleable ConstraintLayout_Layout_flow_firstVerticalBias 22
int styleable ConstraintLayout_Layout_flow_firstVerticalStyle 23
int styleable ConstraintLayout_Layout_flow_horizontalAlign 24
int styleable ConstraintLayout_Layout_flow_horizontalBias 25
int styleable ConstraintLayout_Layout_flow_horizontalGap 26
int styleable ConstraintLayout_Layout_flow_horizontalStyle 27
int styleable ConstraintLayout_Layout_flow_lastHorizontalBias 28
int styleable ConstraintLayout_Layout_flow_lastHorizontalStyle 29
int styleable ConstraintLayout_Layout_flow_lastVerticalBias 30
int styleable ConstraintLayout_Layout_flow_lastVerticalStyle 31
int styleable ConstraintLayout_Layout_flow_maxElementsWrap 32
int styleable ConstraintLayout_Layout_flow_verticalAlign 33
int styleable ConstraintLayout_Layout_flow_verticalBias 34
int styleable ConstraintLayout_Layout_flow_verticalGap 35
int styleable ConstraintLayout_Layout_flow_verticalStyle 36
int styleable ConstraintLayout_Layout_flow_wrapMode 37
int styleable ConstraintLayout_Layout_layoutDescription 38
int styleable ConstraintLayout_Layout_layout_constrainedHeight 39
int styleable ConstraintLayout_Layout_layout_constrainedWidth 40
int styleable ConstraintLayout_Layout_layout_constraintBaseline_creator 41
int styleable ConstraintLayout_Layout_layout_constraintBaseline_toBaselineOf 42
int styleable ConstraintLayout_Layout_layout_constraintBottom_creator 43
int styleable ConstraintLayout_Layout_layout_constraintBottom_toBottomOf 44
int styleable ConstraintLayout_Layout_layout_constraintBottom_toTopOf 45
int styleable ConstraintLayout_Layout_layout_constraintCircle 46
int styleable ConstraintLayout_Layout_layout_constraintCircleAngle 47
int styleable ConstraintLayout_Layout_layout_constraintCircleRadius 48
int styleable ConstraintLayout_Layout_layout_constraintDimensionRatio 49
int styleable ConstraintLayout_Layout_layout_constraintEnd_toEndOf 50
int styleable ConstraintLayout_Layout_layout_constraintEnd_toStartOf 51
int styleable ConstraintLayout_Layout_layout_constraintGuide_begin 52
int styleable ConstraintLayout_Layout_layout_constraintGuide_end 53
int styleable ConstraintLayout_Layout_layout_constraintGuide_percent 54
int styleable ConstraintLayout_Layout_layout_constraintHeight_default 55
int styleable ConstraintLayout_Layout_layout_constraintHeight_max 56
int styleable ConstraintLayout_Layout_layout_constraintHeight_min 57
int styleable ConstraintLayout_Layout_layout_constraintHeight_percent 58
int styleable ConstraintLayout_Layout_layout_constraintHorizontal_bias 59
int styleable ConstraintLayout_Layout_layout_constraintHorizontal_chainStyle 60
int styleable ConstraintLayout_Layout_layout_constraintHorizontal_weight 61
int styleable ConstraintLayout_Layout_layout_constraintLeft_creator 62
int styleable ConstraintLayout_Layout_layout_constraintLeft_toLeftOf 63
int styleable ConstraintLayout_Layout_layout_constraintLeft_toRightOf 64
int styleable ConstraintLayout_Layout_layout_constraintRight_creator 65
int styleable ConstraintLayout_Layout_layout_constraintRight_toLeftOf 66
int styleable ConstraintLayout_Layout_layout_constraintRight_toRightOf 67
int styleable ConstraintLayout_Layout_layout_constraintStart_toEndOf 68
int styleable ConstraintLayout_Layout_layout_constraintStart_toStartOf 69
int styleable ConstraintLayout_Layout_layout_constraintTag 70
int styleable ConstraintLayout_Layout_layout_constraintTop_creator 71
int styleable ConstraintLayout_Layout_layout_constraintTop_toBottomOf 72
int styleable ConstraintLayout_Layout_layout_constraintTop_toTopOf 73
int styleable ConstraintLayout_Layout_layout_constraintVertical_bias 74
int styleable ConstraintLayout_Layout_layout_constraintVertical_chainStyle 75
int styleable ConstraintLayout_Layout_layout_constraintVertical_weight 76
int styleable ConstraintLayout_Layout_layout_constraintWidth_default 77
int styleable ConstraintLayout_Layout_layout_constraintWidth_max 78
int styleable ConstraintLayout_Layout_layout_constraintWidth_min 79
int styleable ConstraintLayout_Layout_layout_constraintWidth_percent 80
int styleable ConstraintLayout_Layout_layout_editor_absoluteX 81
int styleable ConstraintLayout_Layout_layout_editor_absoluteY 82
int styleable ConstraintLayout_Layout_layout_goneMarginBottom 83
int styleable ConstraintLayout_Layout_layout_goneMarginEnd 84
int styleable ConstraintLayout_Layout_layout_goneMarginLeft 85
int styleable ConstraintLayout_Layout_layout_goneMarginRight 86
int styleable ConstraintLayout_Layout_layout_goneMarginStart 87
int styleable ConstraintLayout_Layout_layout_goneMarginTop 88
int styleable ConstraintLayout_Layout_layout_optimizationLevel 89
int[] styleable ConstraintLayout_placeholder { 0x7f04013c, 0x7f040387 }
int styleable ConstraintLayout_placeholder_content 0
int styleable ConstraintLayout_placeholder_placeholder_emptyVisibility 1
int[] styleable ConstraintSet { 0x010100c4, 0x010100d0, 0x010100dc, 0x010100f4, 0x010100f5, 0x010100f7, 0x010100f8, 0x010100f9, 0x010100fa, 0x0101011f, 0x01010120, 0x0101013f, 0x01010140, 0x010101b5, 0x010101b6, 0x0101031f, 0x01010320, 0x01010321, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103b5, 0x010103b6, 0x010103fa, 0x01010440, 0x7f040036, 0x7f04005e, 0x7f04005f, 0x7f040060, 0x7f04009d, 0x7f04013a, 0x7f04017d, 0x7f04018f, 0x7f0401f7, 0x7f0401f8, 0x7f0401f9, 0x7f0401fa, 0x7f0401fb, 0x7f0401fc, 0x7f0401fd, 0x7f0401fe, 0x7f0401ff, 0x7f040200, 0x7f040201, 0x7f040202, 0x7f040203, 0x7f040205, 0x7f040206, 0x7f040207, 0x7f040208, 0x7f040209, 0x7f04028f, 0x7f040290, 0x7f040291, 0x7f040292, 0x7f040293, 0x7f040294, 0x7f040295, 0x7f040296, 0x7f040297, 0x7f040298, 0x7f040299, 0x7f04029a, 0x7f04029b, 0x7f04029c, 0x7f04029d, 0x7f04029e, 0x7f04029f, 0x7f0402a0, 0x7f0402a1, 0x7f0402a2, 0x7f0402a3, 0x7f0402a4, 0x7f0402a5, 0x7f0402a6, 0x7f0402a7, 0x7f0402a8, 0x7f0402a9, 0x7f0402aa, 0x7f0402ab, 0x7f0402ac, 0x7f0402ad, 0x7f0402ae, 0x7f0402af, 0x7f0402b0, 0x7f0402b1, 0x7f0402b2, 0x7f0402b3, 0x7f0402b4, 0x7f0402b5, 0x7f0402b6, 0x7f0402b7, 0x7f0402b8, 0x7f0402ba, 0x7f0402bb, 0x7f0402bc, 0x7f0402bd, 0x7f0402be, 0x7f0402bf, 0x7f0402c0, 0x7f0402c1, 0x7f04034e, 0x7f04034f, 0x7f04037b, 0x7f040382, 0x7f0404d3, 0x7f0404d5 }
int styleable ConstraintSet_android_orientation 0
int styleable ConstraintSet_android_id 1
int styleable ConstraintSet_android_visibility 2
int styleable ConstraintSet_android_layout_width 3
int styleable ConstraintSet_android_layout_height 4
int styleable ConstraintSet_android_layout_marginLeft 5
int styleable ConstraintSet_android_layout_marginTop 6
int styleable ConstraintSet_android_layout_marginRight 7
int styleable ConstraintSet_android_layout_marginBottom 8
int styleable ConstraintSet_android_maxWidth 9
int styleable ConstraintSet_android_maxHeight 10
int styleable ConstraintSet_android_minWidth 11
int styleable ConstraintSet_android_minHeight 12
int styleable ConstraintSet_android_pivotX 13
int styleable ConstraintSet_android_pivotY 14
int styleable ConstraintSet_android_alpha 15
int styleable ConstraintSet_android_transformPivotX 16
int styleable ConstraintSet_android_transformPivotY 17
int styleable ConstraintSet_android_translationX 18
int styleable ConstraintSet_android_translationY 19
int styleable ConstraintSet_android_scaleX 20
int styleable ConstraintSet_android_scaleY 21
int styleable ConstraintSet_android_rotation 22
int styleable ConstraintSet_android_rotationX 23
int styleable ConstraintSet_android_rotationY 24
int styleable ConstraintSet_android_layout_marginStart 25
int styleable ConstraintSet_android_layout_marginEnd 26
int styleable ConstraintSet_android_translationZ 27
int styleable ConstraintSet_android_elevation 28
int styleable ConstraintSet_animate_relativeTo 29
int styleable ConstraintSet_barrierAllowsGoneWidgets 30
int styleable ConstraintSet_barrierDirection 31
int styleable ConstraintSet_barrierMargin 32
int styleable ConstraintSet_chainUseRtl 33
int styleable ConstraintSet_constraint_referenced_ids 34
int styleable ConstraintSet_deriveConstraintsFrom 35
int styleable ConstraintSet_drawPath 36
int styleable ConstraintSet_flow_firstHorizontalBias 37
int styleable ConstraintSet_flow_firstHorizontalStyle 38
int styleable ConstraintSet_flow_firstVerticalBias 39
int styleable ConstraintSet_flow_firstVerticalStyle 40
int styleable ConstraintSet_flow_horizontalAlign 41
int styleable ConstraintSet_flow_horizontalBias 42
int styleable ConstraintSet_flow_horizontalGap 43
int styleable ConstraintSet_flow_horizontalStyle 44
int styleable ConstraintSet_flow_lastHorizontalBias 45
int styleable ConstraintSet_flow_lastHorizontalStyle 46
int styleable ConstraintSet_flow_lastVerticalBias 47
int styleable ConstraintSet_flow_lastVerticalStyle 48
int styleable ConstraintSet_flow_maxElementsWrap 49
int styleable ConstraintSet_flow_verticalAlign 50
int styleable ConstraintSet_flow_verticalBias 51
int styleable ConstraintSet_flow_verticalGap 52
int styleable ConstraintSet_flow_verticalStyle 53
int styleable ConstraintSet_flow_wrapMode 54
int styleable ConstraintSet_layout_constrainedHeight 55
int styleable ConstraintSet_layout_constrainedWidth 56
int styleable ConstraintSet_layout_constraintBaseline_creator 57
int styleable ConstraintSet_layout_constraintBaseline_toBaselineOf 58
int styleable ConstraintSet_layout_constraintBottom_creator 59
int styleable ConstraintSet_layout_constraintBottom_toBottomOf 60
int styleable ConstraintSet_layout_constraintBottom_toTopOf 61
int styleable ConstraintSet_layout_constraintCircle 62
int styleable ConstraintSet_layout_constraintCircleAngle 63
int styleable ConstraintSet_layout_constraintCircleRadius 64
int styleable ConstraintSet_layout_constraintDimensionRatio 65
int styleable ConstraintSet_layout_constraintEnd_toEndOf 66
int styleable ConstraintSet_layout_constraintEnd_toStartOf 67
int styleable ConstraintSet_layout_constraintGuide_begin 68
int styleable ConstraintSet_layout_constraintGuide_end 69
int styleable ConstraintSet_layout_constraintGuide_percent 70
int styleable ConstraintSet_layout_constraintHeight_default 71
int styleable ConstraintSet_layout_constraintHeight_max 72
int styleable ConstraintSet_layout_constraintHeight_min 73
int styleable ConstraintSet_layout_constraintHeight_percent 74
int styleable ConstraintSet_layout_constraintHorizontal_bias 75
int styleable ConstraintSet_layout_constraintHorizontal_chainStyle 76
int styleable ConstraintSet_layout_constraintHorizontal_weight 77
int styleable ConstraintSet_layout_constraintLeft_creator 78
int styleable ConstraintSet_layout_constraintLeft_toLeftOf 79
int styleable ConstraintSet_layout_constraintLeft_toRightOf 80
int styleable ConstraintSet_layout_constraintRight_creator 81
int styleable ConstraintSet_layout_constraintRight_toLeftOf 82
int styleable ConstraintSet_layout_constraintRight_toRightOf 83
int styleable ConstraintSet_layout_constraintStart_toEndOf 84
int styleable ConstraintSet_layout_constraintStart_toStartOf 85
int styleable ConstraintSet_layout_constraintTag 86
int styleable ConstraintSet_layout_constraintTop_creator 87
int styleable ConstraintSet_layout_constraintTop_toBottomOf 88
int styleable ConstraintSet_layout_constraintTop_toTopOf 89
int styleable ConstraintSet_layout_constraintVertical_bias 90
int styleable ConstraintSet_layout_constraintVertical_chainStyle 91
int styleable ConstraintSet_layout_constraintVertical_weight 92
int styleable ConstraintSet_layout_constraintWidth_default 93
int styleable ConstraintSet_layout_constraintWidth_max 94
int styleable ConstraintSet_layout_constraintWidth_min 95
int styleable ConstraintSet_layout_constraintWidth_percent 96
int styleable ConstraintSet_layout_editor_absoluteX 97
int styleable ConstraintSet_layout_editor_absoluteY 98
int styleable ConstraintSet_layout_goneMarginBottom 99
int styleable ConstraintSet_layout_goneMarginEnd 100
int styleable ConstraintSet_layout_goneMarginLeft 101
int styleable ConstraintSet_layout_goneMarginRight 102
int styleable ConstraintSet_layout_goneMarginStart 103
int styleable ConstraintSet_layout_goneMarginTop 104
int styleable ConstraintSet_motionProgress 105
int styleable ConstraintSet_motionStagger 106
int styleable ConstraintSet_pathMotionArc 107
int styleable ConstraintSet_pivotAnchor 108
int styleable ConstraintSet_transitionEasing 109
int styleable ConstraintSet_transitionPathRotate 110
int[] styleable CoordinatorLayout { 0x7f04027e, 0x7f040422 }
int styleable CoordinatorLayout_keylines 0
int styleable CoordinatorLayout_statusBarBackground 1
int[] styleable CoordinatorLayout_Layout { 0x010100b3, 0x7f04028a, 0x7f04028b, 0x7f04028c, 0x7f0402b9, 0x7f0402c2, 0x7f0402c3 }
int styleable CoordinatorLayout_Layout_android_layout_gravity 0
int styleable CoordinatorLayout_Layout_layout_anchor 1
int styleable CoordinatorLayout_Layout_layout_anchorGravity 2
int styleable CoordinatorLayout_Layout_layout_behavior 3
int styleable CoordinatorLayout_Layout_layout_dodgeInsetEdges 4
int styleable CoordinatorLayout_Layout_layout_insetEdge 5
int styleable CoordinatorLayout_Layout_layout_keyline 6
int[] styleable CustomAttribute { 0x7f040040, 0x7f040165, 0x7f040166, 0x7f040167, 0x7f040168, 0x7f040169, 0x7f04016a, 0x7f04016c, 0x7f04016d }
int styleable CustomAttribute_attributeName 0
int styleable CustomAttribute_customBoolean 1
int styleable CustomAttribute_customColorDrawableValue 2
int styleable CustomAttribute_customColorValue 3
int styleable CustomAttribute_customDimension 4
int styleable CustomAttribute_customFloatValue 5
int styleable CustomAttribute_customIntegerValue 6
int styleable CustomAttribute_customPixelDimension 7
int styleable CustomAttribute_customStringValue 8
int[] styleable DefaultTimeBar { 0x7f040028, 0x7f040029, 0x7f04005c, 0x7f04005d, 0x7f040082, 0x7f040388, 0x7f040389, 0x7f0403ba, 0x7f0403bb, 0x7f0403bc, 0x7f0403bd, 0x7f0403be, 0x7f0404c5, 0x7f0404db }
int styleable DefaultTimeBar_ad_marker_color 0
int styleable DefaultTimeBar_ad_marker_width 1
int styleable DefaultTimeBar_bar_gravity 2
int styleable DefaultTimeBar_bar_height 3
int styleable DefaultTimeBar_buffered_color 4
int styleable DefaultTimeBar_played_ad_marker_color 5
int styleable DefaultTimeBar_played_color 6
int styleable DefaultTimeBar_scrubber_color 7
int styleable DefaultTimeBar_scrubber_disabled_size 8
int styleable DefaultTimeBar_scrubber_dragged_size 9
int styleable DefaultTimeBar_scrubber_drawable 10
int styleable DefaultTimeBar_scrubber_enabled_size 11
int styleable DefaultTimeBar_touch_target_height 12
int styleable DefaultTimeBar_unplayed_color 13
int[] styleable DialogFragmentNavigator { 0x01010003 }
int styleable DialogFragmentNavigator_android_name 0
int[] styleable DrawerArrowToggle { 0x7f04003d, 0x7f04003e, 0x7f04005b, 0x7f0400e6, 0x7f040194, 0x7f04021a, 0x7f040405, 0x7f040492 }
int styleable DrawerArrowToggle_arrowHeadLength 0
int styleable DrawerArrowToggle_arrowShaftLength 1
int styleable DrawerArrowToggle_barLength 2
int styleable DrawerArrowToggle_color 3
int styleable DrawerArrowToggle_drawableSize 4
int styleable DrawerArrowToggle_gapBetweenBars 5
int styleable DrawerArrowToggle_spinBars 6
int styleable DrawerArrowToggle_thickness 7
int[] styleable DrawerLayout { 0x7f0401a3 }
int styleable DrawerLayout_elevation 0
int[] styleable ExtendedFloatingActionButton { 0x7f0400dd, 0x7f0401a3, 0x7f0401cb, 0x7f0401cc, 0x7f040230, 0x7f0403dc, 0x7f0403e9 }
int styleable ExtendedFloatingActionButton_collapsedSize 0
int styleable ExtendedFloatingActionButton_elevation 1
int styleable ExtendedFloatingActionButton_extendMotionSpec 2
int styleable ExtendedFloatingActionButton_extendStrategy 3
int styleable ExtendedFloatingActionButton_hideMotionSpec 4
int styleable ExtendedFloatingActionButton_showMotionSpec 5
int styleable ExtendedFloatingActionButton_shrinkMotionSpec 6
int[] styleable ExtendedFloatingActionButton_Behavior_Layout { 0x7f040061, 0x7f040062 }
int styleable ExtendedFloatingActionButton_Behavior_Layout_behavior_autoHide 0
int styleable ExtendedFloatingActionButton_Behavior_Layout_behavior_autoShrink 1
int[] styleable FloatingActionButton { 0x0101000e, 0x7f040053, 0x7f040054, 0x7f04006d, 0x7f0401a3, 0x7f0401b3, 0x7f0401da, 0x7f0401db, 0x7f040230, 0x7f040242, 0x7f040319, 0x7f04039a, 0x7f0403b0, 0x7f0403cb, 0x7f0403d3, 0x7f0403dc, 0x7f0404dd }
int styleable FloatingActionButton_android_enabled 0
int styleable FloatingActionButton_backgroundTint 1
int styleable FloatingActionButton_backgroundTintMode 2
int styleable FloatingActionButton_borderWidth 3
int styleable FloatingActionButton_elevation 4
int styleable FloatingActionButton_ensureMinTouchTargetSize 5
int styleable FloatingActionButton_fabCustomSize 6
int styleable FloatingActionButton_fabSize 7
int styleable FloatingActionButton_hideMotionSpec 8
int styleable FloatingActionButton_hoveredFocusedTranslationZ 9
int styleable FloatingActionButton_maxImageSize 10
int styleable FloatingActionButton_pressedTranslationZ 11
int styleable FloatingActionButton_rippleColor 12
int styleable FloatingActionButton_shapeAppearance 13
int styleable FloatingActionButton_shapeAppearanceOverlay 14
int styleable FloatingActionButton_showMotionSpec 15
int styleable FloatingActionButton_useCompatPadding 16
int[] styleable FloatingActionButton_Behavior_Layout { 0x7f040061 }
int styleable FloatingActionButton_Behavior_Layout_behavior_autoHide 0
int[] styleable FlowLayout { 0x7f040273, 0x7f0402cd }
int styleable FlowLayout_itemSpacing 0
int styleable FlowLayout_lineSpacing 1
int[] styleable FontFamily { 0x7f04020c, 0x7f04020d, 0x7f04020e, 0x7f04020f, 0x7f040210, 0x7f040211, 0x7f040212 }
int styleable FontFamily_fontProviderAuthority 0
int styleable FontFamily_fontProviderCerts 1
int styleable FontFamily_fontProviderFetchStrategy 2
int styleable FontFamily_fontProviderFetchTimeout 3
int styleable FontFamily_fontProviderPackage 4
int styleable FontFamily_fontProviderQuery 5
int styleable FontFamily_fontProviderSystemFontFamily 6
int[] styleable FontFamilyFont { 0x01010532, 0x01010533, 0x0101053f, 0x0101056f, 0x01010570, 0x7f04020a, 0x7f040213, 0x7f040214, 0x7f040215, 0x7f0404da }
int styleable FontFamilyFont_android_font 0
int styleable FontFamilyFont_android_fontWeight 1
int styleable FontFamilyFont_android_fontStyle 2
int styleable FontFamilyFont_android_ttcIndex 3
int styleable FontFamilyFont_android_fontVariationSettings 4
int styleable FontFamilyFont_font 5
int styleable FontFamilyFont_fontStyle 6
int styleable FontFamilyFont_fontVariationSettings 7
int styleable FontFamilyFont_fontWeight 8
int styleable FontFamilyFont_ttcIndex 9
int[] styleable ForegroundLinearLayout { 0x01010109, 0x01010200, 0x7f040218 }
int styleable ForegroundLinearLayout_android_foreground 0
int styleable ForegroundLinearLayout_android_foregroundGravity 1
int styleable ForegroundLinearLayout_foregroundInsidePadding 2
int[] styleable Fragment { 0x01010003, 0x010100d0, 0x010100d1 }
int styleable Fragment_android_name 0
int styleable Fragment_android_id 1
int styleable Fragment_android_tag 2
int[] styleable FragmentContainerView { 0x01010003, 0x010100d1 }
int styleable FragmentContainerView_android_name 0
int styleable FragmentContainerView_android_tag 1
int[] styleable FragmentNavigator { 0x01010003 }
int styleable FragmentNavigator_android_name 0
int[] styleable GradientColor { 0x0101019d, 0x0101019e, 0x010101a1, 0x010101a2, 0x010101a3, 0x010101a4, 0x01010201, 0x0101020b, 0x01010510, 0x01010511, 0x01010512, 0x01010513 }
int styleable GradientColor_android_startColor 0
int styleable GradientColor_android_endColor 1
int styleable GradientColor_android_type 2
int styleable GradientColor_android_centerX 3
int styleable GradientColor_android_centerY 4
int styleable GradientColor_android_gradientRadius 5
int styleable GradientColor_android_tileMode 6
int styleable GradientColor_android_centerColor 7
int styleable GradientColor_android_startX 8
int styleable GradientColor_android_startY 9
int styleable GradientColor_android_endX 10
int styleable GradientColor_android_endY 11
int[] styleable GradientColorItem { 0x010101a5, 0x01010514 }
int styleable GradientColorItem_android_color 0
int styleable GradientColorItem_android_offset 1
int[] styleable ImageFilterView { 0x7f040032, 0x7f040081, 0x7f04014c, 0x7f040162, 0x7f04036a, 0x7f0403b1, 0x7f0403b2, 0x7f0403b4, 0x7f0404e8 }
int styleable ImageFilterView_altSrc 0
int styleable ImageFilterView_brightness 1
int styleable ImageFilterView_contrast 2
int styleable ImageFilterView_crossfade 3
int styleable ImageFilterView_overlay 4
int styleable ImageFilterView_round 5
int styleable ImageFilterView_roundPercent 6
int styleable ImageFilterView_saturation 7
int styleable ImageFilterView_warmth 8
int[] styleable Insets { 0x7f0402e4, 0x7f0402e5, 0x7f0402e6, 0x7f04036c, 0x7f04036e, 0x7f04036f, 0x7f040372 }
int styleable Insets_marginLeftSystemWindowInsets 0
int styleable Insets_marginRightSystemWindowInsets 1
int styleable Insets_marginTopSystemWindowInsets 2
int styleable Insets_paddingBottomSystemWindowInsets 3
int styleable Insets_paddingLeftSystemWindowInsets 4
int styleable Insets_paddingRightSystemWindowInsets 5
int styleable Insets_paddingTopSystemWindowInsets 6
int[] styleable KeyAttribute { 0x0101031f, 0x01010320, 0x01010321, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103fa, 0x01010440, 0x7f040164, 0x7f040219, 0x7f04034e, 0x7f040350, 0x7f0404d3, 0x7f0404d5 }
int styleable KeyAttribute_android_alpha 0
int styleable KeyAttribute_android_transformPivotX 1
int styleable KeyAttribute_android_transformPivotY 2
int styleable KeyAttribute_android_translationX 3
int styleable KeyAttribute_android_translationY 4
int styleable KeyAttribute_android_scaleX 5
int styleable KeyAttribute_android_scaleY 6
int styleable KeyAttribute_android_rotation 7
int styleable KeyAttribute_android_rotationX 8
int styleable KeyAttribute_android_rotationY 9
int styleable KeyAttribute_android_translationZ 10
int styleable KeyAttribute_android_elevation 11
int styleable KeyAttribute_curveFit 12
int styleable KeyAttribute_framePosition 13
int styleable KeyAttribute_motionProgress 14
int styleable KeyAttribute_motionTarget 15
int styleable KeyAttribute_transitionEasing 16
int styleable KeyAttribute_transitionPathRotate 17
int[] styleable KeyCycle { 0x0101031f, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103fa, 0x01010440, 0x7f040164, 0x7f040219, 0x7f04034e, 0x7f040350, 0x7f0404d3, 0x7f0404d5, 0x7f0404ea, 0x7f0404eb, 0x7f0404ec, 0x7f0404ed }
int styleable KeyCycle_android_alpha 0
int styleable KeyCycle_android_translationX 1
int styleable KeyCycle_android_translationY 2
int styleable KeyCycle_android_scaleX 3
int styleable KeyCycle_android_scaleY 4
int styleable KeyCycle_android_rotation 5
int styleable KeyCycle_android_rotationX 6
int styleable KeyCycle_android_rotationY 7
int styleable KeyCycle_android_translationZ 8
int styleable KeyCycle_android_elevation 9
int styleable KeyCycle_curveFit 10
int styleable KeyCycle_framePosition 11
int styleable KeyCycle_motionProgress 12
int styleable KeyCycle_motionTarget 13
int styleable KeyCycle_transitionEasing 14
int styleable KeyCycle_transitionPathRotate 15
int styleable KeyCycle_waveOffset 16
int styleable KeyCycle_wavePeriod 17
int styleable KeyCycle_waveShape 18
int styleable KeyCycle_waveVariesBy 19
int[] styleable KeyFrame { }
int[] styleable KeyFramesAcceleration { }
int[] styleable KeyFramesVelocity { }
int[] styleable KeyPosition { 0x7f040164, 0x7f04018f, 0x7f040219, 0x7f04027c, 0x7f040350, 0x7f04037b, 0x7f04037d, 0x7f04037e, 0x7f04037f, 0x7f040380, 0x7f0403f4, 0x7f0404d3 }
int styleable KeyPosition_curveFit 0
int styleable KeyPosition_drawPath 1
int styleable KeyPosition_framePosition 2
int styleable KeyPosition_keyPositionType 3
int styleable KeyPosition_motionTarget 4
int styleable KeyPosition_pathMotionArc 5
int styleable KeyPosition_percentHeight 6
int styleable KeyPosition_percentWidth 7
int styleable KeyPosition_percentX 8
int styleable KeyPosition_percentY 9
int styleable KeyPosition_sizePercent 10
int styleable KeyPosition_transitionEasing 11
int[] styleable KeyTimeCycle { 0x0101031f, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103fa, 0x01010440, 0x7f040164, 0x7f040219, 0x7f04034e, 0x7f040350, 0x7f0404d3, 0x7f0404d5, 0x7f0404e9, 0x7f0404ea, 0x7f0404eb, 0x7f0404ec }
int styleable KeyTimeCycle_android_alpha 0
int styleable KeyTimeCycle_android_translationX 1
int styleable KeyTimeCycle_android_translationY 2
int styleable KeyTimeCycle_android_scaleX 3
int styleable KeyTimeCycle_android_scaleY 4
int styleable KeyTimeCycle_android_rotation 5
int styleable KeyTimeCycle_android_rotationX 6
int styleable KeyTimeCycle_android_rotationY 7
int styleable KeyTimeCycle_android_translationZ 8
int styleable KeyTimeCycle_android_elevation 9
int styleable KeyTimeCycle_curveFit 10
int styleable KeyTimeCycle_framePosition 11
int styleable KeyTimeCycle_motionProgress 12
int styleable KeyTimeCycle_motionTarget 13
int styleable KeyTimeCycle_transitionEasing 14
int styleable KeyTimeCycle_transitionPathRotate 15
int styleable KeyTimeCycle_waveDecay 16
int styleable KeyTimeCycle_waveOffset 17
int styleable KeyTimeCycle_wavePeriod 18
int styleable KeyTimeCycle_waveShape 19
int[] styleable KeyTrigger { 0x7f040219, 0x7f040350, 0x7f040351, 0x7f040352, 0x7f040363, 0x7f040365, 0x7f040366, 0x7f0404d7, 0x7f0404d8, 0x7f0404d9 }
int styleable KeyTrigger_framePosition 0
int styleable KeyTrigger_motionTarget 1
int styleable KeyTrigger_motion_postLayoutCollision 2
int styleable KeyTrigger_motion_triggerOnCollision 3
int styleable KeyTrigger_onCross 4
int styleable KeyTrigger_onNegativeCross 5
int styleable KeyTrigger_onPositiveCross 6
int styleable KeyTrigger_triggerId 7
int styleable KeyTrigger_triggerReceiver 8
int styleable KeyTrigger_triggerSlack 9
int[] styleable Layout { 0x010100c4, 0x010100f4, 0x010100f5, 0x010100f7, 0x010100f8, 0x010100f9, 0x010100fa, 0x010103b5, 0x010103b6, 0x7f04005e, 0x7f04005f, 0x7f040060, 0x7f04009d, 0x7f04013a, 0x7f04028f, 0x7f040290, 0x7f040291, 0x7f040292, 0x7f040293, 0x7f040294, 0x7f040295, 0x7f040296, 0x7f040297, 0x7f040298, 0x7f040299, 0x7f04029a, 0x7f04029b, 0x7f04029c, 0x7f04029d, 0x7f04029e, 0x7f04029f, 0x7f0402a0, 0x7f0402a1, 0x7f0402a2, 0x7f0402a3, 0x7f0402a4, 0x7f0402a5, 0x7f0402a6, 0x7f0402a7, 0x7f0402a8, 0x7f0402a9, 0x7f0402aa, 0x7f0402ab, 0x7f0402ac, 0x7f0402ad, 0x7f0402af, 0x7f0402b0, 0x7f0402b1, 0x7f0402b2, 0x7f0402b3, 0x7f0402b4, 0x7f0402b5, 0x7f0402b6, 0x7f0402b7, 0x7f0402b8, 0x7f0402ba, 0x7f0402bb, 0x7f0402bc, 0x7f0402bd, 0x7f0402be, 0x7f0402bf, 0x7f0402c0, 0x7f0402c1, 0x7f040318, 0x7f04031c, 0x7f040322, 0x7f040327 }
int styleable Layout_android_orientation 0
int styleable Layout_android_layout_width 1
int styleable Layout_android_layout_height 2
int styleable Layout_android_layout_marginLeft 3
int styleable Layout_android_layout_marginTop 4
int styleable Layout_android_layout_marginRight 5
int styleable Layout_android_layout_marginBottom 6
int styleable Layout_android_layout_marginStart 7
int styleable Layout_android_layout_marginEnd 8
int styleable Layout_barrierAllowsGoneWidgets 9
int styleable Layout_barrierDirection 10
int styleable Layout_barrierMargin 11
int styleable Layout_chainUseRtl 12
int styleable Layout_constraint_referenced_ids 13
int styleable Layout_layout_constrainedHeight 14
int styleable Layout_layout_constrainedWidth 15
int styleable Layout_layout_constraintBaseline_creator 16
int styleable Layout_layout_constraintBaseline_toBaselineOf 17
int styleable Layout_layout_constraintBottom_creator 18
int styleable Layout_layout_constraintBottom_toBottomOf 19
int styleable Layout_layout_constraintBottom_toTopOf 20
int styleable Layout_layout_constraintCircle 21
int styleable Layout_layout_constraintCircleAngle 22
int styleable Layout_layout_constraintCircleRadius 23
int styleable Layout_layout_constraintDimensionRatio 24
int styleable Layout_layout_constraintEnd_toEndOf 25
int styleable Layout_layout_constraintEnd_toStartOf 26
int styleable Layout_layout_constraintGuide_begin 27
int styleable Layout_layout_constraintGuide_end 28
int styleable Layout_layout_constraintGuide_percent 29
int styleable Layout_layout_constraintHeight_default 30
int styleable Layout_layout_constraintHeight_max 31
int styleable Layout_layout_constraintHeight_min 32
int styleable Layout_layout_constraintHeight_percent 33
int styleable Layout_layout_constraintHorizontal_bias 34
int styleable Layout_layout_constraintHorizontal_chainStyle 35
int styleable Layout_layout_constraintHorizontal_weight 36
int styleable Layout_layout_constraintLeft_creator 37
int styleable Layout_layout_constraintLeft_toLeftOf 38
int styleable Layout_layout_constraintLeft_toRightOf 39
int styleable Layout_layout_constraintRight_creator 40
int styleable Layout_layout_constraintRight_toLeftOf 41
int styleable Layout_layout_constraintRight_toRightOf 42
int styleable Layout_layout_constraintStart_toEndOf 43
int styleable Layout_layout_constraintStart_toStartOf 44
int styleable Layout_layout_constraintTop_creator 45
int styleable Layout_layout_constraintTop_toBottomOf 46
int styleable Layout_layout_constraintTop_toTopOf 47
int styleable Layout_layout_constraintVertical_bias 48
int styleable Layout_layout_constraintVertical_chainStyle 49
int styleable Layout_layout_constraintVertical_weight 50
int styleable Layout_layout_constraintWidth_default 51
int styleable Layout_layout_constraintWidth_max 52
int styleable Layout_layout_constraintWidth_min 53
int styleable Layout_layout_constraintWidth_percent 54
int styleable Layout_layout_editor_absoluteX 55
int styleable Layout_layout_editor_absoluteY 56
int styleable Layout_layout_goneMarginBottom 57
int styleable Layout_layout_goneMarginEnd 58
int styleable Layout_layout_goneMarginLeft 59
int styleable Layout_layout_goneMarginRight 60
int styleable Layout_layout_goneMarginStart 61
int styleable Layout_layout_goneMarginTop 62
int styleable Layout_maxHeight 63
int styleable Layout_maxWidth 64
int styleable Layout_minHeight 65
int styleable Layout_minWidth 66
int[] styleable LegacyPlayerControlView { 0x7f040028, 0x7f040029, 0x7f04005c, 0x7f04005d, 0x7f040082, 0x7f04014e, 0x7f040388, 0x7f040389, 0x7f0403ac, 0x7f0403ba, 0x7f0403bb, 0x7f0403bc, 0x7f0403bd, 0x7f0403be, 0x7f0403e1, 0x7f0403e2, 0x7f0403e3, 0x7f0403e4, 0x7f0403e5, 0x7f0403e7, 0x7f0404a5, 0x7f0404c5, 0x7f0404db }
int styleable LegacyPlayerControlView_ad_marker_color 0
int styleable LegacyPlayerControlView_ad_marker_width 1
int styleable LegacyPlayerControlView_bar_gravity 2
int styleable LegacyPlayerControlView_bar_height 3
int styleable LegacyPlayerControlView_buffered_color 4
int styleable LegacyPlayerControlView_controller_layout_id 5
int styleable LegacyPlayerControlView_played_ad_marker_color 6
int styleable LegacyPlayerControlView_played_color 7
int styleable LegacyPlayerControlView_repeat_toggle_modes 8
int styleable LegacyPlayerControlView_scrubber_color 9
int styleable LegacyPlayerControlView_scrubber_disabled_size 10
int styleable LegacyPlayerControlView_scrubber_dragged_size 11
int styleable LegacyPlayerControlView_scrubber_drawable 12
int styleable LegacyPlayerControlView_scrubber_enabled_size 13
int styleable LegacyPlayerControlView_show_fastforward_button 14
int styleable LegacyPlayerControlView_show_next_button 15
int styleable LegacyPlayerControlView_show_previous_button 16
int styleable LegacyPlayerControlView_show_rewind_button 17
int styleable LegacyPlayerControlView_show_shuffle_button 18
int styleable LegacyPlayerControlView_show_timeout 19
int styleable LegacyPlayerControlView_time_bar_min_update_interval 20
int styleable LegacyPlayerControlView_touch_target_height 21
int styleable LegacyPlayerControlView_unplayed_color 22
int[] styleable LinePinField { 0x7f040075 }
int styleable LinePinField_bottomTextPaddingDp 0
int[] styleable LinearLayoutCompat { 0x010100af, 0x010100c4, 0x01010126, 0x01010127, 0x01010128, 0x7f040184, 0x7f040189, 0x7f04031d, 0x7f0403db }
int styleable LinearLayoutCompat_android_gravity 0
int styleable LinearLayoutCompat_android_orientation 1
int styleable LinearLayoutCompat_android_baselineAligned 2
int styleable LinearLayoutCompat_android_baselineAlignedChildIndex 3
int styleable LinearLayoutCompat_android_weightSum 4
int styleable LinearLayoutCompat_divider 5
int styleable LinearLayoutCompat_dividerPadding 6
int styleable LinearLayoutCompat_measureWithLargestChild 7
int styleable LinearLayoutCompat_showDividers 8
int[] styleable LinearLayoutCompat_Layout { 0x010100b3, 0x010100f4, 0x010100f5, 0x01010181 }
int styleable LinearLayoutCompat_Layout_android_layout_gravity 0
int styleable LinearLayoutCompat_Layout_android_layout_width 1
int styleable LinearLayoutCompat_Layout_android_layout_height 2
int styleable LinearLayoutCompat_Layout_android_layout_weight 3
int[] styleable LinearProgressIndicator { 0x7f04024f, 0x7f040253 }
int styleable LinearProgressIndicator_indeterminateAnimationType 0
int styleable LinearProgressIndicator_indicatorDirectionLinear 1
int[] styleable ListPopupWindow { 0x010102ac, 0x010102ad }
int styleable ListPopupWindow_android_dropDownHorizontalOffset 0
int styleable ListPopupWindow_android_dropDownVerticalOffset 1
int[] styleable LoadingImageView { 0x7f0400c1, 0x7f04024c, 0x7f04024d }
int styleable LoadingImageView_circleCrop 0
int styleable LoadingImageView_imageAspectRatio 1
int styleable LoadingImageView_imageAspectRatioAdjust 2
int[] styleable MaterialAlertDialog { 0x7f04004c, 0x7f04004d, 0x7f04004e, 0x7f04004f }
int styleable MaterialAlertDialog_backgroundInsetBottom 0
int styleable MaterialAlertDialog_backgroundInsetEnd 1
int styleable MaterialAlertDialog_backgroundInsetStart 2
int styleable MaterialAlertDialog_backgroundInsetTop 3
int[] styleable MaterialAlertDialogTheme { 0x7f0402e7, 0x7f0402e8, 0x7f0402e9, 0x7f0402ea, 0x7f0402eb, 0x7f0402ec }
int styleable MaterialAlertDialogTheme_materialAlertDialogBodyTextStyle 0
int styleable MaterialAlertDialogTheme_materialAlertDialogButtonSpacerVisibility 1
int styleable MaterialAlertDialogTheme_materialAlertDialogTheme 2
int styleable MaterialAlertDialogTheme_materialAlertDialogTitleIconStyle 3
int styleable MaterialAlertDialogTheme_materialAlertDialogTitlePanelStyle 4
int styleable MaterialAlertDialogTheme_materialAlertDialogTitleTextStyle 5
int[] styleable MaterialAutoCompleteTextView { 0x01010220, 0x0101048c, 0x7f0403ed, 0x7f0403ee, 0x7f0403ef, 0x7f0403f0 }
int styleable MaterialAutoCompleteTextView_android_inputType 0
int styleable MaterialAutoCompleteTextView_android_popupElevation 1
int styleable MaterialAutoCompleteTextView_simpleItemLayout 2
int styleable MaterialAutoCompleteTextView_simpleItemSelectedColor 3
int styleable MaterialAutoCompleteTextView_simpleItemSelectedRippleColor 4
int styleable MaterialAutoCompleteTextView_simpleItems 5
int[] styleable MaterialButton { 0x010100d4, 0x010101b7, 0x010101b8, 0x010101b9, 0x010101ba, 0x010101e5, 0x7f040053, 0x7f040054, 0x7f040156, 0x7f0401a3, 0x7f040243, 0x7f040245, 0x7f040246, 0x7f040247, 0x7f040249, 0x7f04024a, 0x7f0403b0, 0x7f0403cb, 0x7f0403d3, 0x7f040425, 0x7f040426, 0x7f0404b8 }
int styleable MaterialButton_android_background 0
int styleable MaterialButton_android_insetLeft 1
int styleable MaterialButton_android_insetRight 2
int styleable MaterialButton_android_insetTop 3
int styleable MaterialButton_android_insetBottom 4
int styleable MaterialButton_android_checkable 5
int styleable MaterialButton_backgroundTint 6
int styleable MaterialButton_backgroundTintMode 7
int styleable MaterialButton_cornerRadius 8
int styleable MaterialButton_elevation 9
int styleable MaterialButton_icon 10
int styleable MaterialButton_iconGravity 11
int styleable MaterialButton_iconPadding 12
int styleable MaterialButton_iconSize 13
int styleable MaterialButton_iconTint 14
int styleable MaterialButton_iconTintMode 15
int styleable MaterialButton_rippleColor 16
int styleable MaterialButton_shapeAppearance 17
int styleable MaterialButton_shapeAppearanceOverlay 18
int styleable MaterialButton_strokeColor 19
int styleable MaterialButton_strokeWidth 20
int styleable MaterialButton_toggleCheckedStateOnClick 21
int[] styleable MaterialButtonToggleGroup { 0x0101000e, 0x7f0400a2, 0x7f0403c8, 0x7f0403f3 }
int styleable MaterialButtonToggleGroup_android_enabled 0
int styleable MaterialButtonToggleGroup_checkedButton 1
int styleable MaterialButtonToggleGroup_selectionRequired 2
int styleable MaterialButtonToggleGroup_singleSelection 3
int[] styleable MaterialCalendar { 0x0101020d, 0x7f040170, 0x7f040171, 0x7f040172, 0x7f040173, 0x7f04035e, 0x7f0403a2, 0x7f0404fc, 0x7f0404fd, 0x7f0404fe }
int styleable MaterialCalendar_android_windowFullscreen 0
int styleable MaterialCalendar_dayInvalidStyle 1
int styleable MaterialCalendar_daySelectedStyle 2
int styleable MaterialCalendar_dayStyle 3
int styleable MaterialCalendar_dayTodayStyle 4
int styleable MaterialCalendar_nestedScrollable 5
int styleable MaterialCalendar_rangeFillColor 6
int styleable MaterialCalendar_yearSelectedStyle 7
int styleable MaterialCalendar_yearStyle 8
int styleable MaterialCalendar_yearTodayStyle 9
int[] styleable MaterialCalendarItem { 0x010101b7, 0x010101b8, 0x010101b9, 0x010101ba, 0x7f040260, 0x7f04026c, 0x7f04026d, 0x7f040274, 0x7f040275, 0x7f040279 }
int styleable MaterialCalendarItem_android_insetLeft 0
int styleable MaterialCalendarItem_android_insetRight 1
int styleable MaterialCalendarItem_android_insetTop 2
int styleable MaterialCalendarItem_android_insetBottom 3
int styleable MaterialCalendarItem_itemFillColor 4
int styleable MaterialCalendarItem_itemShapeAppearance 5
int styleable MaterialCalendarItem_itemShapeAppearanceOverlay 6
int styleable MaterialCalendarItem_itemStrokeColor 7
int styleable MaterialCalendarItem_itemStrokeWidth 8
int styleable MaterialCalendarItem_itemTextColor 9
int[] styleable MaterialCardView { 0x010101e5, 0x7f040097, 0x7f0400a4, 0x7f0400a6, 0x7f0400a7, 0x7f0400a8, 0x7f0400a9, 0x7f0403b0, 0x7f0403cb, 0x7f0403d3, 0x7f04041c, 0x7f040425, 0x7f040426 }
int styleable MaterialCardView_android_checkable 0
int styleable MaterialCardView_cardForegroundColor 1
int styleable MaterialCardView_checkedIcon 2
int styleable MaterialCardView_checkedIconGravity 3
int styleable MaterialCardView_checkedIconMargin 4
int styleable MaterialCardView_checkedIconSize 5
int styleable MaterialCardView_checkedIconTint 6
int styleable MaterialCardView_rippleColor 7
int styleable MaterialCardView_shapeAppearance 8
int styleable MaterialCardView_shapeAppearanceOverlay 9
int styleable MaterialCardView_state_dragged 10
int styleable MaterialCardView_strokeColor 11
int styleable MaterialCardView_strokeWidth 12
int[] styleable MaterialCheckBox { 0x01010107, 0x7f040088, 0x7f04008a, 0x7f04008c, 0x7f04008d, 0x7f040092, 0x7f04009c, 0x7f0400ab, 0x7f0401b5, 0x7f0401bc, 0x7f0404df }
int styleable MaterialCheckBox_android_button 0
int styleable MaterialCheckBox_buttonCompat 1
int styleable MaterialCheckBox_buttonIcon 2
int styleable MaterialCheckBox_buttonIconTint 3
int styleable MaterialCheckBox_buttonIconTintMode 4
int styleable MaterialCheckBox_buttonTint 5
int styleable MaterialCheckBox_centerIfNoTextEnabled 6
int styleable MaterialCheckBox_checkedState 7
int styleable MaterialCheckBox_errorAccessibilityLabel 8
int styleable MaterialCheckBox_errorShown 9
int styleable MaterialCheckBox_useMaterialThemeColors 10
int[] styleable MaterialCheckBoxStates { 0x7f04041d, 0x7f04041e }
int styleable MaterialCheckBoxStates_state_error 0
int styleable MaterialCheckBoxStates_state_indeterminate 1
int[] styleable MaterialDivider { 0x7f040185, 0x7f040187, 0x7f040188, 0x7f04018a, 0x7f040284 }
int styleable MaterialDivider_dividerColor 0
int styleable MaterialDivider_dividerInsetEnd 1
int styleable MaterialDivider_dividerInsetStart 2
int styleable MaterialDivider_dividerThickness 3
int styleable MaterialDivider_lastItemDecorated 4
int[] styleable MaterialRadioButton { 0x7f040092, 0x7f0404df }
int styleable MaterialRadioButton_buttonTint 0
int styleable MaterialRadioButton_useMaterialThemeColors 1
int[] styleable MaterialShape { 0x7f0403cb, 0x7f0403d3 }
int styleable MaterialShape_shapeAppearance 0
int styleable MaterialShape_shapeAppearanceOverlay 1
int[] styleable MaterialSwitch { 0x7f040495, 0x7f040496, 0x7f040497, 0x7f0404cb, 0x7f0404cc, 0x7f0404cd }
int styleable MaterialSwitch_thumbIcon 0
int styleable MaterialSwitch_thumbIconTint 1
int styleable MaterialSwitch_thumbIconTintMode 2
int styleable MaterialSwitch_trackDecoration 3
int styleable MaterialSwitch_trackDecorationTint 4
int styleable MaterialSwitch_trackDecorationTintMode 5
int[] styleable MaterialTextAppearance { 0x010104b6, 0x0101057f, 0x7f0402cc }
int styleable MaterialTextAppearance_android_letterSpacing 0
int styleable MaterialTextAppearance_android_lineHeight 1
int styleable MaterialTextAppearance_lineHeight 2
int[] styleable MaterialTextView { 0x01010034, 0x0101057f, 0x7f0402cc }
int styleable MaterialTextView_android_textAppearance 0
int styleable MaterialTextView_android_lineHeight 1
int styleable MaterialTextView_lineHeight 2
int[] styleable MaterialTimePicker { 0x7f0400d1, 0x7f04027d }
int styleable MaterialTimePicker_clockIcon 0
int styleable MaterialTimePicker_keyboardIcon 1
int[] styleable MaterialToolbar { 0x7f0402e0, 0x7f0402e2, 0x7f040358, 0x7f04042e, 0x7f0404aa }
int styleable MaterialToolbar_logoAdjustViewBounds 0
int styleable MaterialToolbar_logoScaleType 1
int styleable MaterialToolbar_navigationIconTint 2
int styleable MaterialToolbar_subtitleCentered 3
int styleable MaterialToolbar_titleCentered 4
int[] styleable MenuGroup { 0x0101000e, 0x010100d0, 0x01010194, 0x010101de, 0x010101df, 0x010101e0 }
int styleable MenuGroup_android_enabled 0
int styleable MenuGroup_android_id 1
int styleable MenuGroup_android_visible 2
int styleable MenuGroup_android_menuCategory 3
int styleable MenuGroup_android_orderInCategory 4
int styleable MenuGroup_android_checkableBehavior 5
int[] styleable MenuItem { 0x01010002, 0x0101000e, 0x010100d0, 0x01010106, 0x01010194, 0x010101de, 0x010101df, 0x010101e1, 0x010101e2, 0x010101e3, 0x010101e4, 0x010101e5, 0x0101026f, 0x7f04000e, 0x7f040022, 0x7f040024, 0x7f040031, 0x7f04013d, 0x7f040249, 0x7f04024a, 0x7f040362, 0x7f0403d9, 0x7f0404c0 }
int styleable MenuItem_android_icon 0
int styleable MenuItem_android_enabled 1
int styleable MenuItem_android_id 2
int styleable MenuItem_android_checked 3
int styleable MenuItem_android_visible 4
int styleable MenuItem_android_menuCategory 5
int styleable MenuItem_android_orderInCategory 6
int styleable MenuItem_android_title 7
int styleable MenuItem_android_titleCondensed 8
int styleable MenuItem_android_alphabeticShortcut 9
int styleable MenuItem_android_numericShortcut 10
int styleable MenuItem_android_checkable 11
int styleable MenuItem_android_onClick 12
int styleable MenuItem_actionLayout 13
int styleable MenuItem_actionProviderClass 14
int styleable MenuItem_actionViewClass 15
int styleable MenuItem_alphabeticModifiers 16
int styleable MenuItem_contentDescription 17
int styleable MenuItem_iconTint 18
int styleable MenuItem_iconTintMode 19
int styleable MenuItem_numericModifiers 20
int styleable MenuItem_showAsAction 21
int styleable MenuItem_tooltipText 22
int[] styleable MenuView { 0x010100ae, 0x0101012c, 0x0101012d, 0x0101012e, 0x0101012f, 0x01010130, 0x01010131, 0x7f040399, 0x7f040427 }
int styleable MenuView_android_windowAnimationStyle 0
int styleable MenuView_android_itemTextAppearance 1
int styleable MenuView_android_horizontalDivider 2
int styleable MenuView_android_verticalDivider 3
int styleable MenuView_android_headerBackground 4
int styleable MenuView_android_itemBackground 5
int styleable MenuView_android_itemIconDisabledAlpha 6
int styleable MenuView_preserveIconSpacing 7
int styleable MenuView_subMenuArrow 8
int[] styleable MockView { 0x7f040328, 0x7f040329, 0x7f04032a, 0x7f04032b, 0x7f04032c, 0x7f04032d }
int styleable MockView_mock_diagonalsColor 0
int styleable MockView_mock_label 1
int styleable MockView_mock_labelBackgroundColor 2
int styleable MockView_mock_labelColor 3
int styleable MockView_mock_showDiagonals 4
int styleable MockView_mock_showLabel 5
int[] styleable Motion { 0x7f040036, 0x7f04018f, 0x7f04034d, 0x7f04034f, 0x7f04037b, 0x7f0404d3 }
int styleable Motion_animate_relativeTo 0
int styleable Motion_drawPath 1
int styleable Motion_motionPathRotate 2
int styleable Motion_motionStagger 3
int styleable Motion_pathMotionArc 4
int styleable Motion_transitionEasing 5
int[] styleable MotionHelper { 0x7f040364, 0x7f040367 }
int styleable MotionHelper_onHide 0
int styleable MotionHelper_onShow 1
int[] styleable MotionLayout { 0x7f04003a, 0x7f040163, 0x7f040287, 0x7f04032e, 0x7f04034e, 0x7f0403dd }
int styleable MotionLayout_applyMotionScene 0
int styleable MotionLayout_currentState 1
int styleable MotionLayout_layoutDescription 2
int styleable MotionLayout_motionDebug 3
int styleable MotionLayout_motionProgress 4
int styleable MotionLayout_showPaths 5
int[] styleable MotionScene { 0x7f040174, 0x7f040288 }
int styleable MotionScene_defaultDuration 0
int styleable MotionScene_layoutDuringTransition 1
int[] styleable MotionTelltales { 0x7f04045b, 0x7f04045c, 0x7f04045d }
int styleable MotionTelltales_telltales_tailColor 0
int styleable MotionTelltales_telltales_tailScale 1
int styleable MotionTelltales_telltales_velocityMode 2
int[] styleable NavAction { 0x010100d0, 0x7f04017e, 0x7f0401b4, 0x7f0401bf, 0x7f040285, 0x7f04038b, 0x7f04038c, 0x7f04038d, 0x7f04038e, 0x7f04038f, 0x7f0403ae }
int styleable NavAction_android_id 0
int styleable NavAction_destination 1
int styleable NavAction_enterAnim 2
int styleable NavAction_exitAnim 3
int styleable NavAction_launchSingleTop 4
int styleable NavAction_popEnterAnim 5
int styleable NavAction_popExitAnim 6
int styleable NavAction_popUpTo 7
int styleable NavAction_popUpToInclusive 8
int styleable NavAction_popUpToSaveState 9
int styleable NavAction_restoreState 10
int[] styleable NavArgument { 0x01010003, 0x010101ed, 0x7f04003c, 0x7f040360 }
int styleable NavArgument_android_name 0
int styleable NavArgument_android_defaultValue 1
int styleable NavArgument_argType 2
int styleable NavArgument_nullable 3
int[] styleable NavDeepLink { 0x010104ee, 0x7f040000, 0x7f040321, 0x7f0404dc }
int styleable NavDeepLink_android_autoVerify 0
int styleable NavDeepLink_action 1
int styleable NavDeepLink_mimeType 2
int styleable NavDeepLink_uri 3
int[] styleable NavGraphNavigator { 0x7f040411 }
int styleable NavGraphNavigator_startDestination 0
int[] styleable NavHost { 0x7f040355 }
int styleable NavHost_navGraph 0
int[] styleable NavHostFragment { 0x7f040176 }
int styleable NavHostFragment_defaultNavHost 0
int[] styleable NavInclude { 0x7f04021d }
int styleable NavInclude_graph 0
int[] styleable NavigationBarActiveIndicator { 0x01010155, 0x01010159, 0x010101a5, 0x7f0402e3, 0x7f0403cb }
int styleable NavigationBarActiveIndicator_android_height 0
int styleable NavigationBarActiveIndicator_android_width 1
int styleable NavigationBarActiveIndicator_android_color 2
int styleable NavigationBarActiveIndicator_marginHorizontal 3
int styleable NavigationBarActiveIndicator_shapeAppearance 4
int[] styleable NavigationBarView { 0x7f040053, 0x7f0401a3, 0x7f04025e, 0x7f04025f, 0x7f040264, 0x7f040265, 0x7f040269, 0x7f04026a, 0x7f04026b, 0x7f040277, 0x7f040278, 0x7f040279, 0x7f040282, 0x7f04031e }
int styleable NavigationBarView_backgroundTint 0
int styleable NavigationBarView_elevation 1
int styleable NavigationBarView_itemActiveIndicatorStyle 2
int styleable NavigationBarView_itemBackground 3
int styleable NavigationBarView_itemIconSize 4
int styleable NavigationBarView_itemIconTint 5
int styleable NavigationBarView_itemPaddingBottom 6
int styleable NavigationBarView_itemPaddingTop 7
int styleable NavigationBarView_itemRippleColor 8
int styleable NavigationBarView_itemTextAppearanceActive 9
int styleable NavigationBarView_itemTextAppearanceInactive 10
int styleable NavigationBarView_itemTextColor 11
int styleable NavigationBarView_labelVisibilityMode 12
int styleable NavigationBarView_menu 13
int[] styleable NavigationRailView { 0x7f040229, 0x7f040267, 0x7f040320, 0x7f04036c, 0x7f040372 }
int styleable NavigationRailView_headerLayout 0
int styleable NavigationRailView_itemMinHeight 1
int styleable NavigationRailView_menuGravity 2
int styleable NavigationRailView_paddingBottomSystemWindowInsets 3
int styleable NavigationRailView_paddingTopSystemWindowInsets 4
int[] styleable NavigationView { 0x010100b3, 0x010100d4, 0x010100dd, 0x0101011f, 0x7f040070, 0x7f040187, 0x7f040188, 0x7f04019a, 0x7f0401a3, 0x7f040229, 0x7f04025f, 0x7f040261, 0x7f040263, 0x7f040264, 0x7f040265, 0x7f040266, 0x7f04026b, 0x7f04026c, 0x7f04026d, 0x7f04026e, 0x7f04026f, 0x7f040270, 0x7f040271, 0x7f040272, 0x7f040276, 0x7f040279, 0x7f04027a, 0x7f04031e, 0x7f0403cb, 0x7f0403d3, 0x7f040428, 0x7f040429, 0x7f04042a, 0x7f04042b, 0x7f0404c1 }
int styleable NavigationView_android_layout_gravity 0
int styleable NavigationView_android_background 1
int styleable NavigationView_android_fitsSystemWindows 2
int styleable NavigationView_android_maxWidth 3
int styleable NavigationView_bottomInsetScrimEnabled 4
int styleable NavigationView_dividerInsetEnd 5
int styleable NavigationView_dividerInsetStart 6
int styleable NavigationView_drawerLayoutCornerSize 7
int styleable NavigationView_elevation 8
int styleable NavigationView_headerLayout 9
int styleable NavigationView_itemBackground 10
int styleable NavigationView_itemHorizontalPadding 11
int styleable NavigationView_itemIconPadding 12
int styleable NavigationView_itemIconSize 13
int styleable NavigationView_itemIconTint 14
int styleable NavigationView_itemMaxLines 15
int styleable NavigationView_itemRippleColor 16
int styleable NavigationView_itemShapeAppearance 17
int styleable NavigationView_itemShapeAppearanceOverlay 18
int styleable NavigationView_itemShapeFillColor 19
int styleable NavigationView_itemShapeInsetBottom 20
int styleable NavigationView_itemShapeInsetEnd 21
int styleable NavigationView_itemShapeInsetStart 22
int styleable NavigationView_itemShapeInsetTop 23
int styleable NavigationView_itemTextAppearance 24
int styleable NavigationView_itemTextColor 25
int styleable NavigationView_itemVerticalPadding 26
int styleable NavigationView_menu 27
int styleable NavigationView_shapeAppearance 28
int styleable NavigationView_shapeAppearanceOverlay 29
int styleable NavigationView_subheaderColor 30
int styleable NavigationView_subheaderInsetEnd 31
int styleable NavigationView_subheaderInsetStart 32
int styleable NavigationView_subheaderTextAppearance 33
int styleable NavigationView_topInsetScrimEnabled 34
int[] styleable Navigator { 0x01010001, 0x010100d0, 0x7f0403b3 }
int styleable Navigator_android_label 0
int styleable Navigator_android_id 1
int styleable Navigator_route 2
int[] styleable OnClick { 0x7f0400ce, 0x7f040459 }
int styleable OnClick_clickAction 0
int styleable OnClick_targetId 1
int[] styleable OnSwipe { 0x7f04018c, 0x7f04018d, 0x7f04018e, 0x7f0402cb, 0x7f040314, 0x7f04031b, 0x7f040353, 0x7f04035c, 0x7f040368, 0x7f0404c2, 0x7f0404c3, 0x7f0404c4 }
int styleable OnSwipe_dragDirection 0
int styleable OnSwipe_dragScale 1
int styleable OnSwipe_dragThreshold 2
int styleable OnSwipe_limitBoundsTo 3
int styleable OnSwipe_maxAcceleration 4
int styleable OnSwipe_maxVelocity 5
int styleable OnSwipe_moveWhenScrollAtTop 6
int styleable OnSwipe_nestedScrollFlags 7
int styleable OnSwipe_onTouchUp 8
int styleable OnSwipe_touchAnchorId 9
int styleable OnSwipe_touchAnchorSide 10
int styleable OnSwipe_touchRegionId 11
int[] styleable PinField { 0x7f040183, 0x7f0401e1, 0x7f0401e2, 0x7f040236, 0x7f040237, 0x7f040238, 0x7f040239, 0x7f040258, 0x7f040259, 0x7f0402ce, 0x7f04035f }
int styleable PinField_distanceInBetween 0
int styleable PinField_fieldBgColor 1
int styleable PinField_fieldColor 2
int styleable PinField_highlightColor 3
int styleable PinField_highlightEnabled 4
int styleable PinField_highlightSingleFieldMode 5
int styleable PinField_highlightType 6
int styleable PinField_isCursorEnabled 7
int styleable PinField_isCustomBackground 8
int styleable PinField_lineThickness 9
int styleable PinField_noOfFields 10
int[] styleable PlayerControlView { 0x7f040028, 0x7f040029, 0x7f040038, 0x7f04005c, 0x7f04005d, 0x7f040082, 0x7f04014e, 0x7f040388, 0x7f040389, 0x7f0403ac, 0x7f0403ba, 0x7f0403bb, 0x7f0403bc, 0x7f0403bd, 0x7f0403be, 0x7f0403e1, 0x7f0403e2, 0x7f0403e3, 0x7f0403e4, 0x7f0403e5, 0x7f0403e6, 0x7f0403e7, 0x7f0403e8, 0x7f0404a5, 0x7f0404c5, 0x7f0404db }
int styleable PlayerControlView_ad_marker_color 0
int styleable PlayerControlView_ad_marker_width 1
int styleable PlayerControlView_animation_enabled 2
int styleable PlayerControlView_bar_gravity 3
int styleable PlayerControlView_bar_height 4
int styleable PlayerControlView_buffered_color 5
int styleable PlayerControlView_controller_layout_id 6
int styleable PlayerControlView_played_ad_marker_color 7
int styleable PlayerControlView_played_color 8
int styleable PlayerControlView_repeat_toggle_modes 9
int styleable PlayerControlView_scrubber_color 10
int styleable PlayerControlView_scrubber_disabled_size 11
int styleable PlayerControlView_scrubber_dragged_size 12
int styleable PlayerControlView_scrubber_drawable 13
int styleable PlayerControlView_scrubber_enabled_size 14
int styleable PlayerControlView_show_fastforward_button 15
int styleable PlayerControlView_show_next_button 16
int styleable PlayerControlView_show_previous_button 17
int styleable PlayerControlView_show_rewind_button 18
int styleable PlayerControlView_show_shuffle_button 19
int styleable PlayerControlView_show_subtitle_button 20
int styleable PlayerControlView_show_timeout 21
int styleable PlayerControlView_show_vr_button 22
int styleable PlayerControlView_time_bar_min_update_interval 23
int styleable PlayerControlView_touch_target_height 24
int styleable PlayerControlView_unplayed_color 25
int[] styleable PlayerView { 0x7f040028, 0x7f040029, 0x7f040038, 0x7f04003f, 0x7f040049, 0x7f04005c, 0x7f04005d, 0x7f040082, 0x7f04014e, 0x7f04017a, 0x7f040234, 0x7f040235, 0x7f04027b, 0x7f040388, 0x7f040389, 0x7f04038a, 0x7f0403ac, 0x7f0403ad, 0x7f0403ba, 0x7f0403bb, 0x7f0403bc, 0x7f0403bd, 0x7f0403be, 0x7f0403e0, 0x7f0403e5, 0x7f0403e6, 0x7f0403e7, 0x7f0403e8, 0x7f0403ea, 0x7f040436, 0x7f0404a5, 0x7f0404c5, 0x7f0404db, 0x7f0404e0, 0x7f0404e1 }
int styleable PlayerView_ad_marker_color 0
int styleable PlayerView_ad_marker_width 1
int styleable PlayerView_animation_enabled 2
int styleable PlayerView_artwork_display_mode 3
int styleable PlayerView_auto_show 4
int styleable PlayerView_bar_gravity 5
int styleable PlayerView_bar_height 6
int styleable PlayerView_buffered_color 7
int styleable PlayerView_controller_layout_id 8
int styleable PlayerView_default_artwork 9
int styleable PlayerView_hide_during_ads 10
int styleable PlayerView_hide_on_touch 11
int styleable PlayerView_keep_content_on_player_reset 12
int styleable PlayerView_played_ad_marker_color 13
int styleable PlayerView_played_color 14
int styleable PlayerView_player_layout_id 15
int styleable PlayerView_repeat_toggle_modes 16
int styleable PlayerView_resize_mode 17
int styleable PlayerView_scrubber_color 18
int styleable PlayerView_scrubber_disabled_size 19
int styleable PlayerView_scrubber_dragged_size 20
int styleable PlayerView_scrubber_drawable 21
int styleable PlayerView_scrubber_enabled_size 22
int styleable PlayerView_show_buffering 23
int styleable PlayerView_show_shuffle_button 24
int styleable PlayerView_show_subtitle_button 25
int styleable PlayerView_show_timeout 26
int styleable PlayerView_show_vr_button 27
int styleable PlayerView_shutter_background_color 28
int styleable PlayerView_surface_type 29
int styleable PlayerView_time_bar_min_update_interval 30
int styleable PlayerView_touch_target_height 31
int styleable PlayerView_unplayed_color 32
int styleable PlayerView_use_artwork 33
int styleable PlayerView_use_controller 34
int[] styleable PopupWindow { 0x01010176, 0x010102c9, 0x7f040369 }
int styleable PopupWindow_android_popupBackground 0
int styleable PopupWindow_android_popupAnimationStyle 1
int styleable PopupWindow_overlapAnchor 2
int[] styleable PopupWindowBackgroundState { 0x7f040419 }
int styleable PopupWindowBackgroundState_state_above_anchor 0
int[] styleable PropertySet { 0x010100dc, 0x0101031f, 0x7f0402ae, 0x7f04034e, 0x7f0404e6 }
int styleable PropertySet_android_visibility 0
int styleable PropertySet_android_alpha 1
int styleable PropertySet_layout_constraintTag 2
int styleable PropertySet_motionProgress 3
int styleable PropertySet_visibilityMode 4
int[] styleable RadialViewGroup { 0x7f040303 }
int styleable RadialViewGroup_materialCircleRadius 0
int[] styleable RangeSlider { 0x7f040324, 0x7f0404e2 }
int styleable RangeSlider_minSeparation 0
int styleable RangeSlider_values 1
int[] styleable RecycleListView { 0x7f04036b, 0x7f040371 }
int styleable RecycleListView_paddingBottomNoButtons 0
int styleable RecycleListView_paddingTopNoTitle 1
int[] styleable RecyclerView { 0x010100c4, 0x010100eb, 0x010100f1, 0x7f0401dc, 0x7f0401dd, 0x7f0401de, 0x7f0401df, 0x7f0401e0, 0x7f040289, 0x7f0403af, 0x7f0403fa, 0x7f04040f }
int styleable RecyclerView_android_orientation 0
int styleable RecyclerView_android_clipToPadding 1
int styleable RecyclerView_android_descendantFocusability 2
int styleable RecyclerView_fastScrollEnabled 3
int styleable RecyclerView_fastScrollHorizontalThumbDrawable 4
int styleable RecyclerView_fastScrollHorizontalTrackDrawable 5
int styleable RecyclerView_fastScrollVerticalThumbDrawable 6
int styleable RecyclerView_fastScrollVerticalTrackDrawable 7
int styleable RecyclerView_layoutManager 8
int styleable RecyclerView_reverseLayout 9
int styleable RecyclerView_spanCount 10
int styleable RecyclerView_stackFromEnd 11
int[] styleable ScrimInsetsFrameLayout { 0x7f040257 }
int styleable ScrimInsetsFrameLayout_insetForeground 0
int[] styleable ScrollingPagerIndicator { 0x7f0403fb, 0x7f0403fc, 0x7f0403fd, 0x7f0403fe, 0x7f0403ff, 0x7f040400, 0x7f040401, 0x7f040402, 0x7f040403, 0x7f040404 }
int styleable ScrollingPagerIndicator_spi_dotColor 0
int styleable ScrollingPagerIndicator_spi_dotMinimumSize 1
int styleable ScrollingPagerIndicator_spi_dotSelectedColor 2
int styleable ScrollingPagerIndicator_spi_dotSelectedSize 3
int styleable ScrollingPagerIndicator_spi_dotSize 4
int styleable ScrollingPagerIndicator_spi_dotSpacing 5
int styleable ScrollingPagerIndicator_spi_looped 6
int styleable ScrollingPagerIndicator_spi_orientation 7
int styleable ScrollingPagerIndicator_spi_visibleDotCount 8
int styleable ScrollingPagerIndicator_spi_visibleDotThreshold 9
int[] styleable ScrollingViewBehavior_Layout { 0x7f040068 }
int styleable ScrollingViewBehavior_Layout_behavior_overlapTop 0
int[] styleable SearchBar { 0x01010034, 0x0101014f, 0x01010150, 0x7f040175, 0x7f040178, 0x7f0401a3, 0x7f040217, 0x7f040231, 0x7f040358, 0x7f040425, 0x7f040426, 0x7f0404a8 }
int styleable SearchBar_android_textAppearance 0
int styleable SearchBar_android_text 1
int styleable SearchBar_android_hint 2
int styleable SearchBar_defaultMarginsEnabled 3
int styleable SearchBar_defaultScrollFlagsEnabled 4
int styleable SearchBar_elevation 5
int styleable SearchBar_forceDefaultNavigationOnClickListener 6
int styleable SearchBar_hideNavigationIcon 7
int styleable SearchBar_navigationIconTint 8
int styleable SearchBar_strokeColor 9
int styleable SearchBar_strokeWidth 10
int styleable SearchBar_tintNavigationIcon 11
int[] styleable SearchView { 0x01010034, 0x010100da, 0x0101011f, 0x0101014f, 0x01010150, 0x01010220, 0x01010264, 0x7f040034, 0x7f040035, 0x7f040042, 0x7f0400d3, 0x7f040135, 0x7f040177, 0x7f04021c, 0x7f040229, 0x7f040231, 0x7f04024b, 0x7f040286, 0x7f04039e, 0x7f04039f, 0x7f0403bf, 0x7f0403c0, 0x7f0403c1, 0x7f04042c, 0x7f040435, 0x7f0404de, 0x7f0404e7 }
int styleable SearchView_android_textAppearance 0
int styleable SearchView_android_focusable 1
int styleable SearchView_android_maxWidth 2
int styleable SearchView_android_text 3
int styleable SearchView_android_hint 4
int styleable SearchView_android_inputType 5
int styleable SearchView_android_imeOptions 6
int styleable SearchView_animateMenuItems 7
int styleable SearchView_animateNavigationIcon 8
int styleable SearchView_autoShowKeyboard 9
int styleable SearchView_closeIcon 10
int styleable SearchView_commitIcon 11
int styleable SearchView_defaultQueryHint 12
int styleable SearchView_goIcon 13
int styleable SearchView_headerLayout 14
int styleable SearchView_hideNavigationIcon 15
int styleable SearchView_iconifiedByDefault 16
int styleable SearchView_layout 17
int styleable SearchView_queryBackground 18
int styleable SearchView_queryHint 19
int styleable SearchView_searchHintIcon 20
int styleable SearchView_searchIcon 21
int styleable SearchView_searchPrefixText 22
int styleable SearchView_submitBackground 23
int styleable SearchView_suggestionRowLayout 24
int styleable SearchView_useDrawerArrowDrawable 25
int styleable SearchView_voiceIcon 26
int[] styleable ShapeAppearance { 0x7f040151, 0x7f040152, 0x7f040153, 0x7f040154, 0x7f040155, 0x7f040157, 0x7f040158, 0x7f040159, 0x7f04015a, 0x7f04015b }
int styleable ShapeAppearance_cornerFamily 0
int styleable ShapeAppearance_cornerFamilyBottomLeft 1
int styleable ShapeAppearance_cornerFamilyBottomRight 2
int styleable ShapeAppearance_cornerFamilyTopLeft 3
int styleable ShapeAppearance_cornerFamilyTopRight 4
int styleable ShapeAppearance_cornerSize 5
int styleable ShapeAppearance_cornerSizeBottomLeft 6
int styleable ShapeAppearance_cornerSizeBottomRight 7
int styleable ShapeAppearance_cornerSizeTopLeft 8
int styleable ShapeAppearance_cornerSizeTopRight 9
int[] styleable ShapeableImageView { 0x7f040144, 0x7f040145, 0x7f040146, 0x7f040147, 0x7f040148, 0x7f040149, 0x7f04014a, 0x7f0403cb, 0x7f0403d3, 0x7f040425, 0x7f040426 }
int styleable ShapeableImageView_contentPadding 0
int styleable ShapeableImageView_contentPaddingBottom 1
int styleable ShapeableImageView_contentPaddingEnd 2
int styleable ShapeableImageView_contentPaddingLeft 3
int styleable ShapeableImageView_contentPaddingRight 4
int styleable ShapeableImageView_contentPaddingStart 5
int styleable ShapeableImageView_contentPaddingTop 6
int styleable ShapeableImageView_shapeAppearance 7
int styleable ShapeableImageView_shapeAppearanceOverlay 8
int styleable ShapeableImageView_strokeColor 9
int styleable ShapeableImageView_strokeWidth 10
int[] styleable SideSheetBehavior_Layout { 0x0101011f, 0x01010120, 0x01010440, 0x7f040053, 0x7f040063, 0x7f040150, 0x7f0403cb, 0x7f0403d3 }
int styleable SideSheetBehavior_Layout_android_maxWidth 0
int styleable SideSheetBehavior_Layout_android_maxHeight 1
int styleable SideSheetBehavior_Layout_android_elevation 2
int styleable SideSheetBehavior_Layout_backgroundTint 3
int styleable SideSheetBehavior_Layout_behavior_draggable 4
int styleable SideSheetBehavior_Layout_coplanarSiblingViewId 5
int styleable SideSheetBehavior_Layout_shapeAppearance 6
int styleable SideSheetBehavior_Layout_shapeAppearanceOverlay 7
int[] styleable SignInButton { 0x7f04008f, 0x7f040123, 0x7f0403b5 }
int styleable SignInButton_buttonSize 0
int styleable SignInButton_colorScheme 1
int styleable SignInButton_scopeUris 2
int[] styleable Slider { 0x0101000e, 0x01010024, 0x01010146, 0x010102de, 0x010102df, 0x7f04021e, 0x7f04021f, 0x7f040280, 0x7f040281, 0x7f040326, 0x7f040493, 0x7f040494, 0x7f040498, 0x7f040499, 0x7f04049a, 0x7f04049e, 0x7f04049f, 0x7f0404a0, 0x7f0404a4, 0x7f0404c7, 0x7f0404c8, 0x7f0404c9, 0x7f0404ce }
int styleable Slider_android_enabled 0
int styleable Slider_android_value 1
int styleable Slider_android_stepSize 2
int styleable Slider_android_valueFrom 3
int styleable Slider_android_valueTo 4
int styleable Slider_haloColor 5
int styleable Slider_haloRadius 6
int styleable Slider_labelBehavior 7
int styleable Slider_labelStyle 8
int styleable Slider_minTouchTargetSize 9
int styleable Slider_thumbColor 10
int styleable Slider_thumbElevation 11
int styleable Slider_thumbRadius 12
int styleable Slider_thumbStrokeColor 13
int styleable Slider_thumbStrokeWidth 14
int styleable Slider_tickColor 15
int styleable Slider_tickColorActive 16
int styleable Slider_tickColorInactive 17
int styleable Slider_tickVisible 18
int styleable Slider_trackColor 19
int styleable Slider_trackColorActive 20
int styleable Slider_trackColorInactive 21
int styleable Slider_trackHeight 22
int[] styleable Snackbar { 0x7f0403f7, 0x7f0403f8, 0x7f0403f9 }
int styleable Snackbar_snackbarButtonStyle 0
int styleable Snackbar_snackbarStyle 1
int styleable Snackbar_snackbarTextViewStyle 2
int[] styleable SnackbarLayout { 0x0101011f, 0x7f040023, 0x7f040037, 0x7f040050, 0x7f040053, 0x7f040054, 0x7f0401a3, 0x7f040315, 0x7f0403cb, 0x7f0403d3 }
int styleable SnackbarLayout_android_maxWidth 0
int styleable SnackbarLayout_actionTextColorAlpha 1
int styleable SnackbarLayout_animationMode 2
int styleable SnackbarLayout_backgroundOverlayColorAlpha 3
int styleable SnackbarLayout_backgroundTint 4
int styleable SnackbarLayout_backgroundTintMode 5
int styleable SnackbarLayout_elevation 6
int styleable SnackbarLayout_maxActionInlineWidth 7
int styleable SnackbarLayout_shapeAppearance 8
int styleable SnackbarLayout_shapeAppearanceOverlay 9
int[] styleable Spinner { 0x010100b2, 0x01010176, 0x0101017b, 0x01010262, 0x7f040392 }
int styleable Spinner_android_entries 0
int styleable Spinner_android_popupBackground 1
int styleable Spinner_android_prompt 2
int styleable Spinner_android_dropDownWidth 3
int styleable Spinner_popupTheme 4
int[] styleable SplitPairFilter { 0x7f04039b, 0x7f0403c3, 0x7f0403c4 }
int styleable SplitPairFilter_primaryActivityName 0
int styleable SplitPairFilter_secondaryActivityAction 1
int styleable SplitPairFilter_secondaryActivityName 2
int[] styleable SplitPairRule { 0x7f0400cd, 0x7f0401e5, 0x7f0401e6, 0x7f040409, 0x7f04040a, 0x7f04040b, 0x7f04040c }
int styleable SplitPairRule_clearTop 0
int styleable SplitPairRule_finishPrimaryWithSecondary 1
int styleable SplitPairRule_finishSecondaryWithPrimary 2
int styleable SplitPairRule_splitLayoutDirection 3
int styleable SplitPairRule_splitMinSmallestWidth 4
int styleable SplitPairRule_splitMinWidth 5
int styleable SplitPairRule_splitRatio 6
int[] styleable SplitPlaceholderRule { 0x7f040383, 0x7f040409, 0x7f04040a, 0x7f04040b, 0x7f04040c }
int styleable SplitPlaceholderRule_placeholderActivityName 0
int styleable SplitPlaceholderRule_splitLayoutDirection 1
int styleable SplitPlaceholderRule_splitMinSmallestWidth 2
int styleable SplitPlaceholderRule_splitMinWidth 3
int styleable SplitPlaceholderRule_splitRatio 4
int[] styleable SquarePinField { 0x7f040156 }
int styleable SquarePinField_cornerRadius 0
int[] styleable State { 0x010100d0, 0x7f04013b }
int styleable State_android_id 0
int styleable State_constraints 1
int[] styleable StateListDrawable { 0x0101011c, 0x01010194, 0x01010195, 0x01010196, 0x0101030c, 0x0101030d }
int styleable StateListDrawable_android_dither 0
int styleable StateListDrawable_android_visible 1
int styleable StateListDrawable_android_variablePadding 2
int styleable StateListDrawable_android_constantSize 3
int styleable StateListDrawable_android_enterFadeDuration 4
int styleable StateListDrawable_android_exitFadeDuration 5
int[] styleable StateListDrawableItem { 0x01010199 }
int styleable StateListDrawableItem_android_drawable 0
int[] styleable StateSet { 0x7f040179 }
int styleable StateSet_defaultState 0
int[] styleable SwipeRefreshLayout { 0x7f040437 }
int styleable SwipeRefreshLayout_swipeRefreshLayoutProgressSpinnerBackgroundColor 0
int[] styleable SwitchCompat { 0x01010124, 0x01010125, 0x01010142, 0x7f0403de, 0x7f04040d, 0x7f040438, 0x7f040439, 0x7f04043b, 0x7f04049b, 0x7f04049c, 0x7f04049d, 0x7f0404c6, 0x7f0404d0, 0x7f0404d1 }
int styleable SwitchCompat_android_textOn 0
int styleable SwitchCompat_android_textOff 1
int styleable SwitchCompat_android_thumb 2
int styleable SwitchCompat_showText 3
int styleable SwitchCompat_splitTrack 4
int styleable SwitchCompat_switchMinWidth 5
int styleable SwitchCompat_switchPadding 6
int styleable SwitchCompat_switchTextAppearance 7
int styleable SwitchCompat_thumbTextPadding 8
int styleable SwitchCompat_thumbTint 9
int styleable SwitchCompat_thumbTintMode 10
int styleable SwitchCompat_track 11
int styleable SwitchCompat_trackTint 12
int styleable SwitchCompat_trackTintMode 13
int[] styleable SwitchMaterial { 0x7f0404df }
int styleable SwitchMaterial_useMaterialThemeColors 0
int[] styleable TabItem { 0x01010002, 0x010100f2, 0x0101014f }
int styleable TabItem_android_icon 0
int styleable TabItem_android_layout 1
int styleable TabItem_android_text 2
int[] styleable TabLayout { 0x7f04043c, 0x7f04043d, 0x7f04043e, 0x7f04043f, 0x7f040440, 0x7f040441, 0x7f040442, 0x7f040443, 0x7f040444, 0x7f040445, 0x7f040446, 0x7f040447, 0x7f040448, 0x7f040449, 0x7f04044a, 0x7f04044b, 0x7f04044c, 0x7f04044d, 0x7f04044e, 0x7f04044f, 0x7f040450, 0x7f040451, 0x7f040453, 0x7f040454, 0x7f040456, 0x7f040457, 0x7f040458 }
int styleable TabLayout_tabBackground 0
int styleable TabLayout_tabContentStart 1
int styleable TabLayout_tabGravity 2
int styleable TabLayout_tabIconTint 3
int styleable TabLayout_tabIconTintMode 4
int styleable TabLayout_tabIndicator 5
int styleable TabLayout_tabIndicatorAnimationDuration 6
int styleable TabLayout_tabIndicatorAnimationMode 7
int styleable TabLayout_tabIndicatorColor 8
int styleable TabLayout_tabIndicatorFullWidth 9
int styleable TabLayout_tabIndicatorGravity 10
int styleable TabLayout_tabIndicatorHeight 11
int styleable TabLayout_tabInlineLabel 12
int styleable TabLayout_tabMaxWidth 13
int styleable TabLayout_tabMinWidth 14
int styleable TabLayout_tabMode 15
int styleable TabLayout_tabPadding 16
int styleable TabLayout_tabPaddingBottom 17
int styleable TabLayout_tabPaddingEnd 18
int styleable TabLayout_tabPaddingStart 19
int styleable TabLayout_tabPaddingTop 20
int styleable TabLayout_tabRippleColor 21
int styleable TabLayout_tabSelectedTextAppearance 22
int styleable TabLayout_tabSelectedTextColor 23
int styleable TabLayout_tabTextAppearance 24
int styleable TabLayout_tabTextColor 25
int styleable TabLayout_tabUnboundedRipple 26
int[] styleable TabsRadioGroup { 0x7f04004b, 0x7f0403c9 }
int styleable TabsRadioGroup_backgroundColor 0
int styleable TabsRadioGroup_selectorColor 1
int[] styleable TextAppearance { 0x01010095, 0x01010096, 0x01010097, 0x01010098, 0x0101009a, 0x0101009b, 0x01010161, 0x01010162, 0x01010163, 0x01010164, 0x010103ac, 0x01010585, 0x7f04020b, 0x7f040214, 0x7f04045e, 0x7f04048f }
int styleable TextAppearance_android_textSize 0
int styleable TextAppearance_android_typeface 1
int styleable TextAppearance_android_textStyle 2
int styleable TextAppearance_android_textColor 3
int styleable TextAppearance_android_textColorHint 4
int styleable TextAppearance_android_textColorLink 5
int styleable TextAppearance_android_shadowColor 6
int styleable TextAppearance_android_shadowDx 7
int styleable TextAppearance_android_shadowDy 8
int styleable TextAppearance_android_shadowRadius 9
int styleable TextAppearance_android_fontFamily 10
int styleable TextAppearance_android_textFontWeight 11
int styleable TextAppearance_fontFamily 12
int styleable TextAppearance_fontVariationSettings 13
int styleable TextAppearance_textAllCaps 14
int styleable TextAppearance_textLocale 15
int[] styleable TextInputEditText { 0x7f04048a }
int styleable TextInputEditText_textInputLayoutFocusedRectEnabled 0
int[] styleable TextInputLayout { 0x0101000e, 0x0101009a, 0x0101011f, 0x0101013f, 0x01010150, 0x01010157, 0x0101015a, 0x7f040076, 0x7f040077, 0x7f040078, 0x7f040079, 0x7f04007a, 0x7f04007b, 0x7f04007c, 0x7f04007d, 0x7f04007e, 0x7f04007f, 0x7f040080, 0x7f04015c, 0x7f04015d, 0x7f04015e, 0x7f04015f, 0x7f040160, 0x7f040161, 0x7f0401a9, 0x7f0401aa, 0x7f0401ab, 0x7f0401ac, 0x7f0401ad, 0x7f0401ae, 0x7f0401af, 0x7f0401b0, 0x7f0401b6, 0x7f0401b7, 0x7f0401b8, 0x7f0401b9, 0x7f0401ba, 0x7f0401bb, 0x7f0401bd, 0x7f0401be, 0x7f0401c2, 0x7f04022b, 0x7f04022c, 0x7f04022d, 0x7f04022e, 0x7f04023a, 0x7f04023b, 0x7f04023c, 0x7f04023d, 0x7f040376, 0x7f040377, 0x7f040378, 0x7f040379, 0x7f04037a, 0x7f040384, 0x7f040385, 0x7f040386, 0x7f040396, 0x7f040397, 0x7f040398, 0x7f0403cb, 0x7f0403d3, 0x7f040412, 0x7f040413, 0x7f040414, 0x7f040415, 0x7f040416, 0x7f040417, 0x7f040418, 0x7f040432, 0x7f040433, 0x7f040434 }
int styleable TextInputLayout_android_enabled 0
int styleable TextInputLayout_android_textColorHint 1
int styleable TextInputLayout_android_maxWidth 2
int styleable TextInputLayout_android_minWidth 3
int styleable TextInputLayout_android_hint 4
int styleable TextInputLayout_android_maxEms 5
int styleable TextInputLayout_android_minEms 6
int styleable TextInputLayout_boxBackgroundColor 7
int styleable TextInputLayout_boxBackgroundMode 8
int styleable TextInputLayout_boxCollapsedPaddingTop 9
int styleable TextInputLayout_boxCornerRadiusBottomEnd 10
int styleable TextInputLayout_boxCornerRadiusBottomStart 11
int styleable TextInputLayout_boxCornerRadiusTopEnd 12
int styleable TextInputLayout_boxCornerRadiusTopStart 13
int styleable TextInputLayout_boxStrokeColor 14
int styleable TextInputLayout_boxStrokeErrorColor 15
int styleable TextInputLayout_boxStrokeWidth 16
int styleable TextInputLayout_boxStrokeWidthFocused 17
int styleable TextInputLayout_counterEnabled 18
int styleable TextInputLayout_counterMaxLength 19
int styleable TextInputLayout_counterOverflowTextAppearance 20
int styleable TextInputLayout_counterOverflowTextColor 21
int styleable TextInputLayout_counterTextAppearance 22
int styleable TextInputLayout_counterTextColor 23
int styleable TextInputLayout_endIconCheckable 24
int styleable TextInputLayout_endIconContentDescription 25
int styleable TextInputLayout_endIconDrawable 26
int styleable TextInputLayout_endIconMinSize 27
int styleable TextInputLayout_endIconMode 28
int styleable TextInputLayout_endIconScaleType 29
int styleable TextInputLayout_endIconTint 30
int styleable TextInputLayout_endIconTintMode 31
int styleable TextInputLayout_errorAccessibilityLiveRegion 32
int styleable TextInputLayout_errorContentDescription 33
int styleable TextInputLayout_errorEnabled 34
int styleable TextInputLayout_errorIconDrawable 35
int styleable TextInputLayout_errorIconTint 36
int styleable TextInputLayout_errorIconTintMode 37
int styleable TextInputLayout_errorTextAppearance 38
int styleable TextInputLayout_errorTextColor 39
int styleable TextInputLayout_expandedHintEnabled 40
int styleable TextInputLayout_helperText 41
int styleable TextInputLayout_helperTextEnabled 42
int styleable TextInputLayout_helperTextTextAppearance 43
int styleable TextInputLayout_helperTextTextColor 44
int styleable TextInputLayout_hintAnimationEnabled 45
int styleable TextInputLayout_hintEnabled 46
int styleable TextInputLayout_hintTextAppearance 47
int styleable TextInputLayout_hintTextColor 48
int styleable TextInputLayout_passwordToggleContentDescription 49
int styleable TextInputLayout_passwordToggleDrawable 50
int styleable TextInputLayout_passwordToggleEnabled 51
int styleable TextInputLayout_passwordToggleTint 52
int styleable TextInputLayout_passwordToggleTintMode 53
int styleable TextInputLayout_placeholderText 54
int styleable TextInputLayout_placeholderTextAppearance 55
int styleable TextInputLayout_placeholderTextColor 56
int styleable TextInputLayout_prefixText 57
int styleable TextInputLayout_prefixTextAppearance 58
int styleable TextInputLayout_prefixTextColor 59
int styleable TextInputLayout_shapeAppearance 60
int styleable TextInputLayout_shapeAppearanceOverlay 61
int styleable TextInputLayout_startIconCheckable 62
int styleable TextInputLayout_startIconContentDescription 63
int styleable TextInputLayout_startIconDrawable 64
int styleable TextInputLayout_startIconMinSize 65
int styleable TextInputLayout_startIconScaleType 66
int styleable TextInputLayout_startIconTint 67
int styleable TextInputLayout_startIconTintMode 68
int styleable TextInputLayout_suffixText 69
int styleable TextInputLayout_suffixTextAppearance 70
int styleable TextInputLayout_suffixTextColor 71
int[] styleable ThemeEnforcement { 0x01010034, 0x7f0401b1, 0x7f0401b2 }
int styleable ThemeEnforcement_android_textAppearance 0
int styleable ThemeEnforcement_enforceMaterialTheme 1
int styleable ThemeEnforcement_enforceTextAppearance 2
int[] styleable Toolbar { 0x010100af, 0x01010140, 0x7f040089, 0x7f0400db, 0x7f0400dc, 0x7f04013e, 0x7f04013f, 0x7f040140, 0x7f040141, 0x7f040142, 0x7f040143, 0x7f0402df, 0x7f0402e1, 0x7f040316, 0x7f04031e, 0x7f040356, 0x7f040357, 0x7f040392, 0x7f04042d, 0x7f04042f, 0x7f040430, 0x7f0404a9, 0x7f0404ad, 0x7f0404ae, 0x7f0404af, 0x7f0404b0, 0x7f0404b1, 0x7f0404b2, 0x7f0404b4, 0x7f0404b5 }
int styleable Toolbar_android_gravity 0
int styleable Toolbar_android_minHeight 1
int styleable Toolbar_buttonGravity 2
int styleable Toolbar_collapseContentDescription 3
int styleable Toolbar_collapseIcon 4
int styleable Toolbar_contentInsetEnd 5
int styleable Toolbar_contentInsetEndWithActions 6
int styleable Toolbar_contentInsetLeft 7
int styleable Toolbar_contentInsetRight 8
int styleable Toolbar_contentInsetStart 9
int styleable Toolbar_contentInsetStartWithNavigation 10
int styleable Toolbar_logo 11
int styleable Toolbar_logoDescription 12
int styleable Toolbar_maxButtonHeight 13
int styleable Toolbar_menu 14
int styleable Toolbar_navigationContentDescription 15
int styleable Toolbar_navigationIcon 16
int styleable Toolbar_popupTheme 17
int styleable Toolbar_subtitle 18
int styleable Toolbar_subtitleTextAppearance 19
int styleable Toolbar_subtitleTextColor 20
int styleable Toolbar_title 21
int styleable Toolbar_titleMargin 22
int styleable Toolbar_titleMarginBottom 23
int styleable Toolbar_titleMarginEnd 24
int styleable Toolbar_titleMarginStart 25
int styleable Toolbar_titleMarginTop 26
int styleable Toolbar_titleMargins 27
int styleable Toolbar_titleTextAppearance 28
int styleable Toolbar_titleTextColor 29
int[] styleable Tooltip { 0x01010034, 0x01010098, 0x010100d5, 0x010100f6, 0x0101013f, 0x01010140, 0x0101014f, 0x7f040053 }
int styleable Tooltip_android_textAppearance 0
int styleable Tooltip_android_textColor 1
int styleable Tooltip_android_padding 2
int styleable Tooltip_android_layout_margin 3
int styleable Tooltip_android_minWidth 4
int styleable Tooltip_android_minHeight 5
int styleable Tooltip_android_text 6
int styleable Tooltip_backgroundTint 7
int[] styleable Transform { 0x01010320, 0x01010321, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103fa, 0x01010440 }
int styleable Transform_android_transformPivotX 0
int styleable Transform_android_transformPivotY 1
int styleable Transform_android_translationX 2
int styleable Transform_android_translationY 3
int styleable Transform_android_scaleX 4
int styleable Transform_android_scaleY 5
int styleable Transform_android_rotation 6
int styleable Transform_android_rotationX 7
int styleable Transform_android_rotationY 8
int styleable Transform_android_translationZ 9
int styleable Transform_android_elevation 10
int[] styleable Transition { 0x010100d0, 0x7f040048, 0x7f040138, 0x7f040139, 0x7f04019e, 0x7f040288, 0x7f04034b, 0x7f04037b, 0x7f040410, 0x7f0404d2, 0x7f0404d4 }
int styleable Transition_android_id 0
int styleable Transition_autoTransition 1
int styleable Transition_constraintSetEnd 2
int styleable Transition_constraintSetStart 3
int styleable Transition_duration 4
int styleable Transition_layoutDuringTransition 5
int styleable Transition_motionInterpolator 6
int styleable Transition_pathMotionArc 7
int styleable Transition_staggered 8
int styleable Transition_transitionDisable 9
int styleable Transition_transitionFlags 10
int[] styleable Variant { 0x7f04013b, 0x7f0403a7, 0x7f0403a8, 0x7f0403a9, 0x7f0403aa }
int styleable Variant_constraints 0
int styleable Variant_region_heightLessThan 1
int styleable Variant_region_heightMoreThan 2
int styleable Variant_region_widthLessThan 3
int styleable Variant_region_widthMoreThan 4
int[] styleable View { 0x01010000, 0x010100da, 0x7f04036d, 0x7f040370, 0x7f040491 }
int styleable View_android_theme 0
int styleable View_android_focusable 1
int styleable View_paddingEnd 2
int styleable View_paddingStart 3
int styleable View_theme 4
int[] styleable ViewBackgroundHelper { 0x010100d4, 0x7f040053, 0x7f040054 }
int styleable ViewBackgroundHelper_android_background 0
int styleable ViewBackgroundHelper_backgroundTint 1
int styleable ViewBackgroundHelper_backgroundTintMode 2
int[] styleable ViewPager2 { 0x010100c4 }
int styleable ViewPager2_android_orientation 0
int[] styleable ViewStubCompat { 0x010100d0, 0x010100f2, 0x010100f3 }
int styleable ViewStubCompat_android_id 0
int styleable ViewStubCompat_android_layout 1
int styleable ViewStubCompat_android_inflatedId 2
int[] styleable zxing_camera_preview { 0x7f0404ff, 0x7f040500, 0x7f040502, 0x7f040505 }
int styleable zxing_camera_preview_zxing_framing_rect_height 0
int styleable zxing_camera_preview_zxing_framing_rect_width 1
int styleable zxing_camera_preview_zxing_preview_scaling_strategy 2
int styleable zxing_camera_preview_zxing_use_texture_view 3
int[] styleable zxing_finder { 0x7f040501, 0x7f040503, 0x7f040506, 0x7f040507, 0x7f040508 }
int styleable zxing_finder_zxing_possible_result_points 0
int styleable zxing_finder_zxing_result_view 1
int styleable zxing_finder_zxing_viewfinder_laser 2
int styleable zxing_finder_zxing_viewfinder_laser_visibility 3
int styleable zxing_finder_zxing_viewfinder_mask 4
int[] styleable zxing_view { 0x7f040504 }
int styleable zxing_view_zxing_scanner_layout 0
int xml backup_rules 0x7f170000
int xml data_extraction_rules 0x7f170001
int xml image_share_filepaths 0x7f170002
