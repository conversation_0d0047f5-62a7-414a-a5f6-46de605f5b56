package com.flashbid.luv.ui.fragments.battle

import android.os.Bundle
import android.view.View
import androidx.core.widget.doAfterTextChanged
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import com.flashbid.luv.R
import com.flashbid.luv.adapter.FollowsAdapter
import com.flashbid.luv.databinding.BottomSheetChooseUserBinding
import com.flashbid.luv.databinding.FragmentStartLuvBattleBinding
import com.flashbid.luv.extensions.setOnClickWithDebounce
import com.flashbid.luv.extensions.setVerticalLayout
import com.flashbid.luv.extensions.snackBar
import com.flashbid.luv.extensions.viewBinding
import com.flashbid.luv.models.remote.UserDetails
import com.flashbid.luv.network.Status
import com.flashbid.luv.viewmodels.BattleViewModel
import com.flashbid.luv.viewmodels.TransactionViewModel
import com.flashbid.luv.viewmodels.UserViewModel
import com.google.android.material.bottomsheet.BottomSheetDialog
import org.koin.androidx.viewmodel.ext.android.viewModel

class StartLuvBattleFragment : Fragment(R.layout.fragment_start_luv_battle) {

    private val binding by viewBinding(FragmentStartLuvBattleBinding::bind)
    private val followAdapter by lazy { FollowsAdapter(list, false, this::onUserSelect) }
    private val list: ArrayList<UserDetails> = ArrayList()
    private val viewModel: UserViewModel by viewModel()
    private val bViewModel: BattleViewModel by viewModel()
    private val transactionViewModel: TransactionViewModel by viewModel()
    private val sheet: BottomSheetDialog by lazy { BottomSheetDialog(requireContext()) }

    private fun onUserSelect(item: UserDetails) {
        sheet.dismiss()
        val action =
            StartLuvBattleFragmentDirections.actionStartLuvBattleFragmentToSelectOpponentFragment(
                item
            )
        findNavController().navigate(action)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

         bViewModel.leaveBattle()

        binding.ivBack.setOnClickListener {
            findNavController().popBackStack()
        }

        binding.llTheWorld.setOnClickListener {
            getRandomUser()
        }

        binding.btnFollowers.setOnClickListener {
            showUserSheet(false)
        }

        binding.btnFollowing.setOnClickListener {
            showUserSheet(true)
        }

    }

    private fun getFollows() {
        viewModel.getFollowers("").observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(
                    it.message ?: getString(
                        R.string.something_went_wrong, getString(R.string.try_again)
                    )
                )

                Status.LOADING -> {}
                Status.SUCCESS -> {
                    list.clear()
                    list.addAll(it.data?.list ?: ArrayList())
                    followAdapter.refresh()
                }
            }
        }
    }

    private fun getFollowings() {
        viewModel.getFollowings("").observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(
                    it.message ?: getString(
                        R.string.something_went_wrong, getString(R.string.try_again)
                    )
                )

                Status.LOADING -> {}
                Status.SUCCESS -> {
                    list.clear()
                    list.addAll(it.data?.list ?: ArrayList())
                    followAdapter.refresh()
                }
            }
        }
    }

    private fun getRandomUser() {
        transactionViewModel.getRandomUser().observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(
                    it.message ?: getString(
                        R.string.something_went_wrong, getString(R.string.try_again)
                    )
                )

                Status.LOADING -> {}
                Status.SUCCESS -> {
                    val item = it.data?.data
                    if (item != null) {
                        val data = UserDetails(
                            id = item.user_id,
                            first_name = item.first_name,
                            last_name = item.last_name,
                            username = item.username,
                            photo = item.user_image,
                            user_id = item.user_id
                        )
                        onUserSelect(data)
                    }
                }
            }
        }
    }

    private fun showUserSheet(boolean: Boolean) {
        val view = BottomSheetChooseUserBinding.inflate(layoutInflater)

        view.edtSearch.doAfterTextChanged { followAdapter.filter(it.toString()) }

        view.imageView6.setOnClickWithDebounce {
            sheet.dismiss()
        }
        view.rcvUsers.apply {
            setVerticalLayout()
            adapter = followAdapter
        }
        if (boolean) {
            getFollowings()
        } else {
            getFollows()
        }

        followAdapter.filter("")

        sheet.setCancelable(true)
        sheet.setContentView(view.root)
        sheet.show()
    }


}