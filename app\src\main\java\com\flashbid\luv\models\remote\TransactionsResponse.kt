package com.flashbid.luv.models.remote

data class TransactionsResponse(
    val error: <PERSON><PERSON>an,
    val list: ArrayList<Transaction>,
    val message: String
)

data class Transaction(
    val received_amount: String?,
    val receiver: String,
    val receiver_user_id: Int,
    val sender_photo: String?,
    val receiver_photo: String?,
    val sender: String,
    val gift_name: String?,
    val gift_image: String?,
    val sender_user_id: Int,
    val story: ArrayList<OtherStory>?,
    val update_at: String
)
