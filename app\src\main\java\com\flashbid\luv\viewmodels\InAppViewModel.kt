package com.flashbid.luv.viewmodels

import android.app.Activity
import android.content.Context
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.android.billingclient.api.*
import com.flashbid.luv.models.remote.PurchaseResponse

class InAppViewModel(context: Context) : ViewModel() {

    val purchaseLiveResponse: MutableLiveData<PurchaseResponse> = MutableLiveData()

    private val billingClient: BillingClient =
        BillingClient.newBuilder(context).enablePendingPurchases()
            .setListener { result, purchases ->
                when (result.responseCode) {
                    BillingClient.BillingResponseCode.OK -> {
                        purchases?.forEach { purchase ->
                            handlePurchase(purchase)
                        }
                    }
                    BillingClient.BillingResponseCode.USER_CANCELED -> {
                        // Handle an error caused by a user canceling the purchase flow.
                    }
                    else -> {
                        // Handle any other error codes.
                    }
                }
            }.build()

    fun startBillingConnection() {
        billingClient.startConnection(object : BillingClientStateListener {
            override fun onBillingSetupFinished(responseCode: BillingResult) {
                if (responseCode.responseCode == BillingClient.BillingResponseCode.OK) {
                    // The billing client is ready. You can query for purchases here.
                }
            }

            override fun onBillingServiceDisconnected() {
                // Try to restart the connection on the next request to
                // Google Play by calling the startConnection() method.
            }

        })
    }

    fun endBillingConnection() {
        billingClient.endConnection()
    }

    suspend fun launchBillingFlow(activity: Activity, sku: String) {
        val skuDetails = billingClient.querySkuDetails(
            SkuDetailsParams.newBuilder().setSkusList(listOf(sku))
                .setType(BillingClient.SkuType.INAPP).build()
        ).skuDetailsList?.firstOrNull() ?: return

        billingClient.launchBillingFlow(
            activity, BillingFlowParams.newBuilder().setSkuDetails(skuDetails).build()
        )
    }

    private fun handlePurchase(purchase: Purchase) {
        if (purchase.purchaseState == Purchase.PurchaseState.PURCHASED) {
            val consumeParams = ConsumeParams.newBuilder()
                .setPurchaseToken(purchase.purchaseToken)
                .build()
            billingClient.consumeAsync(consumeParams) { result, purchaseToken ->
                if (result.responseCode == BillingClient.BillingResponseCode.OK) {
                    // Grant the user access to the feature or content they purchased.
                    // Acknowledge the purchase if it hasn't already been acknowledged.
                    if (!purchase.isAcknowledged) {
                        val acknowledgePurchaseParams =
                            AcknowledgePurchaseParams.newBuilder().setPurchaseToken(purchaseToken)
                                .build()
                        billingClient.acknowledgePurchase(acknowledgePurchaseParams) { _ ->
                            purchaseLiveResponse.postValue(
                                PurchaseResponse(
                                    true,
                                    purchaseToken,
                                    purchase.products[0],
                                    purchase.packageName
                                )
                            )
                        }
                    } else {
                        purchaseLiveResponse.postValue(
                            PurchaseResponse(
                                true,
                                purchaseToken,
                                purchase.orderId,
                                purchase.packageName
                            )
                        )
                    }
                } else {
                    // Handle a failed consumption.
                    purchaseLiveResponse.postValue(
                        PurchaseResponse(
                            false,
                            purchaseToken,
                            purchase.orderId,
                            purchase.packageName
                        )
                    )
                }
            }
        } else {
            purchaseLiveResponse.postValue(
                PurchaseResponse(
                    false,
                    purchase.purchaseToken,
                    purchase.orderId,
                    purchase.packageName
                )
            )
        }
    }
}
