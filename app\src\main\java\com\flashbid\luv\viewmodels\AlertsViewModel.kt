package com.flashbid.luv.viewmodels

import androidx.lifecycle.LiveData
import androidx.lifecycle.ViewModel
import com.flashbid.luv.models.remote.*
import com.flashbid.luv.network.Resource
import com.flashbid.luv.repositories.BattleRepository

class AlertsViewModel(
    private val battleRepository: BattleRepository
) : ViewModel() {

    fun getAlerts(): LiveData<Resource<AlertsResponse>> {
        return battleRepository.getAlerts()
    }

    fun updateVideoStatus(
        id:Int,
        request: UpdateVideoStatusRequest
    ): LiveData<Resource<MessageResponse>> {
        return battleRepository.updateVideoStatus(id,
            request
        )
    }

}