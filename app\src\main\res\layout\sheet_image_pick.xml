<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    android:background="@drawable/bg_round_corners"
    android:orientation="vertical"
    android:paddingHorizontal="@dimen/_15sdp"
    android:paddingVertical="@dimen/_10sdp"
    app:layout_behavior="@string/bottom_sheet_behavior">

    <View
        android:id="@+id/view2"
        android:layout_width="@dimen/_40sdp"
        android:layout_height="4dp"
        android:layout_gravity="center"
        android:background="@drawable/bg_edit"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/imageView6"
        android:layout_width="@dimen/_25sdp"
        android:layout_height="@dimen/_25sdp"
        android:layout_marginTop="5dp"
        android:src="@drawable/ic_close_gray"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/view2" />

    <androidx.cardview.widget.CardView
        android:id="@+id/btnGallery"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_20sdp"
        android:paddingVertical="@dimen/_10sdp"
        app:cardBackgroundColor="@color/semiblue"
        app:cardCornerRadius="16dp"
        app:cardElevation="0dp"
        app:contentPadding="@dimen/_10sdp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/imageView6">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="@string/select_from_gallery"
            android:textColor="@color/blue" />

    </androidx.cardview.widget.CardView>

    <androidx.cardview.widget.CardView
        android:id="@+id/btnCamera"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_15sdp"
        android:layout_marginBottom="@dimen/_40sdp"
        app:cardBackgroundColor="@color/semiblue"
        app:cardCornerRadius="16dp"
        app:cardElevation="0dp"
        app:contentPadding="@dimen/_10sdp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/btnGallery">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="@string/open_camera"
            android:textColor="@color/blue" />

    </androidx.cardview.widget.CardView>


</androidx.constraintlayout.widget.ConstraintLayout>