package com.flashbid.luv.ui.fragments.operations.recharge

import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.flashbid.luv.MainActivity
import com.flashbid.luv.R
import com.flashbid.luv.adapter.RechargePlansAdapter
import com.flashbid.luv.databinding.FragmentRechargeBinding
import com.flashbid.luv.extensions.TextState
import com.flashbid.luv.extensions.setGridLayout
import com.flashbid.luv.extensions.setOnClickWithDebounce
import com.flashbid.luv.extensions.snackBar
import com.flashbid.luv.extensions.viewBinding
import com.flashbid.luv.models.remote.AllPlansResponse
import com.flashbid.luv.network.Status
import com.flashbid.luv.viewmodels.InAppViewModel
import com.flashbid.luv.viewmodels.TransactionViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.koin.androidx.viewmodel.ext.android.viewModel

class RechargeFragment : Fragment(R.layout.fragment_recharge) {

    private val binding by viewBinding(FragmentRechargeBinding::bind)
    private val viewModel: TransactionViewModel by viewModel()
    private val inAppViewModel: InAppViewModel by viewModel()
    private val plansList: ArrayList<AllPlansResponse.Plan> = ArrayList()
    private var buyItemId: Int = -1
    private val planAdapter by lazy {
        RechargePlansAdapter(plansList) { item ->
            binding.btnNext.setOnClickWithDebounce {
                buyItemId = item.id
                lifecycleScope.launch(Dispatchers.IO) {
                    inAppViewModel.launchBillingFlow(requireActivity(), "${item.amount}_coins")
                }
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.imageView.setOnClickWithDebounce { findNavController().popBackStack() }
        inAppViewModel.startBillingConnection()

        binding.rcvPlans.apply {
            setGridLayout(4)
            adapter = planAdapter
        }

        observePurchase()

        getAllPlans()

    }

    private fun observePurchase() {
        inAppViewModel.purchaseLiveResponse.observe(viewLifecycleOwner) {
            if (it.success) {
                buyPlan(
                    buyItemId, it.packageName ?: "",
                    it.token ?: "", it.productId ?: ""
                )
            } else snackBar(getString(R.string.something_went_wrong))
        }
    }

    private fun getAllPlans() {
        viewModel.getAllPlans().observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(it.message ?: getString(R.string.something_went_wrong, getString(R.string.try_again)))
                Status.LOADING -> {}
                Status.SUCCESS -> {
                    plansList.apply {
                        clear()
                        addAll(it.data?.list ?: ArrayList())
                    }
                    planAdapter.refresh()
                }
            }
        }
    }

    private fun buyPlan(planId: Int, name: String, token: String, productId: String) {
        viewModel.rechargePlan(planId, name, token, productId).observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(it.message ?: getString(R.string.something_went_wrong, getString(R.string.try_again)))
                Status.LOADING -> {
                }
                Status.SUCCESS -> {
                    (requireActivity() as MainActivity).showSnackBar(getString(R.string.recharge_success), TextState.SUCCESS)
                    findNavController().popBackStack()
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        inAppViewModel.endBillingConnection()
    }
}