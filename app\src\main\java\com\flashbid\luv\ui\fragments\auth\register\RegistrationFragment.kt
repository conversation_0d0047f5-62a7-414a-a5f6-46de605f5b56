package com.flashbid.luv.ui.fragments.auth.register

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.View
import androidx.core.widget.doAfterTextChanged
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import com.flashbid.luv.R
import com.flashbid.luv.data.local.AppPreferences
import com.flashbid.luv.databinding.FragmentRegistrationBinding
import com.flashbid.luv.extensions.*
import com.flashbid.luv.models.remote.UpdateProfileRequest
import com.flashbid.luv.network.Status
import com.flashbid.luv.util.BaseLiveData
import com.flashbid.luv.util.Constants
import com.flashbid.luv.util.RegisterState
import com.flashbid.luv.viewmodels.UserViewModel
import com.poovam.pinedittextfield.PinField
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel


class  RegistrationFragment : Fragment(R.layout.fragment_registration) {

    private val binding by viewBinding(FragmentRegistrationBinding::bind)
    private var currentState: RegisterState = RegisterState.EMAIL
    private val viewModel: UserViewModel by viewModel()
    private val pref by inject<AppPreferences>()
    private var email = ""
    private var password = ""
    private var referralCode: Int? = null

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.edtEmail.doAfterTextChanged { email = it.toString() }
        binding.edtPass.doAfterTextChanged { password = it.toString() }

        setScreenState(RegisterState.EMAIL)

        initClickListeners()

        binding.edtUsername.disableSpaces()
        binding.edtPass.disableSpaces()
        binding.edtEmail.disableSpaces()
        binding.edtPassConfirm.disableSpaces()

        BaseLiveData.referralLiveData.observe(viewLifecycleOwner) {
            referralCode = it
        }
    }

    private fun initClickListeners() {
        binding.imageView.setOnClickWithDebounce {
            when (currentState) {
                RegisterState.EMAIL -> {
                    findNavController().popBackStack()
                }
                RegisterState.VERIFY -> {
                    setScreenState(RegisterState.EMAIL)
                }
                RegisterState.PASSWORD -> {
                    setScreenState(RegisterState.VERIFY)
                }
                RegisterState.USERNAME -> {
                    setScreenState(RegisterState.PASSWORD)
                }
            }
        }
    }

    private fun setScreenState(state: RegisterState = RegisterState.EMAIL) {
        currentState = state
        when (state) {
            RegisterState.EMAIL -> {
                setEmailState()
            }
            RegisterState.VERIFY -> {
                setVerificationState()
            }
            RegisterState.PASSWORD -> {
                setPasswordState()
            }
            RegisterState.USERNAME -> {
                setUsernameState()
            }
        }

    }

    private fun setUsernameState() {
        with(binding) {
            tvInfo.setTextState(getString(R.string.your_name))
            tvNoCode.text = getString(R.string.special_not_allowed)
            btnNext.text = getString(R.string.finish)
            llPass.hide()
            llPassConfirm.hide()
            edtName.show()
            edtUsername.show()

            btnNext.setOnClickWithDebounce {
                if (edtName.checkIsEmpty() || edtUsername.checkIsEmpty()) {
                    tvInfo.setTextState(getString(R.string.fields_empty), TextState.ERROR)
                } else {
                    setUsername(edtName.textToString(), edtUsername.textToString())
                }
            }
        }
    }

    private fun setUsername(name: String, username: String) {
        val words = name.trim().split("\\s+".toRegex()).toTypedArray()
        viewModel.updatePreference(UpdateProfileRequest(UpdateProfileRequest.Payload(username = username, fcm_token = pref.fcmToken)) ).observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> binding.tvInfo.setTextState(
                    it.message ?: getString(R.string.something_went_wrong, getString(R.string.try_again)), TextState.ERROR
                )
                Status.LOADING -> {}
                Status.SUCCESS -> {
                    viewModel.updateProfile(words.first(), if (words.size > 1) words.last() else "")
                        .observe(viewLifecycleOwner) { response ->
                            when (response.status) {
                                Status.ERROR -> binding.tvInfo.setTextState(
                                    it.message ?: getString(R.string.something_went_wrong, getString(R.string.try_again)), TextState.ERROR
                                )
                                Status.LOADING -> {}
                                Status.SUCCESS -> {
                                    findNavController().navigate(R.id.action_registrationFragment_to_onboardFragment)
                                }
                            }
                        }
                }
            }
        }
    }

    private fun setPasswordState() {
        with(binding) {
            tvInfo.setTextState(getString(R.string.password_creation))
            tvNoCode.text = getString(R.string.password_must)
            btnNext.text = getString(R.string.next)
            pinField.hide()
            llPass.show()
            llPassConfirm.show()
            edtName.hide()
            edtUsername.hide()
            btnNext.setOnClickWithDebounce {
                if (edtPass.checkIsEmpty() || edtPassConfirm.checkIsEmpty()) {
                    tvInfo.setTextState(getString(R.string.password_empty), TextState.ERROR)
                } else {
                    if ((edtPass.text?.length ?: 0) < 8) {
                        tvInfo.setTextState(getString(R.string.password_must), TextState.ERROR)
                    } else {
                        if (edtPass.textToString() != edtPassConfirm.textToString()) {
                            tvInfo.setTextState(
                                getString(R.string.invalid_password),
                                TextState.ERROR
                            )
                        } else {
                            registerUser()
                        }
                    }
                }
            }
        }
    }

    private fun registerUser() {

        viewModel.register(email, password, referralCode)
            .observe(viewLifecycleOwner) {
                when (it.status) {
                    Status.ERROR -> binding.tvInfo.setTextState(
                        it.message ?: getString(R.string.something_went_wrong, getString(R.string.try_again)), TextState.ERROR
                    )
                    Status.LOADING -> {}
                    Status.SUCCESS -> {
                        pref.saveUser(
                            it.data?.user_id,
                            it.data?.token,
                            it.data?.role,
                            email,
                            it?.data?.refresh_token, true
                        )
                        setScreenState(RegisterState.USERNAME)
                    }
                }
            }
    }

    private fun setVerificationState() {
        with(binding) {
            viewTerms.cvTerms.hide()
            edtEmail.hide()
            pinField.show()
            tvNoCode.show()
            llPass.hide()
            llPassConfirm.hide()
            tvInfo.setTextState(getString(R.string.email_confirm_code))
            btnNext.text = getString(R.string.resend_code)
            btnNext.setOnClickWithDebounce {
                resendCode()
            }
            pinField.onTextCompleteListener =
                object : PinField.OnTextCompleteListener {
                    override fun onTextComplete(enteredText: String): Boolean {
                        verifyCode(enteredText)
                        return true
                    }
                }
        }
    }

    private fun showPrivacyPolicy() {
        val uriUrl: Uri = Uri.parse(Constants.PRIVACY_URL)
        val launchBrowser = Intent(Intent.ACTION_VIEW, uriUrl)
        startActivity(launchBrowser)
    }

    private fun setEmailState() {
        with(binding) {
            btnNext.text = getString(R.string.next)
            viewTerms.cvTerms.show()
            edtEmail.show()
            pinField.hide()
            tvNoCode.hide()
            tvInfo.setTextState(getString(R.string.your_email))
            viewTerms.textView6.setOnClickWithDebounce {
                showPrivacyPolicy()
            }
            btnNext.setOnClickWithDebounce {
                if (!viewTerms.cbTerms.isChecked) {
                    tvInfo.setTextState(
                        getString(R.string.agree_to_terms),
                        TextState.ERROR
                    )
                } else {
                    if (edtEmail.isEmailValid()) {
                        sendVerificationCode(email = email)
                    } else tvInfo.setTextState(
                        getString(R.string.invalid_email),
                        TextState.ERROR
                    )
                }
            }
        }
    }

    private fun resendCode() {
        binding.btnNext.text = getString(R.string.resend_code_again)
        binding.tvInfo.setTextState(getString(R.string.new_code_sent), TextState.SUCCESS)
    }

    private fun sendVerificationCode(email: String) {
        viewModel.verifyEmail(email).observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> binding.tvInfo.setTextState(
                    it.message ?: getString(R.string.something_went_wrong, getString(R.string.try_again)), TextState.ERROR
                )
                Status.LOADING -> {}
                Status.SUCCESS -> {
                    setScreenState(RegisterState.VERIFY)
                }
            }
        }
    }

    private fun verifyCode(code: String) {
        viewModel.verifyEmailCode(email, code.toInt())
            .observe(viewLifecycleOwner) {
                when (it.status) {
                    Status.ERROR -> {
                        setScreenState(RegisterState.VERIFY)
                        binding.tvInfo.setTextState(
                            it.message ?: getString(R.string.something_went_wrong, getString(R.string.try_again)),
                            TextState.ERROR
                        )
                    }
                    Status.LOADING -> {}
                    Status.SUCCESS -> {
                        setScreenState(RegisterState.PASSWORD)
                    }
                }
            }
    }

}
