<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingHorizontal="@dimen/_20sdp"
    android:paddingVertical="@dimen/_10sdp"
    tools:context=".ui.fragments.follow.FollowersFragment">

    <EditText
        android:id="@+id/edtSearch"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:autofillHints="false"
        android:background="@drawable/bg_edit"
        android:drawableStart="@drawable/search_small"
        android:drawablePadding="@dimen/_10sdp"
        android:hint="@string/search"
        android:inputType="text"
        android:maxLines="1"
        android:padding="@dimen/_8sdp"
        android:textColor="@color/gray"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rcvFollowers"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:paddingTop="@dimen/_10sdp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/edtSearch" />

    <LinearLayout
        android:id="@+id/llNoItems"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        android:paddingVertical="@dimen/_20sdp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.5">

        <com.google.android.material.imageview.ShapeableImageView
            android:layout_width="@dimen/_80sdp"
            android:layout_height="@dimen/_80sdp"
            app:contentPadding="@dimen/_10sdp"
            app:shapeAppearance="@style/ShapeAppearance.Material3.Corner.Full"
            app:srcCompat="@drawable/search_btn" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_5sdp"
            android:gravity="center"
            android:text="@string/found_nothing_by_your_search" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>