package com.flashbid.luv.repositories

import androidx.lifecycle.LiveData
import androidx.lifecycle.liveData
import com.flashbid.luv.models.remote.FollowerResponse
import com.flashbid.luv.models.remote.MessageResponse
import com.flashbid.luv.models.remote.UserLocationRequest
import com.flashbid.luv.network.RemoteDataSource
import com.flashbid.luv.network.Resource
import kotlinx.coroutines.Dispatchers

class LocationRepository(private val remoteDataSource: RemoteDataSource) {

    fun confirmUserLocation(request: UserLocationRequest): LiveData<Resource<MessageResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.confirmUserLocation(request)
            emit(response)
        }

    fun nearbyUser(
    ): LiveData<Resource<FollowerResponse>> = liveData(Dispatchers.IO) {
        emit(Resource.loading(null))
        val response = remoteDataSource.nearbyUser(
        )
        emit(response)
    }

}