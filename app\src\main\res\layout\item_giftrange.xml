<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="2dp"
    app:cardBackgroundColor="@android:color/transparent"
    app:cardCornerRadius="@dimen/_16sdp"
    app:cardElevation="0dp">

    <LinearLayout
        android:id="@+id/llBackground"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_80sdp"
        android:background="@drawable/ic_bg_top"
        android:gravity="center"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/ivHeart"
            android:layout_width="@dimen/_20sdp"
            android:layout_height="@dimen/_20sdp"
            android:src="@drawable/heart_colored"
            tools:ignore="ContentDescription" />

        <TextView
            android:id="@+id/tvRange"
            style="@style/h2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_5sdp"
            android:textColor="@color/black"
            android:textSize="24sp"
            tools:text="1-10" />

    </LinearLayout>

</androidx.cardview.widget.CardView>