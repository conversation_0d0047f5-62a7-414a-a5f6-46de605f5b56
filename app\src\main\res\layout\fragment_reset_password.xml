<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="@dimen/_20sdp"
    tools:context=".ui.fragments.auth.reset.ResetPasswordFragment">

    <ImageView
        android:id="@+id/imageView"
        android:layout_width="@dimen/_30sdp"
        android:layout_height="@dimen/_30sdp"
        android:src="@drawable/ic_back_btn"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/textView"
        style="@style/h1"
        android:transitionName="@string/reset_transition"
        android:gravity="center"
        android:text="@string/password_nreset"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/imageView" />

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/linearLayoutCompat"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvInfo">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/edtEmail"
            style="@style/subtitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_edit"
            android:drawableStart="@drawable/sms"
            android:maxLines="1"
            android:drawablePadding="@dimen/_10sdp"
            android:hint="@string/email"
            android:padding="@dimen/_10sdp" />

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/llPass"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_20sdp"
            app:hintEnabled="false"
            android:visibility="gone"
            app:passwordToggleEnabled="true">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/edtPass"
                style="@style/subtitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_edit"
                android:maxLines="1"
                android:drawableStart="@drawable/ic_lock_small"
                android:drawablePadding="@dimen/_10sdp"
                android:inputType="textPassword"
                android:hint="@string/password"
                android:padding="@dimen/_10sdp" />

        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/llPassConfirm"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_20sdp"
            app:hintEnabled="false"
            android:visibility="gone"
            app:passwordToggleEnabled="true">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/edtPassConfirm"
                style="@style/subtitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:maxLines="1"
                android:layout_marginTop="@dimen/_20sdp"
                android:background="@drawable/bg_edit"
                android:drawableStart="@drawable/ic_lock_small"
                android:drawablePadding="@dimen/_10sdp"
                android:inputType="textPassword"
                android:hint="@string/confirm_password"
                android:padding="@dimen/_10sdp" />

        </com.google.android.material.textfield.TextInputLayout>

    </androidx.appcompat.widget.LinearLayoutCompat>

    <TextView
        android:id="@+id/tvInfo"
        style="@style/text"
        android:layout_width="match_parent"
        android:layout_marginTop="@dimen/_10sdp"
        android:gravity="center"
        android:padding="@dimen/_5sdp"
        android:text="@string/write_your_email"
        android:textColor="@color/gray"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textView" />

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btnNext"
        style="@style/button"
        android:text="@string/next"
        android:textColor="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent" />

    <com.poovam.pinedittextfield.SquarePinField
        android:id="@+id/pinField"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_48sdp"
        android:layout_margin="@dimen/_20sdp"
        android:inputType="number"
        android:visibility="gone"
        app:cornerRadius="16dp"
        app:fieldBgColor="#268181A4"
        app:fieldColor="#268181A4"
        app:highlightColor="@color/gray"
        app:highlightEnabled="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/linearLayoutCompat"
        app:noOfFields="6" />

    <TextView
        android:id="@+id/tvNoCode"
        style="@style/text"
        android:layout_width="wrap_content"
        android:layout_margin="@dimen/_20sdp"
        android:gravity="center"
        android:text="@string/password_must"
        android:textColor="@color/gray"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/linearLayoutCompat" />


</androidx.constraintlayout.widget.ConstraintLayout>