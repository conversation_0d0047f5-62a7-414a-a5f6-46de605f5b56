<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    android:background="@drawable/bg_round_corners"
    android:orientation="vertical"
    android:paddingTop="@dimen/_10sdp"
    app:layout_behavior="@string/bottom_sheet_behavior">

    <View
        android:id="@+id/view2"
        android:layout_width="@dimen/_40sdp"
        android:layout_height="4dp"
        android:layout_gravity="center"
        android:background="@drawable/bg_edit" />

    <ImageView
        android:id="@+id/imageView6"
        android:layout_width="@dimen/_25sdp"
        android:layout_height="@dimen/_25sdp"
        android:layout_gravity="end"
        android:layout_marginEnd="@dimen/_15sdp"
        android:layout_marginTop="5dp"
        android:src="@drawable/ic_close_gray" />

    <TextView
        android:id="@+id/textView36"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center|top"
        android:layout_marginTop="@dimen/_12sdp"
        android:text="@string/top_10_gift_senders"
        android:textSize="@dimen/_20sdp" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rcvGifters"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_10sdp"
        tools:listitem="@layout/item_top_gifter" />

</LinearLayout>
