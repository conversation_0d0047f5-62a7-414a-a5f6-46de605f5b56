package com.flashbid.luv.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.flashbid.luv.R
import com.flashbid.luv.databinding.ItemCustomBrandBinding
import com.flashbid.luv.extensions.getTimeInAgo
import com.flashbid.luv.extensions.hide
import com.flashbid.luv.util.loadImageFromUrl
import com.flashbid.luv.extensions.setOnClickWithDebounce
import com.flashbid.luv.extensions.show
import com.flashbid.luv.models.remote.CustomBrandResponse

class CustomBrandAdapter(
    private val list: List<CustomBrandResponse.CustomBrandDetails>,
    val onClick: (CustomBrandResponse.CustomBrandDetails) -> Unit,
) : RecyclerView.Adapter<CustomBrandAdapter.ViewHolder>() {

    private var filteredList = list

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding =
            ItemCustomBrandBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = filteredList[position]

        with(holder.binding) {

            textView12.text = item.username
            tvUsername.text = root.context.getString(
                R.string.luv_inside_package)
            imageView4.loadImageFromUrl(item.photo)
        }

        holder.itemView.setOnClickWithDebounce {
            onClick(item)
        }


    }

    fun filter(query: String) {
        filteredList = if (query.isEmpty()) {
            list
        } else {
            val lowerCaseQuery = query.lowercase()
            list.filter {
                val fullName = "${it.username ?: ""}".lowercase()
                fullName.contains(lowerCaseQuery)
            }
        }
        refresh()
    }

    override fun getItemCount(): Int = filteredList.size

    inner class ViewHolder(val binding: ItemCustomBrandBinding) :
        RecyclerView.ViewHolder(binding.root)

    fun refresh() = notifyDataSetChanged()


}