package com.flashbid.luv.ui.fragments.operations.sendLuv

import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.flashbid.luv.R
import com.flashbid.luv.adapter.FollowsAdapter
import com.flashbid.luv.databinding.FragmentFollowingsBinding
import com.flashbid.luv.databinding.FragmentNearbyUsersBinding
import com.flashbid.luv.databinding.FragmentSearchReceiverBinding
import com.flashbid.luv.extensions.setVerticalLayout
import com.flashbid.luv.extensions.snackBar
import com.flashbid.luv.extensions.viewBinding
import com.flashbid.luv.models.remote.UserDetails
import com.flashbid.luv.network.Status
import com.flashbid.luv.ui.fragments.follow.FollowersFragment
import com.flashbid.luv.viewmodels.LocationViewModel
import com.flashbid.luv.viewmodels.UserViewModel
import org.koin.androidx.viewmodel.ext.android.viewModel

class NearbyUsersFragment : Fragment(R.layout.fragment_nearby_users) {

    private val binding by viewBinding(FragmentNearbyUsersBinding::bind)
    private lateinit var onSelectCallback: (UserDetails) -> Unit
    private val list: ArrayList<UserDetails> = ArrayList()
    private val followAdapter by lazy { FollowsAdapter(list, false, onSelectCallback) }
    private val viewModel: LocationViewModel by viewModel()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.rcvNearby.apply {
            setVerticalLayout()
            adapter = followAdapter
        }

        getUsers()
    }

    private fun getUsers() {
        viewModel.nearbyUser().observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(it.message ?: getString(R.string.something_went_wrong, getString(R.string.try_again)))
                Status.LOADING -> {}
                Status.SUCCESS -> {
                    list.clear()
                    list.addAll(it.data?.list ?: ArrayList())
                    followAdapter.refresh()
                }
            }
        }
    }

    companion object {
        fun newInstance(onSelectCallback: (UserDetails) -> Unit): NearbyUsersFragment {
            val fragment = NearbyUsersFragment()
            fragment.onSelectCallback = onSelectCallback
            return fragment
        }
    }

}