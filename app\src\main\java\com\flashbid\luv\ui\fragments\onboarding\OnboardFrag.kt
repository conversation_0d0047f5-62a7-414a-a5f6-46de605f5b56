package com.flashbid.luv.ui.fragments.onboarding

import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.viewpager2.widget.ViewPager2
import com.flashbid.luv.R
import com.flashbid.luv.adapter.FragmentPagerAdapter
import com.flashbid.luv.databinding.FragmentOnboardBinding
import com.flashbid.luv.util.Edge
import com.flashbid.luv.util.padding
import com.flashbid.luv.extensions.setOnClickWithDebounce
import com.flashbid.luv.extensions.viewBinding

class OnboardFrag : Fragment(R.layout.fragment_onboard) {

    private val binding by viewBinding(FragmentOnboardBinding::bind)
    private lateinit var onBoardAdapter: FragmentPagerAdapter

    private var myPageChangeCallback = object : ViewPager2.OnPageChangeCallback() {
        override fun onPageSelected(position: Int) {

            val itemIndex = binding.viewPager.currentItem
            if (itemIndex == 3) {
                binding.nextButton.text = getString(R.string.on_board_start)
                binding.nextButton.padding(
                    setOf(Edge.RIGHT, Edge.LEFT),
                    resources.getDimensionPixelOffset(com.intuit.sdp.R.dimen._60sdp)
                )
            } else {
                binding.nextButton.text = getString(R.string.on_board_Next)
                binding.nextButton.padding(
                    setOf(Edge.RIGHT, Edge.LEFT),
                    resources.getDimensionPixelOffset(com.intuit.sdp.R.dimen._30sdp)
                )
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        val list: List<Fragment> =
            ArrayList(
                listOf(
                    OnboardFirstPageFrag(),
                    OnboardSecondPageFrag(),
                    OnboardThirdPageFrag(),
                    OnboardFourthPageFrag()
                )
            )
        onBoardAdapter = FragmentPagerAdapter(childFragmentManager, lifecycle, list)

        binding.viewPager.isUserInputEnabled = true
        binding.viewPager.adapter = onBoardAdapter

        binding.nextButton.setOnClickWithDebounce {
            var itemIndex = binding.viewPager.currentItem
            if (itemIndex != 3) {
                itemIndex += 1
                binding.viewPager.currentItem = itemIndex
                binding.nextButton.text = getString(R.string.on_board_Next)

                binding.nextButton.padding(
                    setOf(Edge.RIGHT, Edge.LEFT),
                    resources.getDimensionPixelOffset(com.intuit.sdp.R.dimen._30sdp)
                )
                if (itemIndex == 3) {
                    binding.nextButton.text = getString(R.string.on_board_start)
                    binding.nextButton.padding(
                        setOf(Edge.RIGHT, Edge.LEFT),
                        resources.getDimensionPixelOffset(com.intuit.sdp.R.dimen._60sdp)
                    )
                }
            } else findNavController().navigate(R.id.chooseFavFragment)

        }

        binding.skipButton.setOnClickWithDebounce {
            findNavController().navigate(R.id.chooseFavFragment)
        }

        binding.viewPager.registerOnPageChangeCallback(myPageChangeCallback)
    }
}