package com.flashbid.luv.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.flashbid.luv.databinding.ItemFavBinding
import com.flashbid.luv.util.loadImageFromUrl
import com.flashbid.luv.extensions.setOnClickWithDebounce
import com.flashbid.luv.models.remote.Favourite

class FavoriteAdapter(
    private val list: MutableList<Favourite>,
    val onClick: (Favourite) -> Unit,
) : RecyclerView.Adapter<FavoriteAdapter.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding =
            ItemFavBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = list[position]

        with(holder.binding) {
            tvBrandName.text = item.company_name ?: ""
            tvLuv.text = item.total_hearts_sent ?: "0"
            ivImage.loadImageFromUrl(item.company_image)
        }

        holder.itemView.setOnClickWithDebounce {
            onClick(item)
        }

    }

    override fun getItemCount(): Int = if (list.size < 10) list.size else 10

    inner class ViewHolder(val binding: ItemFavBinding) :
        RecyclerView.ViewHolder(binding.root)

    fun refresh() = notifyDataSetChanged()


}