package com.flashbid.luv.adapter

import android.location.Geocoder
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.flashbid.luv.databinding.ItemCustomBrandBinding
import com.flashbid.luv.databinding.ItemSearchBinding
import com.flashbid.luv.extensions.hide
import com.flashbid.luv.util.loadImageFromUrl
import com.flashbid.luv.extensions.setOnClickWithDebounce
import com.flashbid.luv.extensions.show
import com.flashbid.luv.models.remote.CustomBrandResponse
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.Locale

class CustomBeaconBrandAdapter(
    private val list: List<CustomBrandResponse.CustomBrandDetails>,
    val onClick: (CustomBrandResponse.CustomBrandDetails) -> Unit,
) : RecyclerView.Adapter<CustomBeaconBrandAdapter.ViewHolder>() {

    private var filteredList = list

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding =
            ItemCustomBrandBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = filteredList[position]

        with(holder.binding) {
            textView12.text = item.first_name + " " + item.last_name
            tvUsername.text = "@" + item.username
            if (item.latitude != null && item.longitude != null) {
                loadLocation(holder, item.latitude, item.longitude)
            } else {
                tvUsername.text = "Location unavailable"
            }

            imageView4.loadImageFromUrl(item.photo)
        }

        holder.itemView.setOnClickWithDebounce {
            onClick(item)
        }


    }

    private fun loadLocation(holder: ViewHolder, lat: Double, lng: Double) {
        val context = holder.binding.root.context

        // Use Coroutines for background processing
        kotlinx.coroutines.GlobalScope.launch(Dispatchers.IO)  {
            try {
                val geocoder = Geocoder(context, Locale.getDefault())
                val addresses = geocoder.getFromLocation(lat, lng, 1)
                val locationName = addresses?.firstOrNull()?.getAddressLine(0) ?: "Unknown location"

                // Update UI on the main thread
                withContext(Dispatchers.Main) {
                    holder.binding.tvUsername.text = locationName
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {

                    holder.binding.tvUsername.text = "Error loading location"
                }
            }
        }
    }


    fun filter(query: String) {
        filteredList = if (query.isEmpty()) {
            list
        } else {
            val lowerCaseQuery = query.lowercase()
            list.filter {
                val fullName = "${it.first_name ?: ""} ${it.last_name ?: ""}".lowercase()
                fullName.contains(lowerCaseQuery)
            }
        }
        refresh()
    }

    override fun getItemCount(): Int = filteredList.size

    inner class ViewHolder(val binding: ItemCustomBrandBinding) :
        RecyclerView.ViewHolder(binding.root)

    fun refresh() = notifyDataSetChanged()


}