<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_nav"
    app:startDestination="@id/homeFragment">

    <fragment
        android:id="@+id/onboardFragment"
        android:name="com.flashbid.luv.ui.fragments.onboarding.OnboardFrag"
        android:label="fragment_onboard"
        tools:layout="@layout/fragment_onboard" />
    <fragment
        android:id="@+id/loginFragment"
        android:name="com.flashbid.luv.ui.fragments.auth.login.LoginFragment"
        android:label="fragment_login"
        tools:layout="@layout/fragment_login" >
        <action
            android:id="@+id/action_loginFragment_to_resetPasswordFragment"
            app:destination="@id/resetPasswordFragment" />
        <action
            android:id="@+id/action_loginFragment_to_onboardFragment"
            app:destination="@id/onboardFragment" />
        <action
            android:id="@+id/action_loginFragment_to_registrationFragment"
            app:destination="@id/registrationFragment" />
        <action
            android:id="@+id/action_loginFragment_to_homeFragment"
            app:destination="@id/homeFragment" />
    </fragment>

    <fragment
        android:id="@+id/registrationFragment"
        android:name="com.flashbid.luv.ui.fragments.auth.register.RegistrationFragment"
        android:label="fragment_registration"
        tools:layout="@layout/fragment_registration" >
        <action
            android:id="@+id/action_registrationFragment_to_onboardFragment"
            app:destination="@id/onboardFragment" />
    </fragment>
    <fragment
        android:id="@+id/resetPasswordFragment"
        android:name="com.flashbid.luv.ui.fragments.auth.reset.ResetPasswordFragment"
        android:label="fragment_reset_password"
        tools:layout="@layout/fragment_reset_password" />

    <fragment
        android:id="@+id/splashFragment"
        android:name="com.flashbid.luv.ui.fragments.onboarding.SplashFragment"
        android:label="fragment_splash"
        tools:layout="@layout/fragment_splash" >
        <action
            android:id="@+id/action_splashFragment_to_authSelectFragment"
            app:destination="@id/authSelectFragment" />
    </fragment>

    <fragment
        android:id="@+id/authSelectFragment"
        android:name="com.flashbid.luv.ui.fragments.auth.AuthSelectFragment"
        android:label="fragment_auth_select"
        tools:layout="@layout/fragment_auth_select" >
        <action
            android:id="@+id/action_authSelectFragment_to_registrationFragment"
            app:destination="@id/registrationFragment" />
        <action
            android:id="@+id/action_authSelectFragment_to_loginFragment"
            app:destination="@id/loginFragment" />
        <action
            android:id="@+id/action_authSelectFragment_to_onboardFragment"
            app:destination="@id/onboardFragment" />
        <action
            android:id="@+id/action_authSelectFragment_to_homeFragment"
            app:destination="@id/homeFragment" />
    </fragment>

    <fragment
        android:id="@+id/giftReceivedDetailsFragment"
        android:name="com.flashbid.luv.ui.fragments.transactions.GiftReceivedDetailsFragment"
        android:label="GiftReceivedDetailsFragment" >
        <argument
            android:name="id"
            app:argType="string" />
    </fragment>

    <fragment
        android:id="@+id/luvDropReceivedDetailsFragment"
        android:name="com.flashbid.luv.ui.fragments.transactions.LuvReceivedFragment"
        android:label="LUvDropReceivedDetailsFragment" >
        <argument
            android:name="id"
            app:argType="string" />
    </fragment>



    <fragment
        android:id="@+id/giftSentDetailsFragment"
        android:name="com.flashbid.luv.ui.fragments.transactions.GiftSentDetailsFragment"
        android:label="GiftSentDetailsFragment" >
        <argument
            android:name="id"
            app:argType="string" />
    </fragment>

    <fragment
        android:id="@+id/onboardFirstPageFrag"
        android:name="com.flashbid.luv.ui.fragments.onboarding.OnboardFirstPageFrag"
        android:label="OnboardFirstPageFrag" />

    <fragment
        android:id="@+id/onboardFourthPageFrag"
        android:name="com.flashbid.luv.ui.fragments.onboarding.OnboardFourthPageFrag"
        android:label="OnboardFourthPageFrag" />

    <fragment
        android:id="@+id/onboardSecondPageFrag"
        android:name="com.flashbid.luv.ui.fragments.onboarding.OnboardSecondPageFrag"
        android:label="OnboardSecondPageFrag" />

    <fragment
        android:id="@+id/onboardThirdPageFrag"
        android:name="com.flashbid.luv.ui.fragments.onboarding.OnboardThirdPageFrag"
        android:label="OnboardThirdPageFrag" />

    <fragment
        android:id="@+id/rechargeDetailsFragment"
        android:name="com.flashbid.luv.ui.fragments.transactions.RechargeDetailsFragment"
        android:label="RechargeDetailsFragment" >
        <argument
            android:name="id"
            app:argType="string" />
    </fragment>

    <fragment
        android:id="@+id/alertFragment"
        android:name="com.flashbid.luv.ui.fragments.alerts.AlertsFragment"
        android:label="fragment_alerts"
        tools:layout="@layout/fragment_alerts" >
        <action
            android:id="@+id/action_alertFragment_to_myProfileFragment"
            app:destination="@id/myProfileFragment" />
        <action
            android:id="@+id/action_alertFragment_to_preRecordingFragment"
            app:destination="@id/preRecordingFragment" />
        <action
            android:id="@+id/action_AlertsFragment_to_videoPlayerWithDetailsFragment"
            app:destination="@id/videoPlayerWithDetailsFragment" />
    </fragment>

    <fragment
        android:id="@+id/createQrGiftingFragment"
        android:name="com.flashbid.luv.ui.fragments.qr.CreateQrGiftingFragment"
        android:label="fragment_create_qr_gifting"
        tools:layout="@layout/fragment_create_qr_gifting" >
        <argument
            android:name="codeDetail"
            app:argType="com.flashbid.luv.models.remote.CodeDetail"
            app:nullable="true"
            android:defaultValue="@null" />
    </fragment>

    <fragment
        android:id="@+id/withdrawDetailsFragment"
        android:name="com.flashbid.luv.ui.fragments.transactions.WithdrawDetailsFragment"
        android:label="fragment_gift_received_details"
        tools:layout="@layout/fragment_gift_received_details" >
        <argument
            android:name="id"
            app:argType="string" />
    </fragment>

    <fragment
        android:id="@+id/homeFragment"
        android:name="com.flashbid.luv.ui.fragments.home.HomeFragment"
        android:label="fragment_home"
        tools:layout="@layout/fragment_home" >
        <action
            android:id="@+id/action_homeFragment_to_createQrGiftingFragment"
            app:destination="@id/createQrGiftingFragment" />
        <action
            android:id="@+id/action_homeFragment_to_qrGiftingFragment"
            app:destination="@id/qrGiftingFragment" />
        <action
            android:id="@+id/action_homeFragment_to_qrReceivingFragment"
            app:destination="@id/qrReceivingFragment" />
        <action
            android:id="@+id/action_homeFragment_to_sendLuvFragment"
            app:destination="@id/sendLuvFragment" />
        <action
            android:id="@+id/action_homeFragment_to_myProfileFragment"
            app:destination="@id/myProfileFragment" />
        <action
            android:id="@+id/action_homeFragment_to_userFollowersFragment"
            app:destination="@id/userFollowersFragment" />
        <action
            android:id="@+id/action_homeFragment_to_userFollowingsFragment"
            app:destination="@id/userFollowingsFragment" />
        <action
            android:id="@+id/action_homeFragment_to_rechargeFragment"
            app:destination="@id/rechargeFragment" />
        <action
            android:id="@+id/action_homeFragment_to_withdrawPaymentSelectFragment"
            app:destination="@id/withdrawPaymentSelectFragment" />
        <action
            android:id="@+id/action_homeFragment_to_userProfileFragment"
            app:destination="@id/userProfileFragment" />
        <action
            android:id="@+id/action_homeFragment_to_topLuversFragment"
            app:destination="@id/topLuversFragment" />
        <action
            android:id="@+id/action_homeFragment_to_rechargeDetailsFragment"
            app:destination="@id/rechargeDetailsFragment" />
        <action
            android:id="@+id/action_homeFragment_to_withdrawDetailsFragment"
            app:destination="@id/withdrawDetailsFragment" />
        <action
            android:id="@+id/action_homeFragment_to_giftReceivedDetailsFragment"
            app:destination="@id/giftReceivedDetailsFragment" />
        <action
            android:id="@+id/action_homeFragment_to_luvDropReceivedDetailsFragment"
            app:destination="@id/luvDropReceivedDetailsFragment" />

        <action
            android:id="@+id/action_homeFragment_to_giftSentDetailsFragment"
            app:destination="@id/giftSentDetailsFragment" />
        <action
            android:id="@+id/action_homeFragment_to_referralAwardFragment"
            app:destination="@id/referralAwardFragment" />
        <action
            android:id="@+id/action_homeFragment_to_openedLuvCrateFragment"
            app:destination="@id/openedLuvCrateFragment" />
        <action
            android:id="@+id/action_homeFragment_to_questCrateFragment"
            app:destination="@id/questCrateFragment" />
        <action
            android:id="@+id/action_homeFragment_to_myChestQrFragment"
            app:destination="@id/myChestQrFragment" />
        <action
            android:id="@+id/action_homeFragment_to_openedLuvChestFragment"
            app:destination="@id/openedLuvChestFragment" />
        <action
            android:id="@+id/action_homeFragment_to_sendLuvGetLuvFragment"
            app:destination="@id/sendLuvGetLuvFragment" />
        <action
            android:id="@+id/action_homeFragment_to_luvDropFragment"
            app:destination="@id/luvDropFragment" />
        <action
            android:id="@+id/action_homeFragment_to_myProfileFragment2"
            app:destination="@id/myProfileFragment" />
        <action
            android:id="@+id/action_homeFragment_to_userStoryFragment"
            app:destination="@id/userStoryFragment" />
    </fragment>

    <fragment
        android:id="@+id/scanFragment"
        android:name="com.flashbid.luv.ui.fragments.qr.QRScanningFragment"
        android:label="fragment_q_r_scanning"
        tools:layout="@layout/fragment_q_r_scanning" >
        <action
            android:id="@+id/action_scanFragment_to_myProfileFragment"
            app:destination="@id/myProfileFragment" />
        <action
            android:id="@+id/action_scanFragment_to_userProfileFragment"
            app:destination="@id/userProfileFragment" />
        <action
            android:id="@+id/action_scanFragment_to_authorizeFragment"
            app:destination="@id/authorizeFragment" />

    </fragment>

    <fragment
        android:id="@+id/qrGiftingFragment"
        android:name="com.flashbid.luv.ui.fragments.qr.QrGiftingFragment"
        android:label="fragment_qr_gifting"
        tools:layout="@layout/fragment_qr_gifting" >
        <action
            android:id="@+id/action_qrGiftingFragment_to_createQrGiftingFragment"
            app:destination="@id/createQrGiftingFragment" />
        <argument
            android:name="qr"
            app:argType="string" />
    </fragment>

    <fragment
        android:id="@+id/qrReceivingFragment"
        android:name="com.flashbid.luv.ui.fragments.qr.QrReceivingFragment"
        android:label="fragment_qr_receiving"
        tools:layout="@layout/fragment_qr_receiving" >
        <argument
            android:name="qr"
            app:argType="string" />
    </fragment>

    <fragment
        android:id="@+id/questFragment"
        android:name="com.flashbid.luv.ui.fragments.quest.QuestFragment"
        android:label="fragment_quest"
        tools:layout="@layout/fragment_quest" >
        <action
            android:id="@+id/action_questFragment_to_myProfileFragment"
            app:destination="@id/myProfileFragment" />
        <action
            android:id="@+id/action_questFragment_to_sendLuvFragment"
            app:destination="@id/sendLuvFragment" />
    </fragment>

    <fragment
        android:id="@+id/searchReceiverFragment"
        android:name="com.flashbid.luv.ui.fragments.operations.sendLuv.SearchReceiverFragment"
        android:label="fragment_search_receiver"
        tools:layout="@layout/fragment_search_receiver" >
        <action
            android:id="@+id/action_searchReceiverFragment_to_selectReceiverFragment"
            app:destination="@id/selectReceiverFragment" />
        <argument
            android:name="gift"
            app:argType="com.flashbid.luv.models.remote.ItemGift" />
        <argument
            android:name="openNearby"
            app:argType="boolean"
            android:defaultValue="false" />
    </fragment>

    <fragment
        android:id="@+id/selectReceiverFragment"
        android:name="com.flashbid.luv.ui.fragments.operations.sendLuv.SelectReceiverFragment"
        android:label="fragment_select_receiver"
        tools:layout="@layout/fragment_select_receiver" >
        <action
            android:id="@+id/action_selectReceiverFragment_to_searchReceiverFragment"
            app:destination="@id/searchReceiverFragment" />
        <action
            android:id="@+id/action_selectReceiverFragment_to_sendLuvQrScanFragment"
            app:destination="@id/sendLuvQrScanFragment" />
        <argument
            android:name="itemGift"
            app:argType="com.flashbid.luv.models.remote.ItemGift" />
        <argument
            android:name="receiver"
            app:argType="com.flashbid.luv.models.remote.UserDetails"
            app:nullable="true" />
    </fragment>

    <fragment
        android:id="@+id/sendLuvFragment"
        android:name="com.flashbid.luv.ui.fragments.operations.sendLuv.SendLuvFragment"
        android:label="fragment_send_luv"
        tools:layout="@layout/fragment_send_luv" >
        <action
            android:id="@+id/action_sendLuvFragment_to_selectReceiverFragment"
            app:destination="@id/selectReceiverFragment" />
        <argument
            android:name="receiver"
            app:argType="com.flashbid.luv.models.remote.UserDetails"
            app:nullable="true"
            android:defaultValue="@null" />
    </fragment>

    <fragment
        android:id="@+id/sendLuvQrScanFragment"
        android:name="com.flashbid.luv.ui.fragments.operations.sendLuv.SendLuvQrScanFragment"
        android:label="fragment_send_luv_qr_scan"
        tools:layout="@layout/fragment_send_luv_qr_scan" >
        <argument
            android:name="gift"
            app:argType="com.flashbid.luv.models.remote.ItemGift" />
        <action
            android:id="@+id/action_sendLuvQrScanFragment_to_selectReceiverFragment"
            app:destination="@id/selectReceiverFragment" />
    </fragment>

    <fragment
        android:id="@+id/userFollowersFragment"
        android:name="com.flashbid.luv.ui.fragments.profile.UserFollowersFragment"
        android:label="fragment_user_followers"
        tools:layout="@layout/fragment_user_followers" >
        <action
            android:id="@+id/action_userFollowersFragment_to_userProfileFragment"
            app:destination="@id/userProfileFragment" />
    </fragment>

    <fragment
        android:id="@+id/userFollowingsFragment"
        android:name="com.flashbid.luv.ui.fragments.profile.UserFollowingsFragment"
        android:label="fragment_user_followings"
        tools:layout="@layout/fragment_user_followings" >
        <action
            android:id="@+id/action_userFollowingsFragment_to_userProfileFragment"
            app:destination="@id/userProfileFragment" />
    </fragment>

    <fragment
        android:id="@+id/userProfileFragment"
        android:name="com.flashbid.luv.ui.fragments.profile.others.UserProfileFragment"
        android:label="fragment_user_profile"
        tools:layout="@layout/fragment_user_profile" >
        <argument
            android:name="userId"
            app:argType="integer" />
        <action
            android:id="@+id/action_userProfileFragment_to_sendLuvFragment"
            app:destination="@id/sendLuvFragment" />
        <action
            android:id="@+id/action_userProfileFragment_self"
            app:destination="@id/userProfileFragment" />
        <action
            android:id="@+id/action_userProfileFragment_to_otherUserFollowingsFragment"
            app:destination="@id/otherUserFollowingsFragment" />
    </fragment>

    <fragment
        android:id="@+id/withdrawAmountSelectFragment"
        android:name="com.flashbid.luv.ui.fragments.operations.withdraw.WithdrawAmountSelectFragment"
        android:label="fragment_withdraw_amount_select"
        tools:layout="@layout/fragment_withdraw_amount_select" >
        <argument
            android:name="country"
            app:argType="string" />
        <argument
            android:name="currency"
            app:argType="string" />
        <argument
            android:name="method"
            app:argType="integer" />
        <argument
            android:name="link"
            app:argType="string" />
    </fragment>

    <fragment
        android:id="@+id/withdrawPaymentSelectFragment"
        android:name="com.flashbid.luv.ui.fragments.operations.withdraw.WithdrawPaymentSelectFragment"
        android:label="fragment_withdraw_payment_select"
        tools:layout="@layout/fragment_withdraw_payment_select" >
        <action
            android:id="@+id/action_withdrawPaymentSelectFragment_to_withdrawAmountSelectFragment"
            app:destination="@id/withdrawAmountSelectFragment" />
    </fragment>

    <fragment
        android:id="@+id/myProfileFragment"
        android:name="com.flashbid.luv.ui.fragments.profile.MyProfileFragment"
        android:label="fragment_my_profile"
        tools:layout="@layout/fragment_my_profile" >
        <action
            android:id="@+id/action_myProfileFragment_to_editProfileFragment"
            app:destination="@id/editProfileFragment" />
        <action
            android:id="@+id/action_myProfileFragment_to_followersFragment"
            app:destination="@id/followersFragment" />
        <action
            android:id="@+id/action_myProfileFragment_to_followingsFragment"
            app:destination="@id/followingsFragment" />
        <action
            android:id="@+id/action_myProfileFragment_to_resetPasswordFragment"
            app:destination="@id/resetPasswordFragment" />
        <action
            android:id="@+id/action_myProfileFragment_to_chooseFavFragment"
            app:destination="@id/chooseFavFragment" />
        <action
            android:id="@+id/action_myProfileFragment_to_userFollowersFragment"
            app:destination="@id/userFollowersFragment" />
        <action
            android:id="@+id/action_myProfileFragment_to_userFollowingsFragment"
            app:destination="@id/userFollowingsFragment" />
        <action
            android:id="@+id/action_myProfileFragment_to_topLuversFragment"
            app:destination="@id/topLuversFragment" />
        <action
            android:id="@+id/action_myProfileFragment_to_userProfileFragment"
            app:destination="@id/userProfileFragment" />
        <action
            android:id="@+id/action_myProfileFragment_to_privacyFragment"
            app:destination="@id/privacyFragment" />
        <action
            android:id="@+id/action_myProfileFragment_to_settingFragment"
            app:destination="@id/settingFragment" />
    </fragment>

    <fragment
        android:id="@+id/editProfileFragment"
        android:name="com.flashbid.luv.ui.fragments.profile.EditProfileFragment"
        android:label="fragment_edit_profile"
        tools:layout="@layout/fragment_edit_profile" />

    <fragment
        android:id="@+id/followersFragment"
        android:name="com.flashbid.luv.ui.fragments.follow.FollowersFragment"
        android:label="fragment_followers"
        tools:layout="@layout/fragment_followers" >
        <action
            android:id="@+id/action_followersFragment_to_userProfileFragment"
            app:destination="@id/userProfileFragment" />
    </fragment>

    <fragment
        android:id="@+id/followingsFragment"
        android:name="com.flashbid.luv.ui.fragments.follow.FollowingsFragment"
        android:label="fragment_followings"
        tools:layout="@layout/fragment_followings" >
        <action
            android:id="@+id/action_followingsFragment_to_userProfileFragment"
            app:destination="@id/userProfileFragment" />
    </fragment>

    <fragment
        android:id="@+id/chooseFavFragment"
        android:name="com.flashbid.luv.ui.fragments.other.ChooseFavFragment"
        android:label="fragment_choose_fav"
        tools:layout="@layout/fragment_choose_fav" >
        <action
            android:id="@+id/action_chooseFavFragment_to_allBrandsFragment"
            app:destination="@id/allBrandsFragment" />
        <action
            android:id="@+id/action_chooseFavFragment_to_userProfileFragment"
            app:destination="@id/userProfileFragment" />
    </fragment>


    <fragment
        android:id="@+id/rechargeFragment"
        android:name="com.flashbid.luv.ui.fragments.operations.recharge.RechargeFragment"
        android:label="fragment_recharge"
        tools:layout="@layout/fragment_recharge" />

    <fragment
        android:id="@+id/topLuversFragment"
        android:name="com.flashbid.luv.ui.fragments.other.TopLuversFragment"
        android:label="fragment_top_luvers"
        tools:layout="@layout/fragment_top_luvers" >
        <action
            android:id="@+id/action_topLuversFragment_to_allBrandsFragment"
            app:destination="@id/allBrandsFragment" />
        <action
            android:id="@+id/action_topLuversFragment_to_userProfileFragment"
            app:destination="@id/userProfileFragment" />
        <argument
            android:name="period"
            app:argType="string"
            app:nullable="true"
            android:defaultValue="daily" />
        <argument
            android:name="type"
            app:argType="string"
            app:nullable="true"
            android:defaultValue="regular" />
        <action
            android:id="@+id/action_topLuversFragment_to_topGifterFilterFragment"
            app:destination="@id/topGifterFilterFragment" />
    </fragment>

    <fragment
        android:id="@+id/allBrandsFragment"
        android:name="com.flashbid.luv.ui.fragments.other.AllBrandsFragment"
        android:label="AllBrandsFragment" >
        <action
            android:id="@+id/action_allBrandsFragment_to_userProfileFragment"
            app:destination="@id/userProfileFragment" />
        <argument
            android:name="name"
            app:argType="string" />
        <argument
            android:name="categoryId"
            app:argType="integer" />
        <argument
            android:name="isFromProfile"
            app:argType="boolean" />

        <argument
            android:name="favBrands"
            app:argType="com.flashbid.luv.models.remote.Brand[]"
            app:nullable="true"
            android:defaultValue="@null"/>
    </fragment>

    <fragment
        android:id="@+id/privacyFragment"
        android:name="com.flashbid.luv.ui.fragments.profile.PrivacyFragment"
        android:label="fragment_privacy"
        tools:layout="@layout/fragment_privacy" />

    <fragment
        android:id="@+id/luvDropFragment"
        android:name="com.flashbid.luv.ui.fragments.transactions.LuvDropFragment"
        android:label="fragment_luv_drop"
        tools:layout="@layout/fragment_luv_drop" >
        <action
            android:id="@+id/action_homeFragment_to_luvDropFragment"
            app:destination="@id/luvDropFragment" />
        <argument
            android:name="id"
            app:argType="string" />
    </fragment>

    <fragment
        android:id="@+id/myChestQrFragment"
        android:name="com.flashbid.luv.ui.fragments.transactions.MyChestQrFragment"
        android:label="fragment_my_chest_qr"
        tools:layout="@layout/fragment_my_chest_qr" >
        <action
            android:id="@+id/action_homeFragment_to_myChestQrFragment"
            app:destination="@id/myChestQrFragment" />
        <argument
            android:name="id"
            app:argType="string" />
    </fragment>

    <fragment
        android:id="@+id/openedLuvChestFragment"
        android:name="com.flashbid.luv.ui.fragments.transactions.OpenedLuvChestFragment"
        android:label="fragment_opened_luv_chest"
        tools:layout="@layout/fragment_opened_luv_chest">
        <action
            android:id="@+id/action_homeFragment_to_openedLuvChestFragment"
            app:destination="@id/openedLuvChestFragment" />
        <argument
            android:name="id"
            app:argType="string" />
    </fragment>

    <fragment
        android:id="@+id/questCrateFragment"
        android:name="com.flashbid.luv.ui.fragments.transactions.QuestCrateFragment"
        android:label="fragment_opened_luv_crate"
        tools:layout="@layout/fragment_opened_luv_crate">
        <action
            android:id="@+id/action_homeFragment_to_questCrateFragment"
            app:destination="@id/questCrateFragment" />
        <argument
            android:name="id"
            app:argType="string"
            />

        <argument
            android:name="senderID"
            app:argType="string" />

        <argument
            android:name="isOpenCrate"
            app:argType="string" />
    </fragment>

    <fragment
        android:id="@+id/openedLuvCrateFragment"
        android:name="com.flashbid.luv.ui.fragments.transactions.OpenedLuvCrateFragment"
        android:label="fragment_quest"
        tools:layout="@layout/fragment_quest">
        <action
            android:id="@+id/action_homeFragment_to_openedLuvCrateFragment"
            app:destination="@id/openedLuvCrateFragment" />
        <argument
            android:name="id"
            app:argType="string" />

        <argument
            android:name="senderID"
            app:argType="string" />
        <argument
            android:name="isOpenCrate"
            app:argType="string" />
    </fragment>

    <fragment
        android:id="@+id/referralAwardFragment"
        android:name="com.flashbid.luv.ui.fragments.transactions.ReferralAwardFragment"
        android:label="fragment_referral_award"
        tools:layout="@layout/fragment_referral_award" >
        <action
            android:id="@+id/action_homeFragment_to_referralAwardFragment"
            app:destination="@id/referralAwardFragment" />
        <argument
            android:name="id"
            app:argType="string" />
    </fragment>

    <fragment
        android:id="@+id/sendLuvGetLuvFragment"
        android:name="com.flashbid.luv.ui.fragments.transactions.SendLuvGetLuvFragment"
        android:label="fragment_send_luv_get_luv"
        tools:layout="@layout/fragment_send_luv_get_luv" >
        <action
            android:id="@+id/action_homeFragment_to_sendLuvGetLuvFragment"
            app:destination="@id/sendLuvGetLuvFragment" />
        <argument
            android:name="id"
            app:argType="string" />
    </fragment>

    <fragment
        android:id="@+id/authorizeFragment"
        android:name="com.flashbid.luv.ui.fragments.auth.AuthorizeFragment"
        android:label="fragment_authorize"
        tools:layout="@layout/fragment_authorize" >

        <argument
            android:name="scanValue"
            app:argType="string" />

    </fragment>

    <fragment
        android:id="@+id/otherUserFollowingsFragment"
        android:name="com.flashbid.luv.ui.fragments.profile.others.OtherUserFollowingsFragment"
        android:label="fragment_other_user_followings"
        tools:layout="@layout/fragment_other_user_followings" >
        <argument
            android:name="userId"
            app:argType="string" />
        <argument
            android:name="followers"
            app:argType="boolean" />
        <action
            android:id="@+id/action_otherUserFollowingsFragment_to_userProfileFragment"
            app:destination="@id/userProfileFragment" />
    </fragment>

    <fragment
        android:id="@+id/settingFragment"
        android:name="com.flashbid.luv.ui.fragments.profile.SettingFragment"
        android:label="fragment_setting"
        tools:layout="@layout/fragment_setting" >
        <action
            android:id="@+id/action_settingFragment_to_editProfileFragment"
            app:destination="@id/editProfileFragment" />
        <action
            android:id="@+id/action_settingFragment_to_privacyFragment"
            app:destination="@id/privacyFragment" />
        <action
            android:id="@+id/action_settingFragment_to_resetPasswordFragment"
            app:destination="@id/resetPasswordFragment" />
    </fragment>

    <fragment
        android:id="@+id/videoPlayerWithDetailsFragment"
        android:name="com.flashbid.luv.ui.fragments.video.VideoPlayerWithDetailsFragment"
        android:label="VideoPlayerWithDetailsFragment">
        <argument
            android:name="alertModel"
            app:argType="com.flashbid.luv.models.remote.AlertModel" />
    </fragment>
<!--    <fragment-->
<!--        android:id="@+id/yourCurrentFragment"-->
<!--        android:name="com.flashbid.luv.ui.fragments.alerts.AlertsFragment"-->
<!--        android:label="YourCurrentFragment">-->
<!--       -->
<!--    </fragment>-->

    <fragment
        android:id="@+id/preRecordingFragment"
        android:name="com.flashbid.luv.ui.fragments.story.PreRecordingFragment"
        android:label="fragment_pre_recording"
        tools:layout="@layout/fragment_pre_recording" >
        <argument
            android:name="receiver"
            app:argType="string" />
        <argument
            android:name="receiver_id"
            app:argType="string" />
        <argument
            android:name="trans_id"
            app:argType="string" />
    </fragment>

    <fragment
        android:id="@+id/userStoryFragment"
        android:name="com.flashbid.luv.ui.fragments.story.UserStoryFragment"
        android:label="fragment_user_story"
        tools:layout="@layout/fragment_user_story" >
        <action
            android:id="@+id/action_userStoryFragment_to_reportVideoFragment"
            app:destination="@id/reportVideoFragment" />
        <argument
            android:name="userStories"
            app:argType="com.flashbid.luv.models.remote.OtherStory[]" />
    </fragment>

    <fragment
        android:id="@+id/reportVideoFragment"
        android:name="com.flashbid.luv.ui.fragments.story.ReportVideoFragment"
        android:label="fragment_report_video"
        tools:layout="@layout/fragment_report_video">
        <action
            android:id="@+id/action_reportVideoFragment_to_homeFragment"
            app:destination="@id/reportVideoFragment" />
    </fragment>

    <fragment
        android:id="@+id/liveStreamingFragment"
        android:name="com.flashbid.luv.ui.fragments.streaming.LiveStreamingFragment"
        android:label="fragment_live_streaming"
        tools:layout="@layout/fragment_live_streaming" />

    <fragment
        android:id="@+id/nearbyUsersFragment"
        android:name="com.flashbid.luv.ui.fragments.operations.sendLuv.NearbyUsersFragment"
        android:label="fragment_nearby_users"
        tools:layout="@layout/fragment_nearby_users" />

    <fragment
        android:id="@+id/startLuvBattleFragment"
        android:name="com.flashbid.luv.ui.fragments.battle.StartLuvBattleFragment"
        android:label="fragment_start_luv_battle"
        tools:layout="@layout/fragment_start_luv_battle" >
        <action
            android:id="@+id/action_startLuvBattleFragment_to_selectOpponentFragment"
            app:destination="@id/selectOpponentFragment" />
<!--        <action-->
<!--            android:id="@+id/action_startLuvBattleFragment_to_fragmentFollowers"-->
<!--            app:destination="@id/followersFragment" />-->
<!--        <action-->
<!--            android:id="@+id/action_startLuvBattleFragment_to_fragmentFollowing"-->
<!--            app:destination="@id/followingsFragment" />-->
    </fragment>

    <fragment
        android:id="@+id/selectOpponentFragment"
        android:name="com.flashbid.luv.ui.fragments.battle.SelectOpponentFragment"
        android:label="fragment_select_opponent"
        tools:layout="@layout/fragment_select_opponent">
        <action
            android:id="@+id/action_selectOpponentFragment_to_battleFragment"
            app:destination="@id/battleFragment" />
        <argument
            android:name="userDetail"
            app:argType="com.flashbid.luv.models.remote.UserDetails" />
    </fragment>

    <fragment
        android:id="@+id/battleFragment"
        android:name="com.flashbid.luv.ui.fragments.battle.BattleFragment"
        android:label="fragment_battle"
        tools:layout="@layout/fragment_battle" >
        <argument
            android:name="channelName"
            app:argType="string" />
        <argument
            android:name="inviterId"
            app:argType="string"
            app:nullable="true" />
    </fragment>

    <action
        android:id="@+id/action_global_battleFragment"
        app:destination="@id/battleFragment"
        app:popUpTo="@id/homeFragment"
        app:popUpToInclusive="true"
        app:enterAnim="@anim/slide_up"
        app:exitAnim="@anim/slide_down"
        app:popEnterAnim="@anim/slide_up"
        app:popExitAnim="@anim/slide_down" />
    <fragment
        android:id="@+id/topGifterFilterFragment"
        android:name="com.flashbid.luv.ui.fragments.other.TopGifterFilterFragment"
        android:label="fragment_top_gifter_filter"
        tools:layout="@layout/fragment_top_gifter_filter" >
        <action
            android:id="@+id/action_topGifterFilterFragment_to_topLuversFragment"
            app:destination="@id/topLuversFragment" />
    </fragment>

</navigation>