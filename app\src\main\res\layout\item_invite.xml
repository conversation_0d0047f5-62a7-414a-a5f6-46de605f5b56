<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="@dimen/_10sdp">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/imageView4"
        android:layout_width="@dimen/_30sdp"
        android:layout_height="@dimen/_30sdp"
        android:scaleType="centerCrop"
        android:src="@drawable/vacter_two"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.5"
        app:shapeAppearance="@style/ShapeAppearance.Material3.Corner.Full"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/tvUserName"
        style="@style/text"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_10sdp"
        android:ellipsize="end"
        android:textSize="@dimen/_12sdp"
        android:maxLines="1"
        app:layout_constraintBottom_toTopOf="@+id/tvUsernameId"
        app:layout_constraintEnd_toStartOf="@+id/cardView9"
        app:layout_constraintStart_toEndOf="@+id/imageView4"
        app:layout_constraintTop_toTopOf="@+id/imageView4"
        tools:text="Dianne Russell" />

    <TextView
        android:id="@+id/tvUsernameId"
        style="@style/small"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:maxLines="1"
        android:textSize="@dimen/_12sdp"
        android:textColor="@color/gray"
        app:layout_constraintBottom_toBottomOf="@+id/imageView4"
        app:layout_constraintEnd_toEndOf="@+id/tvUserName"
        app:layout_constraintStart_toStartOf="@+id/tvUserName"
        app:layout_constraintTop_toBottomOf="@+id/tvUserName"
        tools:text=" @username" />

    <androidx.cardview.widget.CardView
        android:id="@+id/cardView9"
        android:layout_width="@dimen/_30sdp"
        android:layout_height="@dimen/_30sdp"
        app:cardBackgroundColor="#268181A4"
        app:cardCornerRadius="@dimen/_30sdp"
        app:cardElevation="0dp"
        app:layout_constraintBottom_toBottomOf="@+id/imageView4"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/imageView4">

        <ImageView
            android:id="@+id/ivCheck"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:src="@drawable/iv_check_red"
            android:visibility="gone" />

    </androidx.cardview.widget.CardView>

</androidx.constraintlayout.widget.ConstraintLayout>