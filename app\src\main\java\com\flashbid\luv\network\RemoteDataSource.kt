package com.flashbid.luv.network

import com.flashbid.luv.models.remote.AddFavBrandsRequest
import com.flashbid.luv.models.remote.BattleRematchRequest
import com.flashbid.luv.models.remote.BattleResponseRequest
import com.flashbid.luv.models.remote.BeaconClaimRequest
import com.flashbid.luv.models.remote.BeaconRequest
import com.flashbid.luv.models.remote.CounterRequest
import com.flashbid.luv.models.remote.CreateCode
import com.flashbid.luv.models.remote.CreateKeyRequest
import com.flashbid.luv.models.remote.CreateStoresRequest
import com.flashbid.luv.models.remote.EmailCodeRequest
import com.flashbid.luv.models.remote.EmailRequest
import com.flashbid.luv.models.remote.FollowRequest
import com.flashbid.luv.models.remote.LoginAppleRequest
import com.flashbid.luv.models.remote.LoginGoogleRequest
import com.flashbid.luv.models.remote.LoginRequest
import com.flashbid.luv.models.remote.NameRequest
import com.flashbid.luv.models.remote.RechargeRequest
import com.flashbid.luv.models.remote.RefreshRequest
import com.flashbid.luv.models.remote.ResetRequest
import com.flashbid.luv.models.remote.SendLuvRequest
import com.flashbid.luv.models.remote.MessageResponse
import com.flashbid.luv.models.remote.SendGiftRequest
import com.flashbid.luv.models.remote.ShareInviteRequest
import com.flashbid.luv.models.remote.UnlockCrateRequest
import com.flashbid.luv.models.remote.UnlockKeyRequest
import com.flashbid.luv.models.remote.UpdateProfileRequest
import com.flashbid.luv.models.remote.UpdateVideoStatusRequest
import com.flashbid.luv.models.remote.UserIdRequest
import com.flashbid.luv.models.remote.UserLocationRequest
import com.flashbid.luv.models.remote.WithdrawRequest
import com.flashbid.luv.models.remote.YourOwnStoryRequest
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody.Companion.asRequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import org.koin.dsl.module
import java.io.File

val remoteDataSourceModule = module {
    factory { RemoteDataSource(get()) }
}

class RemoteDataSource(
    private val apiService: ApiService
) : BaseDataSource() {

    fun refreshToken(refresh: String) = apiService.refreshToken(RefreshRequest(refresh))

    suspend fun login(request: LoginRequest) = getResult {
        apiService.login(request)
    }

    suspend fun getReferralCode() = getResult {
        apiService.getReferralCode()
    }

    suspend fun loginGoogle(request: LoginGoogleRequest) = getResult {
        apiService.loginGoogle(request)
    }

    suspend fun loginApple(request: LoginAppleRequest) = getResult {
        apiService.loginApple(request)
    }

    suspend fun register(request: LoginRequest) = getResult {
        apiService.register(request)
    }

    suspend fun verifyEmail(request: EmailRequest) = getResult {
        apiService.verifyEmail(request)
    }

    suspend fun verifyEmailCode(request: EmailCodeRequest) = getResult {
        apiService.verifyEmailCode(request)
    }

    suspend fun forgotPassword(request: EmailRequest) = getResult {
        apiService.forgotPassword(request)
    }

    suspend fun resetPassword(request: ResetRequest) = getResult {
        apiService.resetPassword(request)
    }

    suspend fun updateProfile(request: NameRequest) = getResult {
        apiService.updateProfile(request)
    }

    suspend fun updatePreference(request: UpdateProfileRequest) = getResult {
        apiService.updatePreference(request)
    }

    suspend fun getCommunityTransactions(page: Int, limit: Int) = getResult {
        apiService.getCommunityTransactions(page, limit)
    }

    suspend fun getCommunityStats() = getResult {
        apiService.getCommunityStats()
    }

    suspend fun getUserCodes() = getResult {
        apiService.getUserCodes()
    }

    suspend fun getCodeDetail(codeId: String) = getResult {
        apiService.getCodeDetail(codeId, true)
    }

    suspend fun getCodeDetailSponsor(codeId: String, sponsor: Boolean) = getResult {
        apiService.getCodeDetailSponsor(codeId, sponsor)
    }

    suspend fun getHistoryDetail(codeId: String) = getResult {
        apiService.getHistoryDetail(codeId)
    }

    suspend fun getFollowers(query: String?) = getResult {
        apiService.getFollowers(query)
    }

    suspend fun getFollowings(query: String?) = getResult {
        apiService.getFollowings(query)
    }

    suspend fun searchUser(query: String?) = getResult {
        apiService.searchUser(query)
    }

    suspend fun deleteCode(codeId: String) = getResult {
        apiService.deleteCode(codeId)
    }

    suspend fun scanCode(codeId: String) = getResult {
        apiService.scanCode(codeId)
    }

    suspend fun getDiamondRate(codeId: String) = getResult {
        apiService.getDiamondRate(codeId)
    }

    suspend fun updateCodeDetail(codeId: String, request: CreateCode) = getResult {
        apiService.updateCodeDetail(codeId, request)
    }

    suspend fun unlockChest(codeId: String, request: UnlockCrateRequest) = getResult {
        apiService.unlockChest(codeId, request)
    }

    suspend fun unlockCrate(codeId: String, crateType: String, isBonus: Int?) = getResult {
        apiService.unlockCrate(codeId, crateType, isBonus)
    }

    suspend fun addFavBrands(codeId: AddFavBrandsRequest) = getResult {
        apiService.addFavBrands(codeId)
    }

    suspend fun rmvFavBrands(codeId: String) = getResult {
        apiService.rmvFavBrands(codeId)
    }

    suspend fun updateVideoStatus(id:Int,request: UpdateVideoStatusRequest) = getResult {
        apiService.updateVideoStatus(id,request)
    }
    suspend fun createCode(request: CreateCode) = getResult {
        apiService.createCode(request)
    }

    suspend fun getSponsored() = getResult {
        apiService.getSponsored()
    }

    suspend fun unfollowUser(request: FollowRequest) = getResult {
        apiService.unfollowUser(request)
    }

    suspend fun followUser(request: FollowRequest) = getResult {
        apiService.followUser(request)
    }

    suspend fun sendLuv(request: SendLuvRequest) = getResult {
        apiService.sendLuv(request)
    }

    suspend fun getOtherUserData(request: UserIdRequest) = getResult {
        apiService.getOtherUserData(request)
    }

    suspend fun getTopBrands(request: String, userType: String) = getResult {
        apiService.getTopBrands(request, userType)
    }

    suspend fun getAllBrandsByCategory() = getResult {
        apiService.getAllBrandsByCategory()
    }

    suspend fun getAllBrands() = getResult {
        apiService.getAllBrands()
    }

    suspend fun getAllBrands(request: String, category: Int) = getResult {
        apiService.getAllBrands(request, category)
    }

    suspend fun getUserDetails() = getResult {
        apiService.getUserDetails()
    }

    suspend fun getUserStats() = getResult {
        apiService.getUserStats()
    }

    suspend fun getUserHistory(page: Int, limit: Int) = getResult {
        apiService.getUserHistory(page, limit)
    }

    suspend fun getBalance() = getResult {
        apiService.getBalance()
    }

    suspend fun getGifts() = getResult {
        apiService.getGifts()
    }

    suspend fun getAlerts() = getResult {
        apiService.getAlerts()
    }

    suspend fun getAllPlans() = getResult {
        apiService.getAllPlans()
    }

    suspend fun getPayoutData() = getResult {
        apiService.getPayoutData()
    }

    suspend fun getQuestTime() = getResult {
        apiService.getQuestTime()
    }

    suspend fun collectDailyReward() = getResult {
        apiService.collectDailyReward()
    }

    suspend fun deleteAccount() = getResult {
        apiService.deleteAccount()
    }

    suspend fun getWithdrawLimit() = getResult {
        apiService.getWithdrawLimit()
    }

    suspend fun getRandomUser() = getResult {
        apiService.getRandomUser()
    }

    suspend fun getIndustries() = getResult {
        apiService.getIndustries()
    }

    suspend fun rechargePlan(request: RechargeRequest) = getResult {
        apiService.rechargePlan(request)
    }

    suspend fun withdrawDiamonds(request: WithdrawRequest) = getResult {
        apiService.withdrawDiamonds(request)
    }

    suspend fun uploadPicture(file: MultipartBody.Part) = getResult {
        apiService.uploadPicture(file)
    }

    suspend fun getCustomFollowing(codeId: String) = getResult {
        apiService.getCustomFollowing(codeId)
    }

    suspend fun getCustomFollower(codeId: String) = getResult {
        apiService.getCustomFollower(codeId)
    }

    suspend fun createKeyCrate(request: CreateKeyRequest) = getResult {
        apiService.createKeyCrate(request)
    }

    suspend fun unlockKeyCrate(codeId: String, request: UnlockKeyRequest) = getResult {
        apiService.unlockKeyCrate(codeId, request)
    }

    suspend fun createStory(
        filePath: String,
        duration: Int,
        transactionId: String,
        receiverUserId: String,
        viewType: String
    ) = getResult {
//        val durationRequestBody = duration.toRequestBody("text/plain".toMediaTypeOrNull())
        val receiverUserIdRequestBody =
            receiverUserId.toRequestBody("text/plain".toMediaTypeOrNull())
        val viewTypeRequestBody = viewType.toRequestBody("text/plain".toMediaTypeOrNull())
        val transactionIdRequestBody = transactionId.toRequestBody("text/plain".toMediaTypeOrNull())

        val file = File(filePath)
        val requestFile = file.asRequestBody("multipart/form-data".toMediaTypeOrNull())
        val filePart = MultipartBody.Part.createFormData("file", file.name, requestFile)

        apiService.createStory(
            file = filePart,
            duration = duration,
//            duration = durationRequestBody,
            receiverUserId = receiverUserIdRequestBody,
            transactionId = transactionIdRequestBody,
            viewType = viewTypeRequestBody
        )
    }

    suspend fun getStory() = getResult {
        apiService.getStory()
    }

    suspend fun getReports() = getResult {
        apiService.getReports()
    }

    suspend fun createReport(
        type: String, type_id: String, comment: String, category: Int
    ) = getResult {
        apiService.createReport(type, type_id, comment, category)
    }

    suspend fun getYourOwnStory(request: YourOwnStoryRequest) = getResult {
        apiService.getYourOwnStory(request)
    }

    suspend fun joinLive(channel: String, inviter: String) = getResult {
        apiService.joinLive(channel, inviter)
    }

    suspend fun battleResponse(request: BattleResponseRequest) = getResult {
        apiService.battleResponse(request)
    }

    suspend fun leaveBattle(): Resource<MessageResponse> = getResult {
        apiService.leaveBattle()
    }

    suspend fun leaveBattleAudience(channel: String, inviter: String) = getResult {
        apiService.leaveBattleAudience(channel, inviter)
    }
    suspend fun battleRequest(request: BattleResponseRequest) = getResult {
        apiService.battleRequest(request)
    }

    suspend fun getBattleDetails(channel: String) = getResult {
        apiService.getBattleDetails(channel)
    }

    suspend fun shareInvite(request: ShareInviteRequest) = getResult {
        apiService.shareInvite(request)
    }

    suspend fun counter(request: CounterRequest) = getResult {
        apiService.counter(request)
    }

    suspend fun createStores(request: CreateStoresRequest) = getResult {
        apiService.createStores(request)
    }

    suspend fun confirmUserLocation(request: UserLocationRequest) = getResult {
        apiService.confirmUserLocation(request)
    }

    suspend fun stores() = getResult {
        apiService.stores()
    }

    suspend fun nearbyUser() = getResult {
        apiService.nearbyUser()
    }

    suspend fun battleRematch(request: BattleRematchRequest) = getResult {
         apiService.battleRematch(request)
    }

    suspend fun sendGift(request: SendGiftRequest) = getResult {
        apiService.sendGift(request)
    }

    suspend fun battleResult(channel: String) = getResult {
        apiService.battleResult(channel)
    }

    suspend fun endBattle(channelId: String) = getResult {
        apiService.endBattle(channelId)
    }

    suspend fun getTopGifts(channelId: String, userID: Int) = getResult {
        apiService.getTopGifts(channelId, userID)
    }

    suspend fun beacon(request: BeaconRequest) = getResult {
        apiService.beacon(request)
    }

    suspend fun beaconClaim(request: BeaconClaimRequest) = getResult {
        apiService.beaconClaim(request)
    }

    suspend fun customBrands() = getResult {
        apiService.getCustomBrands()
    }

    suspend fun customBeaconBrands() = getResult {
        apiService.getCustomBeaconBrands()
    }

    suspend fun customBrandName(filter: Int) = getResult {
        apiService.getCustomBrandNames(filter)
    }
}