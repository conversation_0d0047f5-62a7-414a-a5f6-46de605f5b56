package com.flashbid.luv

import android.Manifest
import android.app.AlertDialog
import android.content.ContentValues.TAG
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.content.res.ColorStateList
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.media.Ringtone
import android.media.RingtoneManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.View
import android.view.animation.Animation
import android.view.animation.AnimationUtils
import android.widget.LinearLayout
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.annotation.IdRes
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.navigation.NavController
import androidx.navigation.NavDestination
import androidx.navigation.fragment.NavHostFragment
import androidx.navigation.ui.setupWithNavController
import com.flashbid.luv.data.local.AppPreferences
import com.flashbid.luv.databinding.ActivityMainBinding
import com.flashbid.luv.databinding.DialogAnnoucementBinding
import com.flashbid.luv.databinding.DialogBeaconDropBinding
import com.flashbid.luv.databinding.DialogGiveawayBinding
import com.flashbid.luv.extensions.TextState
import com.flashbid.luv.extensions.hide
import com.flashbid.luv.extensions.scaleEffect
import com.flashbid.luv.extensions.setOnClickWithDebounce
import com.flashbid.luv.extensions.show
import com.flashbid.luv.extensions.snackBar
import com.flashbid.luv.models.remote.AlertModel
import com.flashbid.luv.models.remote.UpdateProfileRequest
import com.flashbid.luv.network.Status
import com.flashbid.luv.util.BaseLiveData
import com.flashbid.luv.util.BeaconService
import com.flashbid.luv.util.Constants
import com.flashbid.luv.util.Constants.AUTH_TOKEN
import com.flashbid.luv.util.Constants.BATTLE_INVITE
import com.flashbid.luv.util.Constants.BATTLE_INVITE_RESPONSE
import com.flashbid.luv.util.Constants.BATTLE_SHARE
import com.flashbid.luv.util.Constants.FCM_TOKEN
import com.flashbid.luv.util.Constants.PUBLIC_IP
import com.flashbid.luv.util.HistoryMapping
import com.flashbid.luv.util.LanguageUtil
import com.flashbid.luv.util.RequestCodes
import com.flashbid.luv.viewmodels.BattleViewModel
import com.flashbid.luv.viewmodels.BeaconViewModel
import com.flashbid.luv.viewmodels.UserViewModel
import com.google.android.gms.auth.api.signin.GoogleSignIn
import com.google.android.gms.auth.api.signin.GoogleSignInClient
import com.google.android.gms.auth.api.signin.GoogleSignInOptions
import com.google.android.gms.location.LocationCallback
import com.google.android.gms.location.LocationRequest
import com.google.android.gms.location.LocationResult
import com.google.android.gms.location.LocationServices
import com.google.android.gms.tasks.OnCompleteListener
import com.google.android.material.bottomnavigation.BottomNavigationItemView
import com.google.firebase.dynamiclinks.FirebaseDynamicLinks
import com.google.firebase.messaging.FirebaseMessaging
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.io.BufferedReader
import java.io.InputStreamReader
import java.net.HttpURLConnection
import java.net.URL

class MainActivity : AppCompatActivity(), NavController.OnDestinationChangedListener {

    var latitude = 0.0
    var longitude = 0.0
    override fun onStop() {
        super.onStop()
        fusedLocationProviderClient.removeLocationUpdates(locationCallback)
    }

    private val fusedLocationProviderClient by lazy {
        LocationServices.getFusedLocationProviderClient(
            this
        )
    }
    private val requestNotificationPermissionLauncher =
    registerForActivityResult(ActivityResultContracts.RequestPermission()) {
        if (!it) snackBar("Please grant Notification permission from App Settings")

        requestLocationPermission()
    }

    private val requestCameraPermissionLauncher =
        registerForActivityResult(ActivityResultContracts.RequestPermission()) { isGranted ->
            requestNotificationPermission()
        }

    private val locationRequest by lazy {
        LocationRequest.create().apply {
            priority = LocationRequest.PRIORITY_HIGH_ACCURACY
            interval = 10000 // Update interval in milliseconds
            fastestInterval = 5000 // Fastest update interval in milliseconds
            smallestDisplacement = 20f // Minimum displacement for updates in meters
        }
    }

    private val locationCallback by lazy {
        object : LocationCallback() {
            override fun onLocationResult(locationResult: LocationResult) {
                locationResult.locations.forEach { location ->
                    latitude = location.latitude
                    longitude = location.longitude
                    sendLocationToBackend()
                }
            }
        }
    }

    private val locationPermissionRequest = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted: Boolean ->
        if (isGranted) {
            setupLocationService()
        } else {
            // Handle the case where permission is denied
        }
    }

    companion object {
        lateinit var instance: MainActivity
            private set
    }

    private lateinit var binding: ActivityMainBinding
    private lateinit var navController: NavController
    private val viewModel: UserViewModel by viewModel()
    private val battleViewModel: BattleViewModel by viewModel()
    private val beaconViewModel: BeaconViewModel by viewModel()
    private val pref by inject<AppPreferences>()
    private val googleSignInClient: GoogleSignInClient by lazy {
        GoogleSignIn.getClient(this, GoogleSignInOptions.DEFAULT_SIGN_IN)
    }

    override fun attachBaseContext(newBase: Context) {
        Constants.CURRENT_LOCALE = pref.appLanguage ?: "en"
        super.attachBaseContext(LanguageUtil.setLocale(newBase, pref.appLanguage ?: "en"))
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        instance = this
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setUpNavController()

        setupNavView()
        observeLiveData()
        handleUserSession()
        askForCameraPermission()
        handleIntent(intent)
        checkDynamicLink()
        setupBattleNotificationView()
//        startBeaconService()
        setupLocationService()

        Handler(Looper.getMainLooper()).postDelayed({
            if (pref.isNearbyEnabled == -1) {
                showNearbyNotification()
            } else if (pref.isNearbyEnabled == 1) {
                setupLocationService()
            }
        }, 10000)

    }

    private fun showNearbyNotification() {
        binding.includeLocationShare.root.show()
        // Hide the view after 8 seconds (8000 milliseconds)
        Handler(Looper.getMainLooper()).postDelayed({
            binding.includeLocationShare.root.hide()
        }, 10000)

        binding.includeLocationShare.btnEnableLocation.setOnClickWithDebounce {
            // Cancel the hiding if the button is clicked
            binding.includeLocationShare.root.handler?.removeCallbacksAndMessages(null)
            binding.includeLocationShare.root.hide()
            pref.isNearbyEnabled = 0
            setupLocationService()
        }
    }

    private fun requestLocationPermission() {
        locationPermissionRequest.launch(Manifest.permission.ACCESS_FINE_LOCATION)
    }

    private fun setupLocationService() {
        if (ContextCompat.checkSelfPermission(
                this,
                Manifest.permission.ACCESS_FINE_LOCATION
            ) == PackageManager.PERMISSION_GRANTED
        ) {
            fusedLocationProviderClient.requestLocationUpdates(
                locationRequest,
                locationCallback,
                Looper.getMainLooper()
            )
            pref.isNearbyEnabled = 1
        } else {
            requestLocationPermission()
        }
    }

    fun sendLocationToBackend() {
        if (AUTH_TOKEN.value != null && latitude > 0.0 && longitude > 0.0) {
            viewModel.updatePreference(
                UpdateProfileRequest(
                    UpdateProfileRequest.Payload(
                        latitude = latitude.toString(),
                        longitude = longitude.toString()
                    )
                )
            ).observe(this) {
                when (it.status) {
                    Status.ERROR -> snackBar(
                        it.message ?: getString(
                            R.string.something_went_wrong, getString(R.string.try_again)
                        )
                    )

                    Status.LOADING -> {}
                    Status.SUCCESS -> {

                    }
                }
            }
        }
    }

    private fun setUpNavController() {
        val navHostFragment =
            supportFragmentManager.findFragmentById(R.id.nav_host_fragment_activity_main) as NavHostFragment
        navController = navHostFragment.navController
        binding.navView.setupWithNavController(navController)
        navController.addOnDestinationChangedListener(this)

    }

    private fun observeLiveData() {
        BaseLiveData.isAuthorized.observe(this) { authorized ->
            if (!authorized) {
                handleUnauthorizedUser()
            }
        }
        AUTH_TOKEN.observe(this) {
            pref.accessToken = it
        }

        FCM_TOKEN.observe(this) {
            pref.fcmToken = it
            if (pref.isLoggedIn)
                updateFcmToken(it)
        }

        Constants.REFRESH_AUTH_TOKEN.observe(this) {
            pref.refreshToken = it
        }

        BATTLE_INVITE.observe(this) {
            binding.includeNotification.tvUsername.text =
                getString(R.string.victor_challenged_you_to_a_battle, it.first)
            binding.includeNotification.root.show()
            val notification: Uri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)
            val ringtone: Ringtone = RingtoneManager.getRingtone(applicationContext, notification)
            ringtone.play()
        }

        BATTLE_INVITE_RESPONSE.observe(this) {
            if (it.third != null) {
                navController.navigate(
                    MainNavDirections.actionGlobalBattleFragment(it.third!!, null)
                )
            } else {
                binding.includeNotification.root.hide()
            }
        }

        Constants.CLAIM_STORE_REWARD.observe(this) {
            if (!it.isNullOrEmpty()) {
                claimStoreReward(it)
            }
        }

        BATTLE_SHARE.observe(this) {
            binding.includeNotificationShare.tvUsername.text =
                getString(R.string.shared_live_stream, it.second)
            binding.includeNotificationShare.root.show()
            val notification: Uri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)
            val ringtone: Ringtone = RingtoneManager.getRingtone(applicationContext, notification)
            ringtone.play()
            setupBattleShareView(it)
        }

    }

    private fun claimStoreReward(uid: String) {
        beaconViewModel.beaconClaim(uid).observe(this) {
            when (it.status) {
                Status.ERROR -> snackBar(
                    it.message ?: getString(
                        R.string.something_went_wrong, getString(R.string.try_again)
                    )
                )

                Status.LOADING -> {}
                Status.SUCCESS -> {
                    showBeaconRewardDialog()
                }
            }
        }
    }

    private fun showBeaconRewardDialog() {
        val builder = AlertDialog.Builder(this)
        val binding = DialogBeaconDropBinding.inflate(layoutInflater)
        val dialog = builder.create()

        dialog.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))

        binding.imageView8.setOnClickWithDebounce {
            dialog.dismiss()
        }
        binding.btnOpen.setOnClickWithDebounce {
            dialog.dismiss()
        }

        dialog.setView(binding.root)
        dialog.setCancelable(false)
        dialog.show()
    }

    private fun setupBattleShareView(pair: Triple<String?, String?, String?>) {
        binding.includeNotificationShare.apply {
            btnJoin.setOnClickListener {
                root.hide()
                navController.navigate(
                    MainNavDirections.actionGlobalBattleFragment(
                        pair.third!!,
                        pair.first
                    )
                )
            }
        }
    }

    private fun setupBattleNotificationView() {
        binding.includeNotification.apply {
            btnDecline.setOnClickListener {
                root.hide()
                sendBattleResponse("reject", BATTLE_INVITE.value?.second ?: "")
            }
            btnJoin.setOnClickListener {
                root.hide()
                sendBattleResponse("accept", BATTLE_INVITE.value?.second ?: "")

            }
        }
    }

    private fun sendBattleResponse(status: String, uid: String) {
        if(!uid.isEmpty()) {
            battleViewModel.battleResponse(status, uid.toInt()).observe(this) {
                when (it.status) {
                    Status.ERROR -> snackBar(
                        it.message ?: getString(
                            R.string.something_went_wrong, getString(R.string.try_again)
                        )
                    )

                    Status.LOADING -> {}
                    Status.SUCCESS -> {

                    }
                }
            }
        }
    }

    private fun handleUnauthorizedUser() {
        if (navController.currentDestination?.id != R.id.authSelectFragment) {
            pref.logout()
            googleSignInClient.signOut()
            navController.navigate(R.id.authSelectFragment)
        }
    }

    private fun updateFcmToken(token: String) {
        viewModel.updatePreference(UpdateProfileRequest(UpdateProfileRequest.Payload(fcm_token = token)))
            .observe(this) {
                if (it.status == Status.ERROR) {
                    snackBar(
                        it.message ?: getString(
                            R.string.something_went_wrong, getString(R.string.try_again)
                        )
                    )
                }
            }
    }

    private fun setupNavView() {
        binding.navView.apply {
            itemIconTintList = null
            itemTextColor = createSelectedColorStateList()
            setOnNavigationItemSelectedListener { menuItem ->
                val menuItemId = menuItem.itemId
                findViewById<BottomNavigationItemView?>(menuItemId)?.scaleEffect()
                navigateOnce(menuItemId)
                true
            }
        }
    }

    private fun fetchPublicIPInBackground() {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val publicIP = fetchPublicIP()
                Log.d("PublicIP", "Public IP: $publicIP")
                PUBLIC_IP.postValue(publicIP)
            } catch (e: Exception) {
                Log.e("Error", "Error fetching public IP", e)
            }
        }
    }

    private fun createSelectedColorStateList(): ColorStateList {
        return ColorStateList(
            arrayOf(intArrayOf(android.R.attr.state_checked), intArrayOf()), intArrayOf(
                Color.BLACK, R.color.gray
            )
        )
    }

    private fun handleUserSession() {
        if (pref.isLoggedIn) {
            updateTokens()
            navController.navigate(R.id.homeFragment)
            getFCMToken()
        } else {
            navController.navigate(R.id.authSelectFragment)
        }
    }

    private fun updateTokens() {
        AUTH_TOKEN.value = pref.accessToken.toString()
        FCM_TOKEN.value = pref.fcmToken.toString()
    }

    private fun requestNotificationPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            val currentPermissionStatus = ContextCompat.checkSelfPermission(
                this,
                Manifest.permission.POST_NOTIFICATIONS
            )

            if (currentPermissionStatus != PackageManager.PERMISSION_GRANTED) {
                requestNotificationPermissionLauncher.launch(Manifest.permission.POST_NOTIFICATIONS)
            }
        }
    }

    private fun getFCMToken() {
        FirebaseMessaging.getInstance().token.addOnCompleteListener(OnCompleteListener { task ->
            if (!task.isSuccessful) {
                Log.w(TAG, "Fetching FCM registration token failed", task.exception)
                return@OnCompleteListener
            }
            FCM_TOKEN.postValue(task.result)

            Log.w(TAG, "Fetched FCM Token ${task.result}")

        })
    }

    private fun askForCameraPermission() {
        requestCameraPermissionLauncher.launch(Manifest.permission.CAMERA)
    }

    private fun navigateOnce(@IdRes resId: Int) {
        if (navController.currentDestination?.id != resId) {
            navController.navigate(resId)
        }

        if (resId == R.id.scanFragment) {
            Constants.CRATE_TYPE.postValue(""+ HistoryMapping.ACTION.CRATE)
        }
    }

    @Deprecated("Deprecated in Java")
    override fun onBackPressed() {
        if (navController.currentDestination?.id == R.id.homeFragment || navController.currentDestination?.id == R.id.onboardFragment || navController.currentDestination?.id == R.id.authSelectFragment || navController.currentDestination?.id == R.id.registrationFragment) this.moveTaskToBack(
            false
        )
        else super.onBackPressed()
    }

    override fun onDestinationChanged(
        controller: NavController, destination: NavDestination, arguments: Bundle?
    ) {
        when (destination.id) {
            R.id.homeFragment -> binding.navView.show()
            R.id.alertFragment -> binding.navView.show()
            R.id.questFragment -> binding.navView.show()
            R.id.scanFragment -> binding.navView.show()
            //R.id.startLuvBattleFragment -> binding.navView.show()
            R.id.myProfileFragment -> binding.navView.show()
            else -> binding.navView.hide()
        }
    }

    override fun onResume() {
        super.onResume()
        if (pref.isLoggedIn) getUserDetails()

        checkDynamicLink()

        fetchPublicIPInBackground()

        if (pref.isLoggedIn) {

            if (pref.fcmToken != null) {
                pref.fcmToken?.let { updateFcmToken(it) }
            } else {
                getFCMToken()
            }
            sendLocationToBackend()
        }
    }

    private fun checkDynamicLink() {
        FirebaseDynamicLinks.getInstance().getDynamicLink(intent)
            .addOnSuccessListener(this) { pendingDynamicLinkData ->
                var deepLink: Uri? = null

                if (pendingDynamicLinkData != null) {
                    deepLink = pendingDynamicLinkData.link
                } else {
                }

                deepLink?.let { uri ->
                    val stringUri = uri.toString()
                    //snackBar("String is...."+stringUri)
                    when {
                        stringUri.contains("referral") -> {
                            val codeStartIndex = stringUri.lastIndexOf("=") + 1
                            val code = stringUri.substring(codeStartIndex)
                            if (code.isNotBlank()) {
                                try {
                                    BaseLiveData.referralLiveData.postValue(code.toInt())
                                } catch (e: NumberFormatException) {
                                    snackBar(e.message.toString())
                                }
                            }
                        }
                    }
                }
            }.addOnFailureListener {
                snackBar("${it.message}")
            }
    }

    private fun getUserDetails() {
        viewModel.getUserDetails().observe(this) {
            when (it.status) {
                Status.ERROR -> snackBar(
                    it.message ?: getString(
                        R.string.something_went_wrong, getString(R.string.try_again)
                    )
                )

                Status.LOADING -> {}
                Status.SUCCESS -> {
                    val data = it.data?.message
                    pref.saveUserData(
                        data?.first_name,
                        data?.last_name,
                        data?.username ?: "",
                        data?.photo,
                        data?.bio,
                        data?.user_id,
                        data?.age,
                        data?.gender,
                        data?.show_following,
                        data?.show_follower
                    )
                }
            }
        }
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        handleIntent(intent)
    }

    private fun handleIntent(intent: Intent?) {

        if (pref.isLoggedIn) {
            val notificationTitle = intent?.getStringExtra("notificationTitle") ?: ""
            // Check if it's a ticket notification by title in any language
            val isTicketNotification = notificationTitle.startsWith("Ticket")
            when (intent?.getStringExtra("notificationType")) {
                "1" -> {
                    // Navigate to Alerts Fragment
                    navController.navigate(R.id.alertFragment)
                }

                "2" -> {
                    // Navigate to Quest Fragment
                    navController.navigate(R.id.questFragment)
                }

                "3" -> {
                    // Navigate to Followers Fragment
//                    navController.navigate(R.id.userFollowersFragment)

                    if (isTicketNotification) {
                        // For ticket notifications, go to alerts
                        navController.navigate(R.id.alertFragment)
                    } else {
                        // For other type 3 notifications, go to followers
                        navController.navigate(R.id.userFollowersFragment)
                    }
                }

                "4" -> {
                    val username = intent.getStringExtra("username")
                    val userId = intent.getStringExtra("user_id")
                    if (username != null && userId != null) BATTLE_INVITE.postValue(
                        Pair(
                            username,
                            userId
                        )
                    )
                    else snackBar("data is missing from battle request notification")
                }

                "5" -> {
                    val username = intent.getStringExtra("username")
                    val userId = intent.getStringExtra("user_id")
                    val channelName = intent.getStringExtra("channel_name")
                    if (username != null && userId != null && channelName != null) BATTLE_INVITE_RESPONSE.postValue(
                        Triple(username, userId, channelName)
                    )
                    else snackBar("data is missing from battle request notification")
                }

                "6" -> {
                    val channelName = intent.getStringExtra("channel_name")
                    if (channelName != null) BATTLE_INVITE_RESPONSE.postValue(
                        Triple("", "", channelName)
                    )
                    else snackBar("data is missing from battle request notification")
                }

                "7" -> { // Announcement Alert

                    val notificationTitle = intent.getStringExtra("notificationTitle")
                    val notificationMessage = intent.getStringExtra("notificationMessage")

                    val builder = AlertDialog.Builder(this)
                    if(notificationTitle?.lowercase().equals("Top Gifter".lowercase())) {
                        val binding = DialogAnnoucementBinding.inflate(layoutInflater)
                        val dialog = builder.create()
                        dialog.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
                        binding.tvAmount.text = notificationTitle
                        binding.textView29.text = notificationMessage

                        binding.imageView8.setOnClickWithDebounce {
                            dialog.dismiss()
                        }

                        dialog.setView(binding.root)
                        dialog.setCancelable(false)
                        dialog.show()
                    } else if(notificationTitle?.lowercase().equals("We have a winner".lowercase())
                        || notificationTitle?.lowercase().equals("We have a winner!".lowercase()) ) {

                    }

                }

                else -> {
                    // Navigate to Home Fragment
                    navController.navigate(R.id.homeFragment)
                }
            }
        }
    }

    private fun fetchPublicIP(): String {
        val url = URL("https://api.ipify.org")
        val connection = url.openConnection() as HttpURLConnection
        connection.requestMethod = "GET"
        connection.connect()

        val inputStream = connection.inputStream
        val reader = BufferedReader(InputStreamReader(inputStream))

        return reader.readLine()
    }

    fun showSnackBar(string: String, state: TextState) {
        binding.tvInfo.text = string
        binding.llInfo.showWithAnimation()
        when (state) {
            TextState.ERROR -> {
                binding.tvInfo.setTextColor(getColor(R.color.redgradstart))
                binding.llInfo.setBackgroundColor(getColor(R.color.semired))
            }

            TextState.SUCCESS -> {
                binding.tvInfo.setTextColor(getColor(R.color.green))
                binding.llInfo.setBackgroundColor(getColor(R.color.semigreen))
            }

            TextState.NEUTRAL -> {
                binding.tvInfo.setTextColor(getColor(R.color.gray))
                binding.llInfo.setBackgroundColor(getColor(R.color.md_theme_light_background))
            }
        }
    }

    private fun startBeaconService() {
        Intent(this, BeaconService::class.java).also { intent ->
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                startForegroundService(intent)
            } else {
                startService(intent)
            }
        }
    }
}

fun LinearLayout.showWithAnimation() {
    val slideUp = AnimationUtils.loadAnimation(this.context, R.anim.slide_up)
    val slideDown = AnimationUtils.loadAnimation(this.context, R.anim.slide_down)

    slideDown.setAnimationListener(object : Animation.AnimationListener {
        override fun onAnimationStart(animation: Animation) {}

        override fun onAnimationEnd(animation: Animation) {
            <EMAIL> = View.GONE
        }

        override fun onAnimationRepeat(animation: Animation) {}
    })

    this.visibility = View.VISIBLE
    this.startAnimation(slideUp)

    Handler(Looper.getMainLooper()).postDelayed({
        this.startAnimation(slideDown)
    }, 5000)
}