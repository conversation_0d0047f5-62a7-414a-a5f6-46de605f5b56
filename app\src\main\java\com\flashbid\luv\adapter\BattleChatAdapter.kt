package com.flashbid.luv.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.flashbid.luv.databinding.ItemBattleChatBinding
import com.flashbid.luv.extensions.setOnClickWithDebounce
import com.flashbid.luv.models.battle.Model
import com.flashbid.luv.models.remote.AlertModel

class BattleChatAdapter(private val chatList: ArrayList<Model>) :
    RecyclerView.Adapter<BattleChatAdapter.ViewHolder>() {

    inner class ViewHolder(val binding: ItemBattleChatBinding) :
        RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val itemView =
            ItemBattleChatBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(itemView)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val model = chatList[position]

        with(holder.binding) {
            tvUsername.text = model.name
            tvUsernameChat.text = model.id
        }

    }

    override fun getItemCount(): Int {
        return chatList.size
    }
//
//    fun refresh(newList: ArrayList<AlertModel>) {
//        list.clear()
//        list.addAll(newList)
//        notifyDataSetChanged()
//    }

}