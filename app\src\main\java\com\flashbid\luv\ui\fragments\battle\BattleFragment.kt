package com.flashbid.luv.ui.fragments.battle

import android.Manifest
import android.content.ContentValues
import android.content.pm.PackageManager
import android.graphics.Color
import android.os.Bundle
import android.os.CountDownTimer
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.widget.FrameLayout
import android.widget.ScrollView
import androidx.activity.OnBackPressedCallback
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.core.widget.doAfterTextChanged
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.viewbinding.ViewBinding
import com.flashbid.luv.R
import com.flashbid.luv.adapter.BattleGiftAdapter
import com.flashbid.luv.adapter.InviteFollowersAdapter
import com.flashbid.luv.adapter.TopGiftersAdapter
import com.flashbid.luv.data.local.AppPreferences
import com.flashbid.luv.databinding.BottomSheetInviteSubscribersBinding
import com.flashbid.luv.databinding.BottomSheetYourGiftBinding
import com.flashbid.luv.databinding.FragmentBattleBinding
import com.flashbid.luv.databinding.ItemBattleChatBinding
import com.flashbid.luv.databinding.ItemStreamGiftBinding
import com.flashbid.luv.databinding.SheetEndBattleBinding
import com.flashbid.luv.databinding.SheetTopGiftersBinding
import com.flashbid.luv.extensions.hide
import com.flashbid.luv.extensions.hideSoftKeyboard
import com.flashbid.luv.extensions.setGridLayout
import com.flashbid.luv.extensions.setOnClickWithDebounce
import com.flashbid.luv.extensions.setVerticalLayout
import com.flashbid.luv.extensions.show
import com.flashbid.luv.extensions.snackBar
import com.flashbid.luv.extensions.viewBinding
import com.flashbid.luv.models.battle.UnifiedStreamMessage
import com.flashbid.luv.models.remote.BattleDetailsResponse
import com.flashbid.luv.models.remote.ItemGift
import com.flashbid.luv.models.remote.UserDetails
import com.flashbid.luv.network.Status
import com.flashbid.luv.util.Constants
import com.flashbid.luv.util.DeviceUtils.hideSoftInput
import com.flashbid.luv.util.loadImageFromUrl
import com.flashbid.luv.util.loadImageFromUrlForChat
import com.flashbid.luv.viewmodels.BattleViewModel
import com.flashbid.luv.viewmodels.GiftsViewModel
import com.flashbid.luv.viewmodels.UserViewModel
import com.flashbid.luv.viewmodels.ViewState
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel

class BattleFragment : Fragment(R.layout.fragment_battle) {

    private val TAG = "Battle-Fragment"
    private var opponetUesrID = 0
    private var battleWaitingTimer = "00:30"
    private var battleTimer = "05:00"

    private val permissionRequest =
        registerForActivityResult(ActivityResultContracts.RequestMultiplePermissions()) { permissions ->
            val allGranted = permissions.entries.all { it.value }
            if (allGranted) {
                viewModel.initializeAgoraEngine(requireActivity().applicationContext) {
                    viewModel.loadBattleDetails(args.channelName)
                }
            } else {
                showMessage("Permissions are not granted")
            }
        }

    companion object {
        private val REQUESTED_PERMISSIONS = arrayOf(
            Manifest.permission.RECORD_AUDIO,
            Manifest.permission.CAMERA
        )
    }

    private val viewModel: BattleViewModel by viewModel()
    private val giftsViewModel: GiftsViewModel by viewModel()
    private val userViewModel: UserViewModel by viewModel()
    private val binding by viewBinding(FragmentBattleBinding::bind)
    private val args by navArgs<BattleFragmentArgs>()
    private val pref by inject<AppPreferences>()
    private val giftList: ArrayList<ItemGift> = ArrayList()
    private val followerList: ArrayList<UserDetails> = ArrayList()
    private var inviterId: String? = null
    private var countDownTimer: CountDownTimer? = null
    private var isInitialTimer = true
    private var isRematch = false

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        inviterId = args.inviterId
        checkAndRequestPermissions()
        observeViewModel()
        setupUI()
        loadGifts()
        //loadFollowers()

        requireActivity().onBackPressedDispatcher.addCallback(viewLifecycleOwner,
            object : OnBackPressedCallback(true) {
                override fun handleOnBackPressed() {
                    if (viewModel.userRole == BattleViewModel.UserRole.OPPONENT_BROADCAST
                        || viewModel.userRole == BattleViewModel.UserRole.OWNER_BROADCAST
                        || viewModel.userRole == BattleViewModel.UserRole.AUDIENCE) {
                        showDiscardSheet()
                    } else findNavController().popBackStack()
                }
            }
        )
    }


    private fun setupUI() {
        revertOpponentFullScreen()
        revertOwnerFullScreen()

        binding.apply {
            ivShare.setOnClickListener { showInviteSheet() }
            ivGift.setOnClickListener { showGiftSheet() }
            cardView6.setOnClickListener { viewModel.latestBattleDetails?.activeChannels?.let { it1 ->
                showTopGiftsSheet(
                    it1.owner_user_id)
            } }
            cardView8.setOnClickListener { viewModel.latestBattleDetails?.activeChannels?.let { it1 ->
                showTopGiftsSheet(
                    it1.opponent)
            } }
            btnStartBattle.setOnClickListener {
                //onInitialTimerFinish()

                viewModel.sendBattleStartMessage(
                    battleTimer,
                    1
                )
                binding.hsvGifts.hide()
                binding.rcvGiftsReceived.removeAllViewsInLayout()

            }
            btnCloseMessage.setOnClickListener {
                editText.text = null
                editText.clearFocus()
                hideSoftInput(binding.root)
            }
            ivBack.setOnClickListener { requireActivity().onBackPressed() }
            ivMyImage.loadImageFromUrl(pref.photo)
            editText.doAfterTextChanged {
                if (it.toString().isNotEmpty()) {
                    ivSend.show()
                    btnCloseMessage.show()
                    ivGift.hide()
                } else {
                    btnCloseMessage.hide()
                    ivSend.hide()
                    if (viewModel.userRole == BattleViewModel.UserRole.AUDIENCE && !isInitialTimer) ivGift.show()
                }
            }
        }
    }

    private fun showDiscardSheet() {
        val sheet = BottomSheetDialog(requireContext())
        val view = SheetEndBattleBinding.inflate(layoutInflater)

        view.apply {
            imageView6.setOnClickListener {
                sheet.dismiss()
            }
            btnCancel.setOnClickListener {
                sheet.dismiss()
            }
            btnDiscard.setOnClickListener {
                sheet.dismiss()

                if (viewModel.userRole != BattleViewModel.UserRole.AUDIENCE) {
                    viewModel.sendBattleClosedMessage()
                } else {

                    if (viewModel.userRole == BattleViewModel.UserRole.AUDIENCE && args.inviterId != null) {
                        viewModel.leaveBattleAudience(args.channelName, args.inviterId!!.toInt())
                    }

                }
                //findNavController().popBackStack()
            }
        }

        sheet.setCancelable(true)
        sheet.setContentView(view.root)
        sheet.show()
    }

    private fun setDetailsUI(it: BattleDetailsResponse) {
        Log.d(TAG, "Setting details UI")

        val battleData = it.activeChannels
        viewModel.setUserRole(battleData)
        viewModel.joinChannel(
            battleData.token,
            battleData.channel_name
        )
        setupRoleViews(viewModel.userRole)

        with(binding) {
            ivSend.setOnClickListener {
                val message = editText.text.toString()
                if (message.isNotEmpty()) {

                    var inverterID = ""+pref.userId
                    if (viewModel.userRole == BattleViewModel.UserRole.AUDIENCE) {
                        inverterID = args.inviterId.toString()
                    }
                    viewModel.sendTextMessage(message.trim(), inverterID) {
                        lifecycleScope.launch(Dispatchers.Main) {
                            editText.text = null
                            requireActivity().hideSoftKeyboard()
                        }
                    }
                }
            }

            imageView18.loadImageFromUrl(battleData.owner_image)
            imageView20.loadImageFromUrl(battleData.opponent_image)
            tvMyName.text = battleData.owner_username
            textView44.text = battleData.opponent_username

            if (viewModel.userRole == BattleViewModel.UserRole.AUDIENCE) {

                if(battleData.owner_audience != null) {
                    binding.ownerCount.text = ""+battleData.owner_audience
                }

                if(battleData.opponent_audience != null) {
                    binding.opponentCount.text = ""+battleData.opponent_audience
                }

                if(battleData.owner_gift_sum != null) {
                    binding.ownerGift.text = ""+battleData.owner_gift_sum
                }

                if(battleData.opponent_gift_sum != null) {
                    binding.opponentGift.text = ""+battleData.opponent_gift_sum
                }


                // After updating, get the latest totals
                val updatedOpponentTotalGifts = binding.opponentGift.text.toString().toInt()
                val updatedOwnerTotalGifts = binding.ownerGift.text.toString().toInt()

                // Update CardViews' background colors based on who has more gifts
                binding.cardView7.setCardBackgroundColor(
                    if (updatedOpponentTotalGifts > updatedOwnerTotalGifts) Color.parseColor("#f95050")
                    else Color.parseColor("#388181A4")
                )
                binding.cvGift.setCardBackgroundColor(
                    if (updatedOwnerTotalGifts > updatedOpponentTotalGifts) Color.parseColor("#f95050")
                    else Color.parseColor("#388181A4")
                )

                if (battleData.owner_won_count != null) {

                    cvOppWins.show()
                    cvWins.show()
                    ownerWins.text = ""+battleData.owner_won_count

                }

                if (battleData.opponent_won_count != null) {

                    cvOppWins.show()
                    cvWins.show()
                    opponentWins.text = ""+battleData.opponent_won_count

                }

                if (battleData.owner_won_count != null && battleData.opponent_won_count != null) {
                    setWinColors()
                }

            }
        }

        viewModel.setupRTMClient(requireContext()) {
            viewModel.connectToRtmServer(
                it.chat_token,
                args.channelName,
                viewModel.uid.toString(),
                args.inviterId
            )
        }
    }

    private fun checkAndRequestPermissions() {
        when {
            hasPermissions() -> {
                viewModel.initializeAgoraEngine(requireActivity().applicationContext) {
                    viewModel.loadBattleDetails(args.channelName)
                }
            }

            else -> {
                permissionRequest.launch(REQUESTED_PERMISSIONS)
            }
        }
    }

    private fun hasPermissions() = REQUESTED_PERMISSIONS.all {
        ContextCompat.checkSelfPermission(requireContext(), it) == PackageManager.PERMISSION_GRANTED
    }

    private fun showMessage(message: String?) {
        snackBar(message ?: getString(R.string.something_went_wrong, getString(R.string.try_again)))
    }

    private fun observeViewModel() {
        lifecycleScope.launchWhenStarted {
            viewModel.viewState.collect { viewState ->
                when (viewState) {
                    is ViewState.Idle -> {}
                    is ViewState.UserJoined -> handleUserJoined(viewState.uid, viewState.message)
                    is ViewState.Error -> showMessage(viewState.message)
                    is ViewState.ChatReceived -> displayMessage(viewState.message)
                    is ViewState.BattleDetailsLoaded -> handleBattleDetailsLoaded(viewState.details)
                    is ViewState.AudienceLeave -> handleAudienceLeave(viewState.message)
                    is ViewState.BattleResult -> handleBattleResult(
                        viewState.winner,
                        viewState.loser
                    )

                    is ViewState.BattleRematch -> startRematch()
                }
            }
        }
    }

    private fun startRematch() {
        binding.btnRematch.hide()
        if (viewModel.userRole == BattleViewModel.UserRole.OWNER_BROADCAST || viewModel.userRole == BattleViewModel.UserRole.OPPONENT_BROADCAST) {
            binding.ivShare.show()
        }
        binding.btnRematch.hide()
        binding.llResultOpponent.hide()
        binding.llResultOwner.hide()
        Log.d(ContentValues.TAG, "startRematch: Timer")
        onInitialTimerFinish()
        binding.ownerGift.text = "0"
        binding.opponentGift.text = "0"

        // After updating, get the latest totals
        val updatedOpponentTotalGifts = binding.opponentGift.text.toString().toInt()
        val updatedOwnerTotalGifts = binding.ownerGift.text.toString().toInt()

        // Update CardViews' background colors based on who has more gifts
        binding.cardView7.setCardBackgroundColor(
            if (updatedOpponentTotalGifts > updatedOwnerTotalGifts) Color.parseColor("#f95050")
            else Color.parseColor("#388181A4")
        )
        binding.cvGift.setCardBackgroundColor(
            if (updatedOwnerTotalGifts > updatedOpponentTotalGifts) Color.parseColor("#f95050")
            else Color.parseColor("#388181A4")
        )

        //if (viewModel.userRole == BattleViewModel.UserRole.OWNER_BROADCAST) {
            /*viewModel.sendBattleRematchdMessage({
                lifecycleScope.launch(Dispatchers.Main) {

                }
            })*/
        //}
    }

    private fun handleAudienceLeave(message: String?) {

        viewModel.sendAudianceLeaveMessage("" + args.inviterId!!.toInt())
        findNavController().navigate(R.id.homeFragment)
    }
    private fun handleBattleDetailsLoaded(battleDetails: BattleDetailsResponse?) {
        Log.d(TAG, "Battle details loaded")
        if (battleDetails != null) {
            setDetailsUI(battleDetails)
            if (viewModel.userRole == BattleViewModel.UserRole.AUDIENCE && args.inviterId != null)
                viewModel.joinLive(args.channelName, args.inviterId!!.toInt(),
                    getString(R.string.user_join))
        } else {
            Log.d(TAG, "Battle details are null")
            showMessage("Battle details are null")
        }
    }

    private fun handleUserJoined(uid: Int, message: String) {

//        Toast.makeText(context, "uid...."+uid, Toast.LENGTH_SHORT)
//            .show()


        if (opponetUesrID == 0) {
            opponetUesrID = uid;
            loadFollowers()
        }

        lifecycleScope.launch(Dispatchers.Main) {
            viewModel.setupRemoteVideo(
                requireContext(),
                uid,
                binding.videoView,
                binding.opponentVideoView
            )
        }

        if (viewModel.userRole == BattleViewModel.UserRole.OWNER_BROADCAST || viewModel.userRole == BattleViewModel.UserRole.OPPONENT_BROADCAST) {
            val battleDetails = viewModel.latestBattleDetails?.activeChannels
            if (uid == battleDetails?.opponent || uid == battleDetails?.owner_user_id && countDownTimer == null) {

                binding.btnStartBattle.isEnabled = true
                binding.tvCountdown.text = battleWaitingTimer
                startCountdownTimer(30 * 1000)
                viewModel.sendBattleStartMessage(battleWaitingTimer, 0)
            }
        }
    }

    private val gson: Gson by lazy {
        GsonBuilder()
            .registerTypeAdapter(
                UnifiedStreamMessage::class.java,
                UnifiedStreamMessageDeserializer()
            )
            .create()
    }

    private fun displayMessage(jsonMessage: String) {
        Log.d(TAG, "Displaying message: $jsonMessage")

        //snackBar(jsonMessage)
        val jsonObject = jsonMessage.removePrefix("txt:\"").removeSuffix("\"")

        try {
            when (val message = gson.fromJson(jsonObject, UnifiedStreamMessage::class.java)) {
                is UnifiedStreamMessage.TextMessage -> displayTextMessage(message)
                is UnifiedStreamMessage.GiftMessage -> displayGiftMessage(message)
                is UnifiedStreamMessage.AudienceMessage -> displayAudienceMessage(message)
                is UnifiedStreamMessage.BattleStartMessage -> showBattleTimer(message)
                is UnifiedStreamMessage.BattleEndMessage -> endBattleMessage(message)
                is UnifiedStreamMessage.BattleClosedMessage -> closedBattleMessage(message)
                is UnifiedStreamMessage.BattleRematchMessage -> rematchBattleMessage()
                is UnifiedStreamMessage.AudianceLeaveMessage -> audianceLeaveBattleMessage(message)
                is UnifiedStreamMessage.UnKnownMessage -> unknowMessage(message)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun endBattleMessage(message: UnifiedStreamMessage.BattleEndMessage) {

        //findNavController().navigate(R.id.homeFragment)
    }

    private fun closedBattleMessage(message: UnifiedStreamMessage.BattleClosedMessage) {

         findNavController().navigate(R.id.homeFragment)
    }

    private fun audianceLeaveBattleMessage(message: UnifiedStreamMessage.AudianceLeaveMessage) {

        val details = viewModel.latestBattleDetails
        if (message.audianceLeaveMessage?.inviterId == details?.activeChannels?.opponent.toString()) {

            if (binding.opponentCount.text.toString().toInt() > 0) {
                binding.opponentCount.text =
                    (binding.opponentCount.text.toString().toInt() - 1).toString()
            }
            return
        }

        if (message.audianceLeaveMessage?.inviterId == details?.activeChannels?.owner_user_id.toString()) {

            if (binding.ownerCount.text.toString().toInt() > 0 ) {
                binding.ownerCount.text =
                    (binding.ownerCount.text.toString().toInt() - 1).toString()
            }
            return
        }

    }
    private fun rematchBattleMessage() {


        binding.btnRematch.hide()
        if (viewModel.userRole == BattleViewModel.UserRole.OWNER_BROADCAST || viewModel.userRole == BattleViewModel.UserRole.OPPONENT_BROADCAST) {
            binding.ivShare.show()
        }
        binding.btnRematch.hide()
        binding.llResultOpponent.hide()
        binding.llResultOwner.hide()
        //onInitialTimerFinish()

        //Reset Gift
        binding.ownerGift.text = "0"
        binding.opponentGift.text = "0"

        // After updating, get the latest totals
        val updatedOpponentTotalGifts = binding.opponentGift.text.toString().toInt()
        val updatedOwnerTotalGifts = binding.ownerGift.text.toString().toInt()

        // Update CardViews' background colors based on who has more gifts
        binding.cardView7.setCardBackgroundColor(
            if (updatedOpponentTotalGifts > updatedOwnerTotalGifts) Color.parseColor("#f95050")
            else Color.parseColor("#388181A4")
        )
        binding.cvGift.setCardBackgroundColor(
            if (updatedOwnerTotalGifts > updatedOpponentTotalGifts) Color.parseColor("#f95050")
            else Color.parseColor("#388181A4")
        )
        binding.hsvGifts.hide()
        binding.rcvGiftsReceived.removeAllViewsInLayout()
        //Reset Gift End

        //Reset Winner's
        /*binding.opponentWins.text = "0"
        binding.ownerWins.text = "0"

        binding.cvOppWins.hide()
        binding.cvWins.hide()

        setWinColors()*/
        //Reset Winner's End

    }

    private fun unknowMessage(message: UnifiedStreamMessage.UnKnownMessage) {

    }

    private fun showBattleTimer(message: UnifiedStreamMessage.BattleStartMessage) {
        isInitialTimer = message.battleStartMessage!!.isBattleStart == 0
        if (!isInitialTimer) {
            binding.btnStartBattle.hide()
            if (viewModel.userRole == BattleViewModel.UserRole.AUDIENCE) {
                binding.ivGift.show()
            }
        }
        val parts = (message.battleStartMessage!!.time.toString().ifEmpty { battleWaitingTimer }).split(":").map { it.toInt() }
        val millisUntilFinished = (parts[0] * 60 + parts[1]) * 1000L
        startCountdownTimer(millisUntilFinished)

        //ReMatch if timer reset
        if (isRematch) {
            isRematch = false
            rematchBattleMessage()
        }
    }

    private fun displayTextMessage(message: UnifiedStreamMessage.TextMessage) {

        if ((message.textMessage?.inviterId == inviterId)
            || message.textMessage?.inviterId == ""+pref.userId) {

            val messageBinding = ItemBattleChatBinding.inflate(
                LayoutInflater.from(requireContext()),
                binding.rcvBattleChat,
                false
            )

            messageBinding.tvUsername.text = message.textMessage?.sender
            messageBinding.tvUsernameChat.text = message.textMessage?.text
            messageBinding.ivUserReceived.loadImageFromUrlForChat(message.textMessage?.photo)
            binding.llNoChat.hide()
            binding.rcvBattleChat.addView(messageBinding.root)
            binding.scrollView.post { binding.scrollView.fullScroll(ScrollView.FOCUS_DOWN) }
        }
    }

    private fun showCounterMessage(message: UnifiedStreamMessage.GiftMessage) {

        var isShown = false
        if ((message.giftMessage?.receiverId != inviterId)) {
            isShown = true
        }

        var counterItem = giftList.find { it.id == message.giftMessage?.id?.toInt() ?: -1}
        counterItem?.let { it1 ->

            val dialog = GiftPopupDialogFragment.newInstance(
                message, it1,
                viewModel.userRole,
                isShown
            ) {
                //showGiftSheet()



                var counterGift = giftList.find { it.id == it1.counter}

                //if (pref.balanceLuv >= (message.giftMessage?.value?.toInt() ?: 0)) {
                if (pref.balanceLuv >= counterGift?.amount?.toInt() ?: 0) {

                    /*val gift = ItemGift(
                message.giftMessage?.value?.toInt(),
                "",
                message.giftMessage?.id?.toInt() ?: 0,
                message.giftMessage?.photo.toString(),
                message.giftMessage?.name.toString(),
                0,
                "")*/

                    counterGift?.let { viewModel.sendGiftMessage(it, inviterId) }
                } else {
                    showMessage(getString(R.string.you_dont_enough_balance))
                }
            }
            dialog.show(childFragmentManager, "GiftPopupDialog")
        }
    }

    private fun displayGiftMessage(message: UnifiedStreamMessage.GiftMessage) {
        val details = viewModel.latestBattleDetails

//        viewModel.sendGift(message.giftMessage?.id!!.toInt(), inviterId!!.toInt(),
          //  message.giftMessage?.value!!.toInt())

        if ((message.giftMessage?.receiverId == inviterId)
            || message.giftMessage?.receiverId == ""+pref.userId
            || message.giftMessage?.senderId == ""+pref.userId) {

            addGiftToView(message)
        }

        //addGiftToView(message)



//        if (viewModel.userRole == BattleViewModel.UserRole.AUDIENCE && (message.giftMessage?.value?.toInt()
//                ?: 0) >= 500) {
        if ((message.giftMessage?.value?.toInt()
                ?: 0) >= 500) {

            if (message.giftMessage?.senderId != ""+pref.userId) {

                showCounterMessage(message)
                /*if ((message.giftMessage?.receiverId == inviterId)
                    || message.giftMessage?.receiverId == ""+pref.userId) {
                    showCounterMessage(message)
                }*/
            }
        }

        val opponentTotalGifts = binding.opponentGift.text.toString().toIntOrNull() ?: 0
        val ownerTotalGifts = binding.ownerGift.text.toString().toIntOrNull() ?: 0

        // Update the total gifts based on the message
        when (message.giftMessage?.receiverId) {
            details?.activeChannels?.opponent.toString() -> {
                binding.opponentGift.text = (opponentTotalGifts + (message.giftMessage?.value?.toInt() ?: 0)).toString()
            }

            details?.activeChannels?.owner_user_id.toString() -> {
                binding.ownerGift.text = (ownerTotalGifts + (message.giftMessage?.value?.toInt() ?: 0)).toString()
            }
        }

        // After updating, get the latest totals
        val updatedOpponentTotalGifts = binding.opponentGift.text.toString().toInt()
        val updatedOwnerTotalGifts = binding.ownerGift.text.toString().toInt()

        // Update CardViews' background colors based on who has more gifts
        binding.cardView7.setCardBackgroundColor(
            if (updatedOpponentTotalGifts > updatedOwnerTotalGifts) Color.parseColor("#f95050")
            else Color.parseColor("#388181A4")
        )
        binding.cvGift.setCardBackgroundColor(
            if (updatedOwnerTotalGifts > updatedOpponentTotalGifts) Color.parseColor("#f95050")
            else Color.parseColor("#388181A4")
        )
    }

    private fun addGiftToView(message: UnifiedStreamMessage.GiftMessage) {
        val giftBinding = ItemStreamGiftBinding.inflate(layoutInflater)
        giftBinding.apply {
            ivImage.loadImageFromUrl(message.giftMessage?.photo)
            tvName.text = message.giftMessage?.name
            tvName.text = message.giftMessage?.senderName
            tvLuv.text = message.giftMessage?.value.toString()
        }
        binding.rcvGiftsReceived.addView(giftBinding.root, 0)
        if(!binding.hsvGifts.isVisible) {
            binding.hsvGifts.show()
        }
    }

    private fun displayAudienceMessage(message: UnifiedStreamMessage.AudienceMessage) {
        Log.d(TAG, "setting audience count")
        if (viewModel.userRole == BattleViewModel.UserRole.OWNER_BROADCAST && countDownTimer != null) {
            viewModel.sendBattleStartMessage(
                binding.tvCountdown.text.toString(),
                if (isInitialTimer) 0 else 1
            )
        }

        val details = viewModel.latestBattleDetails
        if (message.audienceMessage?.inviterId == details?.activeChannels?.opponent.toString()) {
            binding.opponentCount.text =
                (binding.opponentCount.text.toString().toInt() + 1).toString()
            return
        }
        if (message.audienceMessage?.inviterId == details?.activeChannels?.owner_user_id.toString()) {
            binding.ownerCount.text = (binding.ownerCount.text.toString().toInt() + 1).toString()
            return
        }
    }

    private fun setupRoleViews(userRole: BattleViewModel.UserRole) {
        Log.d(TAG, "Setting up views for role: $userRole")

        if (userRole == BattleViewModel.UserRole.AUDIENCE) {
            binding.btnStartBattle.hide()
            binding.ivShare.hide()
        } else {
            binding.ivShare.show()
            if (userRole == BattleViewModel.UserRole.OWNER_BROADCAST) {
                binding.btnStartBattle.show()
            } else {
                binding.btnStartBattle.hide()
            }

        }

        lifecycleScope.launch(Dispatchers.Main) {
            when (userRole) {
                BattleViewModel.UserRole.OWNER_BROADCAST -> {
                    val localVideoCanvas = viewModel.setupLocalVideo(requireContext())
                    binding.videoView.addView(localVideoCanvas.view)
                }

                BattleViewModel.UserRole.OPPONENT_BROADCAST -> {
                    val localVideoCanvas = viewModel.setupLocalVideo(requireContext())
                    binding.opponentVideoView.addView(localVideoCanvas.view)
                }

                BattleViewModel.UserRole.AUDIENCE -> {}
            }
        }
    }

    private fun loadGifts() {
        giftsViewModel.getGifts().observe(viewLifecycleOwner) { resource ->
            if (resource.status == Status.SUCCESS) {
                resource.data?.list?.let {
                    giftList.clear()
                    giftList.addAll(it)
                }
            } else if (resource.status == Status.ERROR) {
                showMessage(resource.message)
            }
        }
    }

    private fun setupBottomSheet(
        layoutBinding: ViewBinding,
        onShowBehavior: (BottomSheetDialog, BottomSheetBehavior<FrameLayout>) -> Unit
    ) {
        val sheet = BottomSheetDialog(requireContext()).apply {
            setCancelable(true)
            setContentView(layoutBinding.root)
            setOnShowListener {
                val bottomSheet =
                    findViewById<FrameLayout>(com.google.android.material.R.id.design_bottom_sheet)
                onShowBehavior(this, BottomSheetBehavior.from(bottomSheet!!))
            }
        }
        sheet.show()
    }

    private fun showGiftSheet() {
        val viewBinding = BottomSheetYourGiftBinding.inflate(layoutInflater)
        setupBottomSheet(viewBinding) { sheet, behavior ->
            behavior.peekHeight = binding.clChats.height
            behavior.maxHeight = binding.clChats.height
            behavior.state = BottomSheetBehavior.STATE_EXPANDED

            viewBinding.rcvGift.apply {
                setGridLayout(4)
                adapter = BattleGiftAdapter(giftList) { gift ->
                    if (pref.balanceLuv >= (gift.amount ?: 0)) {
                        viewBinding.btnSend.show()
                    } else viewBinding.btnSend.hide()
                    viewBinding.btnSend.setOnClickWithDebounce {
                        sheet.dismiss()
                        viewModel.sendGiftMessage(gift, inviterId)
                    }
                }
            }
            viewBinding.tvBalance.text = pref.balanceLuv.toString()
            viewBinding.imageView6.setOnClickWithDebounce { sheet.dismiss() }
            viewBinding.llBalance.setOnClickWithDebounce {
                sheet.dismiss()
                navigateToRecharge()
            }
        }
    }

    private fun showInviteSheet() {
        val viewBinding = BottomSheetInviteSubscribersBinding.inflate(layoutInflater)

        setupBottomSheet(viewBinding) { sheet, behavior ->
            behavior.state = BottomSheetBehavior.STATE_EXPANDED
            viewBinding.imageView6.setOnClickListener { sheet.dismiss() }
            viewBinding.setupInviteAdapter(sheet)
        }
    }

    private fun showTopGiftsSheet(uid: Int) {
        val viewBinding = SheetTopGiftersBinding.inflate(layoutInflater)

        setupBottomSheet(viewBinding) { sheet, behavior ->
            behavior.state = BottomSheetBehavior.STATE_EXPANDED
            viewBinding.imageView6.setOnClickListener { sheet.dismiss() }
            viewBinding.setupGiftersAdapter(uid)
        }
    }

    private fun SheetTopGiftersBinding.setupGiftersAdapter(uid: Int) {

        viewModel.getTopGifts(args.channelName, uid) {
            rcvGifters.apply {
                setVerticalLayout()
                adapter = TopGiftersAdapter(it.message)
            }
        }
    }

    private fun loadFollowers() {
        userViewModel.getFollowers("").observe(viewLifecycleOwner) { resource ->
            if (resource.status == Status.SUCCESS) {
                resource.data?.let {
                    followerList.clear()

                    for (user in it.list) {
                        val userIds = user.id ?: user.user_id
                        if (opponetUesrID == userIds) {

                        } else {
                            followerList.add(user)
                        }
                    }
                    //followerList.addAll(it.list)
                }
            } else if (resource.status == Status.ERROR) {
                showMessage(resource.message)
            }
        }
    }

    private fun BottomSheetInviteSubscribersBinding.setupInviteAdapter(sheet: BottomSheetDialog) {
        val subscribersAdapter = InviteFollowersAdapter(followerList) { selectedList ->
            if (selectedList.isNotEmpty()) btnSend.show() else btnSend.hide()
            btnSend.setOnClickWithDebounce {
                 sheet.dismiss()
                inviteSelectedFollowers(selectedList)
            }
        }
        rcvUsers.apply {
            setVerticalLayout()
            adapter = subscribersAdapter
        }

        edtSearch.doAfterTextChanged { subscribersAdapter.filter(it.toString()) }
    }

    private fun inviteSelectedFollowers(list: List<UserDetails>) {
        val userIds = list.mapNotNull { it.id ?: it.user_id }
        if (userIds.isNotEmpty()) {
            inviteFollowers(userIds)
        }
    }

    private fun inviteFollowers(userIds: List<Int>) {
        viewModel.shareInvite(args.channelName, userIds).observe(viewLifecycleOwner) { resource ->
            if (resource.status == Status.SUCCESS) {
                showMessage(getString(R.string.invite_sent))
            } else if (resource.status == Status.ERROR) {
                showMessage(resource.message)
            }
        }
    }

    private fun navigateToRecharge() {
        findNavController().navigate(R.id.rechargeFragment)
    }

    private fun startCountdownTimer(duration: Long) {
        binding.llResultOwner.hide()
        binding.llResultOpponent.hide()
        binding.tvTimerTitle.text = if (isInitialTimer) getString(R.string.batte_start) else getString(R.string.time_left)

        if (!isInitialTimer) {
            binding.pbBattle.max = 300000 //duration.toInt()
            binding.pbBattle.progress = binding.pbBattle.max
        }

         countDownTimer?.cancel()
        countDownTimer = object : CountDownTimer(duration, 1000) {
            override fun onTick(millisUntilFinished: Long) {
                val minutes = (millisUntilFinished / 1000) / 60
                val seconds = (millisUntilFinished / 1000) % 60
                val time = String.format("%02d:%02d", minutes, seconds)
                binding.tvCountdown.text = time

                if (!isInitialTimer) {
                    binding.pbBattle.progress = millisUntilFinished.toInt()
                }
            }

            override fun onFinish() {
                if (isInitialTimer) {
                    Log.d(ContentValues.TAG, "isInitialTimer: Timer")
                    onInitialTimerFinish()
                } else {
                    onFinalTimerFinish()
                }
            }
        }.start()
    }

    private fun onInitialTimerFinish() {
        if (viewModel.userRole == BattleViewModel.UserRole.OWNER_BROADCAST) {
            viewModel.sendBattleStartMessage(
                battleTimer,
                1
            )
        }
        binding.hsvGifts.hide()
        binding.rcvGiftsReceived.removeAllViewsInLayout()
    }

    private fun onFinalTimerFinish() {
        binding.ivGift.hide()
        if (viewModel.userRole == BattleViewModel.UserRole.OWNER_BROADCAST) {
            binding.ivShare.hide()
            binding.btnRematch.apply {
                show()
                setOnClickWithDebounce {
                    viewModel.battleRematch()
                }
            }
        }

        isRematch = true
        // End battle - show results when the 5-minute timer ends
        binding.tvTimerTitle.text = getString(R.string.battle_ended)
        binding.tvCountdown.text = ""

        viewModel.battleResult(args.channelName)
        //viewModel.endBattle(args.channelName)
    }

    private fun handleBattleResult(winner: Int, loser: Int) {

        if(viewModel.userRole == BattleViewModel.UserRole.OWNER_BROADCAST) {
            viewModel.sendBattleEndMessage(winner)
        }

        val owner = viewModel.latestBattleDetails?.activeChannels?.owner_user_id
        val opponent = viewModel.latestBattleDetails?.activeChannels?.opponent

        binding.apply {
            if (winner == owner && loser == opponent) {
                llResultOwner.show()
                llResultOpponent.show()
                cvOppWins.show()
                cvWins.show()
                ownerWins.text = (ownerWins.text.toString().toInt() + 1).toString()

                ivWinnerOwner.show()
                tvWinnerOwner.text =
                    if (viewModel.uid == owner) getString(R.string.you_won) else getString(R.string.winner)
                tvWinnerOwner.setTextColor(
                    resources.getColor(
                        R.color.redgradstart,
                        resources.newTheme()
                    )
                )

                ivWinnerOpponent.hide()
                tvWinnerOpponent.text =
                    if (viewModel.uid == opponent) getString(R.string.you_lost) else getString(R.string.loser)
                tvWinnerOpponent.setTextColor(
                    resources.getColor(
                        R.color.black,
                        resources.newTheme()
                    )
                )

                setWinColors()
                return
            }

            if (winner == opponent && loser == owner) {
                llResultOwner.show()
                llResultOpponent.show()
                cvOppWins.show()
                cvWins.show()
                opponentWins.text = (opponentWins.text.toString().toInt() + 1).toString()

                ivWinnerOwner.hide()
                tvWinnerOwner.text =
                    if (viewModel.uid == owner) getString(R.string.you_lost) else getString(R.string.loser)
                tvWinnerOwner.setTextColor(resources.getColor(R.color.black, resources.newTheme()))

                ivWinnerOpponent.show()
                tvWinnerOpponent.text =
                    if (viewModel.uid == opponent) getString(R.string.you_won) else getString(R.string.winner)
                tvWinnerOpponent.setTextColor(
                    resources.getColor(
                        R.color.redgradstart,
                        resources.newTheme()
                    )
                )

                setWinColors()
                return
            }
        }
    }

    private fun setWinColors() {
        val updatedOpponentTotalGifts = binding.opponentWins.text.toString().toInt()
        val updatedOwnerTotalGifts = binding.ownerWins.text.toString().toInt()

        binding.cvOppWins.setCardBackgroundColor(
            if (updatedOpponentTotalGifts > updatedOwnerTotalGifts) Color.parseColor("#f95050")
            else Color.parseColor("#388181A4")
        )
        binding.cvWins.setCardBackgroundColor(
            if (updatedOwnerTotalGifts > updatedOpponentTotalGifts) Color.parseColor("#f95050")
            else Color.parseColor("#388181A4")
        )
    }

    override fun onPause() {
        super.onPause()
//        countDownTimer?.cancel()
        activity?.getWindow()?.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
    }

    override fun onResume() {
        super.onResume()
        activity?.getWindow()?.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);

    }

    override fun onDestroyView() {
        super.onDestroyView()
        viewModel.onDestroy()
        countDownTimer?.cancel()
    }

    private fun makeOwnerFullScreen() {
        binding.apply {
            cvOwner.setOnClickListener {
                revertOwnerFullScreen()
            }
            clChats.hide()
            cvOpponent.hide()
            cvInfoOpponent.hide()
            constraintLayout7.hide()
        }
    }

    private fun revertOwnerFullScreen() {
        binding.apply {
            cvOwner.setOnClickListener {
                makeOwnerFullScreen()
            }
            clChats.show()
            cvOpponent.show()
            cvInfoOpponent.show()
             constraintLayout7.show()
        }
    }

    private fun makeOpponentFullScreen() {
        binding.apply {
            cvOpponent.setOnClickListener {
                revertOpponentFullScreen()
            }
            clChats.hide()
            cvOwner.hide()
            cvInfo.hide()
            constraintLayout7.hide()
        }
    }

    private fun revertOpponentFullScreen() {
        binding.apply {
            cvOpponent.setOnClickListener {
                makeOpponentFullScreen()
            }
            clChats.show()
            cvOwner.show()
            cvInfo.show()
            constraintLayout7.show()
        }
    }

}
