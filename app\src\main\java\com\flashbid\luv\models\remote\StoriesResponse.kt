package com.flashbid.luv.models.remote

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

data class StoriesResponse(
    val error: <PERSON><PERSON><PERSON>,
    val pagintion: Pagintion,
    val stories: Stories
) {
    data class Pagintion(
        val limit: Int,
        val page: Int,
        val total_count: Int
    )

    data class Stories(
        val my_stories: ArrayList<ArrayList<OtherStory>>,
        val other_stories: ArrayList<ArrayList<OtherStory>>
    )
}

@kotlinx.parcelize.Parcelize
data class OtherStory(
    val create_at: String = "",
    val duration: String = "",
    val gift_receiver_id: Int = 0,
    val gift_receiver_image: String? = null,
    val gift_receiver_name: String = "",
    val gift_sender_id: Int = 0,
    val gift_sender_image: String = "",
    val gift_sender_name: String = "",
    val id: Int = 0,
    val is_following_gift_receiver: Int = 0,
    val is_following_gift_sender: Int = 0,
    val report_count: Int = 0,
    val story_creator_id: Int = 0,
    val story_receiver_id: String? = null,
    val story_type: String = "",
    val transaction_id: Int = 0,
    val update_at: String = "",
    val url: String = "",
    val view_count: Int = 0,
    val views: String? = null
): Parcelable
