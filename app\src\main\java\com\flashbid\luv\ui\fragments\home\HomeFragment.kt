package com.flashbid.luv.ui.fragments.home

import android.content.ContentValues.TAG
import android.os.Bundle
import android.util.Log
import android.view.View
import android.view.ViewTreeObserver
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.FragmentNavigatorExtras
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager.widget.ViewPager
import com.flashbid.luv.R
import com.flashbid.luv.adapter.CommunityTransactionsAdapter
import com.flashbid.luv.adapter.StoryAdapter
import com.flashbid.luv.adapter.UserHistoryAdapter
import com.flashbid.luv.adapter.ViewPagerAdapter
import com.flashbid.luv.data.local.AppPreferences
import com.flashbid.luv.databinding.FragmentHomeBinding
import com.flashbid.luv.extensions.attachToPagerAndShow
import com.flashbid.luv.extensions.hide
import com.flashbid.luv.extensions.invisible
import com.flashbid.luv.extensions.setOnClickWithDebounce
import com.flashbid.luv.extensions.setVisible
import com.flashbid.luv.extensions.setupAdapter
import com.flashbid.luv.extensions.show
import com.flashbid.luv.extensions.snackBar
import com.flashbid.luv.extensions.viewBinding
import com.flashbid.luv.models.remote.OtherStory
import com.flashbid.luv.network.Status
import com.flashbid.luv.util.Constants
import com.flashbid.luv.util.HistoryMapping
import com.flashbid.luv.util.loadImageFromUrl
import com.flashbid.luv.viewmodels.CodesViewModel
import com.flashbid.luv.viewmodels.CommunityViewModel
import com.flashbid.luv.viewmodels.StoryViewModel
import com.flashbid.luv.viewmodels.TransactionViewModel
import com.flashbid.luv.viewmodels.UserViewModel
import com.google.android.material.bottomsheet.BottomSheetBehavior
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.util.Calendar

class HomeFragment : Fragment(R.layout.fragment_home) {

    companion object {
        private const val visibleThreshold = 8
    }

    private val pageSize = 15
    private var isLoadingMore = false

    private val pageSizeHistory = 15
    private var isLoadingMoreHistory = false

    private var isViewCreated = false
    private var isOnPasue = false

    private val binding by viewBinding(FragmentHomeBinding::bind)
    private val viewModel: TransactionViewModel by viewModel()
    private val userViewModel: UserViewModel by viewModel()
    private val codeViewModel: CodesViewModel by viewModel()
    private val storyViewModel: StoryViewModel by viewModel()
    private val communityViewModel: CommunityViewModel by viewModel()
    private val pref by inject<AppPreferences>()
    private val listDate: MutableList<String> = arrayListOf("1", "2", "3")
    private val tranxAdapter = CommunityTransactionsAdapter(this::onUserClick, this::onStoryClick)
    private val historyAdapter = UserHistoryAdapter(this::onHistoryClick,  pref.userId ?: 0)
    private val storyAdapter = StoryAdapter(this::onStoryClick)
    private val viewpagerAdapter by lazy { ViewPagerAdapter(this, listDate) }
    private lateinit var viewTreeObserver: ViewTreeObserver.OnGlobalLayoutListener
    private var myStories: ArrayList<ArrayList<OtherStory>> = ArrayList()

    private fun onStoryClick(model: ArrayList<OtherStory>?, sharedView: View) {
        if (model.isNullOrEmpty()) {
            snackBar("Story data not found")
            return
        }

        val extras = FragmentNavigatorExtras(
            sharedView to "shared_story_transition"
        )
        findNavController().navigate(
            HomeFragmentDirections.actionHomeFragmentToUserStoryFragment(
                model.toTypedArray()
            ), extras
        )
    }

    private fun onHistoryClick(userId: Int, action: Int, senderId: Int) {
        val directions = when (action) {
            HistoryMapping.ACTION.RECEIVED -> HomeFragmentDirections.actionHomeFragmentToGiftReceivedDetailsFragment(
                userId.toString()
            )

            HistoryMapping.ACTION.SENT -> HomeFragmentDirections.actionHomeFragmentToGiftSentDetailsFragment(
                userId.toString()
            )

            HistoryMapping.ACTION.RECHARGE -> HomeFragmentDirections.actionHomeFragmentToRechargeDetailsFragment(
                userId.toString()
            )

            HistoryMapping.ACTION.WITHDRAW -> HomeFragmentDirections.actionHomeFragmentToWithdrawDetailsFragment(
                userId.toString()
            )

            HistoryMapping.ACTION.CHEST -> HomeFragmentDirections.actionHomeFragmentToOpenedLuvChestFragment(
                userId.toString()
            )

            HistoryMapping.ACTION.CRATE -> HomeFragmentDirections.actionHomeFragmentToOpenedLuvCrateFragment(
                userId.toString(),
                senderId.toString(),
                "0"
            )

            HistoryMapping.ACTION.QUEST_CRATE -> HomeFragmentDirections.actionHomeFragmentToQuestCrateFragment(
                userId.toString(),
                senderId.toString(),
                "0"
            )

            HistoryMapping.ACTION.QUEST_REWARD -> HomeFragmentDirections.actionHomeFragmentToQuestCrateFragment(
                userId.toString(),
                senderId.toString(),
                "0"
            )

            HistoryMapping.ACTION.IS_BONUS -> HomeFragmentDirections.actionHomeFragmentToQuestCrateFragment(
                userId.toString(),
                senderId.toString(),
                "0"
            )

            HistoryMapping.ACTION.REFERRAL -> HomeFragmentDirections.actionHomeFragmentToReferralAwardFragment(
                userId.toString()
            )

            HistoryMapping.ACTION.REFERRAL_REWARD -> HomeFragmentDirections.actionHomeFragmentToReferralAwardFragment(
                userId.toString()
            )

            HistoryMapping.ACTION.SEND_LUV -> HomeFragmentDirections.actionHomeFragmentToSendLuvGetLuvFragment(
                userId.toString()
            )

            HistoryMapping.ACTION.DROP_LUV -> HomeFragmentDirections.actionHomeFragmentToLuvDropFragment(
                userId.toString()
            )

            HistoryMapping.ACTION.CHEST_QR -> HomeFragmentDirections.actionHomeFragmentToMyChestQrFragment(
                userId.toString()
            )

            HistoryMapping.ACTION.LUV_REVEIED -> HomeFragmentDirections.actionHomeFragmentToLuvDropReceivedDetailsFragment(
                userId.toString()
            )
            HistoryMapping.ACTION.OPEN_CRATE -> HomeFragmentDirections.actionHomeFragmentToQuestCrateFragment(
                userId.toString(),
                senderId.toString(),
                "1"
            )

            else -> return
        }
        findNavController().navigate(directions)
    }

    private fun onUserClick(userId: Int) {
        println("this is clicked")
        val directions = HomeFragmentDirections.actionHomeFragmentToUserProfileFragment(userId)
        findNavController().navigate(directions)
    }

    private fun setGreetingText() {
        val calendar = Calendar.getInstance()
        val greeting = when (calendar.get(Calendar.HOUR_OF_DAY)) {
            in 0..3 -> getString(R.string.evening)
            in 4..11 -> getString(R.string.morning)
            in 12..15 -> getString(R.string.afternoon)
            in 16..20 -> getString(R.string.evening)
            else -> getString(R.string.evening)
        }
        binding.textView20.text = greeting
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        if (!pref.isLoggedIn) {
            return
        }

        binding.ivPerson.loadImageFromUrl(pref.photo)
        binding.imageView4.loadImageFromUrl(pref.photo)
        binding.textView21.text = buildString {
            append(pref.firstName)
            append(" ")
            append(pref.lastName)
        }

        setupViewPager()
        setupRecyclerViews()
        initBottomSheetBehavior()
        initClickListeners()

        setGreetingText()

        /*binding.rcvTransactionsSwipe.setOnRefreshListener {
            callOnRefresh()
        }*/
    }

    private fun setupViewPager() {
        binding.viewPager.apply {
            adapter = viewpagerAdapter
            addOnPageChangeListener(createPageChangeListener())
        }
        binding.indicator.attachToPagerAndShow(binding.viewPager)
    }

    private fun createPageChangeListener() = object : ViewPager.OnPageChangeListener {
        override fun onPageScrolled(
            position: Int,
            positionOffset: Float,
            positionOffsetPixels: Int
        ) {
        }

        override fun onPageSelected(position: Int) {
            if (isOnPasue) {
                handlePageSelection(position)
            }
            isOnPasue = false
        }

        override fun onPageScrollStateChanged(state: Int) {

            if (state == ViewPager.SCROLL_STATE_IDLE) {
                handlePageSelection(binding.viewPager.currentItem)
            }
        }
    }

    private fun handlePageSelection(position: Int) {
        when (position) {
            1 -> toggleVisibility(
                showButtons = true,
                showTransactions = false,
                showHistory = historyAdapter.itemCount > 0,
                showStories = false
            )

            2 -> toggleVisibility(
                showButtons = false,
                showTransactions = false,
                showHistory = historyAdapter.itemCount > 0,
                showStories = false
            )

            else -> toggleVisibility(
                showButtons = false,
                showTransactions = tranxAdapter.itemCount > 0,
                showHistory = false,
                showStories = true
            )
        }
    }

    private fun toggleVisibility(
        showButtons: Boolean,
        showTransactions: Boolean,
        showHistory: Boolean,
        showStories: Boolean
    ) {
        binding.llButtons.setVisible(showButtons)
        binding.rcvTransactions.setVisible(showTransactions)
        //binding.rcvTransactionsSwipe.setVisible(showTransactions)
        binding.llNoItems.setVisible(!showTransactions && !showHistory)
        binding.rcvHistory.setVisible(showHistory)
        binding.layoutName.setVisible(!showStories)
        binding.layoutStories.setVisible(showStories)
    }

    private fun setupRecyclerViews() {
        binding.rcvTransactions.setupAdapter(tranxAdapter, vertical = true)
        binding.rcvStorie.setupAdapter(storyAdapter, vertical = false)
        binding.rcvHistory.setupAdapter(historyAdapter, vertical = true)
    }

    private fun initBottomSheetBehavior() {
        val behavior = BottomSheetBehavior.from(binding.bottomSheet)
        viewTreeObserver = ViewTreeObserver.OnGlobalLayoutListener {
            val remainingSpace = binding.root.height - binding.constraintLayout2.height
            behavior.peekHeight = remainingSpace - 20
        }
        binding.constraintLayout2.viewTreeObserver.addOnGlobalLayoutListener(viewTreeObserver)
    }

    private fun initClickListeners() {
        binding.btnSendLuv.setOnClickWithDebounce {
            findNavController().navigate(R.id.action_homeFragment_to_sendLuvFragment)
        }
        binding.btnRecharge.setOnClickWithDebounce {
            findNavController().navigate(R.id.action_homeFragment_to_rechargeFragment)
        }
        binding.btnWithdraw.setOnClickWithDebounce {
            findNavController().navigate(R.id.action_homeFragment_to_withdrawPaymentSelectFragment)
        }
        binding.constraintLayout6.setOnClickWithDebounce {

            var hasMyStories = false
            if (!myStories.isNullOrEmpty()) {
                if (myStories != null) {
                    hasMyStories = true
                }
            }

            val extras = FragmentNavigatorExtras(
                it to "shared_story_transition"
            )

            if (hasMyStories) {
                //com.flashbid.luv.models.remote.OtherStory[]
                findNavController().navigate(
                    HomeFragmentDirections.actionHomeFragmentToUserStoryFragment(
                        myStories.first().toTypedArray()
                    ), extras
                )
            } else {


                findNavController().navigate(
                    HomeFragmentDirections.actionHomeFragmentToMyProfileFragment(),
                    extras
                )
            }
        }
        binding.cvImageView.setOnClickWithDebounce {
            val extras = FragmentNavigatorExtras(
                it to "shared_story_transition"
            )
            findNavController().navigate(
                HomeFragmentDirections.actionHomeFragmentToMyProfileFragment(),
                extras
            )
        }
    }

    fun callOnRefresh() {
        lifecycleScope.launch {
            getUserDetails()
            getUserCode()
            getTransactions(1)
            getUserHistory(1)
            getCommunityStats()
            getBalance()
            getUserStories()
        }
    }
    override fun onPause() {
        super.onPause()
        isOnPasue = true
    }

    override fun onResume() {
        super.onResume()
        if (!pref.isLoggedIn) {
            return
        }

        callOnRefresh()
        if (pref.fcmToken != null) {
            //Constants.FCM_TOKEN.postValue(pref.fcmToken)
        }
    }

    override fun onStop() {
        binding.constraintLayout2.viewTreeObserver.removeOnGlobalLayoutListener(viewTreeObserver)
        super.onStop()
    }

    private fun getUserStories() {
        storyViewModel.getStory().observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(
                    it.message ?: getString(
                        R.string.something_went_wrong,
                        getString(R.string.try_again)
                    )
                )

                Status.LOADING -> {}
                Status.SUCCESS -> {
                    val myStories =
                        it.data?.stories?.my_stories?.filter { filtered -> filtered.isNotEmpty() }
                            ?.toCollection(ArrayList())

                    val otherStories =
                        it.data?.stories?.other_stories?.filter { filtered -> filtered.isNotEmpty() }
                            ?.toCollection(ArrayList())
                    if (otherStories.isNullOrEmpty()) binding.rcvStorie.hide()
                    else {
                        storyAdapter.refresh(otherStories)
                        binding.rcvStorie.show()
                    }

                    if (!myStories.isNullOrEmpty()) {
                        if (myStories != null) {
                            this.myStories = myStories
                            binding.constraintLayout3.show()
                        } else {
                            binding.constraintLayout3.invisible()
                        }
                    } else {
                        binding.constraintLayout3.invisible()
                    }
                }
            }
        }
    }

    private fun getBalance() {
        userViewModel.getBalance().observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(
                    it.message ?: getString(
                        R.string.something_went_wrong,
                        getString(R.string.try_again)
                    )
                )

                Status.LOADING -> {}
                Status.SUCCESS -> {
                    val data = it.data?.model
                    viewpagerAdapter.setBalance(
                        data?.heart ?: 0L,
                        data?.diamond ?: 0,
                        data?.usd ?: "0"
                    )
                    pref.setBalance(
                        data?.heart ?: 0L,
                        data?.diamond ?: 0,
                        data?.usd ?: "0"
                    )
                }
            }
        }
    }

    private fun getTransactions(page: Int) {
        if (isLoadingMore) return

        isLoadingMore = true
        viewModel.getCommunityTransactions(page, pageSize).observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> {
                    isLoadingMore = false
                    snackBar(
                        it.message ?: getString(
                            R.string.something_went_wrong,
                            "Error getting transactions"
                        )
                    )
                }

                Status.LOADING -> {}
                Status.SUCCESS -> {
                    isLoadingMore = false
                    val newTransactions = it.data?.list ?: emptyList()
                    if (page == 1) {
                        setupScrollListener()
                        tranxAdapter.refresh(newTransactions)
                    } else {
                        tranxAdapter.appendTransactions(newTransactions)
                    }

                    if (tranxAdapter.itemCount == 0) {
                        binding.llNoItems.show()
                        binding.rcvTransactions.hide()
                        //binding.rcvTransactionsSwipe.hide()
                    } else {
                        if (binding.viewPager.currentItem == 0) {
                            binding.rcvTransactions.show()
                            //binding.rcvTransactionsSwipe.show()
                        }
                        binding.llNoItems.hide()
                    }

                    //binding.viewPager.setCurrentItem(0, true)
                    if (!isViewCreated) {
                        binding.viewPager.setCurrentItem(0, true)
                        isViewCreated = true
                    }
                }
            }

            //binding.rcvTransactionsSwipe.isRefreshing = false
        }
    }

    private fun setupScrollListener() {

        binding.rcvTransactions.removeOnScrollListener(object : RecyclerView.OnScrollListener() {

        })

        binding.rcvTransactions.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                val layoutManager = recyclerView.layoutManager as LinearLayoutManager
                val totalItemCount = layoutManager.itemCount
                val lastVisibleItem = layoutManager.findLastVisibleItemPosition()

                if (!isLoadingMore && totalItemCount <= (lastVisibleItem + visibleThreshold)) {
                    val nextPage = (totalItemCount / pageSize) + 1
                    getTransactions(nextPage)
                }
            }
        })
    }

    private fun setupHistoryScrollListener() {
        binding.rcvHistory.removeOnScrollListener(object : RecyclerView.OnScrollListener() {

        })

        binding.rcvHistory.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                val layoutManager = recyclerView.layoutManager as LinearLayoutManager
                val totalItemCount = layoutManager.itemCount
                val lastVisibleItem = layoutManager.findLastVisibleItemPosition()

                if (!isLoadingMoreHistory && totalItemCount <= (lastVisibleItem + visibleThreshold)) {
                    val nextPage = (totalItemCount / pageSizeHistory) + 1
                    getUserHistory(nextPage)
                }
            }
        })
    }

    private fun getUserHistory(page: Int) {
        if (isLoadingMoreHistory) return

        isLoadingMoreHistory = true

        userViewModel.getUserHistory(page, pageSizeHistory).observe(viewLifecycleOwner) {
            when (it.status) {

                Status.ERROR -> {
                    isLoadingMoreHistory = false
                    snackBar(
                        it.message ?: getString(
                            R.string.something_went_wrong,
                            "Error getting history"
                        )
                    )
                }

                Status.LOADING -> {}
                Status.SUCCESS -> {
                    isLoadingMoreHistory = false

                    val newTransactions = it.data?.message ?: ArrayList()
                    if (page == 1) {
                        setupHistoryScrollListener()
                        historyAdapter.refresh(newTransactions)
                    } else {
                        historyAdapter.appendTransactions(newTransactions)
                    }

                    //historyAdapter.refresh(it.data?.message ?: ArrayList())
                }
            }
        }
    }

    private fun getUserCode() {
        codeViewModel.getUserCodes().observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(
                    it.message ?: getString(
                        R.string.something_went_wrong,
                        "Error getting user code"
                    )
                )

                Status.LOADING -> {}
                Status.SUCCESS -> {
                    val data = it.data?.message
                    Log.d(TAG, "getUserCode: ${data.toString()}")
                    viewpagerAdapter.setQrCodes(data?.receive_code ?: "", data?.gift_code)
                }
            }
        }
    }

    private fun getCommunityStats() {
        communityViewModel.getCommunityStats().observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(
                    it.message ?: getString(
                        R.string.something_went_wrong,
                        "Error getting stats"
                    )
                )

                Status.LOADING -> {}
                Status.SUCCESS -> {
                    val data = it.data?.model
                    viewpagerAdapter.setCommunityStats(
                        data?.love_sent ?: 0,
                        data?.top_gift_amount ?: 0,
                        data?.top_gifter ?: "",
                        data?.top_gifter_image ?: "",
                        data?.top_gift,
                        data?.top_gift_image ?: ""
                    )
                }
            }
        }
    }

    private fun getUserDetails() {
        userViewModel.getUserDetails().observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(
                    it.message ?: getString(
                        R.string.something_went_wrong, getString(R.string.try_again)
                    )
                )

                Status.LOADING -> {}
                Status.SUCCESS -> {
                    val data = it.data?.message
                    pref.saveUserData(
                        data?.first_name,
                        data?.last_name,
                        data?.username ?: "",
                        data?.photo,
                        data?.bio,
                        data?.user_id,
                        data?.age,
                        data?.gender,
                        data?.show_following,
                        data?.show_follower
                    )
                    binding.ivPerson.loadImageFromUrl(pref.photo)
                    binding.textView21.text = buildString {
                        append(pref.firstName)
                        append(" ")
                        append(pref.lastName)
                    }
                }
            }
        }
    }

}