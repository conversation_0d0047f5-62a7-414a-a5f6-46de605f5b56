<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/_2sdp"
    app:strokeColor="#f5f5f8"
    app:cardBackgroundColor="#f5f5f8"
    app:cardCornerRadius="@dimen/_5sdp"
    app:cardElevation="0dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/llBackground"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="@dimen/_10sdp">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/ivImage"
            android:layout_width="@dimen/_26sdp"
            android:layout_height="@dimen/_26sdp"
            android:scaleType="centerCrop"
            android:src="@drawable/heart_two"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_5sdp"
            android:maxLines="1"
            android:ellipsize="end"
            android:textSize="12sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@+id/ivImage"
            app:layout_constraintTop_toBottomOf="@+id/ivImage"
            tools:text="gift name" />

        <ImageView
            android:id="@+id/ivHeart"
            android:layout_width="@dimen/_16sdp"
            android:layout_height="@dimen/_16sdp"
            android:src="@drawable/heart_colored"
            android:layout_marginTop="2dp"
            app:layout_constraintStart_toStartOf="@+id/tvName"
            app:layout_constraintTop_toBottomOf="@+id/tvName" />

        <TextView
            android:id="@+id/tvLuv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/gray"
            android:layout_marginStart="5dp"
            android:textSize="12sp"
            app:layout_constraintBottom_toBottomOf="@+id/ivHeart"
            app:layout_constraintStart_toEndOf="@+id/ivHeart"
            app:layout_constraintTop_toBottomOf="@+id/tvName"
            tools:text="100" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>