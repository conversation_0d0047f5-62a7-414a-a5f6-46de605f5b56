<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/parent"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    android:keepScreenOn="true"
    tools:context=".ui.fragments.battle.BattleFragment">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/constraintLayout7"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/ivBack"
            android:layout_width="@dimen/_30sdp"
            android:layout_height="@dimen/_30sdp"
            android:layout_margin="@dimen/_10sdp"
            android:src="@drawable/back_btn"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="@drawable/ic_gift_box,ContentDescription" />

        <LinearLayout
            android:id="@+id/linearLayout7"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintBottom_toBottomOf="@+id/ivBack"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/ivBack">

            <TextView
                android:id="@+id/tvTimerTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/waiting_for_participants"
                android:textColor="#99FFFFFF"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tvCountdown"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_5sdp"
                android:textColor="#FFFFFF"
                android:textSize="14sp" />

        </LinearLayout>

        <ImageView
            android:id="@+id/ivShare"
            android:layout_width="@dimen/_30sdp"
            android:layout_height="@dimen/_30sdp"
            android:layout_marginEnd="@dimen/_14sdp"
            android:src="@drawable/send_btn"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/ivBack"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/ivBack"
            tools:ignore="@drawable/ic_gift_box,ContentDescription" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnRematch"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_10sdp"
            android:padding="0dp"
            android:text="@string/rematch"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/linearLayout7"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/linearLayout7" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/constraintLayout8"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@+id/clChats"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/constraintLayout7"
        app:layout_constraintVertical_weight="5">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clVideos"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toTopOf="@+id/pbBattle"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">


            <androidx.cardview.widget.CardView
                android:id="@+id/cvOwner"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:cardBackgroundColor="@color/black"
                app:cardCornerRadius="@dimen/_16sdp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/cvOpponent"
                app:layout_constraintHorizontal_weight="1"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.5">

                <FrameLayout
                    android:id="@+id/videoView"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="centerCrop" />

            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:id="@+id/cvOpponent"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:cardBackgroundColor="@color/black"
                app:cardCornerRadius="@dimen/_16sdp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_weight="1"
                app:layout_constraintStart_toEndOf="@+id/cvOwner"
                app:layout_constraintTop_toTopOf="parent">

                <FrameLayout
                    android:id="@+id/opponentVideoView"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="centerCrop" />

            </androidx.cardview.widget.CardView>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <com.google.android.material.progressindicator.LinearProgressIndicator
            android:id="@+id/pbBattle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_8sdp"
            android:layout_marginTop="@dimen/_2sdp"
            android:indeterminate="false"
            android:paddingTop="@dimen/_4sdp"
            app:indicatorColor="#757592"
            app:layout_constraintBottom_toTopOf="@+id/cvInfo"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:trackColor="#2B2B36"
            app:trackCornerRadius="@dimen/_20sdp" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cvInfo"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginTop="@dimen/_2sdp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/cvInfoOpponent"
            app:layout_constraintStart_toStartOf="parent">

            <com.google.android.material.imageview.ShapeableImageView
                android:id="@+id/imageView18"
                android:layout_width="@dimen/_18sdp"
                android:layout_height="@dimen/_18sdp"
                android:layout_margin="@dimen/_5sdp"
                android:scaleType="centerCrop"
                android:src="@drawable/vacter"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:shapeAppearance="@style/ShapeAppearance.Material3.Corner.Full" />

            <TextView
                android:id="@+id/tvMyName"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_5sdp"
                android:ellipsize="end"
                android:maxLines="1"
                android:textAlignment="viewStart"
                android:textColor="@color/white"
                android:textSize="12sp"
                app:layout_constraintBottom_toBottomOf="@+id/imageView18"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/imageView18"
                app:layout_constraintTop_toTopOf="@+id/imageView18" />

            <androidx.cardview.widget.CardView
                android:id="@+id/cvWins"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:cardBackgroundColor="#388181A4"
                app:cardCornerRadius="@dimen/_25sdp"
                app:layout_constraintStart_toStartOf="@+id/imageView18"
                app:layout_constraintTop_toTopOf="@+id/cardView6">

                <ImageView
                    android:id="@+id/ivw"
                    android:layout_width="@dimen/_15sdp"
                    android:layout_height="@dimen/_15sdp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="@dimen/_6sdp"
                    android:importantForAccessibility="no"
                    android:padding="1dp"
                    android:src="@drawable/ic_winner_gray" />

                <TextView
                    android:id="@+id/ownerWins"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginVertical="@dimen/_3sdp"
                    android:layout_marginStart="@dimen/_25sdp"
                    android:layout_marginEnd="@dimen/_10sdp"
                    android:text="0"
                    android:textColor="#E6FFFFFF"
                    android:textSize="12sp" />

            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:id="@+id/cardView6"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_2sdp"
                android:layout_marginTop="@dimen/_4sdp"
                app:cardBackgroundColor="#388181A4"
                app:cardCornerRadius="@dimen/_25sdp"
                app:layout_constraintStart_toEndOf="@+id/cvWins"
                app:layout_constraintTop_toBottomOf="@+id/imageView18">

                <ImageView
                    android:id="@+id/tvp"
                    android:layout_width="@dimen/_15sdp"
                    android:layout_height="@dimen/_15sdp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="@dimen/_6sdp"
                    android:importantForAccessibility="no"
                    android:src="@drawable/profile_n" />

                <TextView
                    android:id="@+id/ownerCount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginVertical="@dimen/_3sdp"
                    android:layout_marginStart="@dimen/_25sdp"
                    android:layout_marginEnd="@dimen/_10sdp"
                    android:text="0"
                    android:textColor="#E6FFFFFF"
                    android:textSize="12sp" />

            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:id="@+id/cvGift"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_2sdp"
                app:cardBackgroundColor="#388181A4"
                app:cardCornerRadius="@dimen/_25sdp"
                app:layout_constraintStart_toEndOf="@+id/cardView6"
                app:layout_constraintTop_toTopOf="@+id/cardView6">

                <ImageView
                    android:id="@+id/ivg"
                    android:layout_width="@dimen/_15sdp"
                    android:layout_height="@dimen/_15sdp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="@dimen/_6sdp"
                    android:importantForAccessibility="no"
                    android:src="@drawable/gift_n" />

                <TextView
                    android:id="@+id/ownerGift"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginVertical="@dimen/_3sdp"
                    android:layout_marginStart="@dimen/_25sdp"
                    android:layout_marginEnd="@dimen/_10sdp"
                    android:text="0"
                    android:textColor="#E6FFFFFF"
                    android:textSize="12sp" />

            </androidx.cardview.widget.CardView>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cvInfoOpponent"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_gravity="end"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/cvInfo">

            <com.google.android.material.imageview.ShapeableImageView
                android:id="@+id/imageView20"
                android:layout_width="@dimen/_18sdp"
                android:layout_height="@dimen/_18sdp"
                android:layout_margin="@dimen/_5sdp"
                android:scaleType="centerCrop"
                android:src="@drawable/vacter"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:shapeAppearance="@style/ShapeAppearance.Material3.Corner.Full" />

            <TextView
                android:id="@+id/textView44"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/_5sdp"
                android:ellipsize="end"
                android:maxLines="1"
                android:textAlignment="viewEnd"
                android:textColor="@color/white"
                android:textSize="12sp"
                app:layout_constraintBottom_toBottomOf="@+id/imageView20"
                app:layout_constraintEnd_toStartOf="@+id/imageView20"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@+id/imageView20" />

            <androidx.cardview.widget.CardView
                android:id="@+id/cvOppWins"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_4sdp"
                android:visibility="gone"
                app:cardBackgroundColor="#388181A4"
                app:cardCornerRadius="@dimen/_25sdp"
                app:layout_constraintEnd_toEndOf="@+id/imageView20"
                app:layout_constraintTop_toBottomOf="@+id/imageView20">

                <ImageView
                    android:id="@+id/ivow"
                    android:layout_width="@dimen/_15sdp"
                    android:layout_height="@dimen/_15sdp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="@dimen/_6sdp"
                    android:importantForAccessibility="no"
                    android:padding="1dp"
                    android:src="@drawable/ic_winner_gray" />

                <TextView
                    android:id="@+id/opponentWins"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginVertical="@dimen/_3sdp"
                    android:layout_marginStart="@dimen/_25sdp"
                    android:layout_marginEnd="@dimen/_10sdp"
                    android:text="0"
                    android:textColor="#E6FFFFFF"
                    android:textSize="12sp" />

            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:id="@+id/cardView8"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_4sdp"
                android:layout_marginEnd="@dimen/_2sdp"
                app:cardBackgroundColor="#388181A4"
                app:cardCornerRadius="@dimen/_25sdp"
                app:layout_constraintEnd_toStartOf="@+id/cvOppWins"
                app:layout_constraintTop_toBottomOf="@+id/imageView20">

                <ImageView
                    android:layout_width="@dimen/_15sdp"
                    android:layout_height="@dimen/_15sdp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="@dimen/_6sdp"
                    android:importantForAccessibility="no"
                    android:src="@drawable/profile_n" />

                <TextView
                    android:id="@+id/opponentCount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginVertical="@dimen/_3sdp"
                    android:layout_marginStart="@dimen/_25sdp"
                    android:layout_marginEnd="@dimen/_10sdp"
                    android:text="0"
                    android:textColor="#E6FFFFFF"
                    android:textSize="12sp" />

            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:id="@+id/cardView7"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/_2sdp"
                app:cardBackgroundColor="#388181A4"
                app:cardCornerRadius="@dimen/_25sdp"
                app:layout_constraintBottom_toBottomOf="@+id/cardView8"
                app:layout_constraintEnd_toStartOf="@+id/cardView8"
                app:layout_constraintTop_toTopOf="@+id/cardView8">

                <ImageView
                    android:layout_width="@dimen/_15sdp"
                    android:layout_height="@dimen/_15sdp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="@dimen/_6sdp"
                    android:importantForAccessibility="no"
                    android:src="@drawable/gift_n" />

                <TextView
                    android:id="@+id/opponentGift"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginVertical="@dimen/_3sdp"
                    android:layout_marginStart="@dimen/_25sdp"
                    android:layout_marginEnd="@dimen/_10sdp"
                    android:text="0"
                    android:textColor="#E6FFFFFF"
                    android:textSize="12sp" />

            </androidx.cardview.widget.CardView>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:id="@+id/llResultOwner"
        style="@style/outlinedButton"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_6sdp"
        android:layout_marginBottom="@dimen/_60sdp"
        android:background="@drawable/bg_button_outlined"
        android:backgroundTint="@color/white"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/_10sdp"
        android:paddingVertical="@dimen/_8sdp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/constraintLayout8"
        app:layout_constraintEnd_toStartOf="@+id/llResultOpponent"
        app:layout_constraintStart_toStartOf="parent">

        <ImageView
            android:id="@+id/ivWinnerOwner"
            android:layout_width="@dimen/_16sdp"
            android:layout_height="@dimen/_16sdp"
            android:layout_gravity="center"
            android:importantForAccessibility="no"
            android:padding="@dimen/_1sdp"
            android:src="@drawable/ic_winner" />

        <TextView
            android:id="@+id/tvWinnerOwner"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center"
            android:paddingStart="0dp"
            android:paddingEnd="@dimen/_12sdp"
            android:text="@string/winner"
            android:textColor="@color/redgradstart" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/llResultOpponent"
        style="@style/outlinedButton"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_6sdp"
        android:layout_marginBottom="@dimen/_60sdp"
        android:background="@drawable/bg_button_outlined"
        android:backgroundTint="@color/white"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/_10sdp"
        android:paddingVertical="@dimen/_8sdp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/constraintLayout8"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/llResultOwner">

        <ImageView
            android:id="@+id/ivWinnerOpponent"
            android:layout_width="@dimen/_16sdp"
            android:layout_height="@dimen/_16sdp"
            android:layout_gravity="center"
            android:importantForAccessibility="no"
            android:padding="@dimen/_1sdp"
            android:src="@drawable/ic_winner" />

        <TextView
            android:id="@+id/tvWinnerOpponent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center"
            android:paddingStart="0dp"
            android:paddingEnd="@dimen/_12sdp"
            android:text="@string/winner"
            android:textColor="@color/redgradstart" />

    </LinearLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clChats"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/_5sdp"
        android:animateLayoutChanges="true"
        android:background="@drawable/bg_corners_top"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/constraintLayout8"
        app:layout_constraintVertical_weight="4">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnStartBattle"
            style="@style/button"
            android:layout_width="match_parent"
            android:layout_margin="@dimen/_8sdp"
            android:enabled="false"
            android:text="@string/start_battle_instantly"
            android:textColor="@color/white"
            android:textStyle="bold"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <HorizontalScrollView
            android:id="@+id/hsvGifts"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:scrollbars="none"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/btnStartBattle">

            <LinearLayout
                android:id="@+id/rcvGiftsReceived"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="@dimen/_4sdp" />

        </HorizontalScrollView>

        <ScrollView
            android:id="@+id/scrollView"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:scrollbars="none"
            app:layout_constraintBottom_toTopOf="@+id/clSend"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/hsvGifts">

            <LinearLayout
                android:id="@+id/rcvBattleChat"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical" />

        </ScrollView>

        <LinearLayout
            android:id="@+id/llNoChat"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="@+id/scrollView"
            app:layout_constraintTop_toBottomOf="@+id/btnStartBattle"
            app:layout_constraintVertical_bias="0.5">

            <ImageView
                android:layout_width="@dimen/_40sdp"
                android:layout_height="@dimen/_40sdp"
                android:src="@drawable/ic_msg" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_8sdp"
                android:textColor="@color/gray"
                android:text="@string/no_chat_message"
                android:textAlignment="center" />

        </LinearLayout>


        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/btnCloseMessage"
            android:layout_width="@dimen/_24sdp"
            android:layout_height="@dimen/_24sdp"
            android:layout_marginBottom="@dimen/_10sdp"
            android:background="#80ffffff"
            android:elevation="@dimen/_10sdp"
            android:src="@drawable/ic_close"
            android:tint="#50000000"
            android:visibility="gone"
            app:contentPadding="@dimen/_6sdp"
            app:layout_constraintBottom_toTopOf="@+id/clSend"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:shapeAppearance="@style/ShapeAppearance.Material3.Corner.Full" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clSend"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <View
                android:id="@+id/view6"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginBottom="@dimen/_10sdp"
                android:background="#F2F2F7"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <EditText
                android:id="@+id/editText"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginVertical="@dimen/_10sdp"
                android:layout_marginStart="@dimen/_5sdp"
                android:layout_marginEnd="@dimen/_5sdp"
                android:background="@drawable/bg_edit_comment"
                android:hint="@string/your_comment"
                android:importantForAutofill="no"
                android:inputType="text"
                android:paddingVertical="@dimen/_10sdp"
                android:paddingStart="@dimen/_40sdp"
                android:paddingEnd="@dimen/_40sdp"
                android:textColor="#B38181A4"
                android:textColorHint="#B38181A4"
                android:textSize="12sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/ivGift"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/view6" />

            <ImageView
                android:id="@+id/ivSend"
                android:layout_width="@dimen/_32sdp"
                android:layout_height="@dimen/_32sdp"
                android:layout_marginEnd="@dimen/_3sdp"
                android:importantForAccessibility="no"
                android:src="@drawable/ic_send"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@+id/editText"
                app:layout_constraintEnd_toEndOf="@+id/editText"
                app:layout_constraintTop_toTopOf="@+id/editText" />

            <com.google.android.material.imageview.ShapeableImageView
                android:id="@+id/ivMyImage"
                android:layout_width="@dimen/_28sdp"
                android:layout_height="@dimen/_28sdp"
                android:layout_margin="@dimen/_5sdp"
                android:scaleType="centerCrop"
                app:layout_constraintBottom_toBottomOf="@+id/editText"
                app:layout_constraintStart_toStartOf="@+id/editText"
                app:layout_constraintTop_toTopOf="@+id/editText"
                app:shapeAppearance="@style/ShapeAppearance.Material3.Corner.Full" />

            <ImageView
                android:id="@+id/ivGift"
                android:layout_width="@dimen/_34sdp"
                android:layout_height="@dimen/_34sdp"
                android:layout_marginEnd="@dimen/_10sdp"
                android:importantForAccessibility="no"
                android:src="@drawable/iv_heart"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@+id/editText"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/editText" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>