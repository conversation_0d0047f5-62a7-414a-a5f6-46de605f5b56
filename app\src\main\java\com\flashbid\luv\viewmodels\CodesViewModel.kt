package com.flashbid.luv.viewmodels

import androidx.lifecycle.LiveData
import androidx.lifecycle.ViewModel
import com.flashbid.luv.models.remote.*
import com.flashbid.luv.network.Resource
import com.flashbid.luv.repositories.CodeRepository

class CodesViewModel(
    private val codeRepository: CodeRepository
) : ViewModel() {

    fun getUserCodes(): LiveData<Resource<GetCodesResponse>> {
        return codeRepository.getUserCodes()
    }

    fun getCodeDetail(codeId: String): LiveData<Resource<CodeDetailResponse>> {
        return codeRepository.getCodeDetail(codeId)
    }

    fun getCodeDetailSponsor(codeId: String, sponsor: Boolean): LiveData<Resource<CodeDetailResponse>> {
        return codeRepository.getCodeDetailSponsor(codeId, sponsor)
    }

    fun deleteCode(codeId: String): LiveData<Resource<MessageResponse>> {
        return codeRepository.deleteCode(codeId)
    }

    fun scanCode(codeId: String): LiveData<Resource<ScanResponse>> {
        return codeRepository.scanCode(codeId)
    }

    fun updateCodeDetail(
        codeId: String, duration: Int, need_key: Int, num_scan: Int, plan: Int, sponsorID: Int?
    ): LiveData<Resource<MessageResponse>> {
        return codeRepository.updateCodeDetail(
            codeId, CreateCode(duration, need_key, num_scan, plan, sponsor_id = sponsorID)
        )
    }

    fun createCode(
        duration: Int, need_key: Int, num_scan: Int, plan: Int, sponsorID: Int?
    ): LiveData<Resource<MessageResponse>> {
        return codeRepository.createCode(CreateCode(duration, need_key, num_scan, plan, sponsor_id = sponsorID))
    }

    fun getSponsored(): LiveData<Resource<SponsoredResponse>> {
        return codeRepository.getSponsored()
    }

}