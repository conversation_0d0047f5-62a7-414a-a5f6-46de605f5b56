package com.flashbid.luv.ui.fragments.transactions

import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.flashbid.luv.R
import com.flashbid.luv.data.local.AppPreferences
import com.flashbid.luv.databinding.FragmentLuvDropBinding
import com.flashbid.luv.extensions.convertDate
import com.flashbid.luv.extensions.getTimeInAgo
import com.flashbid.luv.extensions.hide
import com.flashbid.luv.extensions.setOnClickWithDebounce
import com.flashbid.luv.extensions.snackBar
import com.flashbid.luv.extensions.viewBinding
import com.flashbid.luv.models.remote.HistoryDetailResponse
import com.flashbid.luv.network.Status
import com.flashbid.luv.util.HistoryMapping
import com.flashbid.luv.util.getColor
import com.flashbid.luv.util.loadImageFromUrl
import com.flashbid.luv.viewmodels.TransactionViewModel
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel

class LuvReceivedFragment : Fragment(R.layout.fragment_luv_received) {

    private val binding by viewBinding(FragmentLuvDropBinding::bind)
    private val viewModel by viewModel<TransactionViewModel>()
    private val args by navArgs<LuvDropFragmentArgs>()
    private val pref by inject<AppPreferences>()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.imageView.setOnClickWithDebounce { findNavController().popBackStack() }

        getHistoryDetail(args.id)
    }

    private fun getHistoryDetail(
        id: String
    ) {
        viewModel.getHistoryDetail(id).observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(
                    it.message ?: getString(
                        R.string.something_went_wrong, getString(R.string.try_again)
                    )
                )

                Status.LOADING -> {}
                Status.SUCCESS -> {
                    setData(it.data)
                }
            }
        }
    }

    private fun setData(data: HistoryDetailResponse?) {
        if (data != null) {
            binding.tvName.text = getString(R.string.the_luv_network)
            binding.tvDate.text = data.message.update_at.convertDate()
            binding.tvTransaction.text = data.message.id.toString()
            //binding.tvInfo.text = data.message.update_at.getTimeInAgo()
            binding.tvInfo.hide()
//            binding.ivUserImage.loadImageFromUrl(data.message.photo)

            if (pref.userId == data.message.sender_user_id) {
                //binding.textView.text = binding.textView.context.getString(R.string.sponsored_drop_sent)
                binding.senderText.text = binding.textView.context.getString(R.string.to)
                binding.tvHearts.text = "-${data.message.amount}"
                binding.tvHearts.setTextColor(binding.tvHearts.getColor(R.color.gray))


            } else {
                //binding.textView.text = binding.textView.context.getString(R.string.sponsored_drop_received)
                binding.senderText.text = binding.textView.context.getString(R.string.from)
                binding.tvHearts.text = "+${data.message.amount}"
                binding.tvHearts.setTextColor(binding.tvHearts.getColor(R.color.redgradstart))

            }

            when (data.message.status) {
                HistoryMapping.STATUS.COMPLETED -> binding.tvStatus.text =
                    getString(R.string.completed)

                HistoryMapping.STATUS.PENDING -> binding.tvStatus.text = getString(R.string.pending)
                HistoryMapping.STATUS.VOID -> binding.tvStatus.text = getString(R.string.void_trans)
                HistoryMapping.STATUS.SUSPENDED -> binding.tvStatus.text =
                    getString(R.string.suspended)
            }
        }

    }
}