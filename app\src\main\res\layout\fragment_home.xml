<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg"
    android:orientation="vertical"
    tools:context=".ui.fragments.home.HomeFragment">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/constraintLayout2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:animateLayoutChanges="true">

        <LinearLayout
            android:id="@+id/constraintLayout16"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layoutStories"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/constraintLayout6"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="5dp"
                    android:layout_marginTop="@dimen/_5sdp"
                    android:transitionName="shared_story_transition"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/constraintLayout3"
                        android:layout_width="@dimen/_55sdp"
                        android:layout_height="@dimen/_55sdp"
                        android:background="@drawable/rectangle"
                        android:visibility="invisible"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <com.google.android.material.imageview.ShapeableImageView
                        android:id="@+id/ivPerson"
                        android:layout_width="@dimen/_48sdp"
                        android:layout_height="@dimen/_48sdp"
                        android:scaleType="centerCrop"
                        android:src="@drawable/image"
                        app:layout_constraintBottom_toBottomOf="@+id/constraintLayout3"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:shapeAppearance="@style/ShapeAppearance.Material3.Corner.Full" />

                    <TextView
                        android:id="@+id/tvPerson"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/you"
                        android:textSize="@dimen/_10sdp"
                        app:layout_constraintEnd_toEndOf="@+id/constraintLayout3"
                        app:layout_constraintStart_toStartOf="@+id/constraintLayout3"
                        app:layout_constraintTop_toBottomOf="@+id/constraintLayout3" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <View
                    android:id="@+id/view7"
                    android:layout_width="2dp"
                    android:layout_height="0dp"
                    android:layout_marginVertical="@dimen/_8sdp"
                    android:layout_marginStart="@dimen/_5sdp"
                    android:background="@color/bgGray"
                    app:layout_constraintBottom_toBottomOf="@+id/constraintLayout6"
                    app:layout_constraintStart_toEndOf="@+id/constraintLayout6"
                    app:layout_constraintTop_toTopOf="@+id/constraintLayout6" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rcvStorie"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/view7"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:itemCount="1"
                    tools:listitem="@layout/item_person" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layoutName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone">

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/cvImageView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/_15sdp"
                    android:transitionGroup="true"
                    android:transitionName="@string/profile_picture_transition"
                    app:cardCornerRadius="@dimen/_50sdp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:strokeWidth="0dp">

                    <ImageView
                        android:id="@+id/imageView4"
                        android:layout_width="@dimen/_40sdp"
                        android:layout_height="@dimen/_40sdp"
                        android:background="@android:color/transparent"
                        android:scaleType="centerCrop"
                        app:shapeAppearanceOverlay="@style/ShapeAppearance.Material3.Corner.Full"
                        app:srcCompat="@drawable/ic_user_placeholder" />

                </com.google.android.material.card.MaterialCardView>

                <TextView
                    android:id="@+id/textView20"
                    style="@style/h2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/_15sdp"
                    android:textSize="26sp"
                    tools:text="@string/good_morning"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/textView21"
                    style="@style/subtitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:textColor="@color/gray"
                    app:layout_constraintStart_toStartOf="@+id/textView20"
                    app:layout_constraintTop_toBottomOf="@+id/textView20"
                    tools:text="Victor Bigmad" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </LinearLayout>

        <androidx.viewpager.widget.ViewPager
            android:id="@+id/viewPager"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_140sdp"
            android:padding="0dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/constraintLayout16" />

        <ru.tinkoff.scrollingpagerindicator.ScrollingPagerIndicator
            android:id="@+id/indicator"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="@+id/viewPager"
            app:layout_constraintStart_toStartOf="@+id/viewPager"
            app:layout_constraintTop_toBottomOf="@+id/viewPager"
            app:spi_dotColor="@color/white"
            app:spi_dotSelectedColor="@color/redgradstart" />

        <LinearLayout
            android:id="@+id/llButtons"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_5sdp"
            android:baselineAligned="false"
            android:orientation="horizontal"
            android:paddingHorizontal="@dimen/_10sdp"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/indicator">

            <LinearLayout
                android:id="@+id/btnRecharge"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="@dimen/_45sdp"
                    android:layout_height="@dimen/_45sdp"
                    android:src="@drawable/ic_recharge"
                    tools:ignore="ContentDescription" />

                <TextView
                    style="@style/text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_5sdp"
                    android:text="@string/recharge" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/btnSendLuv"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="@dimen/_45sdp"
                    android:layout_height="@dimen/_45sdp"
                    android:src="@drawable/ic_send_luv"
                    tools:ignore="ContentDescription" />

                <TextView
                    style="@style/text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_5sdp"
                    android:text="@string/send_luv" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/btnWithdraw"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="@dimen/_45sdp"
                    android:layout_height="@dimen/_45sdp"
                    android:src="@drawable/ic_withraw"
                    tools:ignore="ContentDescription" />

                <TextView
                    style="@style/text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_5sdp"
                    android:text="@string/withdraw" />

            </LinearLayout>


        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:id="@+id/bottomSheet"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/sheet_shape"
        android:backgroundTint="@color/white"
        android:orientation="vertical"
        app:layout_behavior="com.google.android.material.bottomsheet.BottomSheetBehavior">

        <View
            android:id="@+id/btnDrag"
            android:layout_width="@dimen/_40sdp"
            android:layout_height="4dp"
            android:layout_gravity="center_horizontal|top"
            android:layout_marginTop="@dimen/_10sdp"
            android:layout_marginBottom="@dimen/_5sdp"
            android:background="@drawable/bg_edit"
            android:backgroundTint="#F2F2F7" />

<!--        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout-->
<!--            android:id="@+id/rcvTransactionsSwipe"-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:orientation="vertical">-->


            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rcvTransactions"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:transitionGroup="true"
                tools:itemCount="1"
                tools:listitem="@layout/item_community_transaction" />

<!--        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>-->



        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rcvHistory"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:transitionGroup="true"
            android:visibility="gone"
            tools:itemCount="1"
            tools:listitem="@layout/item_community_transaction" />

        <LinearLayout
            android:id="@+id/llNoItems"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingVertical="@dimen/_20sdp"
            android:visibility="gone">

            <com.google.android.material.imageview.ShapeableImageView
                android:layout_width="@dimen/_60sdp"
                android:layout_height="@dimen/_60sdp"
                android:padding="2dp"
                app:shapeAppearance="@style/ShapeAppearance.Material3.Corner.Full"
                app:srcCompat="@drawable/ic_money" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_5sdp"
                android:gravity="center"
                android:text="@string/no_transactions_nyet" />

        </LinearLayout>

    </LinearLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>