package com.flashbid.luv.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.navigation.NavController
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.RecyclerView
import com.flashbid.luv.databinding.ItemTitleBinding
import com.flashbid.luv.extensions.setGridLayout
import com.flashbid.luv.extensions.setOnClickWithDebounce
import com.flashbid.luv.models.remote.Brand
import com.flashbid.luv.models.remote.BrandData
import com.flashbid.luv.ui.fragments.other.ChooseFavFragmentDirections


class ChooseAllBrandsAdapter(
    private val fragment: Fragment,
    private val categories: ArrayList<BrandData>,
    val onClick: (Brand) -> Unit,
    private val navController: NavController
) :
    RecyclerView.Adapter<ChooseAllBrandsAdapter.ViewHolder>() {

    inner class ViewHolder(val binding: ItemTitleBinding) :
        RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val itemView =
            ItemTitleBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(itemView)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val model = categories[position]

        with(holder.binding) {
            textView12.text = model.name
            tvSeeAll.setOnClickWithDebounce {
                val action =
                    ChooseFavFragmentDirections.actionChooseFavFragmentToAllBrandsFragment(
                        model.name,
                        model.id,
                        true,
                        model.brands.toTypedArray()
                    )
                fragment.findNavController().navigate(action)
            }
            rcvBrands.apply {
                setGridLayout(3)
                adapter = BrandsItemAdapter(model.brands, onClick, navController,true, true)
            }
        }

    }

    override fun getItemCount(): Int {
        return categories.size
    }

    fun refresh(newList: ArrayList<BrandData>) {
        categories.clear()
        categories.addAll(newList)
        notifyDataSetChanged()
    }

}