<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.fragments.profile.SettingFragment">

    <ImageView
        android:id="@+id/imageView"
        android:layout_width="@dimen/_30sdp"
        android:layout_height="@dimen/_30sdp"
        android:layout_margin="@dimen/_20sdp"
        android:src="@drawable/ic_back_btn"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="@drawable/ic_gift_box" />

    <TextView
        android:id="@+id/tvName"
        style="@style/h3"
        android:layout_width="wrap_content"
        android:gravity="center"
        android:text="@string/setting"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@+id/imageView"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/imageView" />

    <TextView
        android:id="@+id/btnPrivacy"
        style="@style/subtitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16sdp"
        android:layout_marginStart="20dp"
        android:layout_marginTop="@dimen/_40sdp"
        android:layout_marginEnd="20dp"
        android:background="@drawable/bg_edit"
        android:drawableStart="@drawable/ic_key"
        android:drawableEnd="@drawable/ic_arrow_right"
        android:drawablePadding="@dimen/_10sdp"
        android:padding="@dimen/_12sdp"
        android:text="@string/privacy"
        android:textSize="14sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvName" />

    <TextView
        android:id="@+id/btnLanguage"
        style="@style/subtitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16sdp"
        android:layout_marginTop="@dimen/_10sdp"
        android:background="@drawable/bg_edit"
        android:drawableStart="@drawable/ic_globe"
        android:drawableEnd="@drawable/ic_arrow_right"
        android:drawablePadding="@dimen/_10sdp"
        android:padding="@dimen/_12sdp"
        android:text="@string/change_language"
        android:textSize="14sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/btnPrivacy" />

    <TextView
        android:id="@+id/btnEdit"
        style="@style/subtitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16sdp"
        android:layout_marginTop="@dimen/_40sdp"
        android:background="@drawable/bg_edit_blue"
        android:drawableStart="@drawable/ic_edit"
        android:drawablePadding="@dimen/_10sdp"
        android:padding="@dimen/_12sdp"
        android:text="@string/edit_profile"
        android:textSize="14sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/btnLanguage" />

    <TextView
        android:id="@+id/btnReset"
        style="@style/subtitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16sdp"
        android:layout_marginTop="@dimen/_10sdp"
        android:background="@drawable/bg_edit_blue"
        android:drawableStart="@drawable/ic_lock_blue"
        android:drawablePadding="@dimen/_10sdp"
        android:padding="@dimen/_12sdp"
        android:text="@string/reset_password"
        android:textSize="14sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/btnEdit" />

    <TextView
        android:id="@+id/btnLogout"
        style="@style/subtitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16sdp"
        android:drawablePadding="@dimen/_10sdp"
        android:padding="@dimen/_12sdp"
        android:text="@string/log_out"
        android:layout_marginBottom="@dimen/_10sdp"
        android:textAlignment="center"
        android:background="@drawable/bg_edit_red"
        android:textColor="@color/redgradstart"
        android:textSize="14sp"
        app:layout_constraintBottom_toTopOf="@+id/btnDelete"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/btnDelete"
        style="@style/subtitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16sdp"
        android:layout_marginBottom="@dimen/_20sdp"
        android:drawablePadding="@dimen/_10sdp"
        android:padding="@dimen/_12sdp"
        android:text="@string/delete_account"
        android:textAlignment="center"
        android:textColor="@color/redgradstart"
        android:textSize="14sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
