<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingHorizontal="@dimen/_12sdp"
    android:paddingVertical="@dimen/_6sdp">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivUserReceived"
        android:layout_width="@dimen/_30sdp"
        android:layout_height="@dimen/_30sdp"
        android:scaleType="centerCrop"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearanceOverlay="@style/ShapeAppearance.Material3.Corner.Full"
        app:srcCompat="@drawable/vacter" />

    <TextView
        android:id="@+id/tvUsername"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_6sdp"
        android:textSize="12sp"
        app:layout_constraintBottom_toTopOf="@+id/tvUsernameChat"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/ivUserReceived"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="floyd.miles" />

    <TextView
        android:id="@+id/tvUsernameChat"
        style="@style/small"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textColor="@color/gray"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="@+id/ivUserReceived"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@+id/tvUsername"
        app:layout_constraintTop_toBottomOf="@+id/tvUsername"
        tools:text="Let’s goooooo!" />


</androidx.constraintlayout.widget.ConstraintLayout>