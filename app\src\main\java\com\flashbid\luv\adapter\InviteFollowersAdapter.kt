package com.flashbid.luv.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.flashbid.luv.R
import com.flashbid.luv.databinding.ItemInviteBinding
import com.flashbid.luv.databinding.ItemSearchBinding
import com.flashbid.luv.extensions.hide
import com.flashbid.luv.util.loadImageFromUrl
import com.flashbid.luv.extensions.setOnClickWithDebounce
import com.flashbid.luv.extensions.show
import com.flashbid.luv.models.remote.UserDetails
import com.flashbid.luv.util.getColor

class InviteFollowersAdapter(
    private val fullList: List<UserDetails>,
    private val onSelectionChanged: (selectedItems: List<UserDetails>) -> Unit
) : RecyclerView.Adapter<InviteFollowersAdapter.ViewHolder>() {

    private var filteredList = fullList

    private val selectedIndices = mutableSetOf<Int>()

    inner class ViewHolder(val binding: ItemInviteBinding) : RecyclerView.ViewHolder(binding.root) {
        init {
            itemView.setOnClickWithDebounce {
                val position = adapterPosition
                if (position != RecyclerView.NO_POSITION) {
                    handleItemClick(position)
                }
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding = ItemInviteBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = filteredList[position]

        with(holder.binding) {
            tvUserName.text = item.first_name + " " + item.last_name
            tvUsernameId.text = "@" + item.username
            imageView4.loadImageFromUrl(item.photo)

            val isSelected = selectedIndices.contains(position)
            if(isSelected) ivCheck.show() else ivCheck.hide()
        }
    }

    private fun handleItemClick(position: Int) {
        val isSelected = selectedIndices.contains(position)

        if (isSelected) {
            selectedIndices.remove(position)
        } else {
            selectedIndices.add(position)
        }

        notifyItemChanged(position)
        onSelectionChanged(selectedIndices.map { fullList[it] })
    }

    fun filter(query: String) {
        filteredList = if (query.isEmpty()) {
            fullList
        } else {
            val lowerCaseQuery = query.lowercase()
            fullList.filter {
                val fullName = "${it.first_name ?: ""} ${it.last_name ?: ""}".lowercase()
                fullName.contains(lowerCaseQuery)
            }
        }
        notifyDataSetChanged()
    }

    override fun getItemCount(): Int = filteredList.size

}