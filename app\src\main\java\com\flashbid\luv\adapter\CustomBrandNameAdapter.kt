package com.flashbid.luv.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.flashbid.luv.R
import com.flashbid.luv.databinding.ItemBrandNameBinding
import com.flashbid.luv.databinding.ItemCustomBrandBinding
import com.flashbid.luv.extensions.getTimeInAgo
import com.flashbid.luv.extensions.hide
import com.flashbid.luv.util.loadImageFromUrl
import com.flashbid.luv.extensions.setOnClickWithDebounce
import com.flashbid.luv.extensions.show
import com.flashbid.luv.models.remote.CustomBrandNameResponse
import com.flashbid.luv.models.remote.CustomBrandResponse

class CustomBrandNameAdapter(
    private val list: List<CustomBrandNameResponse.CustomBrandNames>,
    val onClick: (CustomBrandNameResponse.CustomBrandNames) -> Unit,
) : RecyclerView.Adapter<CustomBrandNameAdapter.ViewHolder>() {

    private var filteredList = list

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding =
            ItemBrandNameBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = filteredList[position]

        with(holder.binding) {

            textView12.text = "LUV Crates"
            //imageView4.loadImageFromUrl(item.photo)
        }

        holder.itemView.setOnClickWithDebounce {
            onClick(item)
        }


    }

    override fun getItemCount(): Int = filteredList.size

    inner class ViewHolder(val binding: ItemBrandNameBinding) :
        RecyclerView.ViewHolder(binding.root)

    fun refresh() = notifyDataSetChanged()
}