package com.flashbid.luv.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.flashbid.luv.R
import com.flashbid.luv.databinding.ItemSendLuvBinding
import com.flashbid.luv.util.getColor
import com.flashbid.luv.util.loadImageFromUrl
import com.flashbid.luv.extensions.setOnClickWithDebounce
import com.flashbid.luv.models.remote.ItemGift

class GiftsAdapter(
    private val list: MutableList<ItemGift>,
    val onClick: (ItemGift) -> Unit,
) : RecyclerView.Adapter<GiftsAdapter.ViewHolder>() {

    private var index = -1

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding =
            ItemSendLuvBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = list[position]

        with(holder.binding) {
            textView8.text = item.amount.toString()
            tvName.text = item.name
            if (holder.adapterPosition == index) {
                ivHeart.setColorFilter(ivHeart.getColor(R.color.white))
                textView8.setTextColor(textView8.getColor(R.color.white))
                tvName.setTextColor(tvName.getColor(R.color.white))
                llBackground.setBackgroundResource(R.drawable.splash_bg_gradient)
            } else {
                ivHeart.setColorFilter(ivHeart.getColor(R.color.redgradstart))
                tvName.setTextColor(tvName.getColor(R.color.gray))
                textView8.setTextColor(textView8.getColor(R.color.gray))
                llBackground.setBackgroundResource(R.color.white)
            }

            imageView2.loadImageFromUrl(item.image)
        }

        holder.itemView.setOnClickWithDebounce {
            onClick(item)
            index = holder.adapterPosition
            refresh()
        }

    }

    override fun getItemCount(): Int = list.size

    inner class ViewHolder(val binding: ItemSendLuvBinding) :
        RecyclerView.ViewHolder(binding.root)

    fun refresh() = notifyDataSetChanged()

}