package com.flashbid.luv.ui.fragments.story

import android.Manifest
import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.content.pm.PackageManager
import android.graphics.Color
import android.graphics.SurfaceTexture
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.MotionEvent
import android.view.View
import android.view.animation.LinearInterpolator
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.flashbid.luv.MainActivity
import com.flashbid.luv.R
import com.flashbid.luv.databinding.FragmentPreRecordingBinding
import com.flashbid.luv.databinding.SheetDiscardMediaBinding
import com.flashbid.luv.extensions.TextState
import com.flashbid.luv.extensions.hide
import com.flashbid.luv.extensions.invisible
import com.flashbid.luv.extensions.show
import com.flashbid.luv.extensions.snackBar
import com.flashbid.luv.extensions.viewBinding
import com.flashbid.luv.network.Status
import com.flashbid.luv.util.GradientCircularProgressDrawable
import com.flashbid.luv.viewmodels.StoryViewModel
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.tabs.TabLayout
import com.wonderkiln.camerakit.CameraKit
import com.wonderkiln.camerakit.CameraListener
import org.koin.android.ext.android.inject
import java.io.File


class PreRecordingFragment : Fragment(R.layout.fragment_pre_recording) {

    val binding by viewBinding(FragmentPreRecordingBinding::bind)
    private val args by navArgs<PreRecordingFragmentArgs>()
    private val viewModel: StoryViewModel by inject()
    private var viewType = "anyone"

    private val startDelayMillis = 0L
    private val minRecordDurationMillis = 5000L
    private var recordStartTime = 0L

    private val handler = Handler(Looper.getMainLooper())
    private val startRecordingRunnable = Runnable {
        startRecordingVideo()
        recordStartTime = System.currentTimeMillis()
    }

    override fun onResume() {
        super.onResume()
        binding.camera.start()
    }

    override fun onPause() {
        handler.removeCallbacks(startRecordingRunnable)
        binding.camera.stop()
        super.onPause()
    }

    private fun sendStory() {
        if (filePath.isEmpty()) {
            snackBar("File is empty")
            return
        }
        Log.d("TAG", "transId:${args.transId} ")

        Log.d("TAG", "receiverId: ${args.receiverId}")
        Log.d("TAG", "viewType: ${viewType}")
        Log.d("TAG", "duration: ${24}")

//        "duration": "24",
//        "receiver_user_id": "\(rId)",
//        "view_type": type, //   "anyone" or “single"
//        "transaction_id": "\(transId)"

        val duration = if(viewType == "anyone") 24 else 1
        viewModel.createStory(args.transId, file.path, args.receiverId, viewType, 24)
            .observe(viewLifecycleOwner) {
                when (it.status) {
                    Status.ERROR -> snackBar(it.message ?: getString(R.string.something_went_wrong))
                    Status.LOADING -> {}
                    Status.SUCCESS -> {
                        //file.delete()
                        (requireActivity() as MainActivity).showSnackBar(
                            string = if (viewType == "anyone")
                                getString(R.string.thanks_sent_available_to_anyone_for_24h)
                            else getString(R.string.thanks_sent_can_view_it_once, args.receiver),
                            TextState.SUCCESS
                        )
                        findNavController().popBackStack(R.id.homeFragment, false)
                    }
                }
            }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        requestAudioPermission()
    }

    private fun initializeTabs(tabs: Array<String>) {
        for (tabName in tabs) {
            val tab = binding.tabLayout1.newTab().setText(tabName)
            binding.tabLayout1.addTab(tab)
        }

        binding.tabLayout1.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab) {
                if (tab.position == 0) viewType = "anyone"
                else if (tab.position == 1) viewType = "single"
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {
            }

            override fun onTabReselected(tab: TabLayout.Tab?) {
            }
        })
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun setupRecording() {
        binding.cdvImage.setOnClickListener {
            findNavController().popBackStack()
        }

        binding.cdvRepeat.setOnClickListener {
            switchCamera()
        }

        binding.ivNext.setOnClickListener {
            sendStory()
        }

        binding.camera.start()

        binding.btnCameraClick.setOnClickListener {

            if(!isRecording) {
                handler.postDelayed(startRecordingRunnable, startDelayMillis)
            } else {
                handler.removeCallbacks(startRecordingRunnable)
                if (System.currentTimeMillis() - recordStartTime >= 1000L) {
                    updateUiForCompleted()
                } else {

                }
            }
        }
        /*binding.btnCameraClick.setOnTouchListener { _, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    if(!isRecording) {
                        handler.postDelayed(startRecordingRunnable, startDelayMillis)
                    }
                }

                MotionEvent.ACTION_UP -> {
                    handler.removeCallbacks(startRecordingRunnable)
                    if (isRecording) {
                        if (System.currentTimeMillis() - recordStartTime >= minRecordDurationMillis) {
                            updateUiForCompleted()
                        }
                    }
                }
            }
            true
        }*/

        initializeTabs(
            arrayOf(
                getString(R.string.anyone), getString(R.string.only_user_name, args.receiver)
            )
        )

    }

    companion object {
        const val CAMERA_PERMISSION_REQUEST_CODE = 200
        const val AUDIO_PERMISSION_REQUEST_CODE = 201
    }

    /*@Deprecated("Deprecated in Java")
    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        when (requestCode) {
            CAMERA_PERMISSION_REQUEST_CODE -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    // Check for audio permission before opening camera
                    setupRecording()
                } else {
                    // Permission denied. Handle the feature's unavailability.
                }
            }

            AUDIO_PERMISSION_REQUEST_CODE -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    requestCameraPermission()
                } else {
                    // Permission denied. Handle the feature's unavailability.
                }
            }
        }
    }*/

    val audioRequestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            requestCameraPermission()
        } else {

        }
    }

    val cameraRequestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            setupRecording()
        } else {

        }
    }


    private fun requestAudioPermission() {
        if (ContextCompat.checkSelfPermission(
                requireContext(),
                Manifest.permission.RECORD_AUDIO
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            audioRequestPermissionLauncher.launch(Manifest.permission.RECORD_AUDIO)
            /*ActivityCompat.requestPermissions(
                requireActivity(),
                arrayOf(Manifest.permission.RECORD_AUDIO),
                AUDIO_PERMISSION_REQUEST_CODE
            )*/
        } else {
            requestCameraPermission()
        }
    }

    private fun requestCameraPermission() {
        if (ContextCompat.checkSelfPermission(
                requireContext(),
                Manifest.permission.CAMERA
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            cameraRequestPermissionLauncher.launch(Manifest.permission.RECORD_AUDIO)
            /*ActivityCompat.requestPermissions(
                requireActivity(),
                arrayOf(Manifest.permission.CAMERA),
                CAMERA_PERMISSION_REQUEST_CODE
            )*/
        } else {
            setupRecording()
        }
    }

    private lateinit var file: File
    private lateinit var filePath: String
    private var valueAnimator: ValueAnimator? = null
    private var isRecording: Boolean = false

    private fun startRecordingVideo() {
        updateUiForRecording()
        binding.camera.setCameraListener(object : CameraListener() {
            override fun onVideoTaken(video: File?) {
                super.onVideoTaken(video)
                if (video != null) {
                    file = video
                    filePath = file.path
                    if (file.exists() && file.canRead()) playVideo(Uri.fromFile(file))
                    else snackBar("Error reading video file")
                } else {
                    updateUiForIdle()
                    snackBar("Error recording video")
                }
                Log.d("VideoCallback", "Video File: ${file.absolutePath}")
            }
        })
        binding.camera.startRecordingVideo()
        isRecording = true
    }

    private fun switchCamera() {
        if (binding.camera.facing == CameraKit.Constants.FACING_FRONT) binding.camera.facing =
            CameraKit.Constants.FACING_BACK
        else binding.camera.facing = CameraKit.Constants.FACING_FRONT
    }

    private fun updateUiForIdle() {
        //if (::file.isInitialized) file.delete()
        filePath = ""
        binding.btnCameraClick.setImageResource(R.drawable.ic_camera_click)
        binding.btnCameraClick.show()
        binding.linearLayout1.hide()
        binding.linearLayout.show()
        binding.cdvDiscard.hide()
        binding.cdvImage.show()
        binding.circularProgressBar.hide()
        binding.camera.show()
        binding.videoView.hide()
    }

    private fun updateUiForRecording() {
        animateProgress(5000) {
            updateUiForCompleted()
        }
        requireActivity().runOnUiThread {
            binding.btnCameraClick.setImageResource(R.drawable.ic_recording)
            binding.linearLayout.invisible()
            binding.cdvImage.hide()
            binding.circularProgressBar.show()
        }
    }

    private fun updateUiForCompleted() {
        isRecording = false
        binding.camera.stopRecordingVideo()
        valueAnimator?.removeAllUpdateListeners()
        valueAnimator?.removeAllListeners()

        binding.linearLayout.invisible()
        binding.linearLayout1.show()
        binding.cdvDiscard.apply {
            show()
            setOnClickListener {
                showDiscardSheet()
            }
        }
        binding.cdvImage.hide()
        binding.btnCameraClick.hide()
        binding.camera.hide()
        binding.videoView.show()
    }

    private fun playVideo(file: Uri) {
        binding.videoView12.apply {
            setVideoURI(file)
            start()
        }
    }

    private fun animateProgress(duration: Long, onCompletion: () -> Unit) {
        binding.circularProgressBar.show()

        val gradientColors = intArrayOf(
            Color.parseColor("#F34F7F"),
            Color.parseColor("#DE52F9"),
            Color.parseColor("#8489F9"),
            Color.parseColor("#7A8EF9"),
            Color.parseColor("#C263F9"),
            Color.parseColor("#EC50AD"),
            Color.parseColor("#F05095"),
            Color.parseColor("#F44F75"),
            Color.parseColor("#F1508B")
        )
        val drawable = GradientCircularProgressDrawable(gradientColors)

        // Set the drawable to the progressBar
        binding.circularProgressBar.indeterminateDrawable = drawable
        binding.circularProgressBar.invalidate()

        // Now setup the ValueAnimator again
        valueAnimator = ValueAnimator.ofFloat(0f, 360f).apply {
            this.duration = duration
            interpolator = LinearInterpolator()
            addUpdateListener { animator ->
                val value = animator.animatedValue as Float
                // Ensure that the progress drawable is updated on the UI thread
                activity?.runOnUiThread {
                    drawable.setSweepAngle(value)
                }
            }
            addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    // Check if the animation was cancelled before calling onCompletion.
                    activity?.runOnUiThread {
                        onCompletion.invoke()
                    }
                }
            })
        }

        // Start the animation
        valueAnimator?.start()
    }

    private fun showDiscardSheet() {
        val sheet = BottomSheetDialog(requireContext())
        val view = SheetDiscardMediaBinding.inflate(layoutInflater)

        view.apply {
            imageView6.setOnClickListener {
                sheet.dismiss()
            }
            btnCancel.setOnClickListener {
                sheet.dismiss()
            }
            btnDiscard.setOnClickListener {
                sheet.dismiss()
                updateUiForIdle()
            }
        }

        sheet.setCancelable(true)
        sheet.setContentView(view.root)
        sheet.show()
    }

}

