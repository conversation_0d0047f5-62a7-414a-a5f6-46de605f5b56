<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.fragments.qr.QrReceivingFragment">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/imageView"
            android:layout_width="@dimen/_30sdp"
            android:layout_height="@dimen/_30sdp"
            android:layout_margin="@dimen/_20sdp"
            android:src="@drawable/ic_back_btn"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription" />

        <TextView
            android:id="@+id/textView"
            style="@style/h3"
            android:text="@string/my_qr_code"
            app:layout_constraintBottom_toBottomOf="@+id/imageView"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/imageView" />

        <androidx.cardview.widget.CardView
            android:id="@+id/cardView5"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/_20sdp"
            android:alpha="0.8"
            android:src="@drawable/ic_back_btn"
            app:cardBackgroundColor="@color/white"
            app:cardCornerRadius="32dp"
            app:cardElevation="0dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/imageView"
            tools:ignore="ContentDescription">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="@dimen/_20sdp">

                <RelativeLayout
                    android:id="@+id/qrView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <androidx.cardview.widget.CardView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/cvImageView"
                        android:layout_centerHorizontal="true"
                        android:layout_marginTop="-32dp"
                        app:cardBackgroundColor="@color/bgGray"
                        app:cardCornerRadius="12dp">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:paddingHorizontal="@dimen/_30sdp"
                            android:paddingVertical="@dimen/_20sdp">

                            <TextView
                                android:id="@+id/tvName"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                android:layout_marginTop="@dimen/_15sdp"
                                android:text="@string/show_me_some_love"
                                android:textColor="@color/black"
                                android:textStyle="bold" />

                            <ImageView
                                android:id="@+id/ivQrGifting"
                                android:layout_width="@dimen/_120sdp"
                                android:layout_height="@dimen/_100sdp"
                                android:layout_gravity="center"
                                android:layout_marginTop="@dimen/_10sdp"
                                android:src="@drawable/qr_sample" />

                        </LinearLayout>

                    </androidx.cardview.widget.CardView>

                    <androidx.cardview.widget.CardView
                        android:id="@+id/cvImageView"
                        android:layout_width="@dimen/_40sdp"
                        android:layout_height="@dimen/_40sdp"
                        android:layout_alignParentTop="true"
                        android:layout_centerHorizontal="true"
                        android:background="@color/bgGray"
                        android:src="@drawable/app_icon"
                        app:cardCornerRadius="@dimen/_50sdp"
                        app:shapeAppearance="@style/ShapeAppearance.Material3.Corner.Full">

                        <ImageView
                            android:id="@+id/ivUserImage"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:scaleType="centerCrop"
                            android:src="@drawable/ic_user_placeholder" />

                    </androidx.cardview.widget.CardView>

                </RelativeLayout>

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btnNext"
                    style="@style/button"
                    android:layout_width="match_parent"
                    android:layout_marginTop="@dimen/_20sdp"
                    android:drawableEnd="@drawable/ic_share_small"
                    android:paddingStart="@dimen/_30sdp"
                    android:paddingEnd="@dimen/_5sdp"
                    android:text="@string/share" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <TextView
            android:id="@+id/textView16"
            style="@style/text"
            android:layout_width="0dp"
            android:layout_gravity="center"
            android:layout_marginTop="@dimen/_20sdp"
            android:gravity="center"
            android:text="@string/share_this_code_and_tell_your_friends_nto_show_you_some_luv"
            android:textColor="@color/gray"
            app:layout_constraintEnd_toEndOf="@+id/cardView5"
            app:layout_constraintStart_toStartOf="@+id/cardView5"
            app:layout_constraintTop_toBottomOf="@+id/cardView5" />

        <TextView
            android:id="@+id/tvSponsored"
            style="@style/text"
            android:layout_width="0dp"
            android:layout_gravity="center"
            android:layout_marginTop="@dimen/_10sdp"
            android:gravity="center"
            android:text="@string/sponsored_by"
            android:textColor="@color/gray"
            app:layout_constraintEnd_toEndOf="@+id/cardView5"
            app:layout_constraintStart_toStartOf="@+id/cardView5"
            app:layout_constraintTop_toBottomOf="@+id/textView16" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</ScrollView>