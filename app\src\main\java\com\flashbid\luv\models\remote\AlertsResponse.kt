package com.flashbid.luv.models.remote

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

data class AlertsResponse(
    val error: Boolean,
    val message: List<AlertModel>
)


@Parcelize
data class AlertModel(
    val create_at: String,
    val id: Int,
    val image: String,
    val is_read: Int,
    val message: String,
    val body: String,
    val name: String?,
    val company_name: String?,
    val alert_type: Int?,
    val action: Int,
    val amount: Int,
    val update_at: String,
    val user_id: Int,
    val gift_name: String?,
    val transaction_id: String?,
    val sender_id: String?,
    val gift_image: String?,

    val lottery_id: Int?,
    val is_thanks_video: Int?,
    val story_sent: Int?,
    val gift_message: String,
    val drop_amount: Int,
    val is_video_viewed: String?,
    val created_at: String?
): Parcelable
