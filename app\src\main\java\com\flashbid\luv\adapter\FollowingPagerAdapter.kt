package com.flashbid.luv.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.viewpager.widget.PagerAdapter
import com.flashbid.luv.databinding.ItemHomeStatsBinding


class FollowingPagerAdapter(
    val fragment: Fragment,
    private val list: List<String>
) : PagerAdapter() {

    override fun destroyItem(container: ViewGroup, position: Int, `object`: Any) {
        container.removeView(`object` as View)
    }

    override fun getItemPosition(`object`: Any): Int {
        return POSITION_NONE
    }

    override fun getCount(): Int {
        return list.size
    }

    override fun isViewFromObject(view: View, `object`: Any): Bo<PERSON>an {
        return view === `object`
    }

    override fun instantiateItem(container: ViewGroup, position: Int): Any {
        val binding =
            ItemHomeStatsBinding.inflate(LayoutInflater.from(fragment.context), container, false)

        with(binding) {
            when (position) {
                0 -> {
                }
                1 -> {
                }
            }
        }
        container.addView(binding.root)

        return binding.root
    }

}