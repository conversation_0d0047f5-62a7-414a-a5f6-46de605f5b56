<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.media3.ui.PlayerView
        android:id="@+id/playerView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/black"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:use_controller="false" />

    <LinearLayout
        android:id="@+id/progressBarContainer"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_5sdp"
        android:orientation="horizontal"
        android:paddingTop="8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivUser"
        android:layout_width="@dimen/_35sdp"
        android:layout_height="@dimen/_35sdp"
        android:layout_marginStart="@dimen/_10sdp"
        android:layout_marginTop="@dimen/_15sdp"
        android:scaleType="centerCrop"
        android:src="@drawable/image"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/progressBarContainer"
        app:shapeAppearance="@style/ShapeAppearance.Material3.Corner.Full" />

    <androidx.cardview.widget.CardView
        android:id="@+id/cdvUserName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_6sdp"
        app:cardBackgroundColor="@color/white"
        app:cardCornerRadius="@dimen/_20sdp"
        app:cardElevation="0dp"
        app:contentPaddingLeft="@dimen/_10sdp"
        app:contentPaddingRight="@dimen/_10sdp"
        app:layout_constraintBottom_toBottomOf="@+id/ivUser"
        app:layout_constraintStart_toEndOf="@+id/ivUser"
        app:layout_constraintTop_toTopOf="@+id/ivUser">

        <TextView
            android:id="@+id/tvUsername"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:text="Username"
            android:textColor="@color/black"
            android:textSize="@dimen/_10sdp" />

    </androidx.cardview.widget.CardView>

    <androidx.cardview.widget.CardView
        android:id="@+id/cvClose"
        android:layout_width="@dimen/_30sdp"
        android:layout_height="@dimen/_30sdp"
        android:layout_marginTop="@dimen/_15sdp"
        android:layout_marginEnd="@dimen/_10sdp"
        app:cardBackgroundColor="@color/lightBlack"
        app:cardCornerRadius="@dimen/_20sdp"
        app:cardElevation="0dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/progressBarContainer">

        <ImageView
            android:id="@+id/ivClose"
            android:layout_width="@dimen/_20sdp"
            android:layout_height="@dimen/_20sdp"
            android:layout_gravity="center"
            android:src="@drawable/ic_close" />

    </androidx.cardview.widget.CardView>

    <ProgressBar
        android:id="@+id/pgBar"
        android:layout_width="@dimen/_40sdp"
        android:layout_height="@dimen/_40sdp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.5" />

</androidx.constraintlayout.widget.ConstraintLayout>