package com.flashbid.luv.util

import androidx.lifecycle.MutableLiveData

object Constants {
    const val CONTENT_TYPE = "application/json"
    const val PRIVACY_URL = "https://theluvnetwork.com/terms.html"
    val AUTH_TOKEN: MutableLiveData<String> = MutableLiveData()
    val PUBLIC_IP: MutableLiveData<String> = MutableLiveData()
    val FCM_TOKEN: MutableLiveData<String> = MutableLiveData()
    val CRATE_TYPE: MutableLiveData<String> = MutableLiveData()
    val CLAIM_STORE_REWARD: MutableLiveData<String> = MutableLiveData()
    val REFRESH_AUTH_TOKEN: MutableLiveData<String> = MutableLiveData()
    val BATTLE_INVITE: MutableLiveData<Pair<String?, String?>> = MutableLiveData()
    val BATTLE_SHARE: MutableLiveData<Triple<String?, String?, String?>> = MutableLiveData()
    val BATTLE_INVITE_RESPONSE: MutableLiveData<Triple<String?, String?, String?>> =
        MutableLiveData()
    var CURRENT_LOCALE: String = "en"
    const val DYNAMIC_LINK_PREFIX = "https://luvapp.page.link"

}

object BaseLiveData {
    val isAuthorized: MutableLiveData<Boolean> = MutableLiveData()
    val referralLiveData: MutableLiveData<Int> = MutableLiveData()
}

object RequestCodes {
    const val GALLERY_IMAGE = 111
    const val CAMERA_IMAGE = 112
    const val CAMERA_PERMISSION = 1001
}

object InAppConstants {
    const val WEEKLY_WITHDRAW_LIMIT = 1000
    const val MIN_USD_WITHDRAW_LIMIT = 50
}

object ParamCode {
    const val USER_FOLLOWED = 1
    const val USER_UNFOLLOWED = 0
}

object HistoryMapping {
    object ACTION {
        const val SENT = 0
        const val RECEIVED = 1
        const val RECHARGE = 2
        const val WITHDRAW = 3
        const val CHEST = 4
        const val REFERRAL = 5
        const val CRATE = 6
        const val SEND_LUV = 7
        const val DROP_LUV = 8
        const val CHEST_QR = 9
        const val REFERRAL_REWARD = 13
        const val QUEST_CRATE = 14
        const val IS_BONUS = 15
        const val LUV_REVEIED = 16
        const val QUEST_REWARD = 17
        const val OPEN_CRATE = 18
    }

    object AMOUNT {
        const val USD = 0
        const val LUV = 1
        const val DIAMOND = 2
    }

    object STATUS {
        const val PENDING = 0
        const val COMPLETED = 1
        const val VOID = 2
        const val SUSPENDED = 3
    }

    object PAYMENT {
        const val LUV = 0
        const val APPLE = 1
        const val ANDROID = 2
    }

    object PRIVACY {
        const val ONLY_ME = 1
        const val PEOPLE_FOLLOW = 0
    }

    object REPORT {
        const val Inappropriate = 1
    }
}