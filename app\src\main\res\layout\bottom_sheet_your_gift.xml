<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    android:background="@drawable/bg_round_corners"
    android:orientation="vertical"
    android:paddingHorizontal="@dimen/_15sdp"
    android:paddingVertical="@dimen/_10sdp"
    app:layout_behavior="@string/bottom_sheet_behavior">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/imageView6"
            android:layout_width="@dimen/_25sdp"
            android:layout_height="@dimen/_25sdp"
            android:src="@drawable/ic_close_gray"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/textView36"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Gift"
            android:textSize="@dimen/_18sdp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:id="@+id/llBalance"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/ivAdd"
                android:layout_width="@dimen/_30sdp"
                android:layout_height="@dimen/_30sdp"
                android:src="@drawable/iv_add" />

            <TextView
                android:id="@+id/tvBalance"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginStart="@dimen/_5sdp"
                android:textColor="#F95050"
                android:textSize="@dimen/_14sdp"
                tools:text="500" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rcvGift"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            tools:itemCount="2"
            tools:listitem="@layout/item_your_gift" />

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btnSend"
            style="@style/button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/send"
            android:layout_centerHorizontal="true"
            android:layout_margin="@dimen/_6sdp"
            android:layout_alignBottom="@+id/rcvGift"
            android:textColor="@color/white"
            android:visibility="gone"/>

    </RelativeLayout>

</LinearLayout>