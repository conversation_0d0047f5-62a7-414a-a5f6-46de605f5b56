package com.flashbid.luv.ui.fragments.auth

import android.app.Activity
import android.content.ContentValues.TAG
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.View
import androidx.activity.result.ActivityResult
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.view.doOnPreDraw
import androidx.fragment.app.Fragment
import androidx.navigation.NavOptions
import androidx.navigation.fragment.FragmentNavigatorExtras
import androidx.navigation.fragment.findNavController
import com.google.android.gms.auth.api.signin.GoogleSignIn
import com.google.android.gms.auth.api.signin.GoogleSignInAccount
import com.google.android.gms.auth.api.signin.GoogleSignInClient
import com.google.android.gms.auth.api.signin.GoogleSignInOptions
import com.google.android.gms.common.api.ApiException
import com.google.android.gms.tasks.Task
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.OAuthCredential
import com.google.firebase.auth.OAuthProvider
import com.flashbid.luv.BuildConfig
import com.flashbid.luv.R
import com.flashbid.luv.data.local.AppPreferences
import com.flashbid.luv.databinding.FragmentAuthSelectBinding
import com.flashbid.luv.extensions.setOnClickWithDebounce
import com.flashbid.luv.extensions.snackBar
import com.flashbid.luv.extensions.viewBinding
import com.flashbid.luv.network.Status
import com.flashbid.luv.util.BaseLiveData
import com.flashbid.luv.viewmodels.UserViewModel
import com.google.firebase.FirebaseApp
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel

class AuthSelectFragment : Fragment(R.layout.fragment_auth_select) {

    private val binding by viewBinding(FragmentAuthSelectBinding::bind)
    private val viewModel: UserViewModel by viewModel()
    private val pref by inject<AppPreferences>()
    private lateinit var googleSignInClient: GoogleSignInClient
    private lateinit var auth: FirebaseAuth
    private var referralCode: Int? = null

    private val googleResultLauncher: ActivityResultLauncher<Intent> =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult ->
            if (result.resultCode == Activity.RESULT_OK) {
                val task = GoogleSignIn.getSignedInAccountFromIntent(result.data)
                handleSignInResult(task)
            } else {
                snackBar(getString(R.string.something_went_wrong,"User cancelled"))
            }
        }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        BaseLiveData.referralLiveData.observe(viewLifecycleOwner) {
            referralCode = it
        }

        postponeEnterTransition()
        view.doOnPreDraw { startPostponedEnterTransition() }

        initGoogleAuthentication()

        binding.btnRegister.setOnClickWithDebounce {
            val extras =
                FragmentNavigatorExtras(binding.btnRegister to getString(R.string.signup_transition))
            val directions =
                AuthSelectFragmentDirections.actionAuthSelectFragmentToRegistrationFragment()
            findNavController().navigate(directions, extras)
        }

        binding.textView3.setOnClickWithDebounce {
            val extras =
                FragmentNavigatorExtras(binding.textView3 to getString(R.string.login_transition))
            val directions = AuthSelectFragmentDirections.actionAuthSelectFragmentToLoginFragment()
            findNavController().navigate(directions, extras)
        }

        binding.textView2.setOnClickWithDebounce {
            googleResultLauncher.launch(googleSignInClient.signInIntent)
        }

        binding.tvApple.setOnClickWithDebounce {
            loginWithApple()
        }

    }

    private fun loginWithApple() {
        val provider = OAuthProvider.newBuilder("apple.com")
        provider.scopes = mutableListOf("email", "name")
        auth = FirebaseAuth.getInstance()
        val pending = auth.pendingAuthResult
        if (pending != null) {
            pending.addOnSuccessListener { authResult ->
                val user = authResult.credential as OAuthCredential
                handleAppleSignIn(
                    user.idToken.toString(),
                    authResult.user?.email,
                    authResult.user?.displayName
                )
            }.addOnFailureListener { e ->
                Log.w(TAG, "checkPending:onFailure", e)
            }
        } else {
            Log.d(TAG, "pending: null")
            auth.startActivityForSignInWithProvider(requireActivity(), provider.build())
                .addOnSuccessListener { authResult ->
                    val user = authResult.credential as OAuthCredential
                    handleAppleSignIn(
                        user.idToken.toString(),
                        authResult.user?.email,
                        authResult.user?.displayName
                    )
                }
                .addOnFailureListener { e ->
                    Log.w(TAG, "checkPending:onFailure", e)
                }
        }
    }

    private fun handleAppleSignIn(token: String, email: String?, displayName: String?) {
        val words = displayName?.trim()?.split("\\s+".toRegex())?.toTypedArray()

        viewModel.appleLogin(
            words?.first() ?: "",
            if ((words?.size ?: 0) > 1) words?.last()?:"" else "",
            token,
            email ?: ""
        ).observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> {
                    snackBar(
                        it.message ?: getString(R.string.something_went_wrong, "Error logging in Apple ID")

                    )
                }
                Status.LOADING -> {
                }
                Status.SUCCESS -> {
                    val data = it.data
                    pref.saveUser(
                        data?.user_id,
                        data?.token,
                        data?.role,
                        email,
                        data?.refresh_token, false
                    )
                    if (data?.is_new_user == true)
                        findNavController().navigate(R.id.action_authSelectFragment_to_onboardFragment)
                    else {
                        val navController = findNavController()
                        val navOptions = NavOptions.Builder()
                            .setPopUpTo(R.id.homeFragment, true)
                            .build()
                        navController.navigate(R.id.action_authSelectFragment_to_homeFragment, null, navOptions)
                    }
                }
            }
        }
    }

    private fun initGoogleAuthentication() {
        val gso = GoogleSignInOptions.Builder(GoogleSignInOptions.DEFAULT_SIGN_IN)
            .requestIdToken(BuildConfig.GOOGLE_CLIENT_ID)
            .requestServerAuthCode(BuildConfig.GOOGLE_CLIENT_ID)
            .requestEmail()
            .requestProfile()
            .build()
        googleSignInClient = GoogleSignIn.getClient(requireContext(), gso)
    }

    private fun handleSignInResult(completedTask: Task<GoogleSignInAccount>) {
        try {
            val account = completedTask.getResult(ApiException::class.java)
            val serverAuthCode = account.serverAuthCode
            if (serverAuthCode != null) {
                viewModel.googleLogin(serverAuthCode, referralCode).observe(viewLifecycleOwner) {
                    when (it.status) {
                        Status.ERROR -> snackBar(it.message ?: getString(R.string.something_went_wrong, "Error logging in Google ID"))
                        Status.LOADING -> {}
                        Status.SUCCESS -> {
                            val data = it.data
                            pref.saveUser(
                                data?.user_id,
                                data?.token,
                                data?.role,
                                account.email,
                                data?.refresh_token, false
                            )
                            if (data?.is_new_user == true)
                                findNavController().navigate(R.id.action_authSelectFragment_to_onboardFragment)
                            else {
                                val navController = findNavController()
                                val navOptions = NavOptions.Builder()
                                    .setPopUpTo(R.id.homeFragment, true)
                                    .build()
                                navController.navigate(R.id.action_authSelectFragment_to_homeFragment, null, navOptions)
                            }
                        }
                    }
                }
            } else snackBar(getString(R.string.something_went_wrong, getString(R.string.try_again)))
        } catch (e: ApiException) {
            snackBar(e.message ?: getString(R.string.something_went_wrong, getString(R.string.try_again)))
        }
    }
}

