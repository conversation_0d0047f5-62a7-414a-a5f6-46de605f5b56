<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:visibility="gone"
    android:background="@drawable/bg_round_corners"
    android:paddingHorizontal="@dimen/_10sdp"
    android:paddingVertical="@dimen/_10sdp">

    <ImageView
        android:id="@+id/imageView4"
        android:layout_width="@dimen/_40sdp"
        android:layout_height="@dimen/_40sdp"
        android:src="@drawable/ic_h"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvUsername"
        style="@style/small"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_10sdp"
        android:ellipsize="end"
        android:maxLines="3"
        android:text="@string/you_can_now_receive_a_luv_drop_from_people_bear_you_enable_in_settings"
        android:textColor="#99000000"
        android:textSize="@dimen/_10sdp"
        app:layout_constraintBottom_toBottomOf="@+id/imageView4"
        app:layout_constraintEnd_toStartOf="@+id/btnEnableLocation"
        app:layout_constraintStart_toEndOf="@+id/imageView4"
        app:layout_constraintTop_toTopOf="@+id/imageView4" />

    <TextView
        android:id="@+id/btnEnableLocation"
        style="@style/small"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_4sdp"
        android:background="@drawable/bg_edit_red"
        android:gravity="center"
        android:paddingHorizontal="@dimen/_10sdp"
        android:paddingVertical="@dimen/_10sdp"
        android:text="@string/enable"
        android:textColor="#F95050"
        app:layout_constraintBottom_toBottomOf="@+id/tvUsername"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvUsername" />

</androidx.constraintlayout.widget.ConstraintLayout>