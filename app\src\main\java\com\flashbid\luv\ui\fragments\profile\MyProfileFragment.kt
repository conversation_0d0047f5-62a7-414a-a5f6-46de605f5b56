package com.flashbid.luv.ui.fragments.profile

import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import com.flashbid.luv.R
import com.flashbid.luv.adapter.FavoriteAdapter
import com.flashbid.luv.data.local.AppPreferences
import com.flashbid.luv.databinding.FragmentMyProfileBinding
import com.flashbid.luv.extensions.hide
import com.flashbid.luv.extensions.setHorizontalLayout
import com.flashbid.luv.extensions.setOnClickWithDebounce
import com.flashbid.luv.extensions.shortenAmount
import com.flashbid.luv.extensions.show
import com.flashbid.luv.extensions.snackBar
import com.flashbid.luv.extensions.viewBinding
import com.flashbid.luv.models.remote.Favourite
import com.flashbid.luv.models.remote.UpdateProfileRequest
import com.flashbid.luv.models.remote.UserDetailsResponse
import com.flashbid.luv.network.Status
import com.flashbid.luv.util.getColor
import com.flashbid.luv.util.loadImageFromUrl
import com.flashbid.luv.viewmodels.UserViewModel
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel

class MyProfileFragment : Fragment(R.layout.fragment_my_profile) {

    private val binding by viewBinding(FragmentMyProfileBinding::bind)
    private val userViewModel: UserViewModel by viewModel()
    private val favList: ArrayList<Favourite> = ArrayList()
    private val favAdapter by lazy { FavoriteAdapter(favList, this::onFavClick) }
    private val pref by inject<AppPreferences>()

    private fun onFavClick(item: Favourite) {
        val action = MyProfileFragmentDirections.actionMyProfileFragmentToUserProfileFragment(
            item.brand_user_id ?: 0
        )
        findNavController().navigate(action)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.rcvFav.apply {
            setHorizontalLayout()
            adapter = favAdapter
        }

        setBasicProfileData()

        binding.imageView.setOnClickWithDebounce {
            findNavController().popBackStack()
        }

        binding.btnSetting.setOnClickWithDebounce {
            findNavController().navigate(R.id.action_myProfileFragment_to_settingFragment)
        }

        binding.btnSwitchPersonal.setOnClickWithDebounce {
            switchProfile(0)
        }
        binding.btnSwitchBrand.setOnClickWithDebounce {
            switchProfile(1)
        }

        binding.llFollower.setOnClickWithDebounce {
            findNavController().navigate(R.id.action_myProfileFragment_to_userFollowersFragment)
        }

        binding.llFollowing.setOnClickWithDebounce {
            findNavController().navigate(R.id.action_myProfileFragment_to_userFollowingsFragment)
        }

        binding.textView19.setOnClickWithDebounce {
            findNavController().navigate(R.id.action_myProfileFragment_to_chooseFavFragment)
        }

    }

    private fun getUserStats() {
        userViewModel.getUserStats().observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(
                    it.message ?: getString(
                        R.string.something_went_wrong, getString(R.string.try_again)
                    )
                )

                Status.LOADING -> {}
                Status.SUCCESS -> {
                    setUserData(it.data?.message)
                }
            }
        }
    }

    private fun switchProfile(isCompany: Int) {
        userViewModel.updatePreference(
            UpdateProfileRequest(
                UpdateProfileRequest.Payload(
                    is_company = isCompany,
                    fcm_token = pref.fcmToken,
                )
            )
        ).observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> {
                    snackBar(
                        it.message ?: getString(
                            R.string.something_went_wrong, getString(R.string.try_again)
                        )
                    )
                }

                Status.LOADING -> {}
                Status.SUCCESS -> {
                    getUserStats()
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
        getUserDetails()
        getUserStats()
    }

    private fun getUserDetails() {
        userViewModel.getUserDetails().observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(
                    it.message ?: getString(
                        R.string.something_went_wrong, getString(R.string.try_again)
                    )
                )
                Status.LOADING -> {}
                Status.SUCCESS -> {
                    val data = it.data?.message
                    pref.saveUserData(
                        data?.first_name,
                        data?.last_name,
                        data?.username ?: "",
                        data?.photo,
                        data?.bio,
                        data?.user_id,
                        data?.age,
                        data?.gender,
                        data?.show_following,
                        data?.show_follower
                    )
                    setBasicProfileData()
                }
            }
        }
    }

    private fun setBasicProfileData() {
        with(binding) {
            ivImage.loadImageFromUrl(pref.photo)
            tvName.text = pref.firstName + " " + pref.lastName
            textView17.text = "@" + pref.userName
            tvBio.apply {
                if (pref.bio.isNullOrEmpty()) {
                    text = getString(R.string.add_bio)
                    setTextColor(getColor(R.color.redgradstart))
                    setOnClickWithDebounce {
                        findNavController().navigate(R.id.action_myProfileFragment_to_editProfileFragment)
                    }
                } else {
                    text = pref.bio
                    setTextColor(getColor(R.color.gray))
                }
            }
        }
    }

    private fun setUserData(message: UserDetailsResponse.Message?) {
        pref.isBrandProfile = (message?.user_details?.is_company ?: 0) != 0
        with(binding) {
            tvFollowers.text = message?.followers
            tvFollowing.text = message?.following
            tvDiamonds.text = message?.total_diamonds_received?.toInt()?.shortenAmount()
            tvHearts.text = message?.total_luv_sent?.toInt()?.shortenAmount()
            tvRank.text = message?.global_ranking
            tvTopDaily.text = message?.daily_top_gift.toString()
            tvSentGift.text = message?.top_sent_gift
            pref.industry = message?.user_details?.industry ?: message?.industry
            pref.website = message?.user_details?.website
            tvWebsite.text = pref.website
            tvIndustry.text = pref.industry
            tvCompanyBio.text = message?.user_details?.bio
            tvSentGift.text = message?.top_sent_gift

            if (message?.favourites.isNullOrEmpty()) {
                llNoFavs.apply {
                    show()
                    setOnClickWithDebounce {
                        findNavController().navigate(R.id.action_myProfileFragment_to_chooseFavFragment)
                    }
                }
                clFavs.hide()
            } else {
                favList.clear()
                favList.addAll(message?.favourites ?: ArrayList())
                favAdapter.refresh()
                llNoFavs.hide()
                clFavs.show()
            }

            if (pref.isBrandProfile) {
                brandIcon.show()
                btnSwitchBrand.hide()
                tvBio.hide()
                btnSwitchPersonal.show()
                llBrandViews.show()
            } else {
                brandIcon.hide()
                btnSwitchBrand.show()
                btnSwitchPersonal.hide()
                tvBio.show()
                llBrandViews.hide()
            }
        }
    }

}