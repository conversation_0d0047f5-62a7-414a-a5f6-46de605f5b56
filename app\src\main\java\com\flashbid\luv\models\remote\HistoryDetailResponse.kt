package com.flashbid.luv.models.remote

data class HistoryDetailResponse(
    val error: Boolean,
    val mapping: Mapping,
    val message: Message
) {
    data class Mapping(
        val action: Action,
        val payment_method: PaymentMethod,
        val received_amount: ReceivedAmount,
        val sent_unit: SentUnit,
        val status: Status
    ) {
        data class Action(
            val `0`: String,
            val `1`: String,
            val `2`: String,
            val `3`: String
        )

        data class PaymentMethod(
            val `0`: String,
            val `1`: String,
            val `2`: String
        )

        data class ReceivedAmount(
            val `0`: String,
            val `1`: String,
            val `2`: String
        )

        data class SentUnit(
            val `0`: String,
            val `1`: String,
            val `2`: String
        )

        data class Status(
            val `0`: String,
            val `1`: String,
            val `2`: String,
            val `3`: String
        )
    }

    data class Message(
        val action: Int,
        val amount: Int,
        val first_name: String,
        val id: Int,
        val last_name: String,
        val usd: String?,
        val payment_method: Int?,
        val photo: String?,
        val received_amount: Int,
        val status: Int,
        val update_at: String,
        val user_id: Int,
        val receiver_user_id: Int,
        val sender_user_id: Int
    )
}