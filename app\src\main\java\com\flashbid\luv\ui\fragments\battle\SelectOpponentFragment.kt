package com.flashbid.luv.ui.fragments.battle

import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.flashbid.luv.MainNavDirections
import com.flashbid.luv.R
import com.flashbid.luv.databinding.FragmentSelectOpponentBinding
import com.flashbid.luv.extensions.hide
import com.flashbid.luv.extensions.show
import com.flashbid.luv.extensions.snackBar
import com.flashbid.luv.extensions.viewBinding
import com.flashbid.luv.network.Status
import com.flashbid.luv.util.Constants
import com.flashbid.luv.util.loadImageFromUrl
import com.flashbid.luv.viewmodels.BattleViewModel
import org.koin.androidx.viewmodel.ext.android.viewModel

class SelectOpponentFragment : Fragment(R.layout.fragment_select_opponent) {

    private val binding by viewBinding(FragmentSelectOpponentBinding::bind)
    private val args by navArgs<SelectOpponentFragmentArgs>()
    private val viewmodel: BattleViewModel by viewModel()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.tvUsername.text = args.userDetail.first_name
        binding.tvName.text = args.userDetail.username
        binding.ivUser.loadImageFromUrl(args.userDetail.photo)

        binding.ivBack.setOnClickListener { findNavController().popBackStack() }
        binding.btnOk.setOnClickListener { findNavController().popBackStack() }

        binding.btnStartBattle.setOnClickListener {
            findNavController().navigate(R.id.action_selectOpponentFragment_to_battleFragment)
        }

        val opponentId = args.userDetail.user_id ?: args.userDetail.id

        binding.skipButton.setOnClickListener {
            if (opponentId != null) {
                sendBattleResponse("cancel", opponentId)
            }
        }

        binding.btnSearch.show()
        binding.clDeclined.hide()
        Constants.BATTLE_INVITE_RESPONSE.postValue(Triple("null", null, null))


        if (opponentId != null) {
            requestBattle(opponentId)
        } else findNavController().popBackStack()
    }

    private fun sendBattleResponse(status: String, uid: Int) {
        viewmodel.battleResponse(status, uid).observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> /*snackBar(
                    it.message ?: getString(
                        R.string.something_went_wrong, getString(R.string.try_again)
                    )
                )*/
                    findNavController().popBackStack()

                Status.LOADING -> {}
                Status.SUCCESS -> {
                    findNavController().popBackStack()
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
    }

    private fun requestBattle(uid: Int) {
        Constants.BATTLE_INVITE_RESPONSE.observe(viewLifecycleOwner) {
            if (!it.first.equals("null") && it.third.isNullOrEmpty()) {
                binding.btnSearch.hide()
                binding.clDeclined.show()
                binding.tvDeclined.text =
                    it.first
            }
        }
        viewmodel.battleRequest(uid).observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> {
                    snackBar(
                        it.message ?: getString(
                            R.string.something_went_wrong, getString(R.string.try_again)
                        )
                    )

                    if(it.message != null) {

                        if(it.message.contains("User is in a battle") || it.message.contains("User has a battle ongoing")) {
                            binding.tvDeclined.text = it.message
                        } else {
                            binding.tvDeclined.text =
                                getString(R.string.we_couldn_t_find_a_rival_for_you_please_try_later)
                        }
                    } else {
                        binding.tvDeclined.text =
                            getString(R.string.we_couldn_t_find_a_rival_for_you_please_try_later)
                    }

                    binding.btnSearch.hide()
                    binding.clDeclined.show()

                }
                Status.LOADING -> {}
                Status.SUCCESS -> {
                    binding.btnSearch.show()
                    binding.clDeclined.hide()
                    binding.tvStatus.text = getString(R.string.challenge_sent_waiting_for_reply)
                }
            }
        }
    }

}