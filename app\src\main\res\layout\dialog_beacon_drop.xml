<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent">

    <androidx.cardview.widget.CardView
        android:id="@+id/dialogCardView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_15sdp"
        app:cardBackgroundColor="@color/white"
        app:cardCornerRadius="@dimen/_26sdp"
        app:cardElevation="1dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingHorizontal="@dimen/_15sdp"
            android:paddingBottom="@dimen/_20sdp">

            <ImageView
                android:id="@+id/imageView8"
                android:layout_width="@dimen/_28sdp"
                android:layout_height="@dimen/_28sdp"
                android:layout_gravity="end"
                android:layout_marginTop="@dimen/_10sdp"
                android:src="@drawable/ic_close_gray"
                tools:ignore="ContentDescription" />

            <ImageView
                android:id="@+id/iv_gift"
                android:layout_width="match_parent"
                android:layout_height="@dimen/_180sdp"
                android:src="@drawable/brands_welcome"
                app:layout_constraintBottom_toBottomOf="@+id/textView29"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@+id/textView29"
                tools:ignore="ContentDescription" />

            <TextView
                android:id="@+id/tvBrand"
                style="@style/h2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_5sdp"
                android:layout_marginTop="@dimen/_15sdp"
                android:layout_gravity="center"
                android:gravity="center"
                android:text="@string/welcome_to_n_brand_name"
                app:layout_constraintTop_toBottomOf="@+id/iv_gift" />

            <TextView
                android:id="@+id/textView29"
                style="@style/text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="16dp"
                android:layout_marginTop="@dimen/_10sdp"
                android:text="@string/here_s_a_little_welcome_gift"
                android:textAlignment="center"
                android:textColor="@color/gray"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvAmount" />

            <TextView
                android:id="@+id/tvAmount"
                style="@style/h2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_5sdp"
                android:layout_marginTop="@dimen/_15sdp"
                android:layout_gravity="center"
                android:gravity="center"
                android:text="@string/_10_luv"
                app:layout_constraintTop_toBottomOf="@+id/iv_gift" />

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btnOpen"
                style="@style/button"
                android:layout_marginTop="@dimen/_10sdp"
                android:text="@string/awesome"
                android:textColor="@color/white"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvForgot" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

</FrameLayout>