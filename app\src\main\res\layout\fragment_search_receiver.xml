<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.fragments.operations.sendLuv.SearchReceiverFragment">

    <ImageView
        android:id="@+id/imageView"
        android:layout_width="@dimen/_30sdp"
        android:layout_height="@dimen/_30sdp"
        android:layout_margin="@dimen/_20sdp"
        android:src="@drawable/ic_back_btn"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/textView"
        style="@style/h3"
        android:text="@string/send_luv"
        app:layout_constraintBottom_toTopOf="@+id/tvInfo"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/imageView" />

    <TextView
        android:id="@+id/tvInfo"
        style="@style/text"
        android:layout_width="wrap_content"
        android:gravity="center"
        android:text="@string/balance"
        android:textColor="@color/gray"
        app:layout_constraintBottom_toBottomOf="@+id/imageView"
        app:layout_constraintEnd_toStartOf="@+id/tvBalance"
        app:layout_constraintStart_toStartOf="@+id/textView"
        app:layout_constraintTop_toBottomOf="@+id/textView" />

    <TextView
        android:id="@+id/tvBalance"
        style="@style/text"
        android:layout_width="wrap_content"
        android:drawablePadding="@dimen/_2sdp"
        android:gravity="center"
        android:paddingStart="5dp"
        android:paddingEnd="0dp"
        android:textColor="@color/gray"
        app:drawableEndCompat="@drawable/heart_small"
        app:layout_constraintBottom_toBottomOf="@+id/tvInfo"
        app:layout_constraintEnd_toEndOf="@+id/textView"
        app:layout_constraintStart_toEndOf="@+id/tvInfo"
        app:layout_constraintTop_toTopOf="@+id/tvInfo"
        tools:text="500" />

    <androidx.cardview.widget.CardView
        android:id="@+id/cvTabs"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_20sdp"
        android:layout_marginTop="@dimen/_10sdp"
        app:cardBackgroundColor="@color/bgGray"
        app:cardCornerRadius="16dp"
        app:cardElevation="0dp"
        app:contentPadding="6dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="@+id/imageView"
        app:layout_constraintTop_toBottomOf="@+id/imageView">

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tabLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/bgGray"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="@+id/imageView"
            app:layout_constraintTop_toBottomOf="@+id/tvBalance"
            app:tabBackground="@drawable/tab_item_background"
            app:tabGravity="fill"
            app:tabIndicatorHeight="0dp"
            app:tabMode="fixed"
            app:tabRippleColor="@null" />

    </androidx.cardview.widget.CardView>

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/viewPager"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/cvTabs" />

    <!--    <androidx.recyclerview.widget.RecyclerView-->
    <!--        android:layout_width="0dp"-->
    <!--        android:layout_height="0dp"-->
    <!--        app:layout_constraintBottom_toBottomOf="parent"-->
    <!--        app:layout_constraintEnd_toEndOf="parent"-->
    <!--        app:layout_constraintHorizontal_bias="0.5"-->
    <!--        app:layout_constraintStart_toStartOf="parent"-->
    <!--        app:layout_constraintTop_toBottomOf="@+id/tabLayout"-->
    <!--        tools:listitem="@layout/item_search" />-->

</androidx.constraintlayout.widget.ConstraintLayout>