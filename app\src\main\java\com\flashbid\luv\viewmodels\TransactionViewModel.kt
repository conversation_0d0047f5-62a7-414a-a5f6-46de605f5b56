package com.flashbid.luv.viewmodels

import androidx.lifecycle.LiveData
import androidx.lifecycle.ViewModel
import com.flashbid.luv.models.remote.AllPlansResponse
import com.flashbid.luv.models.remote.CreateCode
import com.flashbid.luv.models.remote.CreateKeyRequest
import com.flashbid.luv.models.remote.DiamondRateResponse
import com.flashbid.luv.models.remote.HistoryDetailResponse
import com.flashbid.luv.models.remote.MessageResponse
import com.flashbid.luv.models.remote.PayoutDataResponse
import com.flashbid.luv.models.remote.RandomUserResponse
import com.flashbid.luv.models.remote.RechargeRequest
import com.flashbid.luv.models.remote.SendLuvRequest
import com.flashbid.luv.models.remote.TransactionsResponse
import com.flashbid.luv.models.remote.UnlockKeyRequest
import com.flashbid.luv.models.remote.ValidationData
import com.flashbid.luv.models.remote.WeeklyLimitResponse
import com.flashbid.luv.models.remote.WithdrawRequest
import com.flashbid.luv.network.Resource
import com.flashbid.luv.repositories.TransactionRepository

class TransactionViewModel(
    private val transactionRepository: TransactionRepository
) : ViewModel() {

    fun getCommunityTransactions(page: Int, limit: Int): LiveData<Resource<TransactionsResponse>> {
        return transactionRepository.getCommunityTransactions(page, limit)
    }

    fun getPayoutData(): LiveData<Resource<PayoutDataResponse>> {
        return transactionRepository.getPayoutData()
    }

    fun getAllPlans(): LiveData<Resource<AllPlansResponse>> {
        return transactionRepository.getAllPlans()
    }

    fun getRandomUser(): LiveData<Resource<RandomUserResponse>> {
        return transactionRepository.getRandomUser()
    }

    fun withdrawDiamonds(
        amount: Int, country: String, currency: String, link: String, method: Int
    ): LiveData<Resource<MessageResponse>> {
        return transactionRepository.withdrawDiamonds(
            WithdrawRequest(
                amount, country, currency, link, method
            )
        )
    }

    fun getHistoryDetail(
        id: String
    ): LiveData<Resource<HistoryDetailResponse>> {
        return transactionRepository.getHistoryDetail(id)
    }

    fun sendLuv(giftId: Int, userId: Int, isRandom: Boolean, message: String): LiveData<Resource<MessageResponse>> {
        return transactionRepository.sendLuv(SendLuvRequest(giftId, userId, isRandom, message))
    }

    fun getDiamondRate(amount: String): LiveData<Resource<DiamondRateResponse>> {
        return transactionRepository.getDiamondRate(amount)
    }

    fun getWithdrawLimit(): LiveData<Resource<WeeklyLimitResponse>> {
        return transactionRepository.getWithdrawLimit()
    }

    fun rechargePlan(
        id: Int, name: String, transactionId: String, productId: String
    ): LiveData<Resource<MessageResponse>> {
        return transactionRepository.rechargePlan(
            RechargeRequest(
                id, "android", ValidationData(name, productId, transactionId)
            )
        )
    }

    fun createKeyCrate(
        quantity: Int, total_value: Int, values: List<Int>
    ): LiveData<Resource<MessageResponse>> {
        return transactionRepository.createKeyCrate(
            CreateKeyRequest(
                quantity, total_value, values
            )
        )
    }

    fun unlockKeyCrate(
        codeId: String,
        key_phrase: String
    ): LiveData<Resource<MessageResponse>> {
        return transactionRepository.unlockKeyCrate(codeId, UnlockKeyRequest(key_phrase))
    }

}