<?xml version="1.0" encoding="utf-8"?>

<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/mainLayout"
    tools:context=".ui.fragments.quest.QuestFragment">

    <TextView
        android:id="@+id/textView20"
        style="@style/h2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_20sdp"
        android:text="@string/quests"
        android:textSize="26sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/textView21"
        style="@style/subtitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:maxLines="1"
        android:text="@string/daily_rewards_for_you"
        android:textColor="@color/gray"
        app:layout_constraintStart_toStartOf="@+id/textView20"
        app:layout_constraintTop_toBottomOf="@+id/textView20" />

    <LinearLayout
        android:id="@+id/linearLayout2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_30sdp"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/_20sdp"
        android:paddingVertical="@dimen/_10sdp"
        app:layout_constraintTop_toBottomOf="@+id/textView21">

        <ImageView
            android:layout_width="@dimen/_30sdp"
            android:layout_height="@dimen/_30sdp"
            android:src="@drawable/quest4"
            tools:ignore="ContentDescription" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_10sdp"
            android:orientation="vertical">

            <TextView
                style="@style/text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:text="@string/refer_friends" />

            <TextView
                style="@style/small"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="@string/_500x_luv_each"
                android:textColor="@color/gray" />

        </LinearLayout>

    </LinearLayout>

    <View
        android:id="@+id/view4"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginStart="@dimen/_55sdp"
        android:layout_marginEnd="@dimen/_10sdp"
        android:background="@color/semigray"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/linearLayout2" />

    <LinearLayout
        android:id="@+id/linearLayout3"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/_20sdp"
        android:paddingVertical="@dimen/_10sdp"
        app:layout_constraintTop_toBottomOf="@+id/view4">

        <ImageView
            android:layout_width="@dimen/_30sdp"
            android:layout_height="@dimen/_30sdp"
            android:src="@drawable/quest5"
            tools:ignore="ContentDescription" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginStart="@dimen/_10sdp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvDailyReward"
                style="@style/text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:text="@string/send_get_luv" />

            <TextView
                android:id="@+id/tvTime"
                style="@style/small"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="@string/send_luv_get_luv"
                android:textColor="@color/gray" />

        </LinearLayout>

    </LinearLayout>

    <View
        android:id="@+id/view5"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginStart="@dimen/_55sdp"
        android:layout_marginEnd="@dimen/_10sdp"
        android:background="@color/semigray"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/linearLayout3" />

    <LinearLayout
        android:id="@+id/btnCrate"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/_20sdp"
        android:paddingVertical="@dimen/_10sdp"
        app:layout_constraintTop_toBottomOf="@+id/view5">

        <ImageView
            android:layout_width="@dimen/_30sdp"
            android:layout_height="@dimen/_30sdp"
            android:src="@drawable/quest6"
            tools:ignore="ContentDescription" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_10sdp"
            android:orientation="vertical">

            <TextView
                style="@style/text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:text="@string/open_luv_crate" />

            <TextView
                style="@style/small"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="@string/get_100_extra_luv"
                android:textColor="@color/gray" />

        </LinearLayout>

    </LinearLayout>

    <View
        android:id="@+id/view6"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginStart="@dimen/_55sdp"
        android:layout_marginEnd="@dimen/_10sdp"
        android:background="@color/semigray"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/btnCrate" />

    <LinearLayout
        android:id="@+id/btnEarnDrop"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/_20sdp"
        android:paddingVertical="@dimen/_10sdp"
        app:layout_constraintTop_toBottomOf="@+id/view6">

        <ImageView
            android:layout_width="@dimen/_30sdp"
            android:layout_height="@dimen/_30sdp"
            android:src="@drawable/luvdrop"
            tools:ignore="ContentDescription" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_10sdp"
            android:orientation="vertical">

            <TextView
                style="@style/text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:text="@string/earn_luv_drops" />

            <TextView
                style="@style/small"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="@string/visit_particopating_stores"
                android:textColor="@color/gray" />

        </LinearLayout>

    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>