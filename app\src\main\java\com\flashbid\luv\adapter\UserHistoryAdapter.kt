package com.flashbid.luv.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.flashbid.luv.R
import com.flashbid.luv.databinding.ItemUserHistoryBinding
import com.flashbid.luv.extensions.*
import com.flashbid.luv.models.remote.ItemHistory
import com.flashbid.luv.models.remote.Transaction
import com.flashbid.luv.util.HistoryMapping
import com.flashbid.luv.util.getColor
import com.flashbid.luv.util.loadImageFromUrl

class UserHistoryAdapter(val userClick: (Int, Int, Int) -> Unit, val uid: Int) :
    RecyclerView.Adapter<UserHistoryAdapter.ViewHolder>() {

    private val list: ArrayList<ItemHistory> = ArrayList()

    inner class ViewHolder(val binding: ItemUserHistoryBinding) :
        RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val itemView =
            ItemUserHistoryBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            )
        return ViewHolder(itemView)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val model = list[position]

        with(holder) {
            itemView.setOnClickWithDebounce {
                userClick(model.id, model.action, model.sender_user_id)
            }
            binding.ivCurrency.setImageResource(
                when (model.received_amount) {
                    HistoryMapping.AMOUNT.LUV -> {
                        R.drawable.heart_colored
                    }
                    HistoryMapping.AMOUNT.DIAMOND -> {
                        R.drawable.diamond_colored
                    }
                    HistoryMapping.AMOUNT.USD -> {
                        R.drawable.ic_dollar_small
                    }
                    else -> {
                        R.drawable.heart_colored
                    }
                }
            )

            when (model.action) {
                HistoryMapping.ACTION.RECEIVED -> {
                    binding.tvAmountFirst.hide()
                    binding.ivActionImage.show()
                    binding.ivActionImage.setImageResource(R.drawable.gift)
                    binding.ivActionImage.setColorFilter(binding.ivActionImage.getColor(R.color.gray))
                    binding.ivUserReceived.loadImageFromUrl(model.photo)
                    binding.tvAmount.text = "+ ${model.amount}"
                    if(model.drop_type == 1) {
                        binding.ivActionImage.hide()

                        if (uid == model.sender_user_id) {
                            binding.tvAction.text = binding.tvAction.context.getString(R.string.luv_drop_sent)
                            binding.tvAmount.text = "- ${model.amount}"
                            binding.ivUserReceived.setImageResource(R.drawable.ic_withdraw)
                            binding.ivCurrency.setColorFilter(binding.ivCurrency.getColor(R.color.gray))
                            binding.tvAmount.setTextColor(binding.tvAmount.getColor(R.color.gray))
                        } else {
                            binding.tvAction.text = binding.tvAction.context.getString(R.string.luv_drop)
                            binding.tvAmount.text = "+ ${model.amount}"
                            binding.ivCurrency.setColorFilter(binding.ivCurrency.getColor(R.color.redgradstart))
                            binding.tvAmount.setTextColor(binding.tvAmount.getColor(R.color.redgradstart))
                        }
                        binding.tvUsernameTime.text = binding.tvUsernameTime.context.getString(
                            R.string.username_x_time, "",
                            model.update_at.getTimeInAgo()
                        )

                    } else {
                        binding.ivActionImage.show()
                        binding.tvAction.text = binding.tvAction.context.getString(R.string.gift_received)
                        binding.tvUsernameTime.text = binding.tvUsernameTime.context.getString(
                            R.string.username_x_time, "${model.first_name + " " + model.last_name}",
                            model.update_at.getTimeInAgo()
                        )
                        binding.ivCurrency.setColorFilter(binding.ivCurrency.getColor(R.color.blue))
                        binding.tvAmount.setTextColor(binding.tvAmount.getColor(R.color.blue))
                    }


                }
                HistoryMapping.ACTION.SENT -> {
                    binding.ivActionImage.setImageResource(R.drawable.gift)
                    binding.ivActionImage.setColorFilter(binding.ivActionImage.getColor(R.color.gray))
                    binding.ivActionImage.show()
                    binding.ivUserReceived.loadImageFromUrl(model.photo)
                    binding.tvAmount.text = "- ${model.amount}"
                    binding.tvAction.text = binding.tvAction.context.getString(R.string.gift_sent)
                    binding.tvAmountFirst.hide()
                    binding.ivCurrency.setColorFilter(binding.ivCurrency.getColor(R.color.gray))
                    binding.tvAmount.setTextColor(binding.tvAmount.getColor(R.color.gray))
                    binding.tvUsernameTime.text = binding.tvUsernameTime.context.getString(
                        R.string.username_x_time, "${model.first_name + " " + model.last_name}",
                        model.update_at.getTimeInAgo()
                    )
                }
                HistoryMapping.ACTION.RECHARGE -> {
                    binding.ivUserReceived.setImageResource(R.drawable.ic_recharge_arrow)
                    binding.ivActionImage.hide()
                    binding.tvAmount.text = "+ ${model.amount}"
                    binding.tvAction.text = binding.tvAction.context.getString(R.string.recharge)
                    binding.tvAmountFirst.show()
                    binding.tvAmountFirst.text = "$${model.currency_amount}"
                    binding.tvAmountFirst.setTextColor(binding.tvAmountFirst.getColor(R.color.gray))
                    binding.tvUsernameTime.text = model.update_at.getTimeInAgo()
                    binding.ivCurrency.setColorFilter(binding.ivCurrency.getColor(R.color.redgradstart))
                    binding.tvAmount.setTextColor(binding.tvAmount.getColor(R.color.redgradstart))
                }
                HistoryMapping.ACTION.WITHDRAW -> {
                    binding.ivUserReceived.setImageResource(R.drawable.ic_withdraw)
                    binding.ivActionImage.hide()
                    binding.tvAction.text = binding.tvAction.context.getString(R.string.withdraw)
                    binding.tvAmountFirst.show()
                    binding.tvAmount.text = "- ${model.amount}"
                    binding.tvAmountFirst.text = "+ $${model.currency_amount}"
                    binding.tvAmountFirst.setTextColor(binding.tvAmountFirst.getColor(R.color.green))
                    binding.tvUsernameTime.text = model.update_at.getTimeInAgo()
                    binding.ivCurrency.setColorFilter(binding.ivCurrency.getColor(R.color.gray))
                    binding.tvAmount.setTextColor(binding.tvAmount.getColor(R.color.gray))

                }
                HistoryMapping.ACTION.CHEST -> {

                    if(model.drop_type == 1) {
                        binding.ivActionImage.hide()

                        if (uid == model.sender_user_id) {
                            binding.tvAction.text = binding.tvAction.context.getString(R.string.luv_drop_sent)
                            binding.tvAmount.text = "- ${model.amount}"
                            binding.ivUserReceived.setImageResource(R.drawable.ic_withdraw)
                            binding.ivCurrency.setColorFilter(binding.ivCurrency.getColor(R.color.gray))
                            binding.tvAmount.setTextColor(binding.tvAmount.getColor(R.color.gray))
                        } else {

                            binding.ivUserReceived.loadImageFromUrl(model.photo)
                            binding.tvAction.text = binding.tvAction.context.getString(R.string.luv_drop)
                            binding.tvAmount.text = "+ ${model.amount}"
                            binding.ivCurrency.setColorFilter(binding.ivCurrency.getColor(R.color.redgradstart))
                            binding.tvAmount.setTextColor(binding.tvAmount.getColor(R.color.redgradstart))
                        }
                        binding.tvUsernameTime.text = binding.tvUsernameTime.context.getString(
                            R.string.username_x_time, "",
                            model.update_at.getTimeInAgo()
                        )

                    } else {

                        if (uid == model.sender_user_id) {
                            binding.ivUserReceived.setImageResource(R.drawable.ic_luv_chest_sent)
                            binding.tvAmount.text = "- ${model.amount}"
                            binding.tvAmount.setTextColor(binding.tvAmount.getColor(R.color.gray))
                            binding.ivCurrency.setColorFilter(binding.ivCurrency.getColor(R.color.gray))
                            binding.tvAction.text = binding.tvAction.context.getString(R.string.luv_chest_sent)
                        } else {
                            binding.ivUserReceived.setImageResource(R.drawable.ic_luv_chest_sent)
                            binding.tvAmount.text = "+ ${model.amount}"
                            binding.tvAmount.setTextColor(binding.tvAmount.getColor(R.color.redgradstart))
                            binding.ivCurrency.setColorFilter(binding.ivCurrency.getColor(R.color.redgradstart))
                            binding.tvAction.text = binding.tvAction.context.getString(R.string.opened_chest)
                        }

                        binding.ivActionImage.hide()
                        binding.tvAmountFirst.hide()
                        binding.tvUsernameTime.text = model.update_at.getTimeInAgo()
                    }
                }
                HistoryMapping.ACTION.REFERRAL -> {
                    binding.ivUserReceived.setImageResource(R.drawable.ic_withdraw)
                    binding.ivActionImage.hide()
                    binding.tvAmount.text = "+ ${model.amount}"
                    binding.tvAction.text = binding.tvAction.context.getString(R.string.reward)
                    binding.tvAmountFirst.hide()
                    binding.tvUsernameTime.text = model.update_at.getTimeInAgo()
                    binding.ivCurrency.setColorFilter(binding.ivCurrency.getColor(R.color.gray))
                    binding.tvAmount.setTextColor(binding.tvAmount.getColor(R.color.redgradstart))

                }
                HistoryMapping.ACTION.REFERRAL_REWARD -> {
                    binding.ivUserReceived.setImageResource(R.drawable.ic_recharge_arrow)
                    binding.ivActionImage.hide()
                    binding.tvAmount.text = "+ ${model.amount}"
                    binding.tvAction.text = binding.tvAction.context.getString(R.string.referral_reward_2)
                    binding.tvAmountFirst.hide()
                    binding.tvUsernameTime.text = model.update_at.getTimeInAgo()
                    binding.ivCurrency.setColorFilter(binding.ivCurrency.getColor(R.color.gray))
                    binding.tvAmount.setTextColor(binding.tvAmount.getColor(R.color.redgradstart))

                }

                HistoryMapping.ACTION.CRATE -> {

                    if (uid == model.sender_user_id) {
                        binding.tvAction.text = binding.tvAction.context.getString(R.string.created_crate_bundle)
                        binding.ivUserReceived.setImageResource(R.drawable.sent_luv_image)
                        binding.tvAmount.text = "- ${model.amount}"
                        binding.tvAmount.setTextColor(binding.tvAmount.getColor(R.color.gray))
                        binding.ivCurrency.setColorFilter(binding.ivCurrency.getColor(R.color.gray))
                    } else {
                        binding.tvAction.text = binding.tvAction.context.getString(R.string.opened_luv_cate)
                        binding.ivUserReceived.setImageResource(R.drawable.received_luv_image)
                        binding.tvAmount.text = "+ ${model.amount}"
                        binding.tvAmount.setTextColor(binding.tvAmount.getColor(R.color.redgradstart))
                        binding.ivCurrency.setColorFilter(binding.ivCurrency.getColor(R.color.redgradstart))
                    }

                    binding.ivActionImage.hide()
                    binding.tvAmountFirst.hide()
                    binding.tvUsernameTime.text = model.update_at.getTimeInAgo()

                }

                HistoryMapping.ACTION.QUEST_CRATE -> {

                    if (uid == model.sender_user_id) {
                        //binding.tvAction.text = binding.tvAction.context.getString(R.string.created_crate_bundle)
                        binding.tvAction.text = binding.tvAction.context.getString(R.string.quest_reward)
                        binding.ivUserReceived.setImageResource(R.drawable.ic_withdraw)
                        binding.tvAmount.text = "- ${model.amount}"
                        binding.tvAmount.setTextColor(binding.tvAmount.getColor(R.color.gray))
                        binding.ivCurrency.setColorFilter(binding.ivCurrency.getColor(R.color.gray))
                    } else {
                        //binding.tvAction.text = binding.tvAction.context.getString(R.string.opened_luv_cate)
                        binding.tvAction.text = binding.tvAction.context.getString(R.string.quest_reward)
                        binding.ivUserReceived.setImageResource(R.drawable.ic_recharge_arrow)
                        binding.tvAmount.text = "+ ${model.amount}"
                        binding.tvAmount.setTextColor(binding.tvAmount.getColor(R.color.redgradstart))
                        binding.ivCurrency.setColorFilter(binding.ivCurrency.getColor(R.color.redgradstart))
                    }

                    binding.ivActionImage.hide()
                    binding.tvAmountFirst.hide()
                    binding.tvUsernameTime.text = model.update_at.getTimeInAgo()

                }

                HistoryMapping.ACTION.QUEST_REWARD -> {

                    if (uid == model.sender_user_id) {
                        //binding.tvAction.text = binding.tvAction.context.getString(R.string.created_crate_bundle)
                        binding.tvAction.text = binding.tvAction.context.getString(R.string.quest_reward)
                        binding.ivUserReceived.setImageResource(R.drawable.ic_withdraw)
                        binding.tvAmount.text = "- ${model.amount}"
                        binding.tvAmount.setTextColor(binding.tvAmount.getColor(R.color.gray))
                        binding.ivCurrency.setColorFilter(binding.ivCurrency.getColor(R.color.gray))
                    } else {
                        //binding.tvAction.text = binding.tvAction.context.getString(R.string.opened_luv_cate)
                        binding.tvAction.text = binding.tvAction.context.getString(R.string.quest_reward)
                        binding.ivUserReceived.setImageResource(R.drawable.ic_recharge_arrow)
                        binding.tvAmount.text = "+ ${model.amount}"
                        binding.tvAmount.setTextColor(binding.tvAmount.getColor(R.color.redgradstart))
                        binding.ivCurrency.setColorFilter(binding.ivCurrency.getColor(R.color.redgradstart))
                    }

                    binding.ivActionImage.hide()
                    binding.tvAmountFirst.hide()
                    binding.tvUsernameTime.text = model.update_at.getTimeInAgo()

                }

                HistoryMapping.ACTION.IS_BONUS -> {

                    if (uid == model.sender_user_id) {
                        //binding.tvAction.text = binding.tvAction.context.getString(R.string.created_crate_bundle)
                        binding.tvAction.text = binding.tvAction.context.getString(R.string.quest_reward)
                        binding.ivUserReceived.setImageResource(R.drawable.ic_withdraw)
                        binding.tvAmount.text = "- ${model.amount}"
                        binding.tvAmount.setTextColor(binding.tvAmount.getColor(R.color.gray))
                        binding.ivCurrency.setColorFilter(binding.ivCurrency.getColor(R.color.gray))
                    } else {
                        //binding.tvAction.text = binding.tvAction.context.getString(R.string.opened_luv_cate)
                        binding.tvAction.text = binding.tvAction.context.getString(R.string.quest_reward)
                        binding.ivUserReceived.setImageResource(R.drawable.ic_recharge_arrow)
                        binding.tvAmount.text = "+ ${model.amount}"
                        binding.tvAmount.setTextColor(binding.tvAmount.getColor(R.color.redgradstart))
                        binding.ivCurrency.setColorFilter(binding.ivCurrency.getColor(R.color.redgradstart))
                    }

                    binding.ivActionImage.hide()
                    binding.tvAmountFirst.hide()
                    binding.tvUsernameTime.text = model.update_at.getTimeInAgo()

                }

                HistoryMapping.ACTION.SEND_LUV -> {

                    if (uid == model.sender_user_id) {
                        binding.ivUserReceived.setImageResource(R.drawable.ic_withdraw)
                        binding.tvAmount.text = "- ${model.amount}"
                        binding.tvAmount.setTextColor(binding.tvAmount.getColor(R.color.gray))
                        binding.ivCurrency.setColorFilter(binding.ivCurrency.getColor(R.color.gray))
                    } else {
                        binding.ivUserReceived.setImageResource(R.drawable.ic_recharge_arrow)
                        binding.tvAmount.text = "+ ${model.amount}"
                        binding.tvAmount.setTextColor(binding.tvAmount.getColor(R.color.redgradstart))
                        binding.ivCurrency.setColorFilter(binding.ivCurrency.getColor(R.color.redgradstart))
                    }

                    binding.ivActionImage.hide()
                    //binding.tvAction.text = binding.tvAction.context.getString(R.string.quest_reward)
                    binding.tvAction.text = binding.tvAction.context.getString(R.string.opened_crate)
                    binding.tvAmountFirst.hide()
                    binding.tvUsernameTime.text = model.update_at.getTimeInAgo()

                }
                HistoryMapping.ACTION.OPEN_CRATE-> {

                    if (uid == model.sender_user_id) {

                        binding.ivUserReceived.setImageResource(R.drawable.ic_recharge_arrow)
                        binding.tvAmount.text = "+ ${model.amount}"
                        binding.tvAmount.setTextColor(binding.tvAmount.getColor(R.color.redgradstart))
                        binding.ivCurrency.setColorFilter(binding.ivCurrency.getColor(R.color.redgradstart))
                    } else {
                        binding.ivUserReceived.setImageResource(R.drawable.ic_withdraw)
                        binding.tvAmount.text = "- ${model.amount}"
                        binding.tvAmount.setTextColor(binding.tvAmount.getColor(R.color.gray))
                        binding.ivCurrency.setColorFilter(binding.ivCurrency.getColor(R.color.gray))


                    }

                    binding.ivActionImage.hide()
                    //binding.tvAction.text = binding.tvAction.context.getString(R.string.quest_reward)
                    binding.tvAction.text = binding.tvAction.context.getString(R.string.opened_crate)
                    binding.tvAmountFirst.hide()
                    binding.tvUsernameTime.text = model.update_at.getTimeInAgo()

                }
                HistoryMapping.ACTION.DROP_LUV -> {
                    if (uid == model.sender_user_id) {
                        binding.ivUserReceived.setImageResource(R.drawable.ic_withdraw)
                        binding.tvAmount.text = "- ${model.amount}"
                        binding.tvAmount.setTextColor(binding.tvAmount.getColor(R.color.gray))
                        binding.ivCurrency.setColorFilter(binding.ivCurrency.getColor(R.color.gray))
                        binding.tvAction.text = binding.tvAction.context.getString(R.string.sponsored_drop_sent)
                    } else {
                        binding.ivUserReceived.loadImageFromUrl(model.photo)
                        binding.tvAmount.text = "+ ${model.amount}"
                        binding.tvAmount.setTextColor(binding.tvAmount.getColor(R.color.redgradstart))
                        binding.ivCurrency.setColorFilter(binding.ivCurrency.getColor(R.color.redgradstart))
                        binding.tvAction.text = binding.tvAction.context.getString(R.string.sponsored_drop_received)
                    }

                    binding.ivActionImage.hide()
                    binding.tvAmountFirst.hide()
                    binding.tvUsernameTime.text = model.update_at.getTimeInAgo()
                }
                HistoryMapping.ACTION.CHEST_QR -> {

                    if (uid == model.sender_user_id) {
                        binding.ivUserReceived.setImageResource(R.drawable.ic_withdraw)
                        binding.tvAmount.text = "- ${model.amount}"
                        binding.tvAmount.setTextColor(binding.tvAmount.getColor(R.color.gray))
                        binding.ivCurrency.setColorFilter(binding.ivCurrency.getColor(R.color.gray))
                        binding.tvAction.text = binding.tvAction.context.getString(R.string.luv_chest_sent)

                    } else {
                        binding.ivUserReceived.setImageResource(R.drawable.ic_recharge_arrow)
                        binding.tvAmount.text = "+ ${model.amount}"
                        binding.tvAmount.setTextColor(binding.tvAmount.getColor(R.color.redgradstart))
                        binding.ivCurrency.setColorFilter(binding.ivCurrency.getColor(R.color.redgradstart))
                        binding.tvAction.text = binding.tvAction.context.getString(R.string.luv_chest_received)

                    }

                    binding.ivActionImage.hide()
                    binding.tvAmountFirst.hide()
                    binding.tvUsernameTime.text = model.update_at.getTimeInAgo()
                }
                HistoryMapping.ACTION.LUV_REVEIED -> {

                    binding.tvAmountFirst.hide()
                    binding.ivActionImage.hide()
                    binding.tvAction.text = binding.tvAction.context.getString(R.string.luv_drop)
                    binding.tvUsernameTime.text = binding.tvUsernameTime.context.getString(
                        R.string.username_x_time, "",
                        model.update_at.getTimeInAgo()
                    )
                    binding.ivUserReceived.setImageResource(R.drawable.ic_recharge_arrow)
                    binding.tvAmount.text = "+ ${model.amount}"

                    binding.ivCurrency.setColorFilter(binding.ivCurrency.getColor(R.color.redgradstart))
                    binding.tvAmount.setTextColor(binding.tvAmount.getColor(R.color.redgradstart))

                }

            }

        }

    }

    override fun getItemCount(): Int {
        return list.size
    }

    fun refresh(newList: ArrayList<ItemHistory>) {
        list.clear()
        list.addAll(newList)
        notifyDataSetChanged()
    }

    fun appendTransactions(newTransactions: ArrayList<ItemHistory>) {
        val start = this.list.size
        this.list.addAll(newTransactions)
        notifyItemRangeInserted(start, newTransactions.size)
    }
}