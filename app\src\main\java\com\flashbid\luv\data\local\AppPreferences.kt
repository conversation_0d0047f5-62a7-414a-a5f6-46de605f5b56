package com.flashbid.luv.data.local

import android.content.Context
import android.content.SharedPreferences
import com.flashbid.luv.util.Constants
import org.koin.android.ext.koin.androidContext
import org.koin.dsl.module

val prefModule = module {
    single { AppPreferences(androidContext()) }
}

class AppPreferences(context: Context) {

    private val preferences: SharedPreferences =
        context.getSharedPreferences("prefs", Context.MODE_PRIVATE)

    var isLoggedIn: Boolean
        get() = preferences.getBoolean("isLoggedIn", false)
        set(value) = preferences.edit().putBoolean("isLoggedIn", value).apply()

    var isEmailAccount: Boolean
        get() = preferences.getBoolean("isEmailAccount", false)
        set(value) = preferences.edit().putBoolean("isEmailAccount", value).apply()

    private var _token: String? = null
    private var _refreshToken: String? = null
    private var _fcmToken: String? = null

    var accessToken: String?
        get() = token()
        set(value) = preferences.edit().putString("token", value).apply()

    var refreshToken: String?
        get() = refreshToken()
        set(value) = preferences.edit().putString("refreshToken", value).apply()

    var isBrandProfile: Boolean
        get() = preferences.getBoolean("isBrandProfile", false)
        set(value) = preferences.edit().putBoolean("isBrandProfile", value).apply()

    var email: String?
        get() = preferences.getString("email", null)
        set(value) = preferences.edit().putString("email", value).apply()

    var fcmToken: String?
        get() = preferences.getString("fcmToken", null)
        set(value) = preferences.edit().putString("fcmToken", value).apply()

    var firstName: String?
        get() = preferences.getString("firstName", "")
        set(value) = preferences.edit().putString("firstName", value).apply()

    var userName: String?
        get() = preferences.getString("userName", "")
        set(value) = preferences.edit().putString("userName", value).apply()

    var bio: String?
        get() = preferences.getString("bio", null)
        set(value) = preferences.edit().putString("bio", value).apply()

    var photo: String?
        get() = preferences.getString("photo", null)
        set(value) = preferences.edit().putString("photo", value).apply()

    var lastName: String?
        get() = preferences.getString("lastName", null)
        set(value) = preferences.edit().putString("lastName", value).apply()

    var role: String?
        get() = preferences.getString("role", null)
        set(value) = preferences.edit().putString("role", value).apply()

    var website: String?
        get() = preferences.getString("website", null)
        set(value) = preferences.edit().putString("website", value).apply()

    var referral: String?
        get() = preferences.getString("referral", null)
        set(value) = preferences.edit().putString("referral", value).apply()

    var industry: String?
        get() = preferences.getString("industry", null)
        set(value) = preferences.edit().putString("industry", value).apply()

    var appLanguage: String?
        get() = preferences.getString("appLanguage", null)
        set(value) = preferences.edit().putString("appLanguage", value).apply()

    var userId: Int?
        get() = preferences.getInt("userId", -1)
        set(value) = preferences.edit().putInt("userId", value ?: -1).apply()

    var userAge: String?
        get() = preferences.getString("age", null)
        set(value) = preferences.edit().putString("age", value).apply()

    var genders: String?
        get() = preferences.getString("gender", null)
        set(value) = preferences.edit().putString("gender", value).apply()

    var period: String?
        get() = preferences.getString("period", null)
        set(value) = preferences.edit().putString("period", value).apply()

    private fun token(): String? {
        _token?.let { return it } ?: run {
            _token = preferences.getString("token", null)
            return _token
        }
    }

    private fun refreshToken(): String? {
        _refreshToken?.let { return it } ?: run {
            _refreshToken = preferences.getString("refreshToken", null)
            return _refreshToken
        }
    }

    var balanceLuv: Long
        get() = preferences.getLong("balanceLuv", 0)
        set(value) = preferences.edit().putLong("balanceLuv", value).apply()

    var balanceDiamond: Int
        get() = preferences.getInt("balanceDiamond", 0)
        set(value) = preferences.edit().putInt("balanceDiamond", value).apply()

    var follower: Int?
        get() = preferences.getInt("follower", 0)
        set(value) = preferences.edit().putInt("follower", value ?: -1).apply()

    var following: Int?
        get() = preferences.getInt("following", 0)
        set(value) = preferences.edit().putInt("following", value ?: -1).apply()

    var isNearbyEnabled: Int?
        get() = preferences.getInt("isNearbyEnabled", -1)
        set(value) = preferences.edit().putInt("isNearbyEnabled", value ?: -1).apply()

    var balanceUsd: String?
        get() = preferences.getString("balanceUsd", "0")
        set(value) = preferences.edit().putString("balanceUsd", value).apply()


    fun logout() {
        userId = null
        _token = null
        _fcmToken = null
        accessToken = null
        email = null
        firstName = null
        lastName = null
        userName = null
        bio = null
        photo = null
        referral = null
        role = null
        isLoggedIn = false
        isBrandProfile = false
        isEmailAccount = false
    }

    fun saveUser(
        id: Int?,
        token: String?,
        userRole: String?,
        emailAddress: String?,
        refresh: String?,
        isEmail: Boolean
    ) {
        userId = id
        accessToken = "Bearer $token"
        refreshToken = refresh
        Constants.AUTH_TOKEN.value = "Bearer $token"
        Constants.REFRESH_AUTH_TOKEN.value = refresh ?: ""
        role = userRole
        isLoggedIn = true
        email = emailAddress
        isEmailAccount = isEmail
    }

    fun setBalance(heart: Long, diamond: Int, usd: String) {
        balanceLuv = heart
        balanceDiamond = diamond
        balanceUsd = usd
    }

    fun saveUserData(
        first: String?,
        last: String?,
        username: String?,
        image: String?,
        userBio: String?,
        id: Int?,
        age: String?,
        gender: String?,
        showFollowing: Int?,
        showFollower: Int?
    ) {
        firstName = first
        lastName = last
        userName = username
        photo = image
        bio = userBio
        userId = id
        userAge = age
        genders = gender
        follower = showFollower
        following = showFollowing
    }

}