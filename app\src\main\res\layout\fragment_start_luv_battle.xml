<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.fragments.battle.StartLuvBattleFragment">

    <ImageView
        android:id="@+id/ivBack"
        android:layout_width="@dimen/_30sdp"
        android:layout_height="@dimen/_30sdp"
        android:layout_margin="@dimen/_20sdp"
        android:elevation="10dp"
        android:src="@drawable/ic_back_btn"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="@drawable/ic_gift_box,ContentDescription" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/_50sdp">

            <ImageView
                android:id="@+id/imageView13"
                android:layout_width="@dimen/_80sdp"
                android:layout_height="@dimen/_80sdp"
                android:layout_marginTop="@dimen/_15sdp"
                android:src="@drawable/iv_cup"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvMainFirst"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_14sdp"
                android:text="@string/start_luv_battle"
                android:textColor="@color/black"
                android:textSize="@dimen/_18sdp"
                app:layout_constraintEnd_toEndOf="@+id/imageView13"
                app:layout_constraintStart_toStartOf="@+id/imageView13"
                app:layout_constraintTop_toBottomOf="@+id/imageView13" />

            <TextView
                android:id="@+id/tvMainSecond"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_10sdp"
                android:gravity="center"
                android:text="@string/who_would_you_like_to_nchallenge_to_battle"
                android:textColor="@color/gray"
                android:textSize="@dimen/_12sdp"
                app:layout_constraintEnd_toEndOf="@+id/tvMainFirst"
                app:layout_constraintStart_toStartOf="@+id/tvMainFirst"
                app:layout_constraintTop_toBottomOf="@+id/tvMainFirst" />

            <LinearLayout
                android:id="@+id/llFollowersFollowing"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_12sdp"
                android:layout_marginTop="@dimen/_12sdp"
                android:orientation="horizontal"
                android:weightSum="2"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvMainSecond">

                <LinearLayout
                    android:id="@+id/btnFollowers"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:background="@drawable/bg_corners"
                    android:orientation="vertical"
                    android:padding="@dimen/_10sdp">

                    <ImageView
                        android:id="@+id/iv_followers"
                        android:layout_width="@dimen/_40sdp"
                        android:layout_height="@dimen/_40sdp"
                        android:src="@drawable/iv_followers" />

                    <TextView
                        android:id="@+id/tvFollowers"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_10sdp"
                        android:text="@string/followers"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_14sdp" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/btnFollowing"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_10sdp"
                    android:layout_weight="1"
                    android:background="@drawable/bg_corners"
                    android:orientation="vertical"
                    android:padding="@dimen/_10sdp">

                    <ImageView
                        android:id="@+id/iv_following"
                        android:layout_width="@dimen/_40sdp"
                        android:layout_height="@dimen/_40sdp"
                        android:src="@drawable/iv_following" />

                    <TextView
                        android:id="@+id/tvFollowing"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_10sdp"
                        android:text="@string/following"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_14sdp" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/btnWorld"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_12sdp"
                android:layout_marginTop="@dimen/_10sdp"
                android:orientation="horizontal"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/llFollowersFollowing">

                <LinearLayout
                    android:id="@+id/llTheWorld"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:background="@drawable/bg_corners"
                    android:orientation="vertical"
                    android:padding="@dimen/_10sdp">

                    <ImageView
                        android:layout_width="@dimen/_40sdp"
                        android:layout_height="@dimen/_40sdp"
                        android:src="@drawable/iv_world" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_10sdp"
                        android:text="@string/the_world_random_user"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_14sdp" />

                </LinearLayout>

            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>