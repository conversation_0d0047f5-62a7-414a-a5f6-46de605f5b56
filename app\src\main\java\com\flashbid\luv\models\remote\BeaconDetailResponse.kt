package com.flashbid.luv.models.remote

data class BeaconDetailResponse(
    val `data`: Data,
    val error: Boolean,
    val message: String
) {
    data class Data(
        val beacon: List<Beacon>
    ) {
        data class Beacon(
            val amount: Int,
            val beacon_uid: String,
            val brand_address: String,
            val brand_assigned: String,
            val contact: Any,
            val create_at: String,
            val date_assigned: String,
            val drops_count: Int,
            val frequency: Any,
            val id: Int,
            val name: Any,
            val status: String,
            val update_at: String,
            val user_id: Int,
            val user_name: String
        )
    }
}