package com.flashbid.luv.models.battle

import java.util.UUID


sealed class UnifiedStreamMessage {

    abstract val type: String

    data class BattleStartMessage(
        val battleStartMessage: BattleStartMessage2?,
    ) : UnifiedStreamMessage() {
        override val type: String = "BattleStartMessage"
    }

    data class BattleStartMessage2(
        val time: String?,
        val isBattleStart: Int?,
        val udid: UUID = UUID.randomUUID()
    )

    data class TextMessage(
        val textMessage: TextMessage2?
    ) : UnifiedStreamMessage() {
        override val type: String = "TextMessage"
    }

    data class TextMessage2(
        val text: String,
        val sender: String,
        val photo: String?,
        val uid: String ?,
        val inviterId: String ?,
        val udid: UUID = UUID.randomUUID()
    )

    data class GiftMessage(
        val giftMessage: GiftMessage2?,
    ) : UnifiedStreamMessage() {
        override val type: String = "GiftMessage"
    }

    data class GiftMessage2(
        val value: String?,
        val name: String,
        val photo: String?,
        val id: String?,
        val senderImage: String?,
        val senderName: String?,
        val senderId: String?,
        val receiverId: String?,
        val udid: UUID = UUID.randomUUID()

    )

    data class AudienceMessage(
        val audienceMessage: AudienceMessage2?,
    ) : UnifiedStreamMessage() {
        override val type: String = "AudienceMessage"
    }

    data class AudienceMessage2(
        val joinerImage: String?,
        val joinerName: String?,
        val joinerId: String?,
        val inviterId: String?,
        val udid: UUID = UUID.randomUUID()
    )



    data class BattleEndMessage(
        val battleEndMessage: BattleEndMessage2?
    ) : UnifiedStreamMessage() {
        override val type: String = "BattleEndMessage"
    }

    data class BattleEndMessage2(
        val winnerImage: String?,
        val winnerName: String?,
        val winnerId: String?,
        val udid: UUID = UUID.randomUUID()
    )

    data class BattleClosedMessage(
        val battleClosedMessage: BattleClosedMessage2?
    ) : UnifiedStreamMessage() {
        override val type: String = "BattleClosedMessage"
    }

    data class BattleClosedMessage2(
        val userId: String?,
        val udid: UUID = UUID.randomUUID()
    )

    data class BattleRematchMessage(
        val battleClosedMessage: BattleRematchMessage2?
    ) : UnifiedStreamMessage() {
        override val type: String = "BattleRematchMessage"
    }

    data class BattleRematchMessage2(
        val userId: String?,
        val udid: UUID = UUID.randomUUID()
    )

    data class AudianceLeaveMessage(
        val audianceLeaveMessage: AudianceLeaveMessage2?
    ) : UnifiedStreamMessage() {
        override val type: String = "AudianceLeaveMessage"
    }

    data class AudianceLeaveMessage2(
        val joinerImage: String?,
        val joinerName: String?,
        val joinerId: String?,
        val inviterId: String?,
        val udid: UUID = UUID.randomUUID()
    )

    data class UnKnownMessage(
        val unknowMessage: String?,
    ) : UnifiedStreamMessage() {
        override val type: String = "UnKnownMessage"
    }
}


