<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:animateLayoutChanges="true">

    <androidx.fragment.app.FragmentContainerView
        android:id="@+id/nav_host_fragment_activity_main"
        android:name="androidx.navigation.fragment.NavHostFragment"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:defaultNavHost="true"
        app:layout_constraintBottom_toTopOf="@id/nav_view"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.0"
        app:navGraph="@navigation/mobile_navigation" />

    <include
        android:id="@+id/includeNotification"
        layout="@layout/item_notification_receive"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_8sdp"
        android:layout_marginTop="@dimen/_5sdp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <include
        android:id="@+id/includeNotificationShare"
        layout="@layout/item_share_receive"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_8sdp"
        android:layout_marginTop="@dimen/_5sdp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <include
        android:id="@+id/includeLocationShare"
        layout="@layout/item_notification_nearby"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_8sdp"
        android:layout_marginTop="@dimen/_5sdp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/llInfo"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_38sdp"
        android:background="@color/semigreen"
        android:gravity="center"
        android:orientation="horizontal"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@+id/nav_view"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <ImageView
            android:layout_width="@dimen/_18sdp"
            android:layout_height="@dimen/_18sdp"
            android:layout_marginEnd="@dimen/_10sdp"
            android:src="@drawable/ic_success" />

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/tvInfo"
            style="@style/text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/green"
            android:textSize="16sp"
            android:textStyle="bold" />

    </LinearLayout>


    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/nav_view"
        android:layout_width="0dp"
        android:layout_height="@dimen/_50sdp"
        android:layout_marginStart="0dp"
        android:layout_marginEnd="0dp"
        android:background="?android:attr/windowBackground"
        android:visibility="gone"
        app:itemActiveIndicatorStyle="@style/CustomActiveIndicatorStyle"
        app:itemIconSize="25dp"
        app:itemPaddingBottom="@dimen/_8sdp"
        app:itemRippleColor="@null"
        app:itemTextColor="@color/black"
        app:labelVisibilityMode="labeled"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:menu="@menu/bottom_nav_menu">

        <ImageView
            android:id="@+id/btnBattle"
            android:layout_width="@dimen/_35sdp"
            android:layout_height="@dimen/_35sdp"
            android:layout_gravity="center"
            android:elevation="@dimen/_50sdp"
            android:src="@drawable/iv_add" />

    </com.google.android.material.bottomnavigation.BottomNavigationView>

</androidx.constraintlayout.widget.ConstraintLayout>