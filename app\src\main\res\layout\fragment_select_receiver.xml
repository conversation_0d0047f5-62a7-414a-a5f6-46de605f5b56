<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.fragments.operations.sendLuv.SelectReceiverFragment">

    <ImageView
        android:id="@+id/imageView"
        android:layout_width="@dimen/_30sdp"
        android:layout_height="@dimen/_30sdp"
        android:layout_margin="@dimen/_20sdp"
        android:src="@drawable/ic_back_btn"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/textView"
        style="@style/h3"
        android:text="@string/send_luv"
        app:layout_constraintBottom_toTopOf="@+id/tvInfo"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/imageView" />

    <TextView
        android:id="@+id/tvInfo"
        style="@style/text"
        android:layout_width="wrap_content"
        android:gravity="center"
        android:text="@string/balance"
        android:textColor="@color/gray"
        app:layout_constraintBottom_toBottomOf="@+id/imageView"
        app:layout_constraintEnd_toStartOf="@+id/tvBalance"
        app:layout_constraintStart_toStartOf="@+id/textView"
        app:layout_constraintTop_toBottomOf="@+id/textView" />

    <TextView
        android:id="@+id/tvBalance"
        style="@style/text"
        android:layout_width="wrap_content"
        android:drawablePadding="@dimen/_2sdp"
        android:gravity="center"
        android:paddingStart="5dp"
        android:paddingEnd="0dp"
        android:textColor="@color/gray"
        app:drawableEndCompat="@drawable/heart_small"
        app:layout_constraintBottom_toBottomOf="@+id/tvInfo"
        app:layout_constraintEnd_toEndOf="@+id/textView"
        app:layout_constraintStart_toEndOf="@+id/tvInfo"
        app:layout_constraintTop_toTopOf="@+id/tvInfo"
        tools:text="500" />

    <TextView
        android:id="@+id/textView9"
        style="@style/text"
        android:layout_width="wrap_content"
        android:layout_marginEnd="@dimen/_20sdp"
        android:gravity="center"
        android:text="@string/_1_2"
        android:textColor="@color/gray"
        app:layout_constraintBottom_toBottomOf="@+id/tvInfo"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/textView" />

    <TextView
        android:id="@+id/tvError"
        style="@style/text"
        android:layout_width="0dp"
        android:layout_marginTop="@dimen/_20sdp"
        android:gravity="center"
        android:padding="@dimen/_5sdp"
        android:textColor="@color/gray"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@+id/textView9"
        app:layout_constraintStart_toStartOf="@+id/imageView"
        app:layout_constraintTop_toBottomOf="@+id/tvInfo" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:paddingTop="@dimen/_20sdp"
        app:layout_constraintBottom_toTopOf="@+id/btnNext"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvInfo">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingHorizontal="@dimen/_20sdp">

            <View
                android:id="@+id/view"
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:background="@color/semigray"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />


            <TextView
                android:id="@+id/textView10"
                style="@style/text"
                android:layout_width="wrap_content"
                android:layout_marginTop="@dimen/_20sdp"
                android:gravity="center"
                android:text="@string/your_gift"
                android:textColor="@color/gray"
                app:layout_constraintStart_toStartOf="@+id/view"
                app:layout_constraintTop_toBottomOf="@+id/view" />

            <androidx.cardview.widget.CardView
                android:id="@+id/cardView"
                android:layout_width="@dimen/_120sdp"
                android:layout_height="@dimen/_60sdp"
                app:cardBackgroundColor="#E9E9F0"
                app:cardCornerRadius="20dp"
                app:cardElevation="0dp"
                app:layout_constraintEnd_toEndOf="@+id/view"
                app:layout_constraintTop_toTopOf="@+id/textView10">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:paddingHorizontal="@dimen/_8sdp"
                    android:paddingVertical="@dimen/_5sdp">

                    <ImageView
                        android:id="@+id/imageView2"
                        android:layout_width="@dimen/_40sdp"
                        android:layout_height="@dimen/_40sdp"
                        android:src="@drawable/sendgift"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:ignore="ContentDescription" />

                    <TextView
                        android:id="@+id/tvAmount"
                        style="@style/text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="5dp"
                        android:drawablePadding="@dimen/_5sdp"
                        android:textColor="@color/redgradstart"
                        app:drawableEndCompat="@drawable/heart_small"
                        app:layout_constraintBottom_toBottomOf="@+id/imageView2"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/imageView2"
                        app:layout_constraintTop_toTopOf="@+id/imageView2"
                        tools:text="100" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.cardview.widget.CardView>

            <View
                android:id="@+id/view1"
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_marginTop="@dimen/_20sdp"
                android:background="@color/semigray"
                app:layout_constraintEnd_toEndOf="@+id/view"
                app:layout_constraintStart_toStartOf="@+id/view"
                app:layout_constraintTop_toBottomOf="@+id/cardView" />

            <TextView
                android:id="@+id/textView11"
                style="@style/text"
                android:layout_width="wrap_content"
                android:layout_marginTop="@dimen/_20sdp"
                android:gravity="center"
                android:text="@string/receiver"
                android:textColor="@color/gray"
                app:layout_constraintStart_toStartOf="@+id/view"
                app:layout_constraintTop_toBottomOf="@+id/view1" />

            <RelativeLayout
                android:id="@+id/sendMessage"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                android:layout_marginTop="@dimen/_60sdp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/textView11">

                <TextView
                    style="@style/text"
                    android:layout_width="wrap_content"
                    android:gravity="center"
                    android:text="@string/add_a_message"
                    android:textColor="@color/gray" />

                <TextView
                    android:id="@+id/text_count"
                    style="@style/text"
                    android:layout_width="wrap_content"
                    android:gravity="center"
                    android:layout_alignParentEnd="true"
                    android:text="0 / 50"
                    android:textColor="@color/gray" />

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/edittext_background"
                android:layout_marginTop="@dimen/_30sdp"
                android:padding="10dp">

                <ImageView
                    android:id="@+id/exclamation_icon"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@drawable/ic_exclamation_mark"
                    android:layout_alignParentStart="true"
                    android:layout_marginTop="4dp" />

                <EditText
                    android:id="@+id/edit_message"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:hint="@string/tap_to_type"
                    android:inputType="textMultiLine"
                    android:maxLines="2"
                    android:maxLength="50"
                    android:minHeight="80sp"
                    android:gravity="top"
                    android:scrollbars="vertical"
                    android:padding="0dp"
                    android:layout_toEndOf="@id/exclamation_icon"
                    android:layout_alignParentTop="true"
                    android:layout_marginStart="8dp"
                    android:background="@android:color/transparent"/>


            </RelativeLayout>

            </RelativeLayout>




            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/viewNone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_10sdp"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/textView11">

                <androidx.cardview.widget.CardView
                    android:id="@+id/cardView2"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/_130sdp"
                    android:layout_marginEnd="@dimen/_5sdp"
                    app:cardBackgroundColor="#E2EBF7"
                    app:cardCornerRadius="20dp"
                    app:cardElevation="0dp"
                    app:layout_constraintEnd_toStartOf="@+id/btnSearch"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:paddingHorizontal="@dimen/_8sdp"
                        android:paddingVertical="@dimen/_5sdp">

                        <ImageView
                            android:id="@+id/imageView3"
                            android:layout_width="@dimen/_40sdp"
                            android:layout_height="@dimen/_40sdp"
                            android:layout_margin="@dimen/_10sdp"
                            android:padding="@dimen/_5sdp"
                            android:src="@drawable/scan"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:tint="@color/blue"
                            tools:ignore="ContentDescription" />

                        <TextView
                            style="@style/subtitle"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_margin="@dimen/_10sdp"
                            android:text="@string/scan_code"
                            android:textColor="@color/blue"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toStartOf="parent" />


                    </androidx.constraintlayout.widget.ConstraintLayout>

                </androidx.cardview.widget.CardView>

                <androidx.cardview.widget.CardView
                    android:id="@+id/btnSearch"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/_130sdp"
                    app:cardBackgroundColor="#E2EBF7"
                    app:cardCornerRadius="20dp"
                    app:cardElevation="0dp"
                    app:layout_constraintBottom_toBottomOf="@+id/cardView2"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/cardView2"
                    app:layout_constraintTop_toTopOf="@+id/cardView2">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:paddingHorizontal="@dimen/_8sdp"
                        android:paddingVertical="@dimen/_5sdp">

                        <ImageView
                            android:layout_width="@dimen/_40sdp"
                            android:layout_height="@dimen/_40sdp"
                            android:layout_margin="@dimen/_10sdp"
                            android:padding="@dimen/_5sdp"
                            android:src="@drawable/search"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:tint="@color/blue"
                            tools:ignore="ContentDescription" />

                        <TextView
                            style="@style/subtitle"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_margin="@dimen/_10sdp"
                            android:text="@string/search"
                            android:textColor="@color/blue"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toStartOf="parent" />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                </androidx.cardview.widget.CardView>

                <View
                    android:id="@+id/view2"
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginTop="@dimen/_20sdp"
                    android:background="@color/semigray"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/cardView2" />

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btnRandom"
                    style="@style/grayButton"
                    android:layout_width="match_parent"
                    android:layout_marginHorizontal="5dp"
                    android:layout_marginTop="@dimen/_20sdp"
                    android:layout_marginBottom="@dimen/_10sdp"
                    android:text="@string/select_receiver_randomly"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.5"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/view2" />

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btnNear"
                    style="@style/grayButton"
                    android:layout_width="match_parent"
                    android:layout_marginHorizontal="5dp"
                    android:layout_marginTop="@dimen/_10sdp"
                    android:layout_marginBottom="@dimen/_10sdp"
                    android:text="@string/select_receiver_nearby"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/btnRandom" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.cardview.widget.CardView
                android:id="@+id/viewSelected"
                android:layout_width="@dimen/_120sdp"
                android:layout_height="@dimen/_60sdp"
                android:visibility="gone"
                app:cardBackgroundColor="#E9E9F0"
                app:cardCornerRadius="20dp"
                app:cardElevation="0dp"
                app:layout_constraintEnd_toEndOf="@+id/cardView"
                app:layout_constraintTop_toTopOf="@+id/textView11">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:padding="@dimen/_8sdp">

                    <androidx.cardview.widget.CardView
                        android:id="@+id/cardView4"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:cardCornerRadius="@dimen/_50sdp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <ImageView
                            android:id="@+id/ivUserImage"
                            android:layout_width="@dimen/_25sdp"
                            android:layout_height="@dimen/_25sdp"
                            android:scaleType="centerCrop"
                            android:src="@drawable/ic_user_placeholder"
                            tools:ignore="ContentDescription" />

                    </androidx.cardview.widget.CardView>

                    <TextView
                        android:id="@+id/textView12"
                        style="@style/text"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="5dp"
                        android:ellipsize="end"
                        android:maxLines="2"
                        app:layout_constraintBottom_toBottomOf="@+id/cardView4"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/cardView4"
                        app:layout_constraintTop_toTopOf="@+id/cardView4"
                        tools:text="Dianne Russell" />

                    <TextView
                        android:id="@+id/tvUsername"
                        style="@style/small"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:textColor="@color/gray"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="@+id/textView12"
                        app:layout_constraintTop_toBottomOf="@+id/textView12"
                        tools:text=" @username" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.cardview.widget.CardView>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btnNext"
        style="@style/button"
        android:layout_marginBottom="@dimen/_10sdp"
        android:text="@string/send"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>