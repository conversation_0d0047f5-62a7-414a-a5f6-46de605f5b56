<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.fragments.operations.withdraw.WithdrawPaymentSelectFragment">

    <ImageView
        android:id="@+id/imageView"
        android:layout_width="@dimen/_30sdp"
        android:layout_height="@dimen/_30sdp"
        android:layout_margin="@dimen/_15sdp"
        android:src="@drawable/ic_back_btn"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/textView"
        style="@style/h3"
        android:text="@string/withdraw"
        app:layout_constraintBottom_toTopOf="@+id/tvInfo"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/imageView" />

    <TextView
        android:id="@+id/tvInfo"
        style="@style/text"
        android:layout_width="wrap_content"
        android:gravity="center"
        android:text="@string/balance"
        android:textColor="@color/gray"
        app:layout_constraintBottom_toBottomOf="@+id/imageView"
        app:layout_constraintEnd_toStartOf="@+id/tvBalance"
        app:layout_constraintStart_toStartOf="@+id/textView"
        app:layout_constraintTop_toBottomOf="@+id/textView" />

    <TextView
        android:id="@+id/tvBalance"
        style="@style/text"
        android:layout_width="wrap_content"
        android:drawablePadding="@dimen/_3sdp"
        android:gravity="center"
        android:textColor="@color/gray"
        app:drawableEndCompat="@drawable/diamond_small"
        app:layout_constraintBottom_toBottomOf="@+id/tvInfo"
        app:layout_constraintEnd_toEndOf="@+id/textView"
        app:layout_constraintStart_toEndOf="@+id/tvInfo"
        app:layout_constraintTop_toTopOf="@+id/tvInfo"
        tools:text="500" />

    <View
        android:id="@+id/view"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginTop="@dimen/_10sdp"
        android:background="@color/semigray"
        app:layout_constraintEnd_toEndOf="@+id/textView9"
        app:layout_constraintStart_toStartOf="@+id/imageView"
        app:layout_constraintTop_toBottomOf="@+id/tvInfo" />

    <TextView
        android:id="@+id/textView9"
        style="@style/text"
        android:layout_width="wrap_content"
        android:layout_marginEnd="@dimen/_20sdp"
        android:gravity="center"
        android:text="@string/_1_2"
        android:textColor="@color/gray"
        app:layout_constraintBottom_toBottomOf="@+id/tvInfo"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/textView" />

    <TextView
        android:id="@+id/tvError"
        style="@style/text"
        android:layout_width="match_parent"
        android:gravity="center"
        android:padding="@dimen/_5sdp"
        android:textColor="@color/gray"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/view" />

    <ScrollView
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/_10sdp"
        app:layout_constraintBottom_toTopOf="@+id/btnNext"
        app:layout_constraintEnd_toEndOf="@+id/view"
        app:layout_constraintStart_toStartOf="@+id/view"
        app:layout_constraintTop_toBottomOf="@+id/tvError">

        <LinearLayout
            android:id="@+id/linearLayout5"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                style="@style/text"
                android:layout_width="wrap_content"
                android:layout_marginVertical="@dimen/_5sdp"
                android:layout_marginStart="@dimen/_5sdp"
                android:gravity="center"
                android:text="@string/payout_method"
                android:textColor="@color/gray" />

            <com.google.android.material.textfield.TextInputLayout
                style="@style/Widget.Material3.TextInputLayout.FilledBox.ExposedDropdownMenu"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_edit"
                app:boxStrokeWidth="0dp">

                <com.google.android.material.textfield.MaterialAutoCompleteTextView
                    android:id="@+id/edtMethod"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/bg_edit"
                    android:inputType="none"
                    android:textSize="14sp"
                    android:padding="0dp"
                    android:paddingHorizontal="@dimen/_10sdp" />

            </com.google.android.material.textfield.TextInputLayout>

            <TextView
                style="@style/text"
                android:layout_width="wrap_content"
                android:layout_marginStart="@dimen/_5sdp"
                android:layout_marginTop="@dimen/_10sdp"
                android:layout_marginBottom="@dimen/_5sdp"
                android:gravity="center"
                android:text="@string/country"
                android:textColor="@color/gray" />

            <com.google.android.material.textfield.TextInputLayout
                style="@style/Widget.Material3.TextInputLayout.FilledBox.ExposedDropdownMenu"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_edit"
                app:boxStrokeWidth="0dp">

                <com.google.android.material.textfield.MaterialAutoCompleteTextView
                    android:id="@+id/edtCountry"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/bg_edit"
                    android:inputType="none"
                    android:padding="0dp"
                    android:textSize="14sp"
                    android:paddingHorizontal="@dimen/_10sdp" />

            </com.google.android.material.textfield.TextInputLayout>

            <TextView
                style="@style/text"
                android:layout_width="wrap_content"
                android:layout_marginStart="@dimen/_5sdp"
                android:layout_marginTop="@dimen/_10sdp"
                android:layout_marginBottom="@dimen/_5sdp"
                android:gravity="center"
                android:text="@string/currency"
                android:textColor="@color/gray" />

            <com.google.android.material.textfield.TextInputLayout
                style="@style/Widget.Material3.TextInputLayout.FilledBox.ExposedDropdownMenu"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_edit"
                app:boxStrokeWidth="0dp">

                <com.google.android.material.textfield.MaterialAutoCompleteTextView
                    android:id="@+id/edtCurrency"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/bg_edit"
                    android:inputType="none"
                    android:textSize="14sp"
                    android:padding="0dp"
                    android:paddingHorizontal="@dimen/_10sdp" />

            </com.google.android.material.textfield.TextInputLayout>

            <TextView
                style="@style/text"
                android:layout_width="wrap_content"
                android:layout_marginStart="@dimen/_5sdp"
                android:layout_marginTop="@dimen/_10sdp"
                android:layout_marginBottom="@dimen/_5sdp"
                android:gravity="center"
                android:text="@string/paypal_link"
                android:textColor="@color/gray" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edtPaypalLink"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/bg_edit"
                    android:inputType="text"
                    android:paddingVertical="@dimen/_10sdp"
                    android:maxLines="1"
                    android:textSize="14sp"
                    android:paddingStart="@dimen/_10sdp"
                    android:paddingEnd="@dimen/_40sdp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.5"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="0.5" />

                <ImageButton
                    android:id="@+id/btnPasteText"
                    style="?android:buttonBarButtonStyle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_text_paste"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="0.5"
                    tools:ignore="ContentDescription" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <TextView
                style="@style/text"
                android:layout_width="match_parent"
                android:layout_gravity="center"
                android:layout_marginTop="@dimen/_20sdp"
                android:gravity="center"
                android:text="@string/paypal_account_must_be_created_with_nsame_email_as_user_email"
                android:textColor="@color/gray" />

        </LinearLayout>

    </ScrollView>

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btnNext"
        style="@style/button"
        android:layout_marginBottom="@dimen/_10sdp"
        android:text="@string/next"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>