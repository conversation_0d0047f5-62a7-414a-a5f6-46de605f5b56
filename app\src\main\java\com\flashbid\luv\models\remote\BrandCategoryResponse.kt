package com.flashbid.luv.models.remote
import android.os.Parcelable
import kotlinx.parcelize.Parcelize

data class BrandCategoryResponse(
    val error: Boolean,
    val message: ArrayList<BrandCategories>
)

data class BrandCategories(
    val create_at: String,
    val `data`: ArrayList<Brand>,
    val id: Int,
    val name: String,
    val update_at: String,
    val company_name: String,
    val amount: Int?,

)

@Parcelize
data class Brand(
    val amount: Int,
    val category_id: Int,
    val company_image: String,
    val company_name: String,
    val create_at: String,
    val id: Int,
    val position: Int,
    val update_at: String,
    val user_id: Int,
    var is_favourite: Boolean,
    var selected: Boolean = false
) : Parcelable


