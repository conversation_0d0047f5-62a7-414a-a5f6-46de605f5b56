package com.flashbid.luv.models.remote

data class BattleDetailsResponse(
    val activeChannels: ActiveChannels,
    val chat_token: String,
    val error: Boolean
) {
    data class ActiveChannels(
        val battle_start_time: Any,
        val channel_name: String,
        val chat_token: String,
        val create_at: String,
        val id: Int,
        val opponent: Int,
        val opponent_audience: Int?,
        val opponent_gift_count: Any,
        val opponent_image: String,
        val opponent_username: String,
        val owner_audience: Int?,
        val owner_gift_count: Any,
        val owner_image: String,
        val owner_user_id: Int,
        val owner_username: String,
        val token: String,
        val update_at: String,
        val user_id: Int,
        val owner_gift_sum: Int?,
        val opponent_gift_sum: Int?,
        var opponent_won_count : Int?,
        var owner_won_count : Int?,
    )
}
