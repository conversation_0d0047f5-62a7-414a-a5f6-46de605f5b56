<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent">

    <androidx.cardview.widget.CardView
        android:id="@+id/dialogCardView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_15sdp"
        app:cardBackgroundColor="@color/white"
        app:cardCornerRadius="@dimen/_26sdp"
        app:cardElevation="1dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingHorizontal="@dimen/_15sdp"
            android:paddingBottom="@dimen/_20sdp">

            <ImageView
                android:id="@+id/imageView8"
                android:layout_width="@dimen/_28sdp"
                android:layout_height="@dimen/_28sdp"
                android:layout_gravity="end"
                android:layout_marginTop="@dimen/_10sdp"
                android:src="@drawable/ic_close_gray"
                tools:ignore="ContentDescription" />

            <ImageView
                android:id="@+id/iv_gift"
                android:layout_width="match_parent"
                android:layout_height="@dimen/_150sdp"
                android:src="@drawable/ic_crate"
                app:layout_constraintBottom_toBottomOf="@+id/textView29"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@+id/textView29"
                tools:ignore="ContentDescription" />

            <TextView
                android:id="@+id/textView29"
                style="@style/text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_10sdp"
                android:textAlignment="center"
                android:textColor="@color/gray"
                android:text="@string/has_sent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/textView26"
                app:layout_constraintTop_toBottomOf="@+id/textView26" />

            <TextView
                android:id="@+id/tvAmount"
                style="@style/h2"
                android:visibility="gone"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_5sdp"
                android:layout_marginTop="@dimen/_10sdp"
                android:text="100 LUV" />

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btnOpen"
                style="@style/button"
                android:layout_marginTop="@dimen/_20sdp"
                android:text="@string/awesome"
                android:layout_marginBottom="@dimen/_40sdp"
                android:textColor="@color/white"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvForgot" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

</FrameLayout>