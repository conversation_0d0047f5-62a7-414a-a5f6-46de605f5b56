package com.flashbid.luv.repositories

import androidx.lifecycle.LiveData
import androidx.lifecycle.liveData
import com.flashbid.luv.models.remote.CreateStoresRequest
import com.flashbid.luv.models.remote.MessageResponse
import com.flashbid.luv.models.remote.ReportCategoryResponse
import com.flashbid.luv.models.remote.StoriesResponse
import com.flashbid.luv.models.remote.YourOwnStoryRequest
import com.flashbid.luv.network.RemoteDataSource
import com.flashbid.luv.network.Resource
import kotlinx.coroutines.Dispatchers

class StoryRepository(private val remoteDataSource: RemoteDataSource) {

    fun stores(): LiveData<Resource<MessageResponse>> = liveData(Dispatchers.IO) {
        emit(Resource.loading(null))
        val response = remoteDataSource.stores()
        emit(response)
    }

    fun createStores(request: CreateStoresRequest): LiveData<Resource<MessageResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.createStores(request)
            emit(response)
        }

    fun getYourOwnStory(request: YourOwnStoryRequest): LiveData<Resource<StoriesResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getYourOwnStory(request)
            emit(response)
        }

    fun getStory(): LiveData<Resource<StoriesResponse>> = liveData(Dispatchers.IO) {
        emit(Resource.loading(null))
        val response = remoteDataSource.getStory()
        emit(response)
    }

    fun createStory(
        transactionId: String,
        filePath: String,
        duration: Int,
        receiverUserId: String,
        viewType: String
    ): LiveData<Resource<MessageResponse>> = liveData(Dispatchers.IO) {
        emit(Resource.loading(null))
        val response = remoteDataSource.createStory(
            filePath, duration, transactionId, receiverUserId, viewType
        )
        emit(response)
    }

    fun getReports(): LiveData<Resource<ReportCategoryResponse>> = liveData(Dispatchers.IO) {
        emit(Resource.loading(null))
        val response = remoteDataSource.getReports()
        emit(response)
    }

    fun createReport(
        type: String, type_id: String, comment: String, category: Int
    ): LiveData<Resource<MessageResponse>> = liveData(Dispatchers.IO) {
        emit(Resource.loading(null))
        val response = remoteDataSource.createReport(
            type, type_id, comment, category
        )
        emit(response)
    }

}