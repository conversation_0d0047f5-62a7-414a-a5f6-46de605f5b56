package com.flashbid.luv.repositories

import androidx.lifecycle.LiveData
import androidx.lifecycle.liveData
import com.flashbid.luv.models.remote.AllBrandsResponse
import com.flashbid.luv.models.remote.BrandCategoryResponse
import com.flashbid.luv.models.remote.CategoryBrandsResponse
import com.flashbid.luv.network.RemoteDataSource
import com.flashbid.luv.network.Resource
import kotlinx.coroutines.Dispatchers

class BrandsRepository(private val remoteDataSource: RemoteDataSource) {

    fun getAllBrandsByCategory(): LiveData<Resource<AllBrandsResponse>> = liveData(Dispatchers.IO) {
        emit(Resource.loading(null))
        val response = remoteDataSource.getAllBrandsByCategory()
        emit(response)
    }

    fun getAllBrands(): LiveData<Resource<AllBrandsResponse>> = liveData(Dispatchers.IO) {
        emit(Resource.loading(null))
        val response = remoteDataSource.getAllBrands()
        emit(response)
    }

    fun getAllBrands(request: String, category: Int): LiveData<Resource<CategoryBrandsResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getAllBrands(request, category)
            emit(response)
        }

    fun getTopBrands(request: String, userType: String): LiveData<Resource<BrandCategoryResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getTopBrands(request, userType)
            emit(response)
        }

}