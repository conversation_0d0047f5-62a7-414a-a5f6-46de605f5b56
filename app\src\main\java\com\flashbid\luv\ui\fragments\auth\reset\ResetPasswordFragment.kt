package com.flashbid.luv.ui.fragments.auth.reset

import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import com.flashbid.luv.R
import com.flashbid.luv.databinding.FragmentResetPasswordBinding
import com.flashbid.luv.extensions.*
import com.flashbid.luv.network.Status
import com.flashbid.luv.viewmodels.UserViewModel
import com.poovam.pinedittextfield.PinField
import com.flashbid.luv.extensions.viewBinding
import com.flashbid.luv.util.RegisterState
import org.koin.androidx.viewmodel.ext.android.viewModel

class ResetPasswordFragment : Fragment(R.layout.fragment_reset_password) {

    private val binding by viewBinding(FragmentResetPasswordBinding::bind)
    private var currentState: RegisterState = RegisterState.EMAIL
    private val viewModel: UserViewModel by viewModel()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initClickListeners()
    }

    private fun initClickListeners() {
        binding.imageView.setOnClickWithDebounce {
            when (currentState) {
                RegisterState.EMAIL -> {
                    findNavController().popBackStack()
                }
                RegisterState.VERIFY -> {
                    setScreenState(RegisterState.EMAIL)
                }
                RegisterState.PASSWORD -> {
                    setScreenState(RegisterState.VERIFY)
                }
                else -> {}
            }
        }
        setScreenState()
    }

    private fun setScreenState(state: RegisterState = RegisterState.EMAIL) {
        currentState = state
        when (state) {
            RegisterState.EMAIL -> {
                setEmailState()
            }
            RegisterState.VERIFY -> {
                setVerificationState()
            }
            RegisterState.PASSWORD -> {
                setPasswordState()
            }
            else -> {}
        }
    }

    private fun setEmailState() {
        with(binding) {
            btnNext.text = getString(R.string.next)
            edtEmail.show()
            tvNoCode.hide()
            tvInfo.setTextState(getString(R.string.your_email))
            btnNext.setOnClickWithDebounce {
                if (edtEmail.isEmailValid()) {
                    sendVerificationCode(email = edtEmail.textToString())
                } else tvInfo.setTextState(getString(R.string.invalid_email), TextState.ERROR)
            }
        }
    }

    private fun sendVerificationCode(email: String) {
        viewModel.forgotPassword(email).observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(it.message ?: getString(R.string.something_went_wrong, getString(R.string.try_again)))
                Status.LOADING -> {}
                Status.SUCCESS -> {
                    setScreenState(RegisterState.VERIFY)
                }
            }
        }
    }

    private fun setVerificationState() {
        with(binding) {
            edtEmail.hide()
            pinField.show()
            llPass.hide()
            tvNoCode.hide()
            llPassConfirm.hide()
            tvInfo.setTextState(getString(R.string.email_confirm_code))
            btnNext.text = getString(R.string.resend_code)
            btnNext.setOnClickWithDebounce {
                sendVerificationCode(edtEmail.textToString())
                pinField.text = null
            }
            pinField.onTextCompleteListener =
                object : PinField.OnTextCompleteListener {
                    override fun onTextComplete(enteredText: String): Boolean {
                        setScreenState(RegisterState.PASSWORD)
                        return true
                    }
                }
        }
    }

    private fun setPasswordState() {
        with(binding) {
            tvInfo.setTextState(getString(R.string.password_creation))
            llPass.show()
            pinField.hide()
            tvNoCode.show()
            llPassConfirm.show()
            tvNoCode.text = getString(R.string.password_must)
            btnNext.text = getString(R.string.set_new_password)
            btnNext.setOnClickWithDebounce {
                if (edtPass.textToString().isNotEmpty() &&
                    edtPassConfirm.textToString().isNotEmpty()
                ) {
                    if (edtPass.textToString() != edtPassConfirm.textToString())
                        tvInfo.setTextState(getString(R.string.password_mismatch), TextState.ERROR)
                    else resetPassword(pinField.textToString(), edtPass.textToString())
                } else tvInfo.setTextState(getString(R.string.empty_password), TextState.ERROR)
            }
        }
    }

    private fun resetPassword(code: String, pass: String) {
        viewModel.resetPassword(code, pass).observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> {
                    snackBar(it.message ?: getString(R.string.something_went_wrong, getString(R.string.try_again)))
                    setScreenState(RegisterState.VERIFY)
                }
                Status.LOADING -> {}
                Status.SUCCESS -> {
                    findNavController().navigate(R.id.loginFragment)
                }
            }
        }
    }

}