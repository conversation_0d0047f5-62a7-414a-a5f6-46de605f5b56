package com.flashbid.luv.models.remote

data class PayoutDataResponse(
    val error: <PERSON>olean,
    val message: Message
) {
    data class Message(
        val countries: ArrayList<Country>,
        val currencies: ArrayList<Currency>,
        val payout_methods: ArrayList<PayoutMethod>
    )
}

data class Country(
    val id: Int,
    val name: String
)

data class Currency(
    val id: Int,
    val name: String
)

data class PayoutMethod(
    val id: Int,
    val name: String
)