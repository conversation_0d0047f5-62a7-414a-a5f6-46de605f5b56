package com.flashbid.luv.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.flashbid.luv.R
import com.flashbid.luv.databinding.ItemPlanBinding
import com.flashbid.luv.util.getColor
import com.flashbid.luv.extensions.setOnClickWithDebounce
import com.flashbid.luv.models.remote.AllPlansResponse

class RechargePlansAdapter(
    private val list: MutableList<AllPlansResponse.Plan>,
    val onClick: (AllPlansResponse.Plan) -> Unit,
) : RecyclerView.Adapter<RechargePlansAdapter.ViewHolder>() {

    private var index = -1

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding =
            ItemPlanBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = list[position]

        with(holder.binding) {
            if (holder.adapterPosition == index) {
                ivHeart.setColorFilter(ivHeart.getColor(R.color.white))
                tvNumber.setTextColor(tvNumber.getColor(R.color.white))
                tvAmount.setTextColor(tvAmount.getColor(R.color.white))
                llBackground.setBackgroundResource(R.drawable.splash_bg_gradient)
            } else {
                ivHeart.setColorFilter(ivHeart.getColor(R.color.redgradstart))
                tvAmount.setTextColor(tvAmount.getColor(R.color.black))
                tvNumber.setTextColor(tvNumber.getColor(R.color.black))
                if (holder.adapterPosition % 2 == 1) {
                    llBackground.setBackgroundResource(R.drawable.ic_bg_top)
                } else {
                    llBackground.setBackgroundResource(R.drawable.ic_bg_bottom)
                }
            }


            tvNumber.text = item.amount.toString()
            tvAmount.text = "$" + item.price.toString()
        }

        holder.itemView.setOnClickWithDebounce {
            onClick(item)
            index = holder.adapterPosition
            refresh()
        }

    }

    override fun getItemCount(): Int = list.size

    inner class ViewHolder(val binding: ItemPlanBinding) :
        RecyclerView.ViewHolder(binding.root)

    fun refresh() = notifyDataSetChanged()

}