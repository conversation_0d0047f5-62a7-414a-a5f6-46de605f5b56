package com.flashbid.luv.ui.fragments.other

import android.os.Bundle
import android.util.Log
import android.view.View
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.flashbid.luv.R
import com.flashbid.luv.adapter.AllBrandsAdapter
import com.flashbid.luv.data.local.AppPreferences
import com.flashbid.luv.databinding.FragmentAllBrandsBinding
import com.flashbid.luv.extensions.setGridLayout
import com.flashbid.luv.extensions.setOnClickWithDebounce
import com.flashbid.luv.extensions.snackBar
import com.flashbid.luv.extensions.viewBinding
import com.flashbid.luv.models.remote.BrandItem
import com.flashbid.luv.network.Status
import com.flashbid.luv.viewmodels.BrandsViewModel
import org.koin.androidx.viewmodel.ext.android.viewModel
import org.koin.android.ext.android.inject

class AllBrandsFragment : Fragment(R.layout.fragment_all_brands) {

    private val binding by viewBinding(FragmentAllBrandsBinding::bind)
    private val viewModel: BrandsViewModel by viewModel()
    private val args by navArgs<AllBrandsFragmentArgs>()
    private val brandCategories: ArrayList<BrandItem> = ArrayList()
    private val brandsAdapter by lazy { AllBrandsAdapter(brandCategories, this::onFavClick) }
    private val topLuverArgs by navArgs<TopLuversFragmentArgs>()
    private val pref by inject<AppPreferences>()
    private fun onFavClick(item: BrandItem) {
        val action =
            AllBrandsFragmentDirections.actionAllBrandsFragmentToUserProfileFragment(item.user_id)
        findNavController().navigate(action)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.textView.text = args.name
        binding.imageView.setOnClickWithDebounce {
            findNavController().popBackStack()
        }

        binding.rcvBrands.apply {
            setGridLayout(3)
            adapter = brandsAdapter
        }


        getTopBrands(pref.period?:"daily")

    }

    private fun getTopBrands(period: String) {
        viewModel.getAllBrands(period, args.categoryId).observe(viewLifecycleOwner) {
            when (it.status) {
                  Status.ERROR -> snackBar(it.message ?: getString(R.string.something_went_wrong, getString(R.string.try_again)))
                Status.LOADING -> {}
                Status.SUCCESS -> {
                    val data = it.data?.message!!
                    brandCategories.clear()
                    if (args.isFromProfile) {
                        var brandList = arrayListOf<BrandItem>()
                        for (brand in args.favBrands!!) {

                            var amt = ""
                            if(brand.amount != null) {
                                amt = ""+brand.amount
                            }
                            val brandItem = BrandItem(
                                amt,
                                brand.category_id,
                                brand.company_image,
                                brand.company_name,
                                brand.is_favourite,
                                brand.position,
                                brand.user_id,
                                brand.selected

                                )
                            brandList.add(brandItem)

                        }
                        brandCategories.addAll(brandList)

                    } else {
                        brandCategories.addAll(data)
                    }
                    brandsAdapter.refresh()
                }
            }
        }
    }

}