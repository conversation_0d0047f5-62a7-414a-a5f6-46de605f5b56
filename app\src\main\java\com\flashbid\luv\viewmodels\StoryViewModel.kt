package com.flashbid.luv.viewmodels

import androidx.lifecycle.LiveData
import androidx.lifecycle.ViewModel
import com.flashbid.luv.models.remote.CounterRequest
import com.flashbid.luv.models.remote.CreateStoresRequest
import com.flashbid.luv.models.remote.MessageResponse
import com.flashbid.luv.models.remote.ReportCategoryResponse
import com.flashbid.luv.models.remote.StoriesResponse
import com.flashbid.luv.models.remote.YourOwnStoryRequest
import com.flashbid.luv.network.Resource
import com.flashbid.luv.repositories.StoryRepository

class StoryViewModel(
    private val storyRepository: StoryRepository
) : ViewModel() {

    fun createStory(
        transactionId: String,
        filePath: String,
        receiverUserId: String,
        viewType: String,
        duration: Int
    ): LiveData<Resource<MessageResponse>> {
        return storyRepository.createStory(transactionId, filePath, duration, receiverUserId, viewType)
    }

    fun getStory(): LiveData<Resource<StoriesResponse>> {
        return storyRepository.getStory()
    }

    fun getReports(): LiveData<Resource<ReportCategoryResponse>> {
        return storyRepository.getReports()
    }

    fun getYourOwnStory(
        end_value: Int, quantity: Int, start_value: Int
    ): LiveData<Resource<StoriesResponse>> {
        return storyRepository.getYourOwnStory(
            YourOwnStoryRequest(end_value, quantity, start_value)
        )
    }

    fun createReport(
        type: String, type_id: String, comment: String, category: Int
    ): LiveData<Resource<MessageResponse>> {
        return storyRepository.createReport(type, type_id, comment, category)
    }

    fun createStores(
        beacon_location: String,
        beacon_uuid: String,
        store_latitude: Int,
        store_location: String,
        store_longitude: Int
    ): LiveData<Resource<MessageResponse>> {
        return storyRepository.createStores(
            CreateStoresRequest(
                beacon_location, beacon_uuid, store_latitude, store_location, store_longitude
            )
        )
    }

    fun stores(): LiveData<Resource<MessageResponse>> {
        return storyRepository.stores()
    }

}