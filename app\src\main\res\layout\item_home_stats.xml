<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/_140sdp"
    android:padding="@dimen/_8sdp">

    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardBackgroundColor="#80FFFFFF"
        app:cardCornerRadius="32dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.5"
        app:strokeColor="@color/white"
        app:strokeWidth="4dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingVertical="@dimen/_10sdp">

            <TextView
                android:id="@+id/tvInfo"
                style="@style/text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/_6sdp"
                android:text="@string/community_stats"
                android:textAllCaps="true"
                android:textColor="@color/gray" />

            <LinearLayout
                android:id="@+id/llCommunity"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="horizontal"
                android:visibility="gone">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tvSent"
                        style="@style/subtitle"
                        android:layout_width="wrap_content"
                        android:drawablePadding="@dimen/_5sdp"
                        android:gravity="center"
                        app:drawableTopCompat="@drawable/heart40"
                        tools:text="100K" />

                    <TextView
                        style="@style/small"
                        android:layout_width="wrap_content"
                        android:gravity="center"
                        android:text="@string/luv_sent"
                        android:textColor="@color/gray" />

                </LinearLayout>

                <View
                    android:layout_width="1dp"
                    android:layout_height="match_parent"
                    android:background="@color/semigray" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">

                    <com.google.android.material.imageview.ShapeableImageView
                        android:id="@+id/ivTopGiftImage"
                        android:layout_width="@dimen/_35sdp"
                        android:scaleType="centerCrop"
                        android:layout_height="@dimen/_35sdp"
                        app:srcCompat="@drawable/diamond_colored" />

                    <TextView
                        android:id="@+id/tvTopSent"
                        style="@style/subtitle"
                        android:layout_width="wrap_content"
                        android:gravity="center"
                        tools:text="25K" />

                    <TextView
                        style="@style/small"
                        android:layout_width="wrap_content"
                        android:gravity="center"
                        android:text="@string/top_gift"
                        android:textColor="@color/gray" />

                </LinearLayout>

                <View
                    android:layout_width="1dp"
                    android:layout_height="match_parent"
                    android:background="@color/semigray" />

                <LinearLayout
                    android:id="@+id/llUserProfile"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">

                    <com.google.android.material.imageview.ShapeableImageView
                        android:id="@+id/ivGifter"
                        app:shapeAppearanceOverlay="@style/ShapeAppearance.Material3.Corner.Full"
                        android:layout_width="@dimen/_35sdp"
                        android:scaleType="centerCrop"
                        android:layout_height="@dimen/_35sdp"
                        app:srcCompat="@drawable/user_placeholder" />

                    <TextView
                        android:id="@+id/tvTopUser"
                        style="@style/subtitle"
                        android:layout_width="match_parent"
                        android:layout_marginTop="@dimen/_3sdp"
                        android:ellipsize="end"
                        android:gravity="center"
                        android:maxLines="1"
                        android:textSize="14sp"
                        tools:text="bigmad" />

                    <TextView
                        style="@style/small"
                        android:layout_width="wrap_content"
                        android:gravity="center"
                        android:text="@string/top_gifter"
                        android:textColor="@color/gray" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/llBalance"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="horizontal"
                android:visibility="gone">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tvHearts"
                        style="@style/h2"
                        android:layout_width="wrap_content"
                        android:drawablePadding="@dimen/_3sdp"
                        android:gravity="center"
                        android:textSize="22sp"
                        app:drawableTopCompat="@drawable/heart40"
                        tools:text="10500" />

                </LinearLayout>

                <View
                    android:layout_width="1dp"
                    android:layout_height="match_parent"
                    android:background="@color/semigray" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tvDiamonds"
                        style="@style/h2"
                        android:layout_width="wrap_content"
                        android:drawablePadding="@dimen/_3sdp"
                        android:gravity="center"
                        android:textSize="20sp"
                        app:drawableTopCompat="@drawable/diamond40"
                        tools:text="1700" />

                    <TextView
                        android:id="@+id/tvUsd"
                        style="@style/small"
                        android:layout_width="wrap_content"
                        android:layout_marginTop="-5dp"
                        android:gravity="center"
                        android:textColor="@color/gray"
                        tools:text="$1200" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/llQr"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="horizontal"
                android:visibility="gone">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageView
                        android:id="@+id/ivQrReceiving"
                        android:layout_width="@dimen/_48sdp"
                        android:layout_height="@dimen/_48sdp"
                        android:src="@drawable/qr_sample" />

                    <TextView
                        style="@style/text"
                        android:layout_width="wrap_content"
                        android:layout_marginTop="@dimen/_5sdp"
                        android:gravity="center"
                        android:text="@string/my_qr_code" />

                </LinearLayout>

                <View
                    android:layout_width="1dp"
                    android:layout_height="match_parent"
                    android:background="@color/semigray" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageView
                        android:id="@+id/ivSending"
                        android:layout_width="@dimen/_50sdp"
                        android:layout_height="@dimen/_50sdp"
                        android:src="@drawable/qr_sample"
                        android:visibility="gone" />

                    <ImageView
                        android:id="@+id/ivAddCode"
                        android:layout_width="@dimen/_48sdp"
                        android:layout_height="@dimen/_48sdp"
                        android:src="@drawable/ic_plus_circle" />

                    <TextView
                        android:id="@+id/tvSending"
                        style="@style/text"
                        android:layout_width="wrap_content"
                        android:layout_marginTop="@dimen/_5sdp"
                        android:gravity="center"
                        android:text="@string/my_luv_chest" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

</androidx.constraintlayout.widget.ConstraintLayout>

