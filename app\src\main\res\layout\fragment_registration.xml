<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:animateLayoutChanges="true"
    tools:context=".ui.fragments.auth.register.RegistrationFragment">

    <ImageView
        android:id="@+id/imageView"
        android:layout_width="@dimen/_30sdp"
        android:layout_height="@dimen/_30sdp"
        android:layout_margin="@dimen/_20sdp"
        android:src="@drawable/ic_back_btn"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/textView"
        style="@style/h1"
        android:text="@string/sign_up"
        android:transitionName="@string/signup_transition"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/imageView" />

    <TextView
        android:id="@+id/tvInfo"
        style="@style/text"
        android:layout_width="match_parent"
        android:gravity="center"
        android:padding="@dimen/_5sdp"
        android:text="@string/your_email"
        android:textColor="@color/gray"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textView" />

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/linearLayoutCompat"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_20sdp"
        android:animateLayoutChanges="true"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvInfo">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/edtEmail"
            style="@style/subtitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_20sdp"
            android:background="@drawable/bg_edit"
            android:maxLines="1"
            android:textSize="12sp"
            android:imeOptions="actionNext"
            android:drawableStart="@drawable/sms"
            android:drawablePadding="@dimen/_10sdp"
            android:hint="@string/email"
            android:padding="@dimen/_10sdp" />

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/llPass"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_20sdp"
            app:hintEnabled="false"
            android:visibility="gone"
            app:passwordToggleEnabled="true">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/edtPass"
                style="@style/subtitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_edit"
                android:inputType="textPassword"
                android:maxLines="1"
                android:textSize="12sp"
                android:imeOptions="actionNext"
                android:drawableStart="@drawable/ic_lock_small"
                android:drawablePadding="@dimen/_10sdp"
                android:hint="@string/password"
                android:padding="@dimen/_10sdp" />

        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/llPassConfirm"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_20sdp"
            app:hintEnabled="false"
            android:visibility="gone"
            app:passwordToggleEnabled="true">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/edtPassConfirm"
                style="@style/subtitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_edit"
                android:drawableStart="@drawable/ic_lock_small"
                android:drawablePadding="@dimen/_10sdp"
                android:inputType="textPassword"
                android:maxLines="1"
                android:textSize="12sp"
                android:imeOptions="actionDone"
                android:hint="@string/confirm_password"
                android:padding="@dimen/_10sdp" />

        </com.google.android.material.textfield.TextInputLayout>


        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/edtName"
            style="@style/subtitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_20sdp"
            android:background="@drawable/bg_edit"
            android:drawableStart="@drawable/profile"
            android:maxLines="1"
            android:inputType="textPersonName"
            android:imeOptions="actionNext"
            android:drawablePadding="@dimen/_10sdp"
            android:hint="@string/name"
            android:padding="@dimen/_10sdp"
            android:visibility="gone" />

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/edtUsername"
            style="@style/subtitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_20sdp"
            android:background="@drawable/bg_edit"
            android:maxLines="1"
            android:imeOptions="actionDone"
            android:drawableStart="@drawable/profile"
            android:drawablePadding="@dimen/_10sdp"
            android:hint="@string/username"
            android:padding="@dimen/_10sdp"
            android:visibility="gone" />

        <include
            android:id="@+id/viewTerms"
            layout="@layout/layout_terms" />

    </androidx.appcompat.widget.LinearLayoutCompat>

    <com.poovam.pinedittextfield.SquarePinField
        android:id="@+id/pinField"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_48sdp"
        android:layout_margin="@dimen/_20sdp"
        android:inputType="number"
        android:visibility="gone"
        app:cornerRadius="16dp"
        app:fieldBgColor="#268181A4"
        app:fieldColor="#268181A4"
        app:highlightColor="@color/gray"
        app:highlightEnabled="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/linearLayoutCompat"
        app:noOfFields="6" />

    <TextView
        android:id="@+id/tvNoCode"
        style="@style/text"
        android:layout_width="wrap_content"
        android:layout_margin="@dimen/_20sdp"
        android:gravity="center"
        android:text="@string/didn_t_get_code"
        android:textColor="@color/gray"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/pinField" />

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btnNext"
        style="@style/button"
        android:layout_marginBottom="@dimen/_20sdp"
        android:text="@string/next"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>