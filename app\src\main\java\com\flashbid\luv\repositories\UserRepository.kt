package com.flashbid.luv.repositories

import androidx.lifecycle.LiveData
import androidx.lifecycle.liveData
import com.flashbid.luv.models.remote.AddFavBrandsRequest
import com.flashbid.luv.models.remote.BalanceResponse
import com.flashbid.luv.models.remote.CreateCode
import com.flashbid.luv.models.remote.CustomFollowingResponse
import com.flashbid.luv.models.remote.EmailCodeRequest
import com.flashbid.luv.models.remote.EmailRequest
import com.flashbid.luv.models.remote.FollowRequest
import com.flashbid.luv.models.remote.FollowerResponse
import com.flashbid.luv.models.remote.GetCodesResponse
import com.flashbid.luv.models.remote.IndustriesResponse
import com.flashbid.luv.models.remote.LoginAppleRequest
import com.flashbid.luv.models.remote.LoginGoogleRequest
import com.flashbid.luv.models.remote.LoginRequest
import com.flashbid.luv.models.remote.LoginResponse
import com.flashbid.luv.models.remote.MessageResponse
import com.flashbid.luv.models.remote.NameRequest
import com.flashbid.luv.models.remote.RandomUserResponse
import com.flashbid.luv.models.remote.ReferralCodeResponse
import com.flashbid.luv.models.remote.ResetRequest
import com.flashbid.luv.models.remote.UpdateProfileRequest
import com.flashbid.luv.models.remote.UpdateVideoStatusRequest
import com.flashbid.luv.models.remote.UploadResponse
import com.flashbid.luv.models.remote.UserDetailsResponse
import com.flashbid.luv.models.remote.UserHistoryResponse
import com.flashbid.luv.models.remote.UserIdRequest
import com.flashbid.luv.network.RemoteDataSource
import com.flashbid.luv.network.Resource
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import okhttp3.MultipartBody

class UserRepository(private val remoteDataSource: RemoteDataSource) {

    fun getReferralCode(
    ): LiveData<Resource<ReferralCodeResponse>> = liveData(Dispatchers.IO) {
        emit(Resource.loading(null))
        val response = remoteDataSource.getReferralCode(
        )
        emit(response)
    }

    fun login(
        request: LoginRequest
    ): LiveData<Resource<LoginResponse>> = liveData(Dispatchers.IO) {
        emit(Resource.loading(null))
        val response = remoteDataSource.login(
            request
        )
        emit(response)
    }

    fun loginGoogle(
        request: LoginGoogleRequest
    ): LiveData<Resource<LoginResponse>> = liveData(Dispatchers.IO) {
        emit(Resource.loading(null))
        val response = remoteDataSource.loginGoogle(request)
        emit(response)
    }

    fun loginApple(
        request: LoginAppleRequest
    ): LiveData<Resource<LoginResponse>> = liveData(Dispatchers.IO) {
        emit(Resource.loading(null))
        val response = remoteDataSource.loginApple(request)
        emit(response)
    }

    fun register(
        request: LoginRequest
    ): LiveData<Resource<LoginResponse>> = liveData(Dispatchers.IO) {
        emit(Resource.loading(null))
        val response = remoteDataSource.register(
            request
        )
        emit(response)
    }

    fun verifyEmail(
        request: EmailRequest
    ): LiveData<Resource<MessageResponse>> = liveData(Dispatchers.IO) {
        emit(Resource.loading(null))
        val response = remoteDataSource.verifyEmail(
            request
        )
        emit(response)
    }

    fun verifyEmailCode(
        request: EmailCodeRequest
    ): LiveData<Resource<MessageResponse>> = liveData(Dispatchers.IO) {
        emit(Resource.loading(null))
        val response = remoteDataSource.verifyEmailCode(
            request
        )
        emit(response)
    }

    fun forgotPassword(
        request: EmailRequest
    ): LiveData<Resource<MessageResponse>> = liveData(Dispatchers.IO) {
        emit(Resource.loading(null))
        val response = remoteDataSource.forgotPassword(
            request
        )
        emit(response)
    }

    fun resetPassword(
        request: ResetRequest
    ): LiveData<Resource<MessageResponse>> = liveData(Dispatchers.IO) {
        emit(Resource.loading(null))
        val response = remoteDataSource.resetPassword(
            request
        )
        emit(response)
    }

    fun searchUser(query: String?): LiveData<Resource<FollowerResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.searchUser(query)
            emit(response)
        }

    fun getUserCodes(): LiveData<Resource<GetCodesResponse>> = liveData(Dispatchers.IO) {
        emit(Resource.loading(null))
        val response = remoteDataSource.getUserCodes()
        emit(response)
    }

    fun getRandomUser(): LiveData<Resource<RandomUserResponse>> = liveData(Dispatchers.IO) {
        emit(Resource.loading(null))
        val response = remoteDataSource.getRandomUser()
        emit(response)
    }

    fun getUserDetails(): LiveData<Resource<UserDetailsResponse>> = liveData(Dispatchers.IO) {
        emit(Resource.loading(null))
        val response = remoteDataSource.getUserDetails()
        emit(response)
    }

    fun getUserStats(): LiveData<Resource<UserDetailsResponse>> = liveData(Dispatchers.IO) {
        emit(Resource.loading(null))
        val response = remoteDataSource.getUserStats()
        emit(response)
    }

    fun getUserHistory(page: Int, limit: Int): LiveData<Resource<UserHistoryResponse>> = liveData(Dispatchers.IO) {
        emit(Resource.loading(null))
        val response = remoteDataSource.getUserHistory(page, limit)
        emit(response)
    }

    fun getOtherUserData(request: UserIdRequest): LiveData<Resource<UserDetailsResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getOtherUserData(request)
            emit(response)
        }

    fun uploadPicture(
        request: MultipartBody.Part
    ): LiveData<Resource<UploadResponse>> = liveData(Dispatchers.IO) {
        emit(Resource.loading(null))
        val response = remoteDataSource.uploadPicture(
            request
        )
        emit(response)
    }

    fun updatePreference(
        request: UpdateProfileRequest
    ): LiveData<Resource<MessageResponse>> = liveData(Dispatchers.IO) {
        emit(Resource.loading(null))
        val response = remoteDataSource.updatePreference(
            request
        )
        emit(response)
    }

    fun updateVideoStatus(id: Int,
        request: UpdateVideoStatusRequest
    ): LiveData<Resource<MessageResponse>> = liveData(Dispatchers.IO) {
        emit(Resource.loading(null))
        val response = remoteDataSource.updateVideoStatus(id,
            request
        )
        emit(response)
    }

    fun updateProfile(
        request: NameRequest
    ): LiveData<Resource<MessageResponse>> = liveData(Dispatchers.IO) {
        emit(Resource.loading(null))
        val response = remoteDataSource.updateProfile(
            request
        )
        emit(response)
    }

    fun getBalance(): LiveData<Resource<BalanceResponse>> = liveData(Dispatchers.IO) {
        emit(Resource.loading(null))
        val response = remoteDataSource.getBalance()
        emit(response)
    }

    fun deleteAccount(): LiveData<Resource<MessageResponse>> = liveData(Dispatchers.IO) {
        emit(Resource.loading(null))
        val response = remoteDataSource.deleteAccount()
        emit(response)
    }

    fun getIndustries(): LiveData<Resource<IndustriesResponse>> = liveData(Dispatchers.IO) {
        emit(Resource.loading(null))
        val response = remoteDataSource.getIndustries()
        emit(response)
    }

    fun getFollowings(query: String?): LiveData<Resource<FollowerResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getFollowings(query)
            emit(response)
        }

    fun getFollowers(query: String?): LiveData<Resource<FollowerResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getFollowers(query)
            emit(response)
        }

    fun followUser(request: FollowRequest): LiveData<Resource<MessageResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.followUser(request)
            emit(response)
        }

    fun unfollowUser(request: FollowRequest): LiveData<Resource<MessageResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.unfollowUser(request)
            emit(response)
        }

    fun getCustomFollowing(request: String): LiveData<Resource<CustomFollowingResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getCustomFollowing(request)
            emit(response)
        }

    fun getCustomFollower(request: String): LiveData<Resource<CustomFollowingResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getCustomFollower(request)
            emit(response)
        }

    fun addFavBrands(request: AddFavBrandsRequest): LiveData<Resource<MessageResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.addFavBrands(request)
            emit(response)
        }

    fun rmvFavBrands(request: String): LiveData<Resource<MessageResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.rmvFavBrands(request)
            emit(response)
        }

    fun leaveBattle(): Flow<MessageResponse?> = flow {
        val response = remoteDataSource.leaveBattle().data
        emit(response)
    }

}