package com.flashbid.luv.ui.fragments.onboarding

import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import com.flashbid.luv.R
import java.util.*
import kotlin.concurrent.timerTask

class SplashFragment : Fragment(R.layout.fragment_splash) {

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        Timer().schedule(
            timerTask {
                findNavController().navigate(R.id.action_splashFragment_to_authSelectFragment)
            }, 3000
        )
    }

}