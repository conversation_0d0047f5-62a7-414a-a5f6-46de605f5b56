package com.flashbid.luv.ui.fragments.operations.sendLuv

import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.flashbid.luv.MainActivity
import com.flashbid.luv.R
import com.flashbid.luv.data.local.AppPreferences
import com.flashbid.luv.databinding.FragmentSelectReceiverBinding
import com.flashbid.luv.extensions.TextState
import com.flashbid.luv.extensions.hide
import com.flashbid.luv.extensions.hideSoftKeyboard
import com.flashbid.luv.util.loadImageFromUrl
import com.flashbid.luv.extensions.setOnClickWithDebounce
import com.flashbid.luv.extensions.show
import com.flashbid.luv.extensions.snackBar
import com.flashbid.luv.extensions.viewBinding
import com.flashbid.luv.models.remote.UserData
import com.flashbid.luv.network.Status
import com.flashbid.luv.util.DeviceUtils
import com.flashbid.luv.viewmodels.TransactionViewModel
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel

class SelectReceiverFragment : Fragment(R.layout.fragment_select_receiver) {

    private val binding by viewBinding(FragmentSelectReceiverBinding::bind)
    private val pref by inject<AppPreferences>()
    private val args by navArgs<SelectReceiverFragmentArgs>()
    private val transactionViewModel: TransactionViewModel by viewModel()
    private var randomUser: UserData? = null
    private val maxLength = 50 // Maximum length


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.tvBalance.text = pref.balanceLuv.toString()
        binding.tvAmount.text = args.itemGift.amount.toString()
        binding.imageView2.loadImageFromUrl(args.itemGift.image)

        binding.btnNext.isEnabled = randomUser != null || args.receiver != null

        println("here is the item prop" + args)

        with(binding) {
            if (args.receiver == null) {
                viewNone.show()
                sendMessage.hide()
                viewSelected.hide()
            } else {
                viewNone.hide()
                if (args.itemGift.message == 1){
                    sendMessage.show()
                }

                viewSelected.show()
                textView12.text = args.receiver?.first_name
                tvUsername.text = "@" + args.receiver?.username
                ivUserImage.loadImageFromUrl(args.receiver?.photo)
            }
        }

        binding.btnSearch.setOnClickWithDebounce {
            val action =
                SelectReceiverFragmentDirections.actionSelectReceiverFragmentToSearchReceiverFragment(
                    args.itemGift, false
                )
            findNavController().navigate(action)
        }

        binding.btnNear.setOnClickWithDebounce {
            val action =
                SelectReceiverFragmentDirections.actionSelectReceiverFragmentToSearchReceiverFragment(
                    args.itemGift, true
                )
            findNavController().navigate(action)
        }

        binding.btnRandom.setOnClickWithDebounce {
            getRandomUser()
        }

        binding.cardView2.setOnClickWithDebounce {
            val action =
                SelectReceiverFragmentDirections.actionSelectReceiverFragmentToSendLuvQrScanFragment(
                    args.itemGift
                )
            findNavController().navigate(action)
        }

        binding.imageView.setOnClickWithDebounce {
            findNavController().popBackStack(R.id.sendLuvFragment, false)
        }

        binding.btnNext.setOnClickWithDebounce {
            if (randomUser != null || args.receiver != null) {
                DeviceUtils.hideSoftInput(binding.root)
                sendLuvs(
                    args.itemGift.id,
                    randomUser?.user_id ?: args.receiver?.id ?: args.receiver?.user_id
                )
            }
        }

        binding.editMessage.addTextChangedListener(object: TextWatcher {

            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {
                // Not needed
            }

            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
                // Update the character counter based on current text length
                val currentLength = s.length
                binding.textCount.text = "$currentLength / $maxLength"

            }

            override fun afterTextChanged(s: Editable) {
                // Not needed
            }
        } )
    }

    private fun sendLuvs(giftId: Int, id: Int?) {
        transactionViewModel.sendLuv(giftId, id ?: 0, randomUser != null, binding.editMessage.text.toString())
            .observe(viewLifecycleOwner) {
                when (it.status) {
                    Status.ERROR -> snackBar(
                        it.message ?: getString(
                            R.string.something_went_wrong,
                            ""
                        )
                    )
                    Status.LOADING -> {}
                    Status.SUCCESS -> {
                        (requireActivity() as MainActivity).showSnackBar(getString(R.string.gift_success), TextState.SUCCESS)
                        findNavController().popBackStack(R.id.homeFragment, false)
                    }
                }
            }
    }

    private fun getRandomUser() {
        transactionViewModel.getRandomUser().observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(it.message ?: getString(R.string.something_went_wrong, getString(R.string.try_again)))
                Status.LOADING -> {}
                Status.SUCCESS -> {
                    randomUser = it.data?.data
                    with(binding) {
                        viewNone.hide()
                        viewSelected.show()
                        if (args.itemGift.message == 1){
                            sendMessage.show()
                        }
                        textView12.text = randomUser?.first_name
                        tvUsername.text = "@" + randomUser?.username
                        ivUserImage.loadImageFromUrl(randomUser?.user_image)
                        binding.btnNext.isEnabled = randomUser != null || args.receiver != null
                    }
                }
            }
        }
    }

}