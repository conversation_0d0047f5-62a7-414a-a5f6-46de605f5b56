package com.flashbid.luv.ui.fragments.video

import android.net.Uri
import android.os.Bundle
import android.os.CountDownTimer
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.ProgressBar
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.common.util.UnstableApi
import androidx.media3.exoplayer.ExoPlayer
import androidx.navigation.fragment.findNavController
import com.flashbid.luv.R
import com.flashbid.luv.databinding.FragmentVideoPlayerWithDetailsBinding
import com.flashbid.luv.extensions.hide
import com.flashbid.luv.extensions.show
import com.flashbid.luv.models.remote.AlertModel
import com.flashbid.luv.util.loadImageFromUrl
import kotlin.math.roundToInt

@UnstableApi
class VideoPlayerWithDetailsFragment : Fragment(), Player.Listener {

    private var _binding: FragmentVideoPlayerWithDetailsBinding? = null
    private val binding get() = _binding!!
    private var player: ExoPlayer? = null
    private var videoUrl: String? = null
    private var userName: String? = null
    private var userImageUrl: String? = null
    private var timer: CountDownTimer? = null // Make timer nullable
    private var storyDurationMs = 0L
    private var isTimerRunning = false
    private lateinit var progressBars: List<ProgressBar>
    private var currentProgressIndex = 0
    private var shouldContinuePlaying = true // Flag to control playback on visibility changes
    private var isLooping = false // Flag to indicate if looping is intended
    private var isVideoPrepared = false // Flag to track if the video has been prepared

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            val alertModel = it.getParcelable<AlertModel>("alertModel")
            videoUrl = alertModel?.gift_image
            userName = alertModel?.name
            userImageUrl = alertModel?.image
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentVideoPlayerWithDetailsBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupPlayer()
        setupUserDetails()
        setupListeners()
        initializeProgressBars()
        videoUrl?.let { prepareVideo(it) } // Prepare video initially
        isLooping = true // Enable looping by default
    }

    private fun setupUserDetails() {
        binding.tvUsername.text = userName
        binding.ivUser.loadImageFromUrl(userImageUrl)
    }

    private fun setupListeners() {
        binding.ivClose.setOnClickListener {
            shouldContinuePlaying = false // Stop playback when explicitly closed
            isLooping = false // Disable looping when explicitly closed
            findNavController().popBackStack()
        }
    }

    private fun setupPlayer() {
        player = ExoPlayer.Builder(requireContext()).build().apply {
            addListener(this@VideoPlayerWithDetailsFragment)
            playWhenReady = true
            binding.playerView.player = this
            repeatMode = if (isLooping) Player.REPEAT_MODE_ONE else Player.REPEAT_MODE_OFF
        }
    }

    private fun prepareVideo(url: String) {
        binding.pgBar.show()
        try {
            val mediaItem = MediaItem.fromUri(Uri.parse(url))
            player?.setMediaItem(mediaItem)
            player?.prepare()
        } catch (e: Exception) {
            e.printStackTrace()
            binding.pgBar.hide()
            // Handle video loading error
        }
    }

    private fun initializeProgressBars() {
        binding.progressBarContainer.removeAllViews()
        progressBars = listOf(ProgressBar(context, null, android.R.attr.progressBarStyleHorizontal).apply {
            layoutParams = LinearLayout.LayoutParams(0, 10, 1f)
            progressDrawable = ContextCompat.getDrawable(requireContext(), R.drawable.progress_bar_progress)
            max = 100
            progress = 0
        }.also { binding.progressBarContainer.addView(it) })
    }

    private fun startStoryTimer(progressBar: ProgressBar) {
        progressBar.max = storyDurationMs.toInt()
        timer = object : CountDownTimer(storyDurationMs, 100) { // Increased interval
            override fun onTick(millisUntilFinished: Long) {
                progressBar.progress = (storyDurationMs - millisUntilFinished).toInt()
            }

            override fun onFinish() {
                progressBar.progress = progressBar.max
                isTimerRunning = false
                if (shouldContinuePlaying && isLooping && isVideoPrepared) {
                    // The ExoPlayer will handle looping due to repeatMode
                    if (::progressBars.isInitialized && progressBars.isNotEmpty()) {
                        progressBars[currentProgressIndex].progress = 0 // Reset progress visually
                        startStoryTimer(progressBars[currentProgressIndex]) // Restart timer
                    }
                } else if (!shouldContinuePlaying) {
                    findNavController().popBackStack() // Go back after completion if explicitly closed
                }
            }
        }.start()
        isTimerRunning = true
    }

    override fun onPlayerStateChanged(playWhenReady: Boolean, playbackState: Int) {
        when (playbackState) {
            Player.STATE_READY -> {
                binding.pgBar.hide()
                isVideoPrepared = true // Video is prepared and ready to play/loop
                if (playWhenReady && !isTimerRunning && player?.duration != null && player?.duration != 0L) {
                    storyDurationMs = player!!.duration
                    if (::progressBars.isInitialized && progressBars.isNotEmpty()) {
                        startStoryTimer(progressBars[currentProgressIndex])
                    }
                }
            }
            Player.STATE_ENDED -> {
                // With REPEAT_MODE_ONE, the player will automatically loop.
                // You might only need to handle cases where shouldContinuePlaying is false.
                if (!shouldContinuePlaying) {
                    findNavController().popBackStack()
                }
                // The timer will continue to run due to the video restarting.
                // We might need to reset it here if the video restarts without a READY state change.
            }
            Player.STATE_BUFFERING -> {
                // Only show progress bar if not already playing (prevents flicker on loop)
                if (player?.isPlaying == false && !isVideoPrepared) {
                    binding.pgBar.show()
                } else {
                    binding.pgBar.hide() // Hide buffering if already playing or prepared
                }
            }
            Player.STATE_IDLE -> {
                // Handle idle state
            }
        }
    }

    override fun onResume() {
        super.onResume()
        if (player == null) {
            setupPlayer()
            videoUrl?.let { prepareVideo(it) }
        } else {
            player?.playWhenReady = true
            player?.repeatMode = if (isLooping) Player.REPEAT_MODE_ONE else Player.REPEAT_MODE_OFF
        }
        shouldContinuePlaying = true // Resume playback if the fragment becomes visible again
    }

    override fun onPause() {
        super.onPause()
        player?.playWhenReady = false
        player?.repeatMode = Player.REPEAT_MODE_OFF // Disable looping when paused
        timer?.cancel()
        isTimerRunning = false
    }

    override fun onStop() {
        super.onStop()
        player?.release()
        player = null
        isVideoPrepared = false // Reset the prepared state
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    companion object {
        fun newInstance(alertModel: AlertModel): VideoPlayerWithDetailsFragment {
            return VideoPlayerWithDetailsFragment().apply {
                arguments = Bundle().apply {
                    putParcelable("alertModel", alertModel)
                }
            }
        }
    }
}