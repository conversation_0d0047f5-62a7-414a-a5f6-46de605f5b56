<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="5dp"
    app:cardBackgroundColor="@color/md_theme_light_background"
    app:cardCornerRadius="10dp"
    app:cardElevation="0dp"
    app:contentPadding="@dimen/_10sdp"
    app:strokeWidth="0dp">

    <TextView
        android:id="@+id/textView12"
        style="@style/text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:drawablePadding="@dimen/_8sdp"
        android:ellipsize="end"
        app:drawableStartCompat="@drawable/ic_globe"
        app:drawableTint="@color/blue"
        app:layout_constraintBottom_toTopOf="@+id/tvUsername"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/imageView4"
        tools:text="@string/english" />

</com.google.android.material.card.MaterialCardView>