package com.flashbid.luv.ui.fragments.other

import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import com.flashbid.luv.R
import com.flashbid.luv.adapter.ChooseAllBrandsAdapter
import com.flashbid.luv.databinding.FragmentChooseFavBinding
import com.flashbid.luv.extensions.setOnClickWithDebounce
import com.flashbid.luv.extensions.setVerticalLayout
import com.flashbid.luv.extensions.snackBar
import com.flashbid.luv.extensions.viewBinding
import com.flashbid.luv.models.remote.Brand
import com.flashbid.luv.models.remote.BrandData
import com.flashbid.luv.network.Status
import com.flashbid.luv.viewmodels.BrandsViewModel
import com.flashbid.luv.viewmodels.UserViewModel
import org.koin.androidx.viewmodel.ext.android.viewModel

class ChooseFavFragment : Fragment(R.layout.fragment_choose_fav) {

    private val binding by viewBinding(FragmentChooseFavBinding::bind)
    private val viewModel: BrandsViewModel by viewModel()
    private val userViewModel: UserViewModel by viewModel()
    private val brandCategories: ArrayList<BrandData> = ArrayList()
    private lateinit var brandsAdapter: ChooseAllBrandsAdapter
    private val selectedBrandsId: ArrayList<Int> = ArrayList()
    private val removedBrandsId: ArrayList<Int> = ArrayList()


    private fun onBrandClick(item: Brand) {
        if (item.is_favourite) {
            rmvFav(item.user_id.toString())
            selectedBrandsId.remove(item.user_id)
        } else {
            if (removedBrandsId.contains(item.user_id)) removedBrandsId.remove(item.user_id)
        if (selectedBrandsId.contains(item.user_id)) selectedBrandsId.remove(item.user_id)
        else selectedBrandsId.add(item.user_id)
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        brandsAdapter =
            ChooseAllBrandsAdapter(
                this,
                brandCategories,
                this::onBrandClick,
                findNavController()

            )


        binding.skipButton.setOnClickWithDebounce {
            findNavController().navigate(R.id.homeFragment)
        }

        binding.doneButton.setOnClickWithDebounce {
            if (selectedBrandsId.isNotEmpty()) {
                addFav(selectedBrandsId)
            } else {
                findNavController().navigate(R.id.homeFragment)
            }
        }

        binding.rcvBrands.apply {
            setVerticalLayout()
            adapter = brandsAdapter
        }

        getTopBrands()

    }

    private fun addFav(list: ArrayList<Int>) {
        userViewModel.addFavBrands(list).observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(it.message ?: getString(R.string.something_went_wrong, getString(R.string.try_again)))
                Status.LOADING -> {}
                Status.SUCCESS -> {
                    findNavController().navigate(R.id.homeFragment)
                }
            }
        }
    }

    private fun rmvFav(list: String) {
        userViewModel.rmvFavBrands(list).observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(it.message ?: getString(R.string.something_went_wrong, getString(R.string.try_again)))
                Status.LOADING -> {}
                 Status.LOADING -> {}
                Status.SUCCESS -> {
                }
            }
        }
    }

    private fun getTopBrands() {
        viewModel.getAllBrandsByCategory().observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(it.message ?: getString(R.string.something_went_wrong, getString(R.string.try_again)))
                Status.LOADING -> {}
                Status.SUCCESS -> {
                    val data = it.data?.data!!
                    brandsAdapter.refresh(data.filter { it.brands.isNotEmpty() }
                        .toCollection(ArrayList()))
                }
            }
        }

        /*viewModel.getAllBrands().observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(it.message ?: getString(R.string.something_went_wrong, getString(R.string.try_again)))
                Status.LOADING -> {}
                Status.SUCCESS -> {
                    val data = it.data?.data!!
                    brandsAdapter.refresh(data.filter { it.brands.isNotEmpty() }
                        .toCollection(ArrayList()))
                }
            }
        }*/
    }

}