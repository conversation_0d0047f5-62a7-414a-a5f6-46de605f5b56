<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.fragments.other.ChooseFavFragment">

    <TextView
        android:id="@+id/skipButton"
        style="@style/small"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:background="@drawable/bg_button_gray"
        android:layout_margin="@dimen/_20sdp"
        android:paddingHorizontal="@dimen/_15sdp"
        android:paddingVertical="@dimen/_8sdp"
        android:text="@string/skip"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/textView"
        style="@style/h3"
        android:text="@string/favorite_brands"
        app:layout_constraintBottom_toBottomOf="@+id/skipButton"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/skipButton" />

    <TextView
        android:id="@+id/doneButton"
        style="@style/small"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_marginEnd="@dimen/_20sdp"
        android:background="@drawable/bg_round_corners"
        android:backgroundTint="@color/redgradstart"
        android:paddingHorizontal="@dimen/_12sdp"
        android:paddingVertical="@dimen/_8sdp"
        android:text="@string/done"
        android:textColor="@color/white"
        app:layout_constraintBottom_toBottomOf="@+id/skipButton"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/skipButton" />

    <TextView
        android:id="@+id/textView33"
        style="@style/small"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_20sdp"
        android:gravity="center"
        android:text="@string/add_your_favorite_brands"
        android:textColor="@color/gray"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textView" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rcvBrands"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textView33"
        app:layout_constraintVertical_bias="0.5" />

</androidx.constraintlayout.widget.ConstraintLayout>