<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="@dimen/_7sdp"
    android:paddingBottom="@dimen/_8sdp">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/imageView4"
        android:layout_width="@dimen/_30sdp"
        android:layout_height="@dimen/_30sdp"
        android:scaleType="centerCrop"
        app:shapeAppearance="@style/ShapeAppearance.Material3.Corner.Full"
        android:src="@drawable/ic_user_placeholder"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.5"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/textView12"
        style="@style/text"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_10sdp"
        android:ellipsize="end"
        android:maxLines="1"
        app:layout_constraintBottom_toTopOf="@+id/tvUsername"
        app:layout_constraintEnd_toStartOf="@+id/btnFollow"
        app:layout_constraintStart_toEndOf="@+id/imageView4"
        app:layout_constraintTop_toTopOf="@+id/imageView4"
        tools:text="Dianne Russell" />

    <TextView
        android:id="@+id/tvUsername"
        style="@style/small"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:text=""
        android:maxLines="2"
        android:layout_marginRight="10dp"
        android:textColor="@color/gray"
        app:layout_constraintBottom_toBottomOf="@+id/imageView4"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@+id/textView12"
        app:layout_constraintTop_toBottomOf="@+id/textView12"
         />


</androidx.constraintlayout.widget.ConstraintLayout>