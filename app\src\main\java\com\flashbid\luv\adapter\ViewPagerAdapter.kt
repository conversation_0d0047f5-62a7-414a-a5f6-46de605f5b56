package com.flashbid.luv.adapter

import android.app.AlertDialog
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.navigation.fragment.findNavController
import androidx.viewpager.widget.PagerAdapter
import com.flashbid.luv.R
import com.flashbid.luv.databinding.DialogBalanceInfoBinding
import com.flashbid.luv.databinding.ItemHomeStatsBinding
import com.flashbid.luv.extensions.hide
import com.flashbid.luv.util.loadImageFromUrl
import com.flashbid.luv.extensions.setOnClickWithDebounce
import com.flashbid.luv.extensions.shortenAmount
import com.flashbid.luv.extensions.show
import com.flashbid.luv.ui.fragments.home.HomeFragment
import com.flashbid.luv.ui.fragments.home.HomeFragmentDirections
import net.glxn.qrgen.android.QRCode

class ViewPagerAdapter(
    val fragment: HomeFragment,
    private val list: List<String>
) :
    PagerAdapter() {
    private var giftName = ""
    private var topGiftImage = ""
    private var stats: Triple<Int, Int, String> = Triple(0, 0, "")
    private var balances: Triple<Long, Int, String> = Triple(0, 0, "0.0")
    private var qrCodes: Pair<String?, String?> = Pair("", "")
    private var gifterImage = ""

    override fun destroyItem(container: ViewGroup, position: Int, `object`: Any) {
        container.removeView(`object` as View)
    }

    override fun getItemPosition(`object`: Any): Int {
        return POSITION_NONE
    }

    override fun getCount(): Int {
        return list.size
    }

    override fun isViewFromObject(view: View, `object`: Any): Boolean {
        return view === `object`
    }

    override fun instantiateItem(container: ViewGroup, position: Int): Any {
        val binding =
            ItemHomeStatsBinding.inflate(LayoutInflater.from(fragment.context), container, false)

        with(binding) {
            when (position) {
                0 -> {
                    tvInfo.text = fragment.getString(R.string.community_stats)
                    llCommunity.show()
                    llCommunity?.setOnClickWithDebounce {
                        fragment.findNavController()
                            .navigate(R.id.action_homeFragment_to_topLuversFragment)
                    }
                    llBalance.hide()
                    llQr.hide()
                    tvSent.text = stats.first.shortenAmount()
                    tvTopSent.text = stats.second.shortenAmount()
                    ivGifter.loadImageFromUrl(gifterImage)
                    ivTopGiftImage.loadImageFromUrl(topGiftImage,false)
                    tvTopUser.text = fragment.getString(R.string.username_x, stats.third)
                    llUserProfile.setOnClickWithDebounce {
                        //todo get userId from backend and navigate to user profile
                        // todo val direction = HomeFragmentDirections.actionHomeFragmentToUserProfileFragment(userId)
                    }
                }
                1 -> {
                    tvInfo.text = fragment.getString(R.string.my_balance)
                    llBalance.show()
                    llCommunity.hide()
                    llQr.hide()
                    tvHearts.text = balances.first.shortenAmount()
                    tvDiamonds.text = balances.second.shortenAmount()
                    tvUsd.text = "$" + balances.third
                    llBalance.setOnClickWithDebounce {
                        showInfoDialog(balances)
                    }
                }
                2 -> {
                    tvInfo.text = fragment.getString(R.string.my_codes)
                    llQr.show()
                    llCommunity.hide()
                    llBalance.hide()

                    if (!qrCodes.first.isNullOrEmpty()) {
                        ivQrReceiving.setImageBitmap(QRCode.from(qrCodes.first).bitmap())
                        ivQrReceiving.setOnClickWithDebounce {
                            val action =
                                HomeFragmentDirections.actionHomeFragmentToQrReceivingFragment(qrCodes.first!!)
                            fragment.findNavController().navigate(action)
                        }
                    }

                    if (qrCodes.second.isNullOrEmpty()) {
                        ivSending.hide()
                        ivAddCode.show()
                        ivAddCode.setOnClickWithDebounce {
                            val action = HomeFragmentDirections.actionHomeFragmentToCreateQrGiftingFragment(null)
                            fragment.findNavController().navigate(action)
                        }
                    } else {
                        ivSending.show()
                        ivAddCode.hide()
                        ivSending.setImageBitmap(QRCode.from(qrCodes.second).bitmap())
                        ivSending.setOnClickWithDebounce {
                            val action =
                                HomeFragmentDirections.actionHomeFragmentToQrGiftingFragment(qrCodes.second!!)
                            fragment.findNavController().navigate(action)
                        }
                    }

                }
            }
        }
        container.addView(binding.root)

        return binding.root
    }

    private fun showInfoDialog(stats: Triple<Long, Int, String>) {
        val builder = AlertDialog.Builder(fragment.requireContext())
        val binding = DialogBalanceInfoBinding.inflate(fragment.layoutInflater)
        val dialog = builder.create()

        dialog.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))

        binding.textView28.text = stats.first.toString()
        binding.tvDiamonds.text = stats.second.toString()
        binding.tvUsd.text = stats.third

        binding.imageView8.setOnClickWithDebounce {
            dialog.dismiss()
        }
        dialog.setView(binding.root)
        dialog.setCancelable(false)
        dialog.show()
    }


    fun setCommunityStats(
        sent: Int,
        top: Int,
        topGifter: String,
        topGifterImage: String,
        topGift: String?,
        topGiftImage: String
    ) {
        stats = Triple(sent, top, topGifter)
        gifterImage = topGifterImage
        giftName = topGift?:""
        this.topGiftImage = topGiftImage
        notifyDataSetChanged()
    }

    fun setQrCodes(receiving: String, sending: String?) {
        qrCodes = Pair(receiving, sending)
        notifyDataSetChanged()
    }

    fun setBalance(heart: Long, diamond: Int, usd: String) {
        balances = Triple(heart, diamond, usd)
        notifyDataSetChanged()
    }
}