package com.flashbid.luv.util

import android.annotation.SuppressLint
import android.content.Context
import android.content.res.ColorStateList
import android.graphics.drawable.Drawable
import android.graphics.drawable.GradientDrawable
import android.graphics.drawable.StateListDrawable
import android.util.TypedValue
import androidx.annotation.*
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment

/**
 * <AUTHOR> Khalid
 */
object DrawableUtils {
    /**
     * @see DrawableUtils.createColorStateList
     */
    @Deprecated("Use createColorStateList(...) instead.")
    fun createCheckedColorStateList(
        context: Context?,
        checkedResColorId: Int,
        defaultResColorId: Int
    ): ColorStateList {
        val activeStateColor = ContextCompat.getColor(context!!, checkedResColorId)
        val defaultStateColor = ContextCompat.getColor(context, defaultResColorId)
        return createColorStateList(
            android.R.attr.state_checked,
            activeStateColor,
            defaultStateColor
        )
    }

    @ColorInt
    fun Fragment.getColorFromAttr(
        @AttrRes attrColor: Int,
        typedValue: TypedValue = TypedValue(),
        resolveRefs: Boolean = true
    ): Int {
        requireContext().theme.resolveAttribute(attrColor, typedValue, resolveRefs)
        return typedValue.data
    }

    fun createColorStateList(
        @AttrRes state: Int,
        @ColorInt activeStateColor: Int,
        @ColorInt defaultStateColor: Int
    ): ColorStateList {
        val states = arrayOf(intArrayOf(-state), intArrayOf(state))
        return ColorStateList(states, intArrayOf(defaultStateColor, activeStateColor))
    }

    /**
     * Use the method below instead
     * @see DrawableUtils.createRoundedRectangleBackground
     */
    @Deprecated("")
    fun createRoundedRectangleBackgroundWithColor(
        context: Context,
        color: Int,
        cornerRadius: Int
    ): Drawable {
        val drawable = GradientDrawable()
        drawable.shape = GradientDrawable.RECTANGLE
        drawable.cornerRadius = cornerRadius * context.resources.displayMetrics.density
        drawable.setColor(color)
        return drawable
    }

    private fun createRoundedRectangleBackground(@ColorInt color: Int, @Px cornerRadius: Int): Drawable {
        val drawable = GradientDrawable()
        drawable.shape = GradientDrawable.RECTANGLE
        drawable.cornerRadius = cornerRadius.toFloat()
        drawable.setColor(color)
        return drawable
    }

    @JvmOverloads
    fun createRoundedRectangleBackgroundSelector(
        context: Context, checkedResColorId: Int,
        defaultResColorId: Int, cornerRadius: Int = 24
    ): StateListDrawable {
        val drawableSelector = StateListDrawable()
        drawableSelector.addState(
            intArrayOf(android.R.attr.state_checked),
            createRoundedRectangleBackground(
                ContextCompat.getColor(context, checkedResColorId),
                DeviceUtils.convertDpToPx(context, cornerRadius.toFloat())
            )
        )
        drawableSelector.addState(
            intArrayOf(-android.R.attr.state_checked),
            createRoundedRectangleBackground(
                ContextCompat.getColor(context, defaultResColorId),
                DeviceUtils.convertDpToPx(context, cornerRadius.toFloat())
            )
        )
        return drawableSelector
    }

    @SuppressLint("NewApi")
    fun createRoundedRectangleBackgroundWithColor(
        context: Context,
        @ColorRes resColorId: Int,
        @ColorRes resStrokeColorId: Int,
        cornerRadius: Int
    ): Drawable {
        val drawable = GradientDrawable()
        val density = context.resources.displayMetrics.density
        drawable.shape = GradientDrawable.RECTANGLE
        drawable.cornerRadius = cornerRadius * density
        drawable.setStroke(
            (1.5 * density).toInt(),
            ContextCompat.getColor(context, resStrokeColorId)
        )
        drawable.setColor(ContextCompat.getColor(context, resColorId))
        return drawable
    }

}