package com.flashbid.luv.ui.fragments.auth.login

import android.os.Bundle
import android.view.View
import androidx.core.widget.doAfterTextChanged
import androidx.fragment.app.Fragment
import androidx.navigation.NavOptions
import androidx.navigation.fragment.FragmentNavigatorExtras
import androidx.navigation.fragment.findNavController
import com.flashbid.luv.R
import com.flashbid.luv.data.local.AppPreferences
import com.flashbid.luv.databinding.FragmentLoginBinding
import com.flashbid.luv.extensions.disableSpaces
import com.flashbid.luv.extensions.hideSoftKeyboard
import com.flashbid.luv.extensions.setOnClickWithDebounce
import com.flashbid.luv.extensions.snackBar
import com.flashbid.luv.extensions.viewBinding
import com.flashbid.luv.network.Status
import com.flashbid.luv.util.BaseLiveData
import com.flashbid.luv.viewmodels.UserViewModel
import com.google.firebase.FirebaseApp
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel

class LoginFragment : Fragment(R.layout.fragment_login) {

    private val binding by viewBinding(FragmentLoginBinding::bind)
    private val pref by inject<AppPreferences>()
    private val viewModel: UserViewModel by viewModel()
    private var email = ""
    private var password = ""
    private var referralCode: Int? = null

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        BaseLiveData.referralLiveData.observe(viewLifecycleOwner) {
            referralCode = it
        }

        binding.tvForgot.setOnClickWithDebounce {
            val extras =
                FragmentNavigatorExtras(binding.tvForgot to getString(R.string.reset_transition))
            val directions = LoginFragmentDirections.actionLoginFragmentToResetPasswordFragment()
            findNavController().navigate(directions, extras)
        }

        binding.materialButton.setOnClickWithDebounce {
            val extras =
                FragmentNavigatorExtras(binding.materialButton to getString(R.string.signup_transition))
            val directions = LoginFragmentDirections.actionLoginFragmentToRegistrationFragment()
            findNavController().navigate(directions, extras)
        }

        binding.imageView.setOnClickWithDebounce {
            findNavController().popBackStack()
        }

        binding.btnLogin.setOnClickWithDebounce {
            if (email.isNotEmpty() && password.isNotEmpty()) login(email, password)
            else snackBar(getString(R.string.invalid_email_password))
        }

        binding.textView2.doAfterTextChanged { email = it.toString() }
        binding.edtPass.doAfterTextChanged { password = it.toString() }
        binding.textView2.disableSpaces()
        binding.edtPass.disableSpaces()

    }

    private fun login(email: String, password: String) {
        requireActivity().hideSoftKeyboard()
        viewModel.login(email, password, referralCode).observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(it.message ?: getString(R.string.something_went_wrong, getString(R.string.try_again)))
                Status.LOADING -> {}
                Status.SUCCESS -> {
                    val data = it.data
                    pref.saveUser(
                        data?.user_id,
                        data?.token,
                        data?.role,
                        email,
                        data?.refresh_token, true
                    )
                    /*pref.saveUser(
                        225,
                        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoyMjUsInJvbGUiOiJtZW1iZXIiLCJpYXQiOjE3MTM4MTg2NjUsImV4cCI6MTcxNDQyMzQ2NX0.snncHin3DNmXbFJ1WGHYTeOCUzaqtDIT2sisg2bjyQc",
                        "member",
                        "<EMAIL>",
                        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoyMjUsInJvbGUiOiJtZW1iZXIiLCJpYXQiOjE3MTM4MTg2NjUsImV4cCI6MTcxNDQyMzQ2NX0.snncHin3DNmXbFJ1WGHYTeOCUzaqtDIT2sisg2bjyQc", true
                    )*/

                    if (data?.is_new_user == true)
                        findNavController().navigate(R.id.action_loginFragment_to_onboardFragment)
                    else {
                        val navController = findNavController()
                        val navOptions = NavOptions.Builder()
                            .setPopUpTo(R.id.homeFragment, true)
                            .build()
                        navController.navigate(R.id.action_loginFragment_to_homeFragment, null, navOptions)
                    }
                }
            }
        }
    }

}