package com.flashbid.luv.adapter

import android.app.AlertDialog
import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.RecyclerView
import com.flashbid.luv.R
import com.flashbid.luv.databinding.ItemFavBinding
import com.flashbid.luv.util.getColor
import com.flashbid.luv.util.loadImageFromUrl
import com.flashbid.luv.extensions.setOnClickWithDebounce
import com.flashbid.luv.models.remote.Brand
import com.flashbid.luv.ui.fragments.other.TopLuversFragmentDirections
import androidx.navigation.NavController
import com.flashbid.luv.ui.fragments.other.ChooseFavFragmentDirections

class BrandsItemAdapter(
    private val categories: ArrayList<Brand>,
    val onClick: (Brand) -> Unit,
    private val navController: NavController,
    private val choose: Boolean,
    private val showDialogEnabled: Boolean
) :
    RecyclerView.Adapter<BrandsItemAdapter.ViewHolder>() {

    inner class ViewHolder(val binding: ItemFavBinding) :
        RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val itemView =
            ItemFavBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(itemView)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val model = categories[position]

        with(holder.binding) {
            tvBrandName.text = model.company_name
            tvLuv.text = model.amount.toString()
            ivImage.loadImageFromUrl(model.company_image)

//            if (choose) {
//                if (model.is_favourite) {
//                    ivHeart.setColorFilter(ContextCompat.getColor(ivHeart.context, R.color.white))
//                    tvLuv.setTextColor(tvLuv.getColor(R.color.white))
//                    llBackground.setBackgroundResource(R.drawable.splash_bg_gradient)
//                } else {
//                    ivHeart.setColorFilter(
//                        ContextCompat.getColor(
//                            ivHeart.context,
//                            R.color.redgradstart
//                        )
//                    )
//                    tvLuv.setTextColor(llBackground.getColor(R.color.black))
//                    llBackground.setBackgroundResource(R.drawable.bg_edit)
//                }
//            }

//            root.setOnClickWithDebounce {
//                println("this is clicked 2")
//                onClick(model)
//                if (choose) {
//                    model.is_favourite = !model.is_favourite
//                    notifyDataSetChanged()
//                }
//            }



            // Update UI based on favorite state
            if (model.is_favourite) {
                ivHeart.setColorFilter(ContextCompat.getColor(ivHeart.context, R.color.white))
                tvLuv.setTextColor(tvLuv.getColor(R.color.white))
                llBackground.setBackgroundResource(R.drawable.splash_bg_gradient)
            } else {
                ivHeart.setColorFilter(ContextCompat.getColor(ivHeart.context, R.color.redgradstart))
                tvLuv.setTextColor(llBackground.getColor(R.color.black))
                llBackground.setBackgroundResource(R.drawable.bg_edit)
            }

            // Click listener to show dialog
            root.setOnClickWithDebounce {

                if (showDialogEnabled) {
                    showDialog(root.context, model)
                }

            }


        }

    }

    // Function to show the dialog with two options
    private fun showDialog(context: Context, model: Brand) {
        val options = arrayOf(
            if (model.is_favourite) "Remove from Favorite" else "Add to Favorite",
            "See Profile"
        )

        AlertDialog.Builder(context)
            .setTitle("Choose an Action")
            .setItems(options) { _, which ->
                when (which) {
                    0 -> { // Toggle favorite state
                        model.is_favourite = !model.is_favourite
                        notifyDataSetChanged()
                    }
                    1 -> { // Open profile


                        val action = ChooseFavFragmentDirections
                            .actionChooseFavFragmentToUserProfileFragment(model.user_id)

                             navController.navigate(action) // Use passed NavController

                    }
                }
            }
            .setNegativeButton("Cancel", null)
            .show()
    }

    override fun getItemCount(): Int {
        return categories.size
    }

}