<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="vertical">

    <TextView
        android:id="@+id/textView24"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:text="@string/on_board_third_1"
        android:textAlignment="center"
        android:textColor="@color/gray"
        android:textSize="16sp" />

    <ImageView
        android:id="@+id/imageView7"
        android:layout_width="@dimen/_150sdp"
        android:layout_height="@dimen/_150sdp"
        android:layout_gravity="center"
        android:layout_marginTop="@dimen/_10sdp"
        android:src="@drawable/onboard_3" />

    <TextView
        android:id="@+id/textView25"
        style="@style/h2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_20sdp"
        android:text="@string/on_board_third_2"
        android:textAlignment="center" />

    <TextView
        android:id="@+id/textView23"
        style="@style/text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_40sdp"
        android:layout_marginTop="@dimen/_10sdp"
        android:text="@string/on_board_third_3"
        android:textAlignment="center"
        android:textColor="@color/gray" />
</LinearLayout>