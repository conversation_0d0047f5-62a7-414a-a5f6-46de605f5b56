<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:id="@+id/cvTerms"
    android:visibility="gone"
    android:layout_marginVertical="@dimen/_20sdp"
    app:strokeColor="@color/semigray">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="@dimen/_10sdp">

        <com.google.android.material.checkbox.MaterialCheckBox
            android:id="@+id/cbTerms"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:buttonTint="@color/gray" />

        <TextView
            android:id="@+id/textView4"
            style="@style/text"
            android:gravity="center"
            android:text="@string/by_clicking_i_agree_to_the"
            android:textColor="@color/gray" />

        <TextView
            android:id="@+id/textView6"
            style="@style/text"
            android:gravity="center"
            android:text="@string/terms_of_service_amp_privacy_policy" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>