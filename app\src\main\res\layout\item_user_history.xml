<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingHorizontal="@dimen/_12sdp"
    android:paddingVertical="@dimen/_6sdp">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivUserReceived"
        android:layout_width="@dimen/_30sdp"
        android:layout_height="@dimen/_30sdp"
        android:scaleType="centerCrop"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.5"
        app:shapeAppearanceOverlay="@style/ShapeAppearance.Material3.Corner.Full"
        app:srcCompat="@drawable/ic_user_placeholder" />

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/ivActionSent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:cardCornerRadius="@dimen/_50sdp"
        app:layout_constraintBottom_toBottomOf="@+id/ivUserReceived"
        app:layout_constraintEnd_toEndOf="@+id/ivUserReceived"
        app:strokeColor="@color/white">

        <ImageView
            android:id="@+id/ivActionImage"
            android:layout_width="@dimen/_15sdp"
            android:layout_height="@dimen/_15sdp"
            android:background="@color/bgGray"
            android:padding="4dp"
            android:scaleType="centerCrop"
            app:srcCompat="@drawable/gift"
            app:tint="@color/gray" />
    </com.google.android.material.card.MaterialCardView>

    <TextView
        android:id="@+id/tvAction"
        style="@style/text"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_10sdp"
        android:ellipsize="end"
        android:maxLines="2"
        app:layout_constraintBottom_toTopOf="@+id/tvUsernameTime"
        app:layout_constraintEnd_toStartOf="@+id/linearLayout6"
        app:layout_constraintStart_toEndOf="@+id/ivUserReceived"
        app:layout_constraintTop_toTopOf="@+id/ivUserReceived"
        tools:text="Star Luv received" />

    <TextView
        android:id="@+id/tvUsernameTime"
        style="@style/small"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:transitionGroup="true"
        android:transitionName="@string/user_profile_transition"
        android:maxLines="1"
        android:textColor="@color/gray"
        app:layout_constraintBottom_toBottomOf="@+id/ivUserReceived"
        app:layout_constraintStart_toStartOf="@+id/tvAction"
        app:layout_constraintTop_toBottomOf="@+id/tvAction"
        tools:text="@string/username_x_time" />

    <LinearLayout
        android:id="@+id/linearLayout6"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="@+id/tvAction"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvAction">

        <TextView
            android:id="@+id/tvAmountFirst"
            style="@style/small"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/gray"
            tools:text="+ $1 234.25" />

        <TextView
            android:id="@+id/tvAmount"
            style="@style/small"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/gray"
            tools:text="- 250" />

        <ImageView
            android:id="@+id/ivCurrency"
            android:layout_width="@dimen/_15sdp"
            android:layout_height="@dimen/_15sdp"
            android:layout_marginStart="5dp"
            android:src="@drawable/heart_colored" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>