package com.flashbid.luv.adapter

import android.content.res.ColorStateList
import android.graphics.Color
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.flashbid.luv.databinding.ItemYourGiftBinding
import com.flashbid.luv.models.remote.ItemGift
import com.flashbid.luv.util.loadImageFromUrl

class BattleGiftAdapter(
    private val giftList: List<ItemGift>,
    private val onItemSelected: (ItemGift) -> Unit
) : RecyclerView.Adapter<BattleGiftAdapter.ViewHolder>() {

    private var selectedPosition = RecyclerView.NO_POSITION

    inner class ViewHolder(val binding: ItemYourGiftBinding) :
        RecyclerView.ViewHolder(binding.root) {
        init {
            binding.root.setOnClickListener {
                val position = adapterPosition
                if (position != RecyclerView.NO_POSITION) {
                    val previousSelected = selectedPosition
                    selectedPosition = position
                    notifyItemChanged(previousSelected)
                    notifyItemChanged(selectedPosition)
                    onItemSelected.invoke(giftList[position])
                }
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val itemView =
            ItemYourGiftBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(itemView)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val model = giftList[position]
        with(holder.binding) {
            tvName.text = model.name
            tvLuv.text = model.amount.toString()
            ivImage.loadImageFromUrl(model.image)

            val isSelected = position == selectedPosition
            root.setCardBackgroundColor(
                Color.parseColor(if (isSelected) "#F6E5EB" else "#f5f5f8")
            )
            root.setStrokeColor(
                ColorStateList.valueOf(
                    Color.parseColor(if (isSelected) "#F95050" else "#f5f5f8")
                )
            )
        }
    }

    override fun getItemCount(): Int = giftList.size
}
