<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingHorizontal="@dimen/_20sdp"
    android:paddingVertical="@dimen/_10sdp"
    tools:context=".ui.fragments.operations.sendLuv.NearbyUsersFragment">

    <TextView
        android:id="@+id/textView37"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="#99000000"
        android:text="@string/nearby_requires_users_location_and_must_be_enable_in_settings"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/view"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginTop="@dimen/_16sdp"
        android:background="@color/semigray"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textView37" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rcvNearby"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:paddingTop="@dimen/_10sdp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/view" />

    <LinearLayout
        android:id="@+id/llNoItems"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        android:paddingVertical="@dimen/_20sdp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.5">

        <com.google.android.material.imageview.ShapeableImageView
            android:layout_width="@dimen/_80sdp"
            android:layout_height="@dimen/_80sdp"
            app:contentPadding="@dimen/_10sdp"
            app:shapeAppearance="@style/ShapeAppearance.Material3.Corner.Full"
            app:srcCompat="@drawable/search_btn" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_5sdp"
            android:gravity="center"
            android:text="@string/found_nothing_by_your_search" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>