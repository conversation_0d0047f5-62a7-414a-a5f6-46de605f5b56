package com.flashbid.luv.models.remote

data class UpdateProfileRequest(
    val payload: Payload
) {
    data class Payload(
        val bio: String? = null,
        val category_id: Int? = null,
        val company_image: String? = null,
        val company_name: String? = null,
        val industry_id: Int? = null,
        val is_company: Int? = null,
        val user_image: String? = null,
        val username: String? = null,
        val website: String? = null,
        val fcm_token: String? = null,
        val age: String? = null,
        val gender: String? = null,
        val show_following: String? = null,
        val show_follower: String? = null,
        val latitude: String? = null,
        val longitude: String? = null,
        val lang: String? = null,
    )
}