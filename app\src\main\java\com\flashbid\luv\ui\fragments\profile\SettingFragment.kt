package com.flashbid.luv.ui.fragments.profile

import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.View
import androidx.navigation.fragment.findNavController
import com.flashbid.luv.R
import com.flashbid.luv.adapter.LanguageAdapter
import com.flashbid.luv.data.local.AppPreferences
import com.flashbid.luv.databinding.FragmentSettingBinding
import com.flashbid.luv.databinding.SheetAccountDeleteBinding
import com.flashbid.luv.databinding.SheetLogoutBinding
import com.flashbid.luv.databinding.SheetSelectLanguageBinding
import com.flashbid.luv.extensions.setOnClickWithDebounce
import com.flashbid.luv.extensions.setVerticalLayout
import com.flashbid.luv.extensions.snackBar
import com.flashbid.luv.extensions.viewBinding
import com.flashbid.luv.models.remote.UpdateProfileRequest
import com.flashbid.luv.network.Status
import com.flashbid.luv.util.BaseLiveData
import com.flashbid.luv.util.Constants
import com.flashbid.luv.util.getColor
import com.flashbid.luv.viewmodels.UserViewModel
import com.google.android.material.bottomsheet.BottomSheetDialog
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel

class SettingFragment : Fragment(R.layout.fragment_setting) {

    private val binding by viewBinding(FragmentSettingBinding::bind)
    private val pref by inject<AppPreferences>()
    private val userViewModel: UserViewModel by viewModel()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.imageView.setOnClickWithDebounce {
            findNavController().popBackStack()
        }

        binding.btnLogout.setOnClickWithDebounce {
            showLogoutSheet()
        }

        binding.btnDelete.setOnClickWithDebounce {
            showDeleteSheet()
        }

        binding.btnLanguage.setOnClickWithDebounce {
            showLanguageSheet()
        }

        binding.btnReset.setOnClickWithDebounce {
            findNavController().navigate(R.id.action_settingFragment_to_resetPasswordFragment)
        }

        binding.btnReset.isEnabled = pref.isEmailAccount

        binding.btnEdit.setOnClickWithDebounce {
            findNavController().navigate(R.id.action_settingFragment_to_editProfileFragment)
        }

        binding.btnPrivacy.setOnClickWithDebounce {
            findNavController().navigate(R.id.action_settingFragment_to_privacyFragment)
        }

    }
    private fun showLogoutSheet() {
        val sheet = BottomSheetDialog(requireContext())
        val view = SheetLogoutBinding.inflate(layoutInflater)

        view.imageView6.setOnClickWithDebounce {
            sheet.dismiss()
        }

        view.btnCancel.setOnClickWithDebounce {
            sheet.dismiss()
        }

        view.btnLogout.setOnClickWithDebounce {
            sheet.dismiss()
            BaseLiveData.isAuthorized.postValue(false)
        }

        sheet.setCancelable(true)
        sheet.setContentView(view.root)
        sheet.show()
    }

    private fun showDeleteSheet() {
        val sheet = BottomSheetDialog(requireContext())
        val view = SheetAccountDeleteBinding.inflate(layoutInflater)

        view.imageView6.setOnClickWithDebounce {
            sheet.dismiss()
        }

        view.btnCancel.setOnClickWithDebounce {
            sheet.dismiss()
        }

        view.btnDelete.setOnClickWithDebounce {
            sheet.dismiss()
            BaseLiveData.isAuthorized.postValue(false)
        }

        sheet.setCancelable(true)
        sheet.setContentView(view.root)
        sheet.show()
    }

    private fun showLanguageSheet() {
        val sheet = BottomSheetDialog(requireContext())
        val view = SheetSelectLanguageBinding.inflate(layoutInflater)

        view.imageView6.setOnClickWithDebounce {
            sheet.dismiss()
        }

        view.rcvLanguage.apply {
            setVerticalLayout()
            adapter = LanguageAdapter(
                arrayListOf(
                    Pair(getString(R.string.english), "en"), Pair(getString(R.string.spanish), "es"), Pair(getString(R.string.portuguese), "pt")
                )
            ) {

                switchLangaue(it)
                sheet.dismiss()

                /*pref.appLanguage = it
                Constants.CURRENT_LOCALE = pref.appLanguage ?: "en"
                sheet.dismiss()
                requireActivity().recreate()*/
            }
        }

        sheet.setCancelable(true)
        sheet.setContentView(view.root)
        sheet.show()
    }


    private fun switchLangaue(lang: String) {
        userViewModel.updatePreference(
            UpdateProfileRequest(
                UpdateProfileRequest.Payload(
                    lang = lang,
                    fcm_token = pref.fcmToken,
                )
            )
        ).observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> {
                    snackBar(
                        it.message ?: getString(
                            R.string.something_went_wrong, getString(R.string.try_again)
                        )
                    )
                }

                Status.LOADING -> {}
                Status.SUCCESS -> {

                    pref.appLanguage = lang
                    Constants.CURRENT_LOCALE = pref.appLanguage ?: "en"
                    requireActivity().recreate()
                }
            }
        }
    }

}