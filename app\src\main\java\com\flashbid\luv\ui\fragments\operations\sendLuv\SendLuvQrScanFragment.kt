package com.flashbid.luv.ui.fragments.operations.sendLuv

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.BitmapFactory
import android.os.Bundle
import android.view.SurfaceHolder
import android.view.View
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.google.android.gms.vision.CameraSource
import com.google.android.gms.vision.Detector
import com.google.android.gms.vision.Frame
import com.google.android.gms.vision.barcode.Barcode
import com.google.android.gms.vision.barcode.BarcodeDetector
import com.flashbid.luv.R
import com.flashbid.luv.databinding.FragmentSendLuvQrScanBinding
import com.flashbid.luv.extensions.setOnClickWithDebounce
import com.flashbid.luv.extensions.snackBar
import com.flashbid.luv.models.remote.UserDetails
import com.flashbid.luv.network.Status
import com.flashbid.luv.viewmodels.CodesViewModel
import com.flashbid.luv.extensions.viewBinding
import com.flashbid.luv.util.RequestCodes
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.io.IOException

class SendLuvQrScanFragment : Fragment(R.layout.fragment_send_luv_qr_scan) {

    private val binding by viewBinding(FragmentSendLuvQrScanBinding::bind)
    private val requestCodeCameraPermission = 1001
    private var cameraSource: CameraSource? = null
    private var barcodeDetector: BarcodeDetector? = null
    private var scannedValue = ""
    private var scanning = false
    private lateinit var surfaceHolder: SurfaceHolder
    private val codesViewModel: CodesViewModel by viewModel()
    private val args by navArgs<SendLuvQrScanFragmentArgs>()

    @Deprecated("Deprecated in Java")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (resultCode != Activity.RESULT_CANCELED && requestCode == RequestCodes.GALLERY_IMAGE && resultCode == Activity.RESULT_OK && data != null) {
            val selectedImage = data.data
            if (selectedImage != null) {
                val stream = requireActivity().contentResolver.openInputStream(selectedImage)
                val bitmap = BitmapFactory.decodeStream(stream)
                stream?.close()
                val imageDetector = BarcodeDetector.Builder(requireContext()).build()
                val frame = Frame.Builder().setBitmap(bitmap).build()
                val barcodes = imageDetector.detect(frame)
                if (barcodes.size() > 0) {
                    val qrCode = barcodes.valueAt(0)
                    val qrCodeValue = qrCode.displayValue
                    getDetails(qrCodeValue)
                } else snackBar(getString(R.string.qr_not_found))
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        if (ContextCompat.checkSelfPermission(requireContext(), android.Manifest.permission.CAMERA)
            != PackageManager.PERMISSION_GRANTED
        ) askForCameraPermission()
        else setupControls()

        binding.imageView.setOnClickWithDebounce {
            findNavController().popBackStack()
        }

        binding.photoLib.setOnClickWithDebounce {
            val intent = Intent(Intent.ACTION_PICK)
            intent.type = "image/*"
            startActivityForResult(intent, RequestCodes.GALLERY_IMAGE)
            cameraSource?.stop()
        }

    }

    private fun setupControls() {
        barcodeDetector =
            BarcodeDetector.Builder(requireContext()).setBarcodeFormats(Barcode.ALL_FORMATS).build()

        cameraSource = barcodeDetector?.let {
            CameraSource.Builder(requireContext(), it)
                .setRequestedPreviewSize(1920, 1080)
                .setAutoFocusEnabled(true) //you should add this feature
                .build()
        }

        binding.cameraSurfaceView.holder.addCallback(object : SurfaceHolder.Callback {
            @SuppressLint("MissingPermission")
            override fun surfaceCreated(holder: SurfaceHolder) {
                try {
                    //Start preview after 1s delay
                    surfaceHolder = holder
                    cameraSource?.start(holder)
                } catch (e: IOException) {
                    e.printStackTrace()
                }
            }

            @SuppressLint("MissingPermission")
            override fun surfaceChanged(
                holder: SurfaceHolder,
                format: Int,
                width: Int,
                height: Int
            ) {
                try {
                    cameraSource?.start(holder)
                    surfaceHolder = holder
                } catch (e: IOException) {
                    e.printStackTrace()
                }
            }

            override fun surfaceDestroyed(holder: SurfaceHolder) {
                surfaceHolder = holder
                cameraSource?.stop()
            }
        })


        barcodeDetector?.setProcessor(object : Detector.Processor<Barcode> {
            override fun release() {
            }

            override fun receiveDetections(detections: Detector.Detections<Barcode>) {
                lifecycleScope.launch(Dispatchers.Main) {
                    val barcodes = detections.detectedItems
                    if (barcodes.size() >= 1) {
                        scannedValue = barcodes.valueAt(0).rawValue
                        //Don't forget to add this line printing value or finishing activity must run on main thread
                        cameraSource?.stop()
                        if (scannedValue.isNotEmpty() && !scanning) {
                            getDetails(scannedValue)
                            scanning = true
                        }
                    }
                }
            }
        })
    }

    private fun getDetails(scannedValue: String) {
        codesViewModel.scanCode(scannedValue).observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(it.message ?: getString(R.string.something_went_wrong, getString(R.string.try_again)))
                Status.LOADING -> {}
                Status.SUCCESS -> {
                    val data = it.data
                    if (data != null) {
                        when (data.qr_type) {
                            "user" -> {
                                val item = data.data
                                val action =
                                    SendLuvQrScanFragmentDirections.actionSendLuvQrScanFragmentToSelectReceiverFragment(
                                        args.gift,
                                        UserDetails(
                                            id = item.id,
                                            first_name = item.first_name,
                                            last_name = item.last_name,
                                            username = item.username,
                                            photo = item.photo,
                                            user_id = item.id
                                        )
                                    )
                                findNavController().navigate(action)
                            }
                            else -> {
                                snackBar(getString(R.string.qr_not_found))
                            }
                        }
                    }
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
        scanning = false
        if (barcodeDetector == null || cameraSource == null) setupControls()
    }

    private fun askForCameraPermission() {
        ActivityCompat.requestPermissions(
            requireActivity(),
            arrayOf(android.Manifest.permission.CAMERA),
            requestCodeCameraPermission
        )
    }

    @Deprecated("Deprecated in Java")
    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == requestCodeCameraPermission && grantResults.isNotEmpty()) {
            if (grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                setupControls()
            } else {
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        cameraSource?.stop()
    }
}