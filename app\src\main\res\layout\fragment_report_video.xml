<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.fragments.story.ReportVideoFragment">

    <ImageView
        android:id="@+id/imageView"
        android:layout_width="@dimen/_30sdp"
        android:layout_height="@dimen/_30sdp"
        android:layout_margin="@dimen/_20sdp"
        android:src="@drawable/ic_back_btn"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="@drawable/ic_gift_box" />

    <TextView
        android:id="@+id/tvName"
        style="@style/h3"
        android:layout_width="wrap_content"
        android:gravity="center"
        android:text="@string/report_video"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@+id/imageView"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/imageView" />

    <TextView
        android:id="@+id/btnSubmit"
        style="@style/small"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_20sdp"
        android:background="@drawable/bg_circle_red"
        android:gravity="center"
        android:paddingVertical="@dimen/_10sdp"
        android:text="@string/submit"
        android:textColor="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <View
        android:id="@+id/view"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginHorizontal="@dimen/_20sdp"
        android:layout_marginTop="@dimen/_20sdp"
        android:background="@color/semigray"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvName" />

    <TextView
        android:id="@+id/textView17"
        style="@style/text"
        android:layout_marginTop="@dimen/_15sdp"
        android:gravity="center"
        android:text="@string/why_are_you_reporting_this_video"
        android:textColor="@color/gray"
        app:layout_constraintStart_toStartOf="@+id/view"
        app:layout_constraintTop_toBottomOf="@+id/view" />

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/textInputLayout"
        style="@style/Widget.Material3.TextInputLayout.FilledBox.ExposedDropdownMenu"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_20sdp"
        android:background="@drawable/bg_edit"
        app:boxStrokeWidth="0dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textView17">

        <com.google.android.material.textfield.MaterialAutoCompleteTextView
            android:id="@+id/edtReporting"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/bg_edit"
            android:imeOptions="actionNext"
            android:padding="10dp"
            android:inputType="none"
            android:paddingHorizontal="@dimen/_10sdp"
            android:textSize="14sp" />

    </com.google.android.material.textfield.TextInputLayout>

</androidx.constraintlayout.widget.ConstraintLayout>