<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.fragments.qr.CreateQrGiftingFragment">

    <ImageView
        android:id="@+id/imageView"
        android:layout_width="@dimen/_30sdp"
        android:layout_height="@dimen/_30sdp"
        android:layout_margin="@dimen/_20sdp"
        android:src="@drawable/ic_back_btn"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/textView"
        style="@style/h3"
        android:text="@string/create_luv_chest"
        app:layout_constraintBottom_toBottomOf="@+id/imageView"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/imageView" />

    <View
        android:id="@+id/view"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginTop="@dimen/_20sdp"
        android:background="@color/semigray"
        app:layout_constraintEnd_toEndOf="@+id/imageView1"
        app:layout_constraintStart_toStartOf="@+id/imageView"
        app:layout_constraintTop_toBottomOf="@+id/textView" />

    <ImageView
        android:id="@+id/imageView1"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_margin="@dimen/_20sdp"
        android:src="@drawable/ic_question"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/view">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tvInfo"
                style="@style/text"
                android:layout_width="wrap_content"
                android:layout_margin="@dimen/_15sdp"
                android:gravity="center"
                android:text="@string/number_of_scans"
                android:textColor="@color/gray"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/textView13"
                style="@style/text"
                android:layout_width="wrap_content"
                android:gravity="center"
                android:text="@string/per"
                android:textColor="@color/gray"
                app:layout_constraintStart_toStartOf="@+id/edtPerDay"
                app:layout_constraintTop_toTopOf="@+id/tvInfo" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/edtEmail"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/_15sdp"
                app:hintEnabled="false"
                app:layout_constraintEnd_toStartOf="@+id/edtPerDay"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toStartOf="@+id/tvInfo"
                app:layout_constraintTop_toBottomOf="@+id/tvInfo">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/acScans"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/bg_edit"
                    android:digits="123456789"
                    android:inputType="number"
                    android:maxLines="1"
                    android:textSize="12sp"
                    android:paddingHorizontal="@dimen/_20sdp"
                    android:paddingVertical="@dimen/_10sdp" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/edtPerDay"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                app:hintEnabled="false"
                app:layout_constraintBottom_toBottomOf="@+id/edtEmail"
                app:layout_constraintEnd_toEndOf="parent"
                android:layout_marginEnd="@dimen/_20sdp"
                android:textSize="12sp"
                app:layout_constraintStart_toEndOf="@+id/edtEmail"
                app:layout_constraintTop_toTopOf="@+id/edtEmail">

                <AutoCompleteTextView
                    android:id="@+id/acDays"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/bg_edit"
                    android:hint="@string/days"
                    android:inputType="none"
                    android:textSize="12sp"
                    android:paddingHorizontal="@dimen/_20sdp"
                    android:paddingVertical="@dimen/_6sdp" />

            </com.google.android.material.textfield.TextInputLayout>

            <TextView
                android:id="@+id/textView14"
                style="@style/text"
                android:layout_width="0dp"
                android:layout_marginTop="@dimen/_20sdp"
                android:gravity="center"
                android:text="@string/select_the_frequency_to_reward_users_when_they_scan_your_qr_for_example_1_per_day"
                android:textColor="@color/gray"
                app:layout_constraintEnd_toEndOf="@+id/edtPerDay"
                app:layout_constraintStart_toStartOf="@+id/edtEmail"
                app:layout_constraintTop_toBottomOf="@+id/edtEmail" />

            <View
                android:id="@+id/view3"
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_margin="@dimen/_20sdp"
                android:background="@color/semigray"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/textView14" />

            <TextView
                android:id="@+id/textView15"
                style="@style/text"
                android:layout_marginTop="@dimen/_20sdp"
                android:text="@string/gift_range"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/view3" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rcvRange"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                app:layout_constraintEnd_toEndOf="@+id/view3"
                app:layout_constraintStart_toStartOf="@+id/view3"
                app:layout_constraintTop_toBottomOf="@+id/textView15"
                tools:itemCount="1"
                tools:listitem="@layout/item_giftrange" />

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/mt"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginVertical="@dimen/_20sdp"
                app:cardBackgroundColor="@color/md_theme_light_background"
                app:layout_constraintEnd_toEndOf="@+id/rcvRange"
                app:layout_constraintStart_toStartOf="@+id/rcvRange"
                app:layout_constraintTop_toBottomOf="@+id/rcvRange"
                app:strokeColor="@color/semigray">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="@dimen/_10sdp">

                    <com.google.android.material.checkbox.MaterialCheckBox
                        android:id="@+id/cbNeedKey"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:buttonTint="@color/gray" />

                    <TextView
                        android:id="@+id/textView4"
                        style="@style/text"
                        android:gravity="center"
                        android:text="@string/generate_key_users_will_be_able_nto_use_code_only_with_it"
                        android:textColor="@color/gray" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <TextView
                android:id="@+id/tvSponsored"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_25sdp"
                android:layout_marginStart="@dimen/_8sdp"
                android:text="@string/sponsored_by_2"
                android:textColor="@color/gray"
                app:layout_constraintStart_toStartOf="@+id/mt"
                app:layout_constraintTop_toBottomOf="@+id/mt" />



            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/textInputLayout3"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_20sdp"
                android:background="@drawable/bg_edit"
                app:layout_constraintTop_toBottomOf="@+id/tvSponsored"
                tools:layout_editor_absoluteX="26dp"
                app:hintEnabled="false">


                <AutoCompleteTextView
                    android:id="@+id/edtSponsored"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/bg_edit"
                    android:hint="@string/empty"
                    android:inputType="none"
                    android:textSize="14sp"
                    android:paddingVertical="@dimen/_6sdp"
                    android:paddingHorizontal="@dimen/_20sdp" />

            </com.google.android.material.textfield.TextInputLayout>

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btnNext"
                style="@style/button"
                android:layout_margin="@dimen/_20sdp"
                android:text="@string/submit"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/textInputLayout3" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>