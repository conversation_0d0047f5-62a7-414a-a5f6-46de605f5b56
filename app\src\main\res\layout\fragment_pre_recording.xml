<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    tools:context=".ui.fragments.story.PreRecordingFragment">

    <androidx.cardview.widget.CardView
        android:id="@+id/cardView3"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/_10sdp"
        app:cardCornerRadius="@dimen/_20sdp"
        app:layout_constraintBottom_toTopOf="@+id/linearLayout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.wonderkiln.camerakit.CameraView
            android:id="@+id/camera"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:adjustViewBounds="true" />

        <RelativeLayout
            android:id="@+id/videoView"
            android:layout_width="fill_parent"
            android:layout_height="fill_parent"
            android:visibility="gone">

            <VideoView
                android:id="@+id/videoView12"
                android:layout_width="fill_parent"
                android:layout_height="fill_parent"
                android:layout_alignParentLeft="true"
                android:layout_alignParentRight="true" />

        </RelativeLayout>

    </androidx.cardview.widget.CardView>

    <androidx.cardview.widget.CardView
        android:id="@+id/cdvImage"
        android:layout_width="@dimen/_35sdp"
        android:layout_height="@dimen/_35sdp"
        android:layout_margin="@dimen/_13sdp"
        app:cardBackgroundColor="@color/lightBlack"
        app:cardCornerRadius="@dimen/_20sdp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/cardView3">

        <ImageView
            android:id="@+id/ivClose"
            android:layout_width="@dimen/_25sdp"
            android:layout_height="@dimen/_25sdp"
            android:layout_gravity="center"
            android:src="@drawable/ic_close" />

    </androidx.cardview.widget.CardView>

    <androidx.cardview.widget.CardView
        android:id="@+id/cdvDiscard"
        android:layout_width="@dimen/_35sdp"
        android:layout_height="@dimen/_35sdp"
        android:layout_margin="@dimen/_13sdp"
        android:visibility="gone"
        app:cardBackgroundColor="@color/lightBlack"
        app:cardCornerRadius="@dimen/_20sdp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/cardView3">

        <ImageView
            android:layout_width="@dimen/_25sdp"
            android:layout_height="@dimen/_25sdp"
            android:layout_gravity="center"
            android:src="@drawable/ic_arrow_left"
            app:tint="@color/white" />

    </androidx.cardview.widget.CardView>

    <ImageView
        android:id="@+id/btnCameraClick"
        android:layout_width="@dimen/_60sdp"
        android:layout_height="@dimen/_60sdp"
        android:layout_marginBottom="@dimen/_20sdp"
        android:elevation="@dimen/_100sdp"
        android:padding="@dimen/_4sdp"
        android:src="@drawable/ic_camera_click"
        app:layout_constraintBottom_toBottomOf="@+id/cardView3"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent" />

    <ProgressBar
        android:id="@+id/circularProgressBar"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:elevation="@dimen/_50sdp"
        android:indeterminate="true"
        android:indeterminateBehavior="repeat"
        android:indeterminateOnly="true"
        android:progressDrawable="@drawable/layered_progress_bar"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/btnCameraClick"
        app:layout_constraintEnd_toEndOf="@+id/btnCameraClick"
        app:layout_constraintStart_toStartOf="@+id/btnCameraClick"
        app:layout_constraintTop_toTopOf="@+id/btnCameraClick" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/linearLayout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <androidx.cardview.widget.CardView
            android:id="@+id/cdvRepeat"
            android:layout_width="@dimen/_45sdp"
            android:layout_height="@dimen/_45sdp"
            android:layout_margin="@dimen/_10sdp"
            app:cardBackgroundColor="#333333"
            app:cardCornerRadius="@dimen/_50sdp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/ivRepeat"
                android:layout_width="@dimen/_25sdp"
                android:layout_height="@dimen/_25sdp"
                android:layout_gravity="center"
                android:src="@drawable/ic_repeat" />

        </androidx.cardview.widget.CardView>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/linearLayout1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="10dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <androidx.cardview.widget.CardView
            android:id="@+id/cvTabs"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_10sdp"
            app:cardBackgroundColor="@color/tab_unselected"
            app:cardCornerRadius="16dp"
            app:cardElevation="0dp"
            app:contentPadding="6dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/ivNext"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.5">

            <com.google.android.material.tabs.TabLayout
                android:id="@+id/tabLayout1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/tab_unselected"
                app:tabBackground="@drawable/tab_new_background"
                app:tabGravity="fill"
                app:tabIndicatorHeight="0dp"
                app:tabMode="fixed"
                app:tabRippleColor="@null"
                app:tabSelectedTextColor="@color/white"
                app:tabTextColor="@color/white" />

        </androidx.cardview.widget.CardView>

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/ivNext"
            android:layout_width="@dimen/_50sdp"
            android:layout_height="@dimen/_50sdp"
            android:layout_gravity="center"
            android:background="@color/white"
            android:padding="8dp"
            android:src="@drawable/arrow_forward"
            app:contentPadding="8dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.5"
            app:shapeAppearance="@style/ShapeAppearance.Material3.Corner.Full" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>