plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'kotlin-parcelize'
    id 'androidx.navigation.safeargs.kotlin'
    id 'com.google.gms.google-services'
    id 'com.google.firebase.crashlytics'
}

android {
    namespace 'com.flashbid.luv'
    compileSdk 34

    bundle {

        language {
            enableSplit = false
        }
    }

    defaultConfig {
        applicationId "com.flashbid.luv"
        minSdk 26
        targetSdk 34
        versionCode 169
//        versionCode 150
        versionName "6.3.8"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        buildConfigField "String", "AGORA_APP_ID", "\"e520bf6c31004dd4a7795164e1c92a7a\""
        buildConfigField "String", "BASE_URL", "\"https://api.theluvnetwork.com/\""
        buildConfigField "String", "BASE_URL_DEV", "\"https://luvapi.manaknightdigital.com/\""
        buildConfigField "String", "PROJECT_ID", "\"bHV2Omc2d3QxNjdndjZ3bDVyY2dlcGdtaW80cTBwNHlhbTY=\""
        buildConfigField "String", "GOOGLE_CLIENT_ID", "\"683588524903-62e8gl2mr6qnbkbqfeadhca0cofbp1r3.apps.googleusercontent.com\""
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    android.applicationVariants.all { variant ->
        variant.outputs.all {
            outputFileName = "${variant.applicationId}-${variant.versionName}.apk"
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = '17'
    }
    buildFeatures {
        viewBinding true
    }
}

dependencies {
    implementation 'androidx.appcompat:appcompat:1.6.0'
    implementation 'com.google.android.material:material:1.8.0'
    implementation 'com.google.android.gms:play-services-vision-common:19.1.3'
    implementation 'com.google.android.gms:play-services-vision:20.1.3'
    implementation 'com.google.android.gms:play-services-auth:20.4.1'
    implementation 'com.google.android.gms:play-services-location:21.0.1'
    implementation 'androidx.swiperefreshlayout:swiperefreshlayout:1.1.0'
    implementation 'androidx.recyclerview:recyclerview:1.1.0'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'

    //Firebase
    implementation platform('com.google.firebase:firebase-bom:31.1.1')
    implementation 'com.google.firebase:firebase-auth-ktx'
    implementation 'com.google.firebase:firebase-crashlytics-ktx'
    implementation 'com.google.firebase:firebase-analytics-ktx'
    implementation 'com.google.firebase:firebase-messaging-ktx'
    implementation 'com.google.firebase:firebase-dynamic-links-ktx'

    //Lifecycle
    def lifecycleVersion = "2.5.1"
    implementation "androidx.lifecycle:lifecycle-livedata-ktx:$lifecycleVersion"
    implementation "androidx.lifecycle:lifecycle-viewmodel-ktx:$lifecycleVersion"

    //Navigation
    def navVersion = "2.5.3"
    implementation "androidx.navigation:navigation-fragment-ktx:$navVersion"
    implementation "androidx.navigation:navigation-ui-ktx:$navVersion"

    //Splash screen
    implementation "androidx.core:core-splashscreen:1.0.0"

    //Sizing
    implementation "com.intuit.sdp:sdp-android:1.1.0"

    // Koin DI
    def koin_version= "3.2.2"
    implementation "io.insert-koin:koin-core:$koin_version"
    implementation "io.insert-koin:koin-android:3.3.0"

    // Retrofit
    def retrofitVersion = "2.9.0"
    implementation "com.squareup.retrofit2:retrofit:$retrofitVersion"
    implementation "com.squareup.retrofit2:converter-gson:$retrofitVersion"

    // Network Requests interceptor
    implementation "com.squareup.okhttp3:logging-interceptor:5.0.0-alpha.2"

    //passcode Pin
    implementation 'com.github.poovamraj:PinEditTextField:1.2.6'

    //Dots indicator
    implementation "ru.tinkoff.scrollingpagerindicator:scrollingpagerindicator:1.2.1"

    //Create QR Code
    implementation 'com.github.kenglxn.QRGen:android:3.0.1'
    implementation 'com.journeyapps:zxing-android-embedded:4.3.0'

    //Permissions
    implementation 'com.karumi:dexter:6.2.3'

    //Image processing
    implementation 'com.github.bumptech.glide:glide:4.14.2'

    //IAP
    def billing_version = "6.0.1"
    implementation("com.android.billingclient:billing-ktx:$billing_version")

    //Autofit Textview
    implementation 'me.grantland:autofittextview:0.2.1'

    //beacon
    implementation 'org.altbeacon:android-beacon-library:2.20'

    implementation "androidx.media3:media3-exoplayer:1.1.1"
    implementation "androidx.media3:media3-ui:1.1.1"
    implementation "androidx.media3:media3-common:1.1.1"
    implementation 'com.github.bruferrari:CameraKit-Android:0.10.1'

    implementation 'io.agora.rtc:full-sdk:4.2.3'
    implementation 'io.agora.rtc:chat-sdk:1.2.0'
    implementation 'io.agora.rtm:rtm-sdk:1.4.4'

}