<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="@dimen/_20sdp"
    tools:context=".ui.fragments.auth.login.LoginFragment">

    <ImageView
        android:id="@+id/imageView"
        android:layout_width="@dimen/_30sdp"
        android:layout_height="@dimen/_30sdp"
        android:src="@drawable/ic_back_btn"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/textView"
        style="@style/h1"
        android:text="@string/sign_in"
        android:transitionName="@string/login_transition"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/imageView" />

    <com.google.android.material.textfield.TextInputEditText
        android:id="@+id/textView2"
        style="@style/subtitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_60sdp"
        android:background="@drawable/bg_edit"
        android:drawableStart="@drawable/sms"
        android:drawablePadding="@dimen/_10sdp"
        android:hint="@string/email"
        android:maxLines="1"
        android:padding="@dimen/_10sdp"
        android:imeOptions="actionNext"
        android:textSize="12sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textView" />

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/textView3"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:hintEnabled="false"
        android:layout_marginTop="@dimen/_20sdp"
        app:passwordToggleEnabled="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textView2">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/edtPass"
            style="@style/subtitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:maxLines="1"
            android:imeOptions="actionDone"
            android:textSize="12sp"
            android:background="@drawable/bg_edit"
            android:drawableStart="@drawable/ic_lock_small"
            android:drawablePadding="@dimen/_10sdp"
            android:inputType="textPassword"
            android:hint="@string/password"
            android:padding="@dimen/_10sdp" />

    </com.google.android.material.textfield.TextInputLayout>

    <TextView
        android:id="@+id/tvForgot"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_10sdp"
        android:transitionName="@string/reset_transition"
        android:gravity="center"
        android:text="@string/forgot_password"
        android:textColor="@color/gray"
        android:textSize="14sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textView3" />

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btnLogin"
        style="@style/button"
        android:layout_marginTop="@dimen/_20sdp"
        android:text="@string/sign_in"
        android:textColor="@color/white"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvForgot" />

    <TextView
        android:id="@+id/textView7"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/_10sdp"
        android:gravity="center"
        android:text="@string/don_t_have_an_account"
        android:textColor="@color/gray"
        android:textSize="14sp"
        app:layout_constraintBottom_toTopOf="@+id/materialButton"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent" />

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/materialButton"
        style="@style/outlinedButton"
        android:layout_margin="@dimen/_30sdp"
        android:text="@string/sign_up"
        android:transitionName="@string/signup_transition"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>