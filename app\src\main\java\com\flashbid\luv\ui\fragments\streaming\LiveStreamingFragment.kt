package com.flashbid.luv.ui.fragments.streaming

import androidx.fragment.app.Fragment


class LiveStreamingFragment : Fragment() {

//
//    fun joinChannel(view: View?) {
//        if (checkSelfPermission()) {
//            val options = ChannelMediaOptions()
//            // For Live Streaming, set the channel profile as LIVE_BROADCASTING.
//            options.channelProfile = Constants.CHANNEL_PROFILE_LIVE_BROADCASTING
//            // Set the client role as BROADCASTER or AUDIENCE according to the scenario.
//            if (audienceRole.isChecked()) { //Audience
//                options.clientRoleType = Constants.CLIENT_ROLE_AUDIENCE
//            } else { //Host
//                options.clientRoleType = Constants.CLIENT_ROLE_BROADCASTER
//                // Display LocalSurfaceView.
//                setupLocalVideo()
//                localSurfaceView.setVisibility(View.VISIBLE)
//                // Start local preview.
//                agoraEngine.startPreview()
//            }
//            audienceRole.setEnabled(false) // Disable the switch
//            // Join the channel with a temp token.
//            // You need to specify the user ID yourself, and ensure that it is unique in the channel.
//            agoraEngine.joinChannel(token, channelName, uid, options)
//        } else {
//            Toast.makeText(
//                getApplicationContext(),
//                "Permissions was not granted",
//                Toast.LENGTH_SHORT
//            ).show()
//        }
//    }
//
//    fun leaveChannel(view: View?) {
//        if (!isJoined) {
//            showMessage("Join a channel first")
//        } else {
//            agoraEngine.leaveChannel()
//            showMessage("You left the channel")
//            // Stop remote video rendering.
//            if (remoteSurfaceView != null) remoteSurfaceView.setVisibility(View.GONE)
//            // Stop local video rendering.
//            if (localSurfaceView != null) localSurfaceView.setVisibility(View.GONE)
//            isJoined = false
//        }
//        audienceRole.setEnabled(true) // Enable the switch
//    }


}