package com.flashbid.luv.ui.fragments.transactions

import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.flashbid.luv.R
import com.flashbid.luv.databinding.FragmentMyChestQrBinding
import com.flashbid.luv.extensions.convertDate
import com.flashbid.luv.extensions.getTimeInAgo
import com.flashbid.luv.extensions.setOnClickWithDebounce
import com.flashbid.luv.extensions.snackBar
import com.flashbid.luv.extensions.viewBinding
import com.flashbid.luv.models.remote.HistoryDetailResponse
import com.flashbid.luv.network.Status
import com.flashbid.luv.util.HistoryMapping
import com.flashbid.luv.util.loadImageFromUrl
import com.flashbid.luv.viewmodels.TransactionViewModel
import org.koin.androidx.viewmodel.ext.android.viewModel

class MyChestQrFragment : Fragment(R.layout.fragment_my_chest_qr) {

    private val binding by viewBinding(FragmentMyChestQrBinding::bind)
    private val viewModel by viewModel<TransactionViewModel>()
    private val args by navArgs<MyChestQrFragmentArgs>()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.imageView.setOnClickWithDebounce { findNavController().popBackStack() }

        getHistoryDetail(args.id)
    }

    private fun getHistoryDetail(
        id: String
    ) {
        viewModel.getHistoryDetail(id).observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(
                    it.message ?: getString(
                        R.string.something_went_wrong, getString(R.string.try_again)
                    )
                )

                Status.LOADING -> {}
                Status.SUCCESS -> {
                    setData(it.data)
                }
            }
        }
    }

    private fun setData(data: HistoryDetailResponse?) {
        if (data != null) {
            binding.ivUserImage.loadImageFromUrl(data.message.photo)
            binding.tvName.text = data.message.first_name
            binding.tvDate.text = data.message.update_at.convertDate()
            binding.tvInfo.text = data.message.update_at.getTimeInAgo()
            binding.tvTransaction.text = data.message.id.toString()
            binding.tvHearts.text = "-${data.message.amount}"

            when (data.message.status) {
                HistoryMapping.STATUS.COMPLETED -> binding.tvStatus.text =
                    getString(R.string.completed)

                HistoryMapping.STATUS.PENDING -> binding.tvStatus.text = getString(R.string.pending)
                HistoryMapping.STATUS.VOID -> binding.tvStatus.text = getString(R.string.void_trans)
                HistoryMapping.STATUS.SUSPENDED -> binding.tvStatus.text =
                    getString(R.string.suspended)
            }
        }

    }
}