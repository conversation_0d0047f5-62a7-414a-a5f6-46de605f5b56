<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent">

    <androidx.cardview.widget.CardView
        android:id="@+id/dialogCardView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_15sdp"
        app:cardBackgroundColor="@color/white"
        app:cardCornerRadius="@dimen/_26sdp"
        app:cardElevation="1dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingHorizontal="@dimen/_15sdp"
            android:paddingBottom="@dimen/_40sdp">

            <ImageView
                android:id="@+id/imageView8"
                android:layout_width="@dimen/_28sdp"
                android:layout_height="@dimen/_28sdp"
                android:layout_gravity="end"
                android:layout_marginTop="@dimen/_10sdp"
                android:src="@drawable/ic_close_gray"
                tools:ignore="ContentDescription" />

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/_150sdp">

                <ImageView
                    android:id="@+id/iv_blank"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_150sdp"
                    android:src="@drawable/blank"
                    tools:ignore="ContentDescription" />

                <ImageView
                    android:id="@+id/iv_gift"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_150sdp"
                    android:src="@drawable/ic_crate"
                    android:padding="15sp"
                    android:layout_centerInParent="true"
                    tools:ignore="ContentDescription" />

            </RelativeLayout>


            <TextView
                android:id="@+id/tvAmount"
                style="@style/h2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_5sdp"
                android:layout_marginTop="@dimen/_10sdp"
                android:text="100 LUV" />

            <TextView
                android:id="@+id/textView29"
                style="@style/text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_5sdp"
                android:text="@string/with_luv_from"
                android:textAlignment="center"
                android:textColor="@color/gray"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/textView26"
                app:layout_constraintTop_toBottomOf="@+id/textView26" />

            <TextView
                android:id="@+id/textView30"
                style="@style/text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_10sdp"
                android:text="@string/with_luv_from"
                android:textAlignment="center"
                android:textColor="@color/gray"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/textView26"
                app:layout_constraintTop_toBottomOf="@+id/textView26" />


            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btnOpen"
                style="@style/button"
                android:layout_width="wrap_content"
                android:layout_marginTop="@dimen/_20sdp"
                android:text="@string/awesome"
                android:textColor="@color/white"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvForgot" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnSendVideo"
                style="@style/button"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_marginVertical="@dimen/_15sdp"
                android:text="@string/send_a_thank_you_video"
                android:textColor="#F95050"
                app:backgroundTint="#FFF2F2"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvForgot" />

            <TextView
                android:id="@+id/tvInviteMore"
                style="@style/subtitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_5sdp"
                android:layout_marginTop="@dimen/_20sdp"
                android:layout_marginBottom="@dimen/_10sdp"
                android:text="@string/invite_more"
                app:layout_constraintTop_toBottomOf="@+id/iv_gift"
                android:visibility="gone"/>

        </LinearLayout>

    </androidx.cardview.widget.CardView>

</FrameLayout>