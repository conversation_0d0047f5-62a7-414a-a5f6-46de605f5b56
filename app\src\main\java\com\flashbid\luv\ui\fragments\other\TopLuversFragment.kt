package com.flashbid.luv.ui.fragments.other

import android.os.Bundle
import android.view.View
import androidx.activity.OnBackPressedCallback
import androidx.fragment.app.Fragment
import androidx.navigation.NavOptions
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.RecyclerView
import com.flashbid.luv.R
import com.flashbid.luv.adapter.BrandsAdapter
import com.flashbid.luv.adapter.RegularAdapter
import com.flashbid.luv.adapter.RegularItemAdapter
import com.flashbid.luv.data.local.AppPreferences
import com.flashbid.luv.databinding.FragmentTopLuversBinding
import com.flashbid.luv.extensions.setGridLayout
import com.flashbid.luv.extensions.setOnClickWithDebounce
import com.flashbid.luv.extensions.setVerticalLayout
import com.flashbid.luv.extensions.snackBar
import com.flashbid.luv.extensions.viewBinding
import com.flashbid.luv.models.remote.Brand
import com.flashbid.luv.models.remote.BrandCategories
import com.flashbid.luv.network.Status
import com.flashbid.luv.viewmodels.BrandsViewModel
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel

class TopLuversFragment : Fragment(R.layout.fragment_top_luvers) {

    private val binding by viewBinding(FragmentTopLuversBinding::bind)
    private val viewModel: BrandsViewModel by viewModel()
    private val brandCategories: ArrayList<BrandCategories> = ArrayList()
    private val brands: ArrayList<Brand> = ArrayList()
    private val brandsAdapter by lazy { BrandsAdapter(this, brandCategories, this::onBrandClick,  findNavController()) }

    private val regularItemAdapter by lazy { RegularItemAdapter(brands) { brand ->
        val action = TopLuversFragmentDirections.actionTopLuversFragmentToUserProfileFragment(brand.user_id)
        findNavController().navigate(action)
    }}
    private val args by navArgs<TopLuversFragmentArgs>()
    private val pref by inject<AppPreferences>()

    private fun onBrandClick(item: Brand) {
        val action =
            TopLuversFragmentDirections.actionTopLuversFragmentToUserProfileFragment(item.user_id)
        findNavController().navigate(action)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.imageView.setOnClickWithDebounce {
            requireActivity().onBackPressed()
        }

        binding.btnFilter.setOnClickWithDebounce {
            val action =
                TopLuversFragmentDirections.actionTopLuversFragmentToTopGifterFilterFragment()
            findNavController().navigate(action)
        }

        binding.rcvBrands.apply {
            setVerticalLayout()
            adapter = brandsAdapter
        }
        binding.rcvBrands.apply {
            setGridLayout(3)
            adapter = regularItemAdapter
        }

        requireActivity().onBackPressedDispatcher.addCallback(viewLifecycleOwner,
            object : OnBackPressedCallback(true) {
                override fun handleOnBackPressed() {
                    val navController = findNavController()
                    val navOptions = NavOptions.Builder()
                        .setPopUpTo(R.id.homeFragment, true)
                        .build()
                    navController.navigate(R.id.homeFragment, null, navOptions)
                }
            }
        )

    }

    override fun onResume() {
        super.onResume()
        getTopBrands(args.period?:"daily", args.type?:"regular")
    }

    private fun getTopBrands(period: String, userType: String) {
        pref.period =period
        binding.tvType.text = when (userType) {
            "regular" -> getString(R.string.user)
            "brand" -> getString(R.string.brand)
            else -> ""
        }
        viewModel.getTopBrands(period, userType).observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(
                    it.message ?: getString(
                        R.string.something_went_wrong,
                        getString(R.string.try_again)
                    )
                )

                Status.LOADING -> {}

                Status.SUCCESS -> {
                    val data = it.data?.message ?: emptyList()
                    println("filteredList (data) -> $data")

                    if (data.isNotEmpty()) {
                        // Filter data first
                        val brandData = data.filter { it.data != null } as ArrayList<BrandCategories>
                        val companyData = data.filter { !it.company_name.isNullOrEmpty() } as ArrayList<BrandCategories>

                        // Convert companyData (ArrayList<BrandCategories>) into an ArrayList<Brand>
                        val companyList = companyData.map { model ->
                            Brand(
                                amount = model.amount?:0,
                                category_id = model.id,
                                company_image = "",
                                company_name = model.company_name ?: "",
                                create_at = model.create_at,
                                id = model.id,
                                position = 0,
                                update_at = model.update_at,
                                user_id = 0,
                                is_favourite = false
                            )
                        } as ArrayList<Brand>

                        // Set the correct adapter dynamically
                        val selectedAdapter: RecyclerView.Adapter<*>? = when {
                            brandData.isNotEmpty() -> {
                                println("filteredList (brandsAdapter) -> $brandData")
                                brandsAdapter.refresh(brandData)
                                binding.rcvBrands.setVerticalLayout()
                                brandsAdapter
                            }
                            companyData.isNotEmpty() -> {
                                println("filteredList (regularItemAdapter) -> $companyData")
                                regularItemAdapter.refresh(companyList) // Refresh instead of reinitializing
                                binding.rcvBrands.setGridLayout(3)
                                regularItemAdapter
                            }
                            else -> null // No valid data
                        }

                        // Set the selected adapter only if it's not null
                        selectedAdapter?.let {
                            binding.rcvBrands.adapter = it
                        }
                    }
                }


            }
        }
    }




}