package com.flashbid.luv.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.AsyncDifferConfig
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import androidx.viewbinding.ViewBinding

class BaseViewHolder<T : ViewBinding>(
    val binding: T,
) : RecyclerView.ViewHolder(binding.root)

class BaseListAdapter(
    private val itemClickCallback: ((BaseItem<*>) -> Unit)?
) : ListAdapter<BaseItem<*>, BaseViewHolder<*>>(

    AsyncDifferConfig.Builder(object : DiffUtil.ItemCallback<BaseItem<*>>() {
        override fun areItemsTheSame(oldItem: BaseItem<*>, newItem: BaseItem<*>): Boolean {
            return oldItem.uniqueId == newItem.uniqueId
        }

        override fun areContentsTheSame(oldItem: BaseItem<*>, newItem: BaseItem<*>): Boolean {
            return oldItem == newItem
        }
    }).build()

) {
    private var lastItemForViewTypeLookup: BaseItem<*>? = null

    override fun getItemViewType(position: Int) = getItem(position).layoutId

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseViewHolder<*> {

        val itemView = LayoutInflater.from(parent.context).inflate(viewType, parent, false)

        val item = getItemForViewType(viewType)
        return BaseViewHolder(item.initializeViewBinding(itemView))
    }

    override fun onBindViewHolder(holder: BaseViewHolder<*>, position: Int) {
        getItem(position).bind(holder, itemClickCallback)
    }

    private fun getItemForViewType(viewType: Int): BaseItem<*> {
        val lastItemForViewTypeLookup = lastItemForViewTypeLookup
        if (lastItemForViewTypeLookup != null
            && lastItemForViewTypeLookup.layoutId == viewType
        ) {
            return lastItemForViewTypeLookup
        }

        for (i in 0 until itemCount) {
            val item: BaseItem<*> = getItem(i)
            if (item.layoutId == viewType) {
                return item
            }
        }
        throw IllegalStateException("Could not find model for view type: $viewType")
    }
}

interface BaseItem<T : ViewBinding> {

    val layoutId: Int

    val uniqueId: Any

    fun initializeViewBinding(view: View): T

    fun bind(holder: BaseViewHolder<*>, itemClickCallback: ((BaseItem<T>) -> Unit)?) {
        val specificHolder = holder as BaseViewHolder<T>
        bind(specificHolder.binding, itemClickCallback)
    }

    fun bind(binding: T, itemClickCallback: ((BaseItem<T>) -> Unit)?)

    override fun equals(other: Any?): Boolean
}

