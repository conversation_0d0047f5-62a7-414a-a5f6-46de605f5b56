package com.flashbid.luv.fcm

import android.app.ActivityManager
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.ContentValues.TAG
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.media.RingtoneManager
import android.os.Build
import android.util.Log
import androidx.core.app.NotificationCompat
import androidx.core.content.ContextCompat
import com.flashbid.luv.MainActivity
import com.flashbid.luv.R
import com.flashbid.luv.util.Constants
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage

const val channelId = "luvNotifications"
const val channelName = "LUV Notifications"

class MyFirebasePushNotifications : FirebaseMessagingService() {

    override fun onMessageReceived(message: RemoteMessage) {
        Log.d(TAG, "onMessageReceived: ${message.data}")


        val title: String
        val body: String
        val notificationType: String

        message.notification?.let {
            title = it.title ?: ""
            body = it.body ?: ""
            notificationType = message.data["notificationType"] ?: "0"

            val isAppInForeground = isAppInForeground()

            if (notificationType == "4" && isAppInForeground) {
                val username = message.data["username"]
                val userId = message.data["user_id"]
                Constants.BATTLE_INVITE.postValue(Pair(username, userId))
            } else if (notificationType == "5" && isAppInForeground) {
                val username = message.data["username"]
                val userId = message.data["user_id"]
                val channelName = message.data["channel_name"]
                if (channelName.isNullOrEmpty()) {
                    generatePushNotifications(title, body, notificationType, channelName)
                    Constants.BATTLE_INVITE_RESPONSE.postValue(Triple(body, userId, channelName))
                } else Constants.BATTLE_INVITE_RESPONSE.postValue(Triple(username, userId, channelName))
            } else if (notificationType == "6" && isAppInForeground) {
                val channelName = message.data["channel_name"]
                val inviterId = message.data["inviter"]
                val inviterName = message.data["inviter_name"]
                if (inviterId != null) Constants.BATTLE_SHARE.postValue(Triple(inviterId, inviterName, channelName))
                else generatePushNotifications(title, body, notificationType, channelName)
            } else if (notificationType == "7" && isAppInForeground) {
                generatePushNotifications(title, body, notificationType, "")
            }
            else generatePushNotifications(title, body, notificationType, "")
        }

    }

    private fun generatePushNotifications(
        title: String,
        message: String,
        notificationType: String,
        channel: String?
    ) {
        val intent = Intent(this, MainActivity::class.java)
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
        intent.putExtra("notificationType", notificationType)
        intent.putExtra("notificationTitle", title)
        intent.putExtra("notificationMessage", message)
        if (!channel.isNullOrEmpty()) intent.putExtra("channel_name", channel)

        val pendingIntent = PendingIntent.getActivity(
            this,
            0,
            intent,
            PendingIntent.FLAG_ONE_SHOT or PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
        )

        val defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)

        val builder: NotificationCompat.Builder =
            NotificationCompat.Builder(applicationContext, channelId)
                .setSmallIcon(R.drawable.app_icon)
                .setColor(
                    ContextCompat.getColor(
                        this,
                        R.color.redgradstart
                    )
                ) // set the color of the notification
                .setContentTitle(title)
                .setContentText(message)
                .setAutoCancel(true)
                .setSound(defaultSoundUri) // set the default notification sound
                .setContentIntent(pendingIntent)
                .setPriority(NotificationCompat.PRIORITY_HIGH) // set the priority to high

        val notificationManager =
            getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationChannel =
                NotificationChannel(channelId, channelName, NotificationManager.IMPORTANCE_HIGH)
            notificationChannel.enableLights(true)
            notificationChannel.lightColor = Color.BLUE
            notificationChannel.enableVibration(true)
            notificationChannel.vibrationPattern = longArrayOf(500, 1000, 500, 1000)
            notificationChannel.setShowBadge(true)
            notificationManager.createNotificationChannel(notificationChannel)
        }

        notificationManager.notify(0, builder.build())
    }


    override fun onNewToken(token: String) {
        Log.d(TAG, "Refreshed token: $token")
        Constants.FCM_TOKEN.postValue(token)
    }

    private fun isAppInForeground(): Boolean {
        val appProcessInfo = ActivityManager.RunningAppProcessInfo()
        ActivityManager.getMyMemoryState(appProcessInfo)
        return appProcessInfo.importance == ActivityManager.RunningAppProcessInfo.IMPORTANCE_FOREGROUND
    }
}
