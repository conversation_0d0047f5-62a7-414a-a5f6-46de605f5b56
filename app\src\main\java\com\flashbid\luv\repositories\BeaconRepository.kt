package com.flashbid.luv.repositories

import androidx.lifecycle.LiveData
import androidx.lifecycle.liveData
import com.flashbid.luv.models.battle.TopGiftersResponse
import com.flashbid.luv.models.remote.AlertsResponse
import com.flashbid.luv.models.remote.BattleDetailsResponse
import com.flashbid.luv.models.remote.BattleRematchRequest
import com.flashbid.luv.models.remote.BattleResponseRequest
import com.flashbid.luv.models.remote.BattleResultResponse
import com.flashbid.luv.models.remote.BeaconClaimRequest
import com.flashbid.luv.models.remote.BeaconDetailResponse
import com.flashbid.luv.models.remote.BeaconRequest
import com.flashbid.luv.models.remote.CounterRequest
import com.flashbid.luv.models.remote.MessageResponse
import com.flashbid.luv.models.remote.SendGiftRequest
import com.flashbid.luv.models.remote.ShareInviteRequest
import com.flashbid.luv.network.RemoteDataSource
import com.flashbid.luv.network.Resource
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow

class BeaconRepository(private val remoteDataSource: RemoteDataSource) {

    suspend fun getBeacon(request: BeaconRequest): Resource<BeaconDetailResponse> {
        return try {
            val response = remoteDataSource.beacon(request)
            Resource.success(response.data)
        } catch (e: Exception) {
            Resource.error("Error occurred: ${e.message}", null)
        }
    }

    fun beaconClaim(request: BeaconClaimRequest): LiveData<Resource<MessageResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.beaconClaim(request)
            emit(response)
        }

}