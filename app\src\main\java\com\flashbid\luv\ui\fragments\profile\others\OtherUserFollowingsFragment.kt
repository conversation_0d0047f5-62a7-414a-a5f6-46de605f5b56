package com.flashbid.luv.ui.fragments.profile.others

import android.os.Bundle
import android.view.View
import android.view.inputmethod.EditorInfo
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.flashbid.luv.R
import com.flashbid.luv.adapter.UsersListAdapter
import com.flashbid.luv.databinding.FragmentUserFollowersBinding
import com.flashbid.luv.extensions.hide
import com.flashbid.luv.extensions.setOnClickWithDebounce
import com.flashbid.luv.extensions.setVerticalLayout
import com.flashbid.luv.extensions.show
import com.flashbid.luv.extensions.snackBar
import com.flashbid.luv.extensions.viewBinding
import com.flashbid.luv.models.remote.UserDetails
import com.flashbid.luv.network.Status
import com.flashbid.luv.ui.fragments.profile.UserFollowersFragmentDirections
import com.flashbid.luv.viewmodels.UserViewModel
import org.koin.androidx.viewmodel.ext.android.viewModel

class OtherUserFollowingsFragment : Fragment(R.layout.fragment_other_user_followings) {

    private val binding by viewBinding(FragmentUserFollowersBinding::bind)
    private val viewModel: UserViewModel by viewModel()
    private val args by navArgs<OtherUserFollowingsFragmentArgs>()
    private val list: ArrayList<UserDetails> = ArrayList()
    private val followAdapter by lazy {
        UsersListAdapter(
            list,
            true,
            this::onUserClick,
            this::onFollowClick
        )
    }

    private fun onUserClick(item: UserDetails) {
        val action =
            OtherUserFollowingsFragmentDirections.actionOtherUserFollowingsFragmentToUserProfileFragment(item.id?:-1)
        findNavController().navigate(action)
    }

    private fun onFollowClick(item: UserDetails, isFollowed: Int) {
        if (isFollowed == 0) followUser(item.id?:0)
        if (isFollowed == 1) unfollowUser(item.id?:0)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.imageView.setOnClickWithDebounce {
            findNavController().popBackStack()
        }

        binding.rcvUsers.apply {
            setVerticalLayout()
            adapter = followAdapter
        }

        if (args.followers) {
            binding.textView.text = getString(R.string.followers)
            getFollows(args.userId)
        } else {
            binding.textView.text = getString(R.string.following)
            getFollowing(args.userId)
        }

        binding.edtSearch.imeOptions = EditorInfo.IME_ACTION_SEARCH
        binding.edtSearch.setOnEditorActionListener { _, actionId, _ ->
            if (actionId == EditorInfo.IME_ACTION_SEARCH) {
//                val query = binding.edtSearch.text.toString()
//                if (query.isNotEmpty()) searchUser(query)
//                else getFollows(query)
                return@setOnEditorActionListener true
            }
            false
        }
    }

    private fun getFollows(id: String) {
        viewModel.getCustomFollower(id).observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(it.message ?: getString(R.string.something_went_wrong, getString(R.string.try_again)))
                Status.LOADING -> {}
                Status.SUCCESS -> {
                    list.clear()
                    list.addAll(it.data?.list ?: ArrayList())
                    followAdapter.refresh()
                }
            }
        }
    }

    private fun getFollowing(id: String) {
        viewModel.getCustomFollowing(id).observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(it.message ?: getString(R.string.something_went_wrong, getString(R.string.try_again)))
                Status.LOADING -> {}
                Status.SUCCESS -> {
                    list.clear()
                    list.addAll(it.data?.list ?: ArrayList())
                    followAdapter.refresh()
                }
            }
        }
    }

    private fun followUser(id: Int) {
        viewModel.followUser(id).observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(it.message ?: getString(R.string.something_went_wrong, getString(R.string.try_again)))
                Status.LOADING -> {}
                Status.SUCCESS -> {
                    if (args.followers) getFollows(args.userId)
                    else getFollowing(args.userId)
                }
            }
        }
    }

    private fun unfollowUser(id: Int) {
        viewModel.unfollowUser(id).observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(it.message ?: getString(R.string.something_went_wrong, getString(R.string.try_again)))
                Status.LOADING -> {}
                Status.SUCCESS -> {
                    if (args.followers) getFollows(args.userId)
                    else getFollowing(args.userId)
                }
            }
        }
    }

}