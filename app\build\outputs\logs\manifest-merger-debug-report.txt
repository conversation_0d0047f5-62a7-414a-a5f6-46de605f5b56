-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:2:1-95:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:2:1-95:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:2:1-95:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:2:1-95:12
MERGED from [androidx.databinding:viewbinding:7.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\ba37b4e00910c03cc852b6d3ce4ef5ad\transformed\jetified-viewbinding-7.3.1\AndroidManifest.xml:2:1-9:12
MERGED from [io.insert-koin:koin-android:3.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0a270d3d09b7a32767732d54c373893a\transformed\jetified-koin-android-3.3.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.github.poovamraj:PinEditTextField:1.2.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\3cde7b7814185a493ce31876767726b3\transformed\jetified-PinEditTextField-1.2.6\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.firebase:firebase-dynamic-links-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8a1e83306d631bfa7be20217a9d58500\transformed\jetified-firebase-dynamic-links-ktx-21.1.0\AndroidManifest.xml:15:1-33:12
MERGED from [com.google.firebase:firebase-crashlytics-ktx:18.3.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\dfd474a0c38a487413af5e9cc8d67afc\transformed\jetified-firebase-crashlytics-ktx-18.3.2\AndroidManifest.xml:15:1-32:12
MERGED from [com.google.firebase:firebase-analytics-ktx:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4633587b00bb6458a88b4bd9541faf20\transformed\jetified-firebase-analytics-ktx-21.2.0\AndroidManifest.xml:2:1-17:12
MERGED from [com.google.firebase:firebase-auth-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ddbadfd7d70329eb339fcd0762c6a5a6\transformed\jetified-firebase-auth-ktx-21.1.0\AndroidManifest.xml:2:1-17:12
MERGED from [com.google.firebase:firebase-messaging-ktx:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\4533ad2b63c6d6128c8298815b20a971\transformed\jetified-firebase-messaging-ktx-23.1.1\AndroidManifest.xml:17:1-34:12
MERGED from [com.google.firebase:firebase-common-ktx:20.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\de59d1332438e2bc7040e58d2c13e54c\transformed\jetified-firebase-common-ktx-20.2.0\AndroidManifest.xml:2:1-20:12
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\122f93913edc6855b2697d126574e839\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:17:1-32:12
MERGED from [androidx.navigation:navigation-ui-ktx:2.5.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\276cfdc2714f88e956d35a1cb60b7970\transformed\navigation-ui-ktx-2.5.3\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.navigation:navigation-ui:2.5.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\664a550a52a62520ffa9dd59f274cd07\transformed\navigation-ui-2.5.3\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.material:material:1.8.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\32d2a8530f6227a47fcbb10dca01385a\transformed\material-1.8.0\AndroidManifest.xml:17:1-26:12
MERGED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:2:1-60:12
MERGED from [com.github.bruferrari:CameraKit-Android:0.10.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1f0df7c8b3a52f309b3391a1ea5030d2\transformed\jetified-CameraKit-Android-0.10.1\AndroidManifest.xml:2:1-25:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d3c9c95361c84aec381d9ad83984ce2a\transformed\jetified-appcompat-resources-1.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\e260726610cb34da9bdc212872873b11\transformed\constraintlayout-2.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat:1.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\354b5f101558a31ea117b07ac97d3a86\transformed\appcompat-1.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\068cfc6cfd939845ffece9a7aaeffb7e\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:17:1-40:12
MERGED from [com.github.bumptech.glide:glide:4.14.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\730a385321bcf11c1a3df28b66b4fdd8\transformed\jetified-glide-4.14.2\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1de81cdf9c2968d1349ad3a849fba315\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-vision:20.1.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\d1a44f49ad1d0d47179b603dda485d60\transformed\jetified-play-services-vision-20.1.3\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-vision-common:19.1.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\18102cfb1368e71d68bd9e3c8d68c785\transformed\jetified-play-services-vision-common-19.1.3\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0e863a316aaed80707f21732523ccd32\transformed\jetified-play-services-location-21.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-clearcut:17.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\657068aa693444468a5b68c6989424a9\transformed\jetified-play-services-clearcut-17.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-flags:17.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1381ff2c5725933cd3a75a905cb27975\transformed\jetified-play-services-flags-17.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-phenotype:17.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9bbb88b3cbae5cd562dcbe33ece0fd89\transformed\jetified-play-services-phenotype-17.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aab237e9eab9fdab0322490d74dca402\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:17:1-73:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\8307c34221b31441b41792d431bb0175\transformed\jetified-play-services-auth-api-phone-18.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\ace90f46b8d8893a51db3e9d11cfc7d8\transformed\jetified-play-services-auth-base-18.0.4\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-fido:19.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\eda927dd89891e099bc0d5ff43129816\transformed\jetified-play-services-fido-19.0.1\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-dynamic-links:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cacaab093d05956aa25d8a6eb172d90c\transformed\jetified-firebase-dynamic-links-21.1.0\AndroidManifest.xml:15:1-32:12
MERGED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\50f13cf14d327801350c587d0bf46851\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:17:1-61:12
MERGED from [com.google.android.gms:play-services-safetynet:17.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9f599c6a2d7fd9fa927878677129d894\transformed\jetified-play-services-safetynet-17.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cdfce347bcc0c6410f73de8b2f931e7b\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.firebase:firebase-crashlytics:18.3.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\c7c80e3f565c1b8cf2aedc222f8b8d9d\transformed\jetified-firebase-crashlytics-18.3.2\AndroidManifest.xml:2:1-23:12
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1235b2b5df9176be90e28461d33cdc7b\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-analytics:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd32e84611d6d00f7aef9e73667cd8d8\transformed\jetified-firebase-analytics-21.2.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\231488c28dff287f2e7cb7e3e1ee51ab\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:17:1-37:12
MERGED from [com.google.firebase:firebase-installations:17.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1d9d7423538685835871cb5aa6ad5398\transformed\jetified-firebase-installations-17.1.0\AndroidManifest.xml:2:1-23:12
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13c74bafdc5d245406b0a8efef9b5ab\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:15:1-33:12
MERGED from [com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\43505c5b2b7b6eb07641cdceee51b9d9\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:15:1-39:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0a85bca42c8d61facba9c3d2c391bdef\transformed\jetified-firebase-installations-interop-17.1.0\AndroidManifest.xml:15:1-22:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\b49bdb60bc03ef79ccc823c576b103d3\transformed\jetified-play-services-cloud-messaging-17.0.1\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3f3ea9365bb857b407eba8b32629050d\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\833bc5b898aba2aeab0516728f2c1dd4\transformed\jetified-play-services-tasks-18.0.2\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\91da59b5ad3228609b89208ebe21e120\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1a1d4f07b33d8783f810104ef22bf50f\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:17:1-46:12
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a5adfa83012f7b7e3bd1938ef58620fc\transformed\jetified-play-services-measurement-sdk-21.2.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-measurement-impl:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7ed85f6081beebf021a7a48aab2066c3\transformed\jetified-play-services-measurement-impl-21.2.0\AndroidManifest.xml:17:1-32:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\ec3cce55d98a8c21f96680072ca9e0ca\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\6dfdae5ee7b8086dfdf091db6a066743\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1b940cc19d8a2309aa6fdcc2f41576d7\transformed\jetified-play-services-measurement-sdk-api-21.2.0\AndroidManifest.xml:17:1-28:12
MERGED from [com.google.android.gms:play-services-measurement-base:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1e59ea33b5030e7ad85c834906711a97\transformed\jetified-play-services-measurement-base-21.2.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b138217803023ee017e5a16beaa9ca30\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.lifecycle:lifecycle-extensions:2.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f55d2b74f290905d488ed33a919a21f7\transformed\lifecycle-extensions-2.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [androidx.navigation:navigation-fragment-ktx:2.5.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\6d08609ab8bf94cdb32ba9c15ebb3784\transformed\navigation-fragment-ktx-2.5.3\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.navigation:navigation-fragment:2.5.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\5d054a5d9d40db0dcbdeebd928343e9f\transformed\navigation-fragment-2.5.3\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment-ktx:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\a9e6f63eb8e0aed47cd58b34b1254e3f\transformed\jetified-fragment-ktx-1.5.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\80e1918d29ade8e31a3c6c6ef08ae0aa\transformed\fragment-1.5.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3b9d470c4d0af13d26912524f9e7bda6\transformed\jetified-activity-1.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.5.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\594b77e3da677699275ea6c4f66f62a9\transformed\navigation-runtime-ktx-2.5.3\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.navigation:navigation-runtime:2.5.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\296e9c80ad048a44e8b162f2d976a5dc\transformed\navigation-runtime-2.5.3\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity-ktx:1.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d1878d4972647677c718954717d76c86\transformed\jetified-activity-ktx-1.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.5.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\d6cffc0566d9b4f767f7657a96cf05ae\transformed\navigation-common-ktx-2.5.3\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.navigation:navigation-common:2.5.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc768622065776318f70b85c89036ee3\transformed\navigation-common-2.5.3\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\ef0f9f3ee8eed15ac5bbb85915803514\transformed\jetified-lifecycle-viewmodel-ktx-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f319c5ec20f998a7849ab30da6a55143\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1a3c1ee3cc50dda12daf37cac84dbc97\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b519920493394b0a927cac954e563b11\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd8427f03765bba530cec71a8714806e\transformed\lifecycle-viewmodel-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0208f7f9150d75c3f9f8c109cfbe4251\transformed\jetified-lifecycle-viewmodel-savedstate-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0dfcb053a91b8e50dde4bb5b248c0e5b\transformed\jetified-lifecycle-runtime-ktx-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2e86650b5801a2105e5b7637597025b6\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0c501d704c37b63fce0fcc21c8a605d7\transformed\jetified-window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\88f1268acdc5dc55f3d7fabd1dad4d8f\transformed\jetified-lifecycle-livedata-ktx-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.android.billingclient:billing-ktx:6.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\8fd2ce73963686689756265ec0c19b02\transformed\jetified-billing-ktx-6.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\889f0c8d9e3e6f21024affc0b01fbf38\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-exoplayer:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\4676e4f2f1d99a6b2788d76ae553ee3c\transformed\jetified-media3-exoplayer-1.1.1\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.media3:media3-extractor:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\246de9a5a8a5a83bd2fbc7f311e041a5\transformed\jetified-media3-extractor-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-container:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\ae5438ff60ed92503df9fd8b539c2613\transformed\jetified-media3-container-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-datasource:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f0937dc850f7cea7820989628c6a1d74\transformed\jetified-media3-datasource-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-decoder:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dcef5f361a62dec2f53b069f8ac2d66c\transformed\jetified-media3-decoder-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-database:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\4fe4fb887a7feae2eb5df3d3dc0cae1b\transformed\jetified-media3-database-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-common:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0026ddd4934cb7793394b15c8d4b1a45\transformed\jetified-media3-common-1.1.1\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.media3:media3-ui:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\9ae1a9d39a55f693a968b3b15fc3c155\transformed\jetified-media3-ui-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\47ebb256640077a88149edf2e6f204e9\transformed\recyclerview-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-splashscreen:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9dfba2aaa159b9d5bb454e3116e31aed\transformed\jetified-core-splashscreen-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.intuit.sdp:sdp-android:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ee19f26ed639c4d8a56ec59e1a8db528\transformed\jetified-sdp-android-1.1.0\AndroidManifest.xml:2:1-11:12
MERGED from [ru.tinkoff.scrollingpagerindicator:scrollingpagerindicator:1.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\2c03a6ec8dd190a643642ea6463bf674\transformed\jetified-scrollingpagerindicator-1.2.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e090038d7f4ca21339112e8dc687487e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:17:1-56:12
MERGED from [me.grantland:autofittextview:0.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\c86535eada25a33339954ab169dc4767\transformed\jetified-autofittextview-0.2.1\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:full-sdk:4.2.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\82c4ddc6253b1248ea129c4d11d0f549\transformed\jetified-full-sdk-4.2.3\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:chat-sdk:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c60d590547afbf659e292737dc13f17d\transformed\jetified-chat-sdk-1.2.0\AndroidManifest.xml:2:1-13:12
MERGED from [io.agora.rtm:rtm-sdk:1.4.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\ab2879f44f3ba482c945ac951906de64\transformed\jetified-rtm-sdk-1.4.4\AndroidManifest.xml:2:1-11:12
MERGED from [com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\bc76bf08a15cc9c299066e5371206aa1\transformed\jetified-billing-6.0.1\AndroidManifest.xml:2:1-30:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\dacfb878dcf3ada73c635c44ab3d3981\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:15:1-37:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\a5092a4592b4dfb55a0ebcf35f9d7ea4\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ce89adf6efb3aea35e71ff31420ef698\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:15:1-23:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4423838a08c1e6ff712e5d68d9b32bbb\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a0c43417dbd8eb111e7690e9d88b867f\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\8f7f089b746b9b8b422a0b2a96cec963\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\170df353d1e5a899c6560810681b3c87\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\6e91c13c347c2fd2610c1b0e40e049d0\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7538b987a423fa186b676bef44a61e75\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media:media:1.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0ab82a10af3754d031dc1e097bfacaf7\transformed\media-1.6.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dad989dae116586b71a09e64439534d3\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4232c109d453aca88888bbd38f745b0b\transformed\jetified-core-ktx-1.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2453a466d3c7e55e34cdaba682fe2e3f\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5bb83fb57197ba70b38dc88240e8feff\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2dcc6ca2abc100d4b96ec117eccb3ec6\transformed\browser-1.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\77624dcfe0fcbd09e62b5387cc7d95b0\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fdc9cfd33c5a21669dc8cdc95e0cd352\transformed\core-1.9.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\227e04a3690a247fb01d7e3fe5ea6a87\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\2b0313006e36c3f090e112081263bb70\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:17:1-37:12
MERGED from [androidx.lifecycle:lifecycle-service:2.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\62e78e2a753104082fcfe22a3cb09688\transformed\jetified-lifecycle-service-2.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\98957cc2aede5b873dff3772b840f827\transformed\lifecycle-runtime-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ad17ef8c0895522f6b707bf8f19fb0a2\transformed\jetified-savedstate-ktx-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\37cf7bcade2efc076d701d17562ad8d0\transformed\jetified-savedstate-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7afe374c1d74e82971f9e08332ebd727\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e6ea47aa5e7b5f2c7c899e8e27b46020\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\8d5714312545606c4e00ce1cf4eeb2f8\transformed\lifecycle-livedata-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\e78ab4bdc705ecc019f74564b817aaac\transformed\jetified-lifecycle-livedata-core-ktx-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\916d9822370003865867938196eba547\transformed\lifecycle-livedata-core-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.14.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\6a0a6cbb76b7be9dd1063837d6389eaf\transformed\jetified-gifdecoder-4.14.2\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.exifinterface:exifinterface:1.3.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\293b167cbab26f9cb74a347295ace4f8\transformed\exifinterface-1.3.3\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\53e667bfd73b817246c3e7be86a6326a\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\6a8f4e17ad8b77e96242eddd93c4e3ec\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.firebase:firebase-components:17.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0c6b74b185aee1df9b409655041ee832\transformed\jetified-firebase-components-17.0.1\AndroidManifest.xml:15:1-23:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\709db1e72172f1165e7fe2f89f8b19f8\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8b1cdb93a9bbbb38bb4eb143ed6cb571\transformed\core-runtime-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.datatransport:transport-api:3.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f7ee510409d631e88d1f24a417894727\transformed\jetified-transport-api-3.0.0\AndroidManifest.xml:15:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9f872b2f66068404b2fa649f8577fdbd\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9cc6704bdb443b02a473a6c1c8dceb64\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d0e0b0ccaefe1921180f410a79014b03\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1fb94f6348c985e70e19241813e7eb25\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:2:1-95:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:2:1-95:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:2:1-95:12
	package
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:2:1-95:12
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:2:1-95:12
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:2:1-95:12
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:5:5-79
MERGED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aab237e9eab9fdab0322490d74dca402\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aab237e9eab9fdab0322490d74dca402\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\50f13cf14d327801350c587d0bf46851\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\50f13cf14d327801350c587d0bf46851\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\231488c28dff287f2e7cb7e3e1ee51ab\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\231488c28dff287f2e7cb7e3e1ee51ab\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-installations:17.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1d9d7423538685835871cb5aa6ad5398\transformed\jetified-firebase-installations-17.1.0\AndroidManifest.xml:9:5-79
MERGED from [com.google.firebase:firebase-installations:17.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1d9d7423538685835871cb5aa6ad5398\transformed\jetified-firebase-installations-17.1.0\AndroidManifest.xml:9:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\b49bdb60bc03ef79ccc823c576b103d3\transformed\jetified-play-services-cloud-messaging-17.0.1\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\b49bdb60bc03ef79ccc823c576b103d3\transformed\jetified-play-services-cloud-messaging-17.0.1\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1a1d4f07b33d8783f810104ef22bf50f\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1a1d4f07b33d8783f810104ef22bf50f\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7ed85f6081beebf021a7a48aab2066c3\transformed\jetified-play-services-measurement-impl-21.2.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7ed85f6081beebf021a7a48aab2066c3\transformed\jetified-play-services-measurement-impl-21.2.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1b940cc19d8a2309aa6fdcc2f41576d7\transformed\jetified-play-services-measurement-sdk-api-21.2.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1b940cc19d8a2309aa6fdcc2f41576d7\transformed\jetified-play-services-measurement-sdk-api-21.2.0\AndroidManifest.xml:24:5-79
MERGED from [androidx.media3:media3-exoplayer:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\4676e4f2f1d99a6b2788d76ae553ee3c\transformed\jetified-media3-exoplayer-1.1.1\AndroidManifest.xml:24:5-79
MERGED from [androidx.media3:media3-exoplayer:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\4676e4f2f1d99a6b2788d76ae553ee3c\transformed\jetified-media3-exoplayer-1.1.1\AndroidManifest.xml:24:5-79
MERGED from [androidx.media3:media3-common:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0026ddd4934cb7793394b15c8d4b1a45\transformed\jetified-media3-common-1.1.1\AndroidManifest.xml:24:5-79
MERGED from [androidx.media3:media3-common:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0026ddd4934cb7793394b15c8d4b1a45\transformed\jetified-media3-common-1.1.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\dacfb878dcf3ada73c635c44ab3d3981\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\dacfb878dcf3ada73c635c44ab3d3981\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\a5092a4592b4dfb55a0ebcf35f9d7ea4\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\a5092a4592b4dfb55a0ebcf35f9d7ea4\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:22:5-79
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:5:22-76
uses-permission#android.permission.CAMERA
ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:6:5-65
MERGED from [com.github.bruferrari:CameraKit-Android:0.10.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1f0df7c8b3a52f309b3391a1ea5030d2\transformed\jetified-CameraKit-Android-0.10.1\AndroidManifest.xml:9:5-65
MERGED from [com.github.bruferrari:CameraKit-Android:0.10.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1f0df7c8b3a52f309b3391a1ea5030d2\transformed\jetified-CameraKit-Android-0.10.1\AndroidManifest.xml:9:5-65
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e090038d7f4ca21339112e8dc687487e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:22:5-65
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e090038d7f4ca21339112e8dc687487e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:22:5-65
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:6:22-62
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:7:5-67
MERGED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aab237e9eab9fdab0322490d74dca402\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aab237e9eab9fdab0322490d74dca402\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.firebase:firebase-crashlytics:18.3.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\c7c80e3f565c1b8cf2aedc222f8b8d9d\transformed\jetified-firebase-crashlytics-18.3.2\AndroidManifest.xml:9:5-67
MERGED from [com.google.firebase:firebase-crashlytics:18.3.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\c7c80e3f565c1b8cf2aedc222f8b8d9d\transformed\jetified-firebase-crashlytics-18.3.2\AndroidManifest.xml:9:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\231488c28dff287f2e7cb7e3e1ee51ab\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\231488c28dff287f2e7cb7e3e1ee51ab\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.firebase:firebase-installations:17.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1d9d7423538685835871cb5aa6ad5398\transformed\jetified-firebase-installations-17.1.0\AndroidManifest.xml:10:5-67
MERGED from [com.google.firebase:firebase-installations:17.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1d9d7423538685835871cb5aa6ad5398\transformed\jetified-firebase-installations-17.1.0\AndroidManifest.xml:10:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\b49bdb60bc03ef79ccc823c576b103d3\transformed\jetified-play-services-cloud-messaging-17.0.1\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\b49bdb60bc03ef79ccc823c576b103d3\transformed\jetified-play-services-cloud-messaging-17.0.1\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1a1d4f07b33d8783f810104ef22bf50f\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1a1d4f07b33d8783f810104ef22bf50f\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7ed85f6081beebf021a7a48aab2066c3\transformed\jetified-play-services-measurement-impl-21.2.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7ed85f6081beebf021a7a48aab2066c3\transformed\jetified-play-services-measurement-impl-21.2.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1b940cc19d8a2309aa6fdcc2f41576d7\transformed\jetified-play-services-measurement-sdk-api-21.2.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1b940cc19d8a2309aa6fdcc2f41576d7\transformed\jetified-play-services-measurement-sdk-api-21.2.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\dacfb878dcf3ada73c635c44ab3d3981\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\dacfb878dcf3ada73c635c44ab3d3981\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:25:5-67
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:7:22-64
uses-permission#com.android.vending.BILLING
ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:8:5-67
MERGED from [com.android.billingclient:billing-ktx:6.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\8fd2ce73963686689756265ec0c19b02\transformed\jetified-billing-ktx-6.0.1\AndroidManifest.xml:9:5-67
MERGED from [com.android.billingclient:billing-ktx:6.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\8fd2ce73963686689756265ec0c19b02\transformed\jetified-billing-ktx-6.0.1\AndroidManifest.xml:9:5-67
MERGED from [com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\bc76bf08a15cc9c299066e5371206aa1\transformed\jetified-billing-6.0.1\AndroidManifest.xml:10:5-67
MERGED from [com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\bc76bf08a15cc9c299066e5371206aa1\transformed\jetified-billing-6.0.1\AndroidManifest.xml:10:5-67
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:8:22-64
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:9:5-77
MERGED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\50f13cf14d327801350c587d0bf46851\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:25:5-77
MERGED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\50f13cf14d327801350c587d0bf46851\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:25:5-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:9:22-74
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:10:5-79
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:10:22-76
uses-permission#android.permission.BLUETOOTH_ADMIN
ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:11:5-74
MERGED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:10:5-12:36
MERGED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:10:5-12:36
	android:required
		ADDED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:12:9-33
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:11:22-71
uses-permission#android.permission.READ_PHONE_STATE
ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:14:5-74
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:14:22-72
uses-permission#android.permission.RECORD_AUDIO
ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:15:5-71
MERGED from [com.github.bruferrari:CameraKit-Android:0.10.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1f0df7c8b3a52f309b3391a1ea5030d2\transformed\jetified-CameraKit-Android-0.10.1\AndroidManifest.xml:10:5-71
MERGED from [com.github.bruferrari:CameraKit-Android:0.10.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1f0df7c8b3a52f309b3391a1ea5030d2\transformed\jetified-CameraKit-Android-0.10.1\AndroidManifest.xml:10:5-71
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:15:22-68
uses-permission#android.permission.MODIFY_AUDIO_SETTINGS
ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:16:5-80
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:16:22-77
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:17:5-76
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:17:22-73
uses-permission#android.permission.BLUETOOTH_CONNECT
ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:20:5-76
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:20:22-73
uses-permission#android.permission.BLUETOOTH
ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:21:5-68
MERGED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:7:5-9:36
MERGED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:7:5-9:36
	android:required
		ADDED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:9:9-33
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:21:22-65
uses-permission#android.permission.BLUETOOTH_SCAN
ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:22:5-73
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:22:22-70
uses-permission#android.permission.BLUETOOTH_PRIVILEGED
ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:23:5-79
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:23:22-76
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:25:5-80
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:25:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:26:5-80
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:26:22-77
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:28:5-67
MERGED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\50f13cf14d327801350c587d0bf46851\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:26:5-68
MERGED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\50f13cf14d327801350c587d0bf46851\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:26:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\231488c28dff287f2e7cb7e3e1ee51ab\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\231488c28dff287f2e7cb7e3e1ee51ab\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\b49bdb60bc03ef79ccc823c576b103d3\transformed\jetified-play-services-cloud-messaging-17.0.1\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\b49bdb60bc03ef79ccc823c576b103d3\transformed\jetified-play-services-cloud-messaging-17.0.1\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1a1d4f07b33d8783f810104ef22bf50f\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1a1d4f07b33d8783f810104ef22bf50f\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7ed85f6081beebf021a7a48aab2066c3\transformed\jetified-play-services-measurement-impl-21.2.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7ed85f6081beebf021a7a48aab2066c3\transformed\jetified-play-services-measurement-impl-21.2.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1b940cc19d8a2309aa6fdcc2f41576d7\transformed\jetified-play-services-measurement-sdk-api-21.2.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1b940cc19d8a2309aa6fdcc2f41576d7\transformed\jetified-play-services-measurement-sdk-api-21.2.0\AndroidManifest.xml:25:5-68
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:28:22-65
uses-permission#android.permission.SCHEDULE_EXACT_ALARM
ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:29:5-79
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:29:22-76
uses-feature#android.hardware.camera
ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:31:5-60
MERGED from [com.github.bruferrari:CameraKit-Android:0.10.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1f0df7c8b3a52f309b3391a1ea5030d2\transformed\jetified-CameraKit-Android-0.10.1\AndroidManifest.xml:12:5-60
MERGED from [com.github.bruferrari:CameraKit-Android:0.10.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1f0df7c8b3a52f309b3391a1ea5030d2\transformed\jetified-CameraKit-Android-0.10.1\AndroidManifest.xml:12:5-60
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e090038d7f4ca21339112e8dc687487e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:25:5-27:36
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e090038d7f4ca21339112e8dc687487e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:25:5-27:36
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:31:19-57
uses-feature#android.hardware.camera.autofocus
ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:32:5-70
MERGED from [com.github.bruferrari:CameraKit-Android:0.10.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1f0df7c8b3a52f309b3391a1ea5030d2\transformed\jetified-CameraKit-Android-0.10.1\AndroidManifest.xml:13:5-15:36
MERGED from [com.github.bruferrari:CameraKit-Android:0.10.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1f0df7c8b3a52f309b3391a1ea5030d2\transformed\jetified-CameraKit-Android-0.10.1\AndroidManifest.xml:13:5-15:36
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e090038d7f4ca21339112e8dc687487e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:33:5-35:36
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e090038d7f4ca21339112e8dc687487e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:33:5-35:36
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:32:19-67
application
ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:34:5-93:19
MERGED from [io.insert-koin:koin-android:3.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0a270d3d09b7a32767732d54c373893a\transformed\jetified-koin-android-3.3.0\AndroidManifest.xml:7:5-20
MERGED from [io.insert-koin:koin-android:3.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0a270d3d09b7a32767732d54c373893a\transformed\jetified-koin-android-3.3.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-dynamic-links-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8a1e83306d631bfa7be20217a9d58500\transformed\jetified-firebase-dynamic-links-ktx-21.1.0\AndroidManifest.xml:23:5-31:19
MERGED from [com.google.firebase:firebase-dynamic-links-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8a1e83306d631bfa7be20217a9d58500\transformed\jetified-firebase-dynamic-links-ktx-21.1.0\AndroidManifest.xml:23:5-31:19
MERGED from [com.google.firebase:firebase-crashlytics-ktx:18.3.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\dfd474a0c38a487413af5e9cc8d67afc\transformed\jetified-firebase-crashlytics-ktx-18.3.2\AndroidManifest.xml:22:5-30:19
MERGED from [com.google.firebase:firebase-crashlytics-ktx:18.3.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\dfd474a0c38a487413af5e9cc8d67afc\transformed\jetified-firebase-crashlytics-ktx-18.3.2\AndroidManifest.xml:22:5-30:19
MERGED from [com.google.firebase:firebase-analytics-ktx:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4633587b00bb6458a88b4bd9541faf20\transformed\jetified-firebase-analytics-ktx-21.2.0\AndroidManifest.xml:7:5-15:19
MERGED from [com.google.firebase:firebase-analytics-ktx:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4633587b00bb6458a88b4bd9541faf20\transformed\jetified-firebase-analytics-ktx-21.2.0\AndroidManifest.xml:7:5-15:19
MERGED from [com.google.firebase:firebase-auth-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ddbadfd7d70329eb339fcd0762c6a5a6\transformed\jetified-firebase-auth-ktx-21.1.0\AndroidManifest.xml:7:5-15:19
MERGED from [com.google.firebase:firebase-auth-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ddbadfd7d70329eb339fcd0762c6a5a6\transformed\jetified-firebase-auth-ktx-21.1.0\AndroidManifest.xml:7:5-15:19
MERGED from [com.google.firebase:firebase-messaging-ktx:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\4533ad2b63c6d6128c8298815b20a971\transformed\jetified-firebase-messaging-ktx-23.1.1\AndroidManifest.xml:24:5-32:19
MERGED from [com.google.firebase:firebase-messaging-ktx:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\4533ad2b63c6d6128c8298815b20a971\transformed\jetified-firebase-messaging-ktx-23.1.1\AndroidManifest.xml:24:5-32:19
MERGED from [com.google.firebase:firebase-common-ktx:20.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\de59d1332438e2bc7040e58d2c13e54c\transformed\jetified-firebase-common-ktx-20.2.0\AndroidManifest.xml:10:5-18:19
MERGED from [com.google.firebase:firebase-common-ktx:20.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\de59d1332438e2bc7040e58d2c13e54c\transformed\jetified-firebase-common-ktx-20.2.0\AndroidManifest.xml:10:5-18:19
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\122f93913edc6855b2697d126574e839\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:26:5-30:19
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\122f93913edc6855b2697d126574e839\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:26:5-30:19
MERGED from [com.google.android.material:material:1.8.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\32d2a8530f6227a47fcbb10dca01385a\transformed\material-1.8.0\AndroidManifest.xml:24:5-20
MERGED from [com.google.android.material:material:1.8.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\32d2a8530f6227a47fcbb10dca01385a\transformed\material-1.8.0\AndroidManifest.xml:24:5-20
MERGED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:17:5-58:19
MERGED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:17:5-58:19
MERGED from [com.github.bruferrari:CameraKit-Android:0.10.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1f0df7c8b3a52f309b3391a1ea5030d2\transformed\jetified-CameraKit-Android-0.10.1\AndroidManifest.xml:23:5-20
MERGED from [com.github.bruferrari:CameraKit-Android:0.10.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1f0df7c8b3a52f309b3391a1ea5030d2\transformed\jetified-CameraKit-Android-0.10.1\AndroidManifest.xml:23:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\e260726610cb34da9bdc212872873b11\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\e260726610cb34da9bdc212872873b11\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\068cfc6cfd939845ffece9a7aaeffb7e\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\068cfc6cfd939845ffece9a7aaeffb7e\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:22:5-38:19
MERGED from [com.github.bumptech.glide:glide:4.14.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\730a385321bcf11c1a3df28b66b4fdd8\transformed\jetified-glide-4.14.2\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:glide:4.14.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\730a385321bcf11c1a3df28b66b4fdd8\transformed\jetified-glide-4.14.2\AndroidManifest.xml:9:5-20
MERGED from [com.google.android.gms:play-services-vision:20.1.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\d1a44f49ad1d0d47179b603dda485d60\transformed\jetified-play-services-vision-20.1.3\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-vision:20.1.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\d1a44f49ad1d0d47179b603dda485d60\transformed\jetified-play-services-vision-20.1.3\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-vision-common:19.1.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\18102cfb1368e71d68bd9e3c8d68c785\transformed\jetified-play-services-vision-common-19.1.3\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-vision-common:19.1.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\18102cfb1368e71d68bd9e3c8d68c785\transformed\jetified-play-services-vision-common-19.1.3\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0e863a316aaed80707f21732523ccd32\transformed\jetified-play-services-location-21.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0e863a316aaed80707f21732523ccd32\transformed\jetified-play-services-location-21.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-clearcut:17.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\657068aa693444468a5b68c6989424a9\transformed\jetified-play-services-clearcut-17.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-clearcut:17.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\657068aa693444468a5b68c6989424a9\transformed\jetified-play-services-clearcut-17.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-phenotype:17.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9bbb88b3cbae5cd562dcbe33ece0fd89\transformed\jetified-play-services-phenotype-17.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-phenotype:17.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9bbb88b3cbae5cd562dcbe33ece0fd89\transformed\jetified-play-services-phenotype-17.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aab237e9eab9fdab0322490d74dca402\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:26:5-71:19
MERGED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aab237e9eab9fdab0322490d74dca402\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:26:5-71:19
MERGED from [com.google.android.gms:play-services-fido:19.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\eda927dd89891e099bc0d5ff43129816\transformed\jetified-play-services-fido-19.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:19.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\eda927dd89891e099bc0d5ff43129816\transformed\jetified-play-services-fido-19.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-dynamic-links:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cacaab093d05956aa25d8a6eb172d90c\transformed\jetified-firebase-dynamic-links-21.1.0\AndroidManifest.xml:22:5-30:19
MERGED from [com.google.firebase:firebase-dynamic-links:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cacaab093d05956aa25d8a6eb172d90c\transformed\jetified-firebase-dynamic-links-21.1.0\AndroidManifest.xml:22:5-30:19
MERGED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\50f13cf14d327801350c587d0bf46851\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:30:5-59:19
MERGED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\50f13cf14d327801350c587d0bf46851\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:30:5-59:19
MERGED from [com.google.android.gms:play-services-safetynet:17.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9f599c6a2d7fd9fa927878677129d894\transformed\jetified-play-services-safetynet-17.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-safetynet:17.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9f599c6a2d7fd9fa927878677129d894\transformed\jetified-play-services-safetynet-17.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cdfce347bcc0c6410f73de8b2f931e7b\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cdfce347bcc0c6410f73de8b2f931e7b\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.firebase:firebase-crashlytics:18.3.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\c7c80e3f565c1b8cf2aedc222f8b8d9d\transformed\jetified-firebase-crashlytics-18.3.2\AndroidManifest.xml:13:5-21:19
MERGED from [com.google.firebase:firebase-crashlytics:18.3.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\c7c80e3f565c1b8cf2aedc222f8b8d9d\transformed\jetified-firebase-crashlytics-18.3.2\AndroidManifest.xml:13:5-21:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1235b2b5df9176be90e28461d33cdc7b\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1235b2b5df9176be90e28461d33cdc7b\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-analytics:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd32e84611d6d00f7aef9e73667cd8d8\transformed\jetified-firebase-analytics-21.2.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd32e84611d6d00f7aef9e73667cd8d8\transformed\jetified-firebase-analytics-21.2.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\231488c28dff287f2e7cb7e3e1ee51ab\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\231488c28dff287f2e7cb7e3e1ee51ab\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.firebase:firebase-installations:17.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1d9d7423538685835871cb5aa6ad5398\transformed\jetified-firebase-installations-17.1.0\AndroidManifest.xml:13:5-21:19
MERGED from [com.google.firebase:firebase-installations:17.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1d9d7423538685835871cb5aa6ad5398\transformed\jetified-firebase-installations-17.1.0\AndroidManifest.xml:13:5-21:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13c74bafdc5d245406b0a8efef9b5ab\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:23:5-31:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13c74bafdc5d245406b0a8efef9b5ab\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:23:5-31:19
MERGED from [com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\43505c5b2b7b6eb07641cdceee51b9d9\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:24:5-37:19
MERGED from [com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\43505c5b2b7b6eb07641cdceee51b9d9\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:24:5-37:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3f3ea9365bb857b407eba8b32629050d\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3f3ea9365bb857b407eba8b32629050d\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\833bc5b898aba2aeab0516728f2c1dd4\transformed\jetified-play-services-tasks-18.0.2\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\833bc5b898aba2aeab0516728f2c1dd4\transformed\jetified-play-services-tasks-18.0.2\AndroidManifest.xml:5:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\91da59b5ad3228609b89208ebe21e120\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\91da59b5ad3228609b89208ebe21e120\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1a1d4f07b33d8783f810104ef22bf50f\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1a1d4f07b33d8783f810104ef22bf50f\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a5adfa83012f7b7e3bd1938ef58620fc\transformed\jetified-play-services-measurement-sdk-21.2.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a5adfa83012f7b7e3bd1938ef58620fc\transformed\jetified-play-services-measurement-sdk-21.2.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-impl:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7ed85f6081beebf021a7a48aab2066c3\transformed\jetified-play-services-measurement-impl-21.2.0\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-measurement-impl:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7ed85f6081beebf021a7a48aab2066c3\transformed\jetified-play-services-measurement-impl-21.2.0\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\ec3cce55d98a8c21f96680072ca9e0ca\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\ec3cce55d98a8c21f96680072ca9e0ca\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\6dfdae5ee7b8086dfdf091db6a066743\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\6dfdae5ee7b8086dfdf091db6a066743\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1e59ea33b5030e7ad85c834906711a97\transformed\jetified-play-services-measurement-base-21.2.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1e59ea33b5030e7ad85c834906711a97\transformed\jetified-play-services-measurement-base-21.2.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b138217803023ee017e5a16beaa9ca30\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b138217803023ee017e5a16beaa9ca30\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.lifecycle:lifecycle-extensions:2.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f55d2b74f290905d488ed33a919a21f7\transformed\lifecycle-extensions-2.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [androidx.lifecycle:lifecycle-extensions:2.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f55d2b74f290905d488ed33a919a21f7\transformed\lifecycle-extensions-2.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0c501d704c37b63fce0fcc21c8a605d7\transformed\jetified-window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0c501d704c37b63fce0fcc21c8a605d7\transformed\jetified-window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e090038d7f4ca21339112e8dc687487e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:46:5-54:19
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e090038d7f4ca21339112e8dc687487e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:46:5-54:19
MERGED from [com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\bc76bf08a15cc9c299066e5371206aa1\transformed\jetified-billing-6.0.1\AndroidManifest.xml:18:5-28:19
MERGED from [com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\bc76bf08a15cc9c299066e5371206aa1\transformed\jetified-billing-6.0.1\AndroidManifest.xml:18:5-28:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\dacfb878dcf3ada73c635c44ab3d3981\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\dacfb878dcf3ada73c635c44ab3d3981\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\a5092a4592b4dfb55a0ebcf35f9d7ea4\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\a5092a4592b4dfb55a0ebcf35f9d7ea4\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:25:5-39:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a0c43417dbd8eb111e7690e9d88b867f\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a0c43417dbd8eb111e7690e9d88b867f\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fdc9cfd33c5a21669dc8cdc95e0cd352\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fdc9cfd33c5a21669dc8cdc95e0cd352\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\2b0313006e36c3f090e112081263bb70\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\2b0313006e36c3f090e112081263bb70\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:25:5-35:19
MERGED from [com.github.bumptech.glide:gifdecoder:4.14.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\6a0a6cbb76b7be9dd1063837d6389eaf\transformed\jetified-gifdecoder-4.14.2\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:gifdecoder:4.14.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\6a0a6cbb76b7be9dd1063837d6389eaf\transformed\jetified-gifdecoder-4.14.2\AndroidManifest.xml:9:5-20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\53e667bfd73b817246c3e7be86a6326a\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\53e667bfd73b817246c3e7be86a6326a\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\709db1e72172f1165e7fe2f89f8b19f8\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\709db1e72172f1165e7fe2f89f8b19f8\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:appComponentFactory
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fdc9cfd33c5a21669dc8cdc95e0cd352\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:43:9-35
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:40:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:38:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:42:9-51
	tools:targetApi
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:45:9-28
	android:largeHeap
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:41:9-33
	android:icon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:39:9-40
	android:allowBackup
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:36:9-35
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:44:9-40
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:37:9-65
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:35:9-28
activity#com.flashbid.luv.MainActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:47:9-74:20
	android:screenOrientation
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:50:13-49
	android:launchMode
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:52:13-44
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:49:13-36
	tools:ignore
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:51:13-53
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:48:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:53:13-57:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:54:17-69
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:54:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:56:17-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:56:27-74
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:luvapp.page.link+data:scheme:https
ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:59:13-68:29
action#android.intent.action.VIEW
ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:60:17-69
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:60:25-66
category#android.intent.category.DEFAULT
ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:62:17-76
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:62:27-73
category#android.intent.category.BROWSABLE
ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:63:17-78
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:63:27-75
data
ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:65:17-67:46
	android:host
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:66:21-52
	android:scheme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:67:21-43
meta-data#android.app.lib_name
ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:70:13-72:36
	android:value
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:72:17-33
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:71:17-52
service#com.flashbid.luv.fcm.MyFirebasePushNotifications
ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:76:9-82:19
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:78:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:77:13-60
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:79:13-81:29
action#com.google.firebase.MESSAGING_EVENT
ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:80:17-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:80:25-75
service#com.flashbid.luv.util.BeaconService
ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:85:9-87:39
	android:enabled
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:86:13-35
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:87:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml:85:18-52
uses-sdk
INJECTED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:7.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\ba37b4e00910c03cc852b6d3ce4ef5ad\transformed\jetified-viewbinding-7.3.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.databinding:viewbinding:7.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\ba37b4e00910c03cc852b6d3ce4ef5ad\transformed\jetified-viewbinding-7.3.1\AndroidManifest.xml:5:5-7:41
MERGED from [io.insert-koin:koin-android:3.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0a270d3d09b7a32767732d54c373893a\transformed\jetified-koin-android-3.3.0\AndroidManifest.xml:5:5-44
MERGED from [io.insert-koin:koin-android:3.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0a270d3d09b7a32767732d54c373893a\transformed\jetified-koin-android-3.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.github.poovamraj:PinEditTextField:1.2.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\3cde7b7814185a493ce31876767726b3\transformed\jetified-PinEditTextField-1.2.6\AndroidManifest.xml:7:5-9:41
MERGED from [com.github.poovamraj:PinEditTextField:1.2.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\3cde7b7814185a493ce31876767726b3\transformed\jetified-PinEditTextField-1.2.6\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.firebase:firebase-dynamic-links-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8a1e83306d631bfa7be20217a9d58500\transformed\jetified-firebase-dynamic-links-ktx-21.1.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-dynamic-links-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8a1e83306d631bfa7be20217a9d58500\transformed\jetified-firebase-dynamic-links-ktx-21.1.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-crashlytics-ktx:18.3.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\dfd474a0c38a487413af5e9cc8d67afc\transformed\jetified-firebase-crashlytics-ktx-18.3.2\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-crashlytics-ktx:18.3.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\dfd474a0c38a487413af5e9cc8d67afc\transformed\jetified-firebase-crashlytics-ktx-18.3.2\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-analytics-ktx:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4633587b00bb6458a88b4bd9541faf20\transformed\jetified-firebase-analytics-ktx-21.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics-ktx:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4633587b00bb6458a88b4bd9541faf20\transformed\jetified-firebase-analytics-ktx-21.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ddbadfd7d70329eb339fcd0762c6a5a6\transformed\jetified-firebase-auth-ktx-21.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ddbadfd7d70329eb339fcd0762c6a5a6\transformed\jetified-firebase-auth-ktx-21.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-messaging-ktx:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\4533ad2b63c6d6128c8298815b20a971\transformed\jetified-firebase-messaging-ktx-23.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-messaging-ktx:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\4533ad2b63c6d6128c8298815b20a971\transformed\jetified-firebase-messaging-ktx-23.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-common-ktx:20.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\de59d1332438e2bc7040e58d2c13e54c\transformed\jetified-firebase-common-ktx-20.2.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-common-ktx:20.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\de59d1332438e2bc7040e58d2c13e54c\transformed\jetified-firebase-common-ktx-20.2.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\122f93913edc6855b2697d126574e839\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:22:5-24:41
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\122f93913edc6855b2697d126574e839\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:22:5-24:41
MERGED from [androidx.navigation:navigation-ui-ktx:2.5.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\276cfdc2714f88e956d35a1cb60b7970\transformed\navigation-ui-ktx-2.5.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-ui-ktx:2.5.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\276cfdc2714f88e956d35a1cb60b7970\transformed\navigation-ui-ktx-2.5.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-ui:2.5.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\664a550a52a62520ffa9dd59f274cd07\transformed\navigation-ui-2.5.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-ui:2.5.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\664a550a52a62520ffa9dd59f274cd07\transformed\navigation-ui-2.5.3\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.material:material:1.8.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\32d2a8530f6227a47fcbb10dca01385a\transformed\material-1.8.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.material:material:1.8.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\32d2a8530f6227a47fcbb10dca01385a\transformed\material-1.8.0\AndroidManifest.xml:20:5-22:41
MERGED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:5:5-44
MERGED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:5:5-44
MERGED from [com.github.bruferrari:CameraKit-Android:0.10.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1f0df7c8b3a52f309b3391a1ea5030d2\transformed\jetified-CameraKit-Android-0.10.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bruferrari:CameraKit-Android:0.10.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1f0df7c8b3a52f309b3391a1ea5030d2\transformed\jetified-CameraKit-Android-0.10.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d3c9c95361c84aec381d9ad83984ce2a\transformed\jetified-appcompat-resources-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d3c9c95361c84aec381d9ad83984ce2a\transformed\jetified-appcompat-resources-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\e260726610cb34da9bdc212872873b11\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\e260726610cb34da9bdc212872873b11\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat:1.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\354b5f101558a31ea117b07ac97d3a86\transformed\appcompat-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\354b5f101558a31ea117b07ac97d3a86\transformed\appcompat-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\068cfc6cfd939845ffece9a7aaeffb7e\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\068cfc6cfd939845ffece9a7aaeffb7e\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:glide:4.14.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\730a385321bcf11c1a3df28b66b4fdd8\transformed\jetified-glide-4.14.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.14.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\730a385321bcf11c1a3df28b66b4fdd8\transformed\jetified-glide-4.14.2\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1de81cdf9c2968d1349ad3a849fba315\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1de81cdf9c2968d1349ad3a849fba315\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.gms:play-services-vision:20.1.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\d1a44f49ad1d0d47179b603dda485d60\transformed\jetified-play-services-vision-20.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-vision:20.1.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\d1a44f49ad1d0d47179b603dda485d60\transformed\jetified-play-services-vision-20.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-vision-common:19.1.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\18102cfb1368e71d68bd9e3c8d68c785\transformed\jetified-play-services-vision-common-19.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-vision-common:19.1.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\18102cfb1368e71d68bd9e3c8d68c785\transformed\jetified-play-services-vision-common-19.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0e863a316aaed80707f21732523ccd32\transformed\jetified-play-services-location-21.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0e863a316aaed80707f21732523ccd32\transformed\jetified-play-services-location-21.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-clearcut:17.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\657068aa693444468a5b68c6989424a9\transformed\jetified-play-services-clearcut-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-clearcut:17.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\657068aa693444468a5b68c6989424a9\transformed\jetified-play-services-clearcut-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-flags:17.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1381ff2c5725933cd3a75a905cb27975\transformed\jetified-play-services-flags-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-flags:17.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1381ff2c5725933cd3a75a905cb27975\transformed\jetified-play-services-flags-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-phenotype:17.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9bbb88b3cbae5cd562dcbe33ece0fd89\transformed\jetified-play-services-phenotype-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-phenotype:17.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9bbb88b3cbae5cd562dcbe33ece0fd89\transformed\jetified-play-services-phenotype-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aab237e9eab9fdab0322490d74dca402\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aab237e9eab9fdab0322490d74dca402\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\8307c34221b31441b41792d431bb0175\transformed\jetified-play-services-auth-api-phone-18.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\8307c34221b31441b41792d431bb0175\transformed\jetified-play-services-auth-api-phone-18.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\ace90f46b8d8893a51db3e9d11cfc7d8\transformed\jetified-play-services-auth-base-18.0.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\ace90f46b8d8893a51db3e9d11cfc7d8\transformed\jetified-play-services-auth-base-18.0.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:19.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\eda927dd89891e099bc0d5ff43129816\transformed\jetified-play-services-fido-19.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:19.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\eda927dd89891e099bc0d5ff43129816\transformed\jetified-play-services-fido-19.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-dynamic-links:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cacaab093d05956aa25d8a6eb172d90c\transformed\jetified-firebase-dynamic-links-21.1.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-dynamic-links:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cacaab093d05956aa25d8a6eb172d90c\transformed\jetified-firebase-dynamic-links-21.1.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\50f13cf14d327801350c587d0bf46851\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\50f13cf14d327801350c587d0bf46851\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.gms:play-services-safetynet:17.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9f599c6a2d7fd9fa927878677129d894\transformed\jetified-play-services-safetynet-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-safetynet:17.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9f599c6a2d7fd9fa927878677129d894\transformed\jetified-play-services-safetynet-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cdfce347bcc0c6410f73de8b2f931e7b\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cdfce347bcc0c6410f73de8b2f931e7b\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.firebase:firebase-crashlytics:18.3.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\c7c80e3f565c1b8cf2aedc222f8b8d9d\transformed\jetified-firebase-crashlytics-18.3.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-crashlytics:18.3.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\c7c80e3f565c1b8cf2aedc222f8b8d9d\transformed\jetified-firebase-crashlytics-18.3.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1235b2b5df9176be90e28461d33cdc7b\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1235b2b5df9176be90e28461d33cdc7b\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd32e84611d6d00f7aef9e73667cd8d8\transformed\jetified-firebase-analytics-21.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd32e84611d6d00f7aef9e73667cd8d8\transformed\jetified-firebase-analytics-21.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\231488c28dff287f2e7cb7e3e1ee51ab\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\231488c28dff287f2e7cb7e3e1ee51ab\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-installations:17.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1d9d7423538685835871cb5aa6ad5398\transformed\jetified-firebase-installations-17.1.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-installations:17.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1d9d7423538685835871cb5aa6ad5398\transformed\jetified-firebase-installations-17.1.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13c74bafdc5d245406b0a8efef9b5ab\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13c74bafdc5d245406b0a8efef9b5ab\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\43505c5b2b7b6eb07641cdceee51b9d9\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\43505c5b2b7b6eb07641cdceee51b9d9\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-installations-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0a85bca42c8d61facba9c3d2c391bdef\transformed\jetified-firebase-installations-interop-17.1.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-installations-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0a85bca42c8d61facba9c3d2c391bdef\transformed\jetified-firebase-installations-interop-17.1.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\b49bdb60bc03ef79ccc823c576b103d3\transformed\jetified-play-services-cloud-messaging-17.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\b49bdb60bc03ef79ccc823c576b103d3\transformed\jetified-play-services-cloud-messaging-17.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3f3ea9365bb857b407eba8b32629050d\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3f3ea9365bb857b407eba8b32629050d\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\833bc5b898aba2aeab0516728f2c1dd4\transformed\jetified-play-services-tasks-18.0.2\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\833bc5b898aba2aeab0516728f2c1dd4\transformed\jetified-play-services-tasks-18.0.2\AndroidManifest.xml:4:5-43
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\91da59b5ad3228609b89208ebe21e120\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\91da59b5ad3228609b89208ebe21e120\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1a1d4f07b33d8783f810104ef22bf50f\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1a1d4f07b33d8783f810104ef22bf50f\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a5adfa83012f7b7e3bd1938ef58620fc\transformed\jetified-play-services-measurement-sdk-21.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a5adfa83012f7b7e3bd1938ef58620fc\transformed\jetified-play-services-measurement-sdk-21.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7ed85f6081beebf021a7a48aab2066c3\transformed\jetified-play-services-measurement-impl-21.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7ed85f6081beebf021a7a48aab2066c3\transformed\jetified-play-services-measurement-impl-21.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\ec3cce55d98a8c21f96680072ca9e0ca\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\ec3cce55d98a8c21f96680072ca9e0ca\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\6dfdae5ee7b8086dfdf091db6a066743\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\6dfdae5ee7b8086dfdf091db6a066743\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1b940cc19d8a2309aa6fdcc2f41576d7\transformed\jetified-play-services-measurement-sdk-api-21.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1b940cc19d8a2309aa6fdcc2f41576d7\transformed\jetified-play-services-measurement-sdk-api-21.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1e59ea33b5030e7ad85c834906711a97\transformed\jetified-play-services-measurement-base-21.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1e59ea33b5030e7ad85c834906711a97\transformed\jetified-play-services-measurement-base-21.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b138217803023ee017e5a16beaa9ca30\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b138217803023ee017e5a16beaa9ca30\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.lifecycle:lifecycle-extensions:2.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f55d2b74f290905d488ed33a919a21f7\transformed\lifecycle-extensions-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-extensions:2.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f55d2b74f290905d488ed33a919a21f7\transformed\lifecycle-extensions-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.5.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\6d08609ab8bf94cdb32ba9c15ebb3784\transformed\navigation-fragment-ktx-2.5.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-fragment-ktx:2.5.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\6d08609ab8bf94cdb32ba9c15ebb3784\transformed\navigation-fragment-ktx-2.5.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-fragment:2.5.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\5d054a5d9d40db0dcbdeebd928343e9f\transformed\navigation-fragment-2.5.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-fragment:2.5.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\5d054a5d9d40db0dcbdeebd928343e9f\transformed\navigation-fragment-2.5.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment-ktx:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\a9e6f63eb8e0aed47cd58b34b1254e3f\transformed\jetified-fragment-ktx-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment-ktx:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\a9e6f63eb8e0aed47cd58b34b1254e3f\transformed\jetified-fragment-ktx-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\80e1918d29ade8e31a3c6c6ef08ae0aa\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\80e1918d29ade8e31a3c6c6ef08ae0aa\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3b9d470c4d0af13d26912524f9e7bda6\transformed\jetified-activity-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3b9d470c4d0af13d26912524f9e7bda6\transformed\jetified-activity-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.5.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\594b77e3da677699275ea6c4f66f62a9\transformed\navigation-runtime-ktx-2.5.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-runtime-ktx:2.5.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\594b77e3da677699275ea6c4f66f62a9\transformed\navigation-runtime-ktx-2.5.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-runtime:2.5.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\296e9c80ad048a44e8b162f2d976a5dc\transformed\navigation-runtime-2.5.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-runtime:2.5.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\296e9c80ad048a44e8b162f2d976a5dc\transformed\navigation-runtime-2.5.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity-ktx:1.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d1878d4972647677c718954717d76c86\transformed\jetified-activity-ktx-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d1878d4972647677c718954717d76c86\transformed\jetified-activity-ktx-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.5.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\d6cffc0566d9b4f767f7657a96cf05ae\transformed\navigation-common-ktx-2.5.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-common-ktx:2.5.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\d6cffc0566d9b4f767f7657a96cf05ae\transformed\navigation-common-ktx-2.5.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-common:2.5.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc768622065776318f70b85c89036ee3\transformed\navigation-common-2.5.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-common:2.5.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc768622065776318f70b85c89036ee3\transformed\navigation-common-2.5.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\ef0f9f3ee8eed15ac5bbb85915803514\transformed\jetified-lifecycle-viewmodel-ktx-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\ef0f9f3ee8eed15ac5bbb85915803514\transformed\jetified-lifecycle-viewmodel-ktx-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f319c5ec20f998a7849ab30da6a55143\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f319c5ec20f998a7849ab30da6a55143\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1a3c1ee3cc50dda12daf37cac84dbc97\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1a3c1ee3cc50dda12daf37cac84dbc97\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b519920493394b0a927cac954e563b11\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b519920493394b0a927cac954e563b11\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd8427f03765bba530cec71a8714806e\transformed\lifecycle-viewmodel-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd8427f03765bba530cec71a8714806e\transformed\lifecycle-viewmodel-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0208f7f9150d75c3f9f8c109cfbe4251\transformed\jetified-lifecycle-viewmodel-savedstate-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0208f7f9150d75c3f9f8c109cfbe4251\transformed\jetified-lifecycle-viewmodel-savedstate-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0dfcb053a91b8e50dde4bb5b248c0e5b\transformed\jetified-lifecycle-runtime-ktx-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0dfcb053a91b8e50dde4bb5b248c0e5b\transformed\jetified-lifecycle-runtime-ktx-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2e86650b5801a2105e5b7637597025b6\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2e86650b5801a2105e5b7637597025b6\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0c501d704c37b63fce0fcc21c8a605d7\transformed\jetified-window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0c501d704c37b63fce0fcc21c8a605d7\transformed\jetified-window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\88f1268acdc5dc55f3d7fabd1dad4d8f\transformed\jetified-lifecycle-livedata-ktx-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\88f1268acdc5dc55f3d7fabd1dad4d8f\transformed\jetified-lifecycle-livedata-ktx-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.android.billingclient:billing-ktx:6.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\8fd2ce73963686689756265ec0c19b02\transformed\jetified-billing-ktx-6.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.android.billingclient:billing-ktx:6.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\8fd2ce73963686689756265ec0c19b02\transformed\jetified-billing-ktx-6.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\889f0c8d9e3e6f21024affc0b01fbf38\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\889f0c8d9e3e6f21024affc0b01fbf38\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media3:media3-exoplayer:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\4676e4f2f1d99a6b2788d76ae553ee3c\transformed\jetified-media3-exoplayer-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media3:media3-exoplayer:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\4676e4f2f1d99a6b2788d76ae553ee3c\transformed\jetified-media3-exoplayer-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media3:media3-extractor:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\246de9a5a8a5a83bd2fbc7f311e041a5\transformed\jetified-media3-extractor-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media3:media3-extractor:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\246de9a5a8a5a83bd2fbc7f311e041a5\transformed\jetified-media3-extractor-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media3:media3-container:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\ae5438ff60ed92503df9fd8b539c2613\transformed\jetified-media3-container-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media3:media3-container:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\ae5438ff60ed92503df9fd8b539c2613\transformed\jetified-media3-container-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media3:media3-datasource:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f0937dc850f7cea7820989628c6a1d74\transformed\jetified-media3-datasource-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media3:media3-datasource:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f0937dc850f7cea7820989628c6a1d74\transformed\jetified-media3-datasource-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media3:media3-decoder:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dcef5f361a62dec2f53b069f8ac2d66c\transformed\jetified-media3-decoder-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media3:media3-decoder:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dcef5f361a62dec2f53b069f8ac2d66c\transformed\jetified-media3-decoder-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media3:media3-database:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\4fe4fb887a7feae2eb5df3d3dc0cae1b\transformed\jetified-media3-database-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media3:media3-database:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\4fe4fb887a7feae2eb5df3d3dc0cae1b\transformed\jetified-media3-database-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media3:media3-common:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0026ddd4934cb7793394b15c8d4b1a45\transformed\jetified-media3-common-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media3:media3-common:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0026ddd4934cb7793394b15c8d4b1a45\transformed\jetified-media3-common-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media3:media3-ui:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\9ae1a9d39a55f693a968b3b15fc3c155\transformed\jetified-media3-ui-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media3:media3-ui:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\9ae1a9d39a55f693a968b3b15fc3c155\transformed\jetified-media3-ui-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\47ebb256640077a88149edf2e6f204e9\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\47ebb256640077a88149edf2e6f204e9\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-splashscreen:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9dfba2aaa159b9d5bb454e3116e31aed\transformed\jetified-core-splashscreen-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-splashscreen:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9dfba2aaa159b9d5bb454e3116e31aed\transformed\jetified-core-splashscreen-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.intuit.sdp:sdp-android:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ee19f26ed639c4d8a56ec59e1a8db528\transformed\jetified-sdp-android-1.1.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.intuit.sdp:sdp-android:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ee19f26ed639c4d8a56ec59e1a8db528\transformed\jetified-sdp-android-1.1.0\AndroidManifest.xml:7:5-9:41
MERGED from [ru.tinkoff.scrollingpagerindicator:scrollingpagerindicator:1.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\2c03a6ec8dd190a643642ea6463bf674\transformed\jetified-scrollingpagerindicator-1.2.1\AndroidManifest.xml:5:5-7:41
MERGED from [ru.tinkoff.scrollingpagerindicator:scrollingpagerindicator:1.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\2c03a6ec8dd190a643642ea6463bf674\transformed\jetified-scrollingpagerindicator-1.2.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e090038d7f4ca21339112e8dc687487e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e090038d7f4ca21339112e8dc687487e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:20:5-44
MERGED from [me.grantland:autofittextview:0.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\c86535eada25a33339954ab169dc4767\transformed\jetified-autofittextview-0.2.1\AndroidManifest.xml:7:5-9:41
MERGED from [me.grantland:autofittextview:0.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\c86535eada25a33339954ab169dc4767\transformed\jetified-autofittextview-0.2.1\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-sdk:4.2.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\82c4ddc6253b1248ea129c4d11d0f549\transformed\jetified-full-sdk-4.2.3\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-sdk:4.2.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\82c4ddc6253b1248ea129c4d11d0f549\transformed\jetified-full-sdk-4.2.3\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:chat-sdk:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c60d590547afbf659e292737dc13f17d\transformed\jetified-chat-sdk-1.2.0\AndroidManifest.xml:8:5-11:39
MERGED from [io.agora.rtc:chat-sdk:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c60d590547afbf659e292737dc13f17d\transformed\jetified-chat-sdk-1.2.0\AndroidManifest.xml:8:5-11:39
MERGED from [io.agora.rtm:rtm-sdk:1.4.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\ab2879f44f3ba482c945ac951906de64\transformed\jetified-rtm-sdk-1.4.4\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtm:rtm-sdk:1.4.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\ab2879f44f3ba482c945ac951906de64\transformed\jetified-rtm-sdk-1.4.4\AndroidManifest.xml:7:5-9:41
MERGED from [com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\bc76bf08a15cc9c299066e5371206aa1\transformed\jetified-billing-6.0.1\AndroidManifest.xml:6:5-8:41
MERGED from [com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\bc76bf08a15cc9c299066e5371206aa1\transformed\jetified-billing-6.0.1\AndroidManifest.xml:6:5-8:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\dacfb878dcf3ada73c635c44ab3d3981\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\dacfb878dcf3ada73c635c44ab3d3981\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\a5092a4592b4dfb55a0ebcf35f9d7ea4\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\a5092a4592b4dfb55a0ebcf35f9d7ea4\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ce89adf6efb3aea35e71ff31420ef698\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ce89adf6efb3aea35e71ff31420ef698\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4423838a08c1e6ff712e5d68d9b32bbb\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4423838a08c1e6ff712e5d68d9b32bbb\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a0c43417dbd8eb111e7690e9d88b867f\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a0c43417dbd8eb111e7690e9d88b867f\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\8f7f089b746b9b8b422a0b2a96cec963\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\8f7f089b746b9b8b422a0b2a96cec963\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\170df353d1e5a899c6560810681b3c87\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\170df353d1e5a899c6560810681b3c87\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\6e91c13c347c2fd2610c1b0e40e049d0\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\6e91c13c347c2fd2610c1b0e40e049d0\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7538b987a423fa186b676bef44a61e75\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7538b987a423fa186b676bef44a61e75\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0ab82a10af3754d031dc1e097bfacaf7\transformed\media-1.6.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0ab82a10af3754d031dc1e097bfacaf7\transformed\media-1.6.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dad989dae116586b71a09e64439534d3\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dad989dae116586b71a09e64439534d3\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4232c109d453aca88888bbd38f745b0b\transformed\jetified-core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4232c109d453aca88888bbd38f745b0b\transformed\jetified-core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2453a466d3c7e55e34cdaba682fe2e3f\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2453a466d3c7e55e34cdaba682fe2e3f\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5bb83fb57197ba70b38dc88240e8feff\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5bb83fb57197ba70b38dc88240e8feff\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2dcc6ca2abc100d4b96ec117eccb3ec6\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2dcc6ca2abc100d4b96ec117eccb3ec6\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\77624dcfe0fcbd09e62b5387cc7d95b0\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\77624dcfe0fcbd09e62b5387cc7d95b0\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fdc9cfd33c5a21669dc8cdc95e0cd352\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fdc9cfd33c5a21669dc8cdc95e0cd352\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\227e04a3690a247fb01d7e3fe5ea6a87\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\227e04a3690a247fb01d7e3fe5ea6a87\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\2b0313006e36c3f090e112081263bb70\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\2b0313006e36c3f090e112081263bb70\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-service:2.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\62e78e2a753104082fcfe22a3cb09688\transformed\jetified-lifecycle-service-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\62e78e2a753104082fcfe22a3cb09688\transformed\jetified-lifecycle-service-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\98957cc2aede5b873dff3772b840f827\transformed\lifecycle-runtime-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\98957cc2aede5b873dff3772b840f827\transformed\lifecycle-runtime-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ad17ef8c0895522f6b707bf8f19fb0a2\transformed\jetified-savedstate-ktx-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ad17ef8c0895522f6b707bf8f19fb0a2\transformed\jetified-savedstate-ktx-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\37cf7bcade2efc076d701d17562ad8d0\transformed\jetified-savedstate-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\37cf7bcade2efc076d701d17562ad8d0\transformed\jetified-savedstate-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7afe374c1d74e82971f9e08332ebd727\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7afe374c1d74e82971f9e08332ebd727\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e6ea47aa5e7b5f2c7c899e8e27b46020\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e6ea47aa5e7b5f2c7c899e8e27b46020\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\8d5714312545606c4e00ce1cf4eeb2f8\transformed\lifecycle-livedata-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata:2.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\8d5714312545606c4e00ce1cf4eeb2f8\transformed\lifecycle-livedata-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\e78ab4bdc705ecc019f74564b817aaac\transformed\jetified-lifecycle-livedata-core-ktx-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\e78ab4bdc705ecc019f74564b817aaac\transformed\jetified-lifecycle-livedata-core-ktx-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\916d9822370003865867938196eba547\transformed\lifecycle-livedata-core-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\916d9822370003865867938196eba547\transformed\lifecycle-livedata-core-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.14.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\6a0a6cbb76b7be9dd1063837d6389eaf\transformed\jetified-gifdecoder-4.14.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.14.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\6a0a6cbb76b7be9dd1063837d6389eaf\transformed\jetified-gifdecoder-4.14.2\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.3.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\293b167cbab26f9cb74a347295ace4f8\transformed\exifinterface-1.3.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\293b167cbab26f9cb74a347295ace4f8\transformed\exifinterface-1.3.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\53e667bfd73b817246c3e7be86a6326a\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\53e667bfd73b817246c3e7be86a6326a\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\6a8f4e17ad8b77e96242eddd93c4e3ec\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\6a8f4e17ad8b77e96242eddd93c4e3ec\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-components:17.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0c6b74b185aee1df9b409655041ee832\transformed\jetified-firebase-components-17.0.1\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-components:17.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0c6b74b185aee1df9b409655041ee832\transformed\jetified-firebase-components-17.0.1\AndroidManifest.xml:19:5-21:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\709db1e72172f1165e7fe2f89f8b19f8\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\709db1e72172f1165e7fe2f89f8b19f8\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8b1cdb93a9bbbb38bb4eb143ed6cb571\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8b1cdb93a9bbbb38bb4eb143ed6cb571\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.datatransport:transport-api:3.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f7ee510409d631e88d1f24a417894727\transformed\jetified-transport-api-3.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f7ee510409d631e88d1f24a417894727\transformed\jetified-transport-api-3.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9f872b2f66068404b2fa649f8577fdbd\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9f872b2f66068404b2fa649f8577fdbd\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9cc6704bdb443b02a473a6c1c8dceb64\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9cc6704bdb443b02a473a6c1c8dceb64\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d0e0b0ccaefe1921180f410a79014b03\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d0e0b0ccaefe1921180f410a79014b03\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1fb94f6348c985e70e19241813e7eb25\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1fb94f6348c985e70e19241813e7eb25\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
INJECTED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml
	tools:ignore
		ADDED from [io.agora.rtc:chat-sdk:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c60d590547afbf659e292737dc13f17d\transformed\jetified-chat-sdk-1.2.0\AndroidManifest.xml:11:9-36
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\luv_android\app\src\main\AndroidManifest.xml
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-dynamic-links-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8a1e83306d631bfa7be20217a9d58500\transformed\jetified-firebase-dynamic-links-ktx-21.1.0\AndroidManifest.xml:24:9-30:19
MERGED from [com.google.firebase:firebase-crashlytics-ktx:18.3.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\dfd474a0c38a487413af5e9cc8d67afc\transformed\jetified-firebase-crashlytics-ktx-18.3.2\AndroidManifest.xml:23:9-29:19
MERGED from [com.google.firebase:firebase-crashlytics-ktx:18.3.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\dfd474a0c38a487413af5e9cc8d67afc\transformed\jetified-firebase-crashlytics-ktx-18.3.2\AndroidManifest.xml:23:9-29:19
MERGED from [com.google.firebase:firebase-analytics-ktx:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4633587b00bb6458a88b4bd9541faf20\transformed\jetified-firebase-analytics-ktx-21.2.0\AndroidManifest.xml:8:9-14:19
MERGED from [com.google.firebase:firebase-analytics-ktx:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4633587b00bb6458a88b4bd9541faf20\transformed\jetified-firebase-analytics-ktx-21.2.0\AndroidManifest.xml:8:9-14:19
MERGED from [com.google.firebase:firebase-auth-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ddbadfd7d70329eb339fcd0762c6a5a6\transformed\jetified-firebase-auth-ktx-21.1.0\AndroidManifest.xml:8:9-14:19
MERGED from [com.google.firebase:firebase-auth-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ddbadfd7d70329eb339fcd0762c6a5a6\transformed\jetified-firebase-auth-ktx-21.1.0\AndroidManifest.xml:8:9-14:19
MERGED from [com.google.firebase:firebase-messaging-ktx:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\4533ad2b63c6d6128c8298815b20a971\transformed\jetified-firebase-messaging-ktx-23.1.1\AndroidManifest.xml:25:9-31:19
MERGED from [com.google.firebase:firebase-messaging-ktx:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\4533ad2b63c6d6128c8298815b20a971\transformed\jetified-firebase-messaging-ktx-23.1.1\AndroidManifest.xml:25:9-31:19
MERGED from [com.google.firebase:firebase-common-ktx:20.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\de59d1332438e2bc7040e58d2c13e54c\transformed\jetified-firebase-common-ktx-20.2.0\AndroidManifest.xml:11:9-17:19
MERGED from [com.google.firebase:firebase-common-ktx:20.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\de59d1332438e2bc7040e58d2c13e54c\transformed\jetified-firebase-common-ktx-20.2.0\AndroidManifest.xml:11:9-17:19
MERGED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aab237e9eab9fdab0322490d74dca402\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:64:9-70:19
MERGED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aab237e9eab9fdab0322490d74dca402\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:64:9-70:19
MERGED from [com.google.firebase:firebase-dynamic-links:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cacaab093d05956aa25d8a6eb172d90c\transformed\jetified-firebase-dynamic-links-21.1.0\AndroidManifest.xml:23:9-29:19
MERGED from [com.google.firebase:firebase-dynamic-links:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cacaab093d05956aa25d8a6eb172d90c\transformed\jetified-firebase-dynamic-links-21.1.0\AndroidManifest.xml:23:9-29:19
MERGED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\50f13cf14d327801350c587d0bf46851\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:52:9-58:19
MERGED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\50f13cf14d327801350c587d0bf46851\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:52:9-58:19
MERGED from [com.google.firebase:firebase-crashlytics:18.3.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\c7c80e3f565c1b8cf2aedc222f8b8d9d\transformed\jetified-firebase-crashlytics-18.3.2\AndroidManifest.xml:14:9-20:19
MERGED from [com.google.firebase:firebase-crashlytics:18.3.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\c7c80e3f565c1b8cf2aedc222f8b8d9d\transformed\jetified-firebase-crashlytics-18.3.2\AndroidManifest.xml:14:9-20:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\231488c28dff287f2e7cb7e3e1ee51ab\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\231488c28dff287f2e7cb7e3e1ee51ab\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.firebase:firebase-installations:17.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1d9d7423538685835871cb5aa6ad5398\transformed\jetified-firebase-installations-17.1.0\AndroidManifest.xml:14:9-20:19
MERGED from [com.google.firebase:firebase-installations:17.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1d9d7423538685835871cb5aa6ad5398\transformed\jetified-firebase-installations-17.1.0\AndroidManifest.xml:14:9-20:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13c74bafdc5d245406b0a8efef9b5ab\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:24:9-30:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13c74bafdc5d245406b0a8efef9b5ab\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:24:9-30:19
MERGED from [com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\43505c5b2b7b6eb07641cdceee51b9d9\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:32:9-36:35
MERGED from [com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\43505c5b2b7b6eb07641cdceee51b9d9\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:32:9-36:35
	android:exported
		ADDED from [com.google.firebase:firebase-dynamic-links-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8a1e83306d631bfa7be20217a9d58500\transformed\jetified-firebase-dynamic-links-ktx-21.1.0\AndroidManifest.xml:26:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\43505c5b2b7b6eb07641cdceee51b9d9\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:36:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\43505c5b2b7b6eb07641cdceee51b9d9\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:34:13-43
	android:name
		ADDED from [com.google.firebase:firebase-dynamic-links-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8a1e83306d631bfa7be20217a9d58500\transformed\jetified-firebase-dynamic-links-ktx-21.1.0\AndroidManifest.xml:25:13-84
meta-data#com.google.firebase.components:com.google.firebase.dynamiclinks.ktx.FirebaseDynamicLinksKtxRegistrar
ADDED from [com.google.firebase:firebase-dynamic-links-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8a1e83306d631bfa7be20217a9d58500\transformed\jetified-firebase-dynamic-links-ktx-21.1.0\AndroidManifest.xml:27:13-29:85
	android:value
		ADDED from [com.google.firebase:firebase-dynamic-links-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8a1e83306d631bfa7be20217a9d58500\transformed\jetified-firebase-dynamic-links-ktx-21.1.0\AndroidManifest.xml:29:17-82
	android:name
		ADDED from [com.google.firebase:firebase-dynamic-links-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8a1e83306d631bfa7be20217a9d58500\transformed\jetified-firebase-dynamic-links-ktx-21.1.0\AndroidManifest.xml:28:17-132
meta-data#com.google.firebase.components:com.google.firebase.crashlytics.ktx.FirebaseCrashlyticsKtxRegistrar
ADDED from [com.google.firebase:firebase-crashlytics-ktx:18.3.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\dfd474a0c38a487413af5e9cc8d67afc\transformed\jetified-firebase-crashlytics-ktx-18.3.2\AndroidManifest.xml:26:13-28:85
	android:value
		ADDED from [com.google.firebase:firebase-crashlytics-ktx:18.3.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\dfd474a0c38a487413af5e9cc8d67afc\transformed\jetified-firebase-crashlytics-ktx-18.3.2\AndroidManifest.xml:28:17-82
	android:name
		ADDED from [com.google.firebase:firebase-crashlytics-ktx:18.3.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\dfd474a0c38a487413af5e9cc8d67afc\transformed\jetified-firebase-crashlytics-ktx-18.3.2\AndroidManifest.xml:27:17-130
meta-data#com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsKtxRegistrar
ADDED from [com.google.firebase:firebase-analytics-ktx:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4633587b00bb6458a88b4bd9541faf20\transformed\jetified-firebase-analytics-ktx-21.2.0\AndroidManifest.xml:11:13-13:85
	android:value
		ADDED from [com.google.firebase:firebase-analytics-ktx:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4633587b00bb6458a88b4bd9541faf20\transformed\jetified-firebase-analytics-ktx-21.2.0\AndroidManifest.xml:13:17-82
	android:name
		ADDED from [com.google.firebase:firebase-analytics-ktx:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4633587b00bb6458a88b4bd9541faf20\transformed\jetified-firebase-analytics-ktx-21.2.0\AndroidManifest.xml:12:17-126
meta-data#com.google.firebase.components:com.google.firebase.auth.ktx.FirebaseAuthKtxRegistrar
ADDED from [com.google.firebase:firebase-auth-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ddbadfd7d70329eb339fcd0762c6a5a6\transformed\jetified-firebase-auth-ktx-21.1.0\AndroidManifest.xml:11:13-13:85
	android:value
		ADDED from [com.google.firebase:firebase-auth-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ddbadfd7d70329eb339fcd0762c6a5a6\transformed\jetified-firebase-auth-ktx-21.1.0\AndroidManifest.xml:13:17-82
	android:name
		ADDED from [com.google.firebase:firebase-auth-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ddbadfd7d70329eb339fcd0762c6a5a6\transformed\jetified-firebase-auth-ktx-21.1.0\AndroidManifest.xml:12:17-116
meta-data#com.google.firebase.components:com.google.firebase.messaging.ktx.FirebaseMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-messaging-ktx:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\4533ad2b63c6d6128c8298815b20a971\transformed\jetified-firebase-messaging-ktx-23.1.1\AndroidManifest.xml:28:13-30:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging-ktx:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\4533ad2b63c6d6128c8298815b20a971\transformed\jetified-firebase-messaging-ktx-23.1.1\AndroidManifest.xml:30:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging-ktx:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\4533ad2b63c6d6128c8298815b20a971\transformed\jetified-firebase-messaging-ktx-23.1.1\AndroidManifest.xml:29:17-126
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:20.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\de59d1332438e2bc7040e58d2c13e54c\transformed\jetified-firebase-common-ktx-20.2.0\AndroidManifest.xml:14:13-16:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:20.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\de59d1332438e2bc7040e58d2c13e54c\transformed\jetified-firebase-common-ktx-20.2.0\AndroidManifest.xml:16:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:20.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\de59d1332438e2bc7040e58d2c13e54c\transformed\jetified-firebase-common-ktx-20.2.0\AndroidManifest.xml:15:17-113
activity#com.karumi.dexter.DexterActivity
ADDED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\122f93913edc6855b2697d126574e839\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:27:9-29:72
	android:theme
		ADDED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\122f93913edc6855b2697d126574e839\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\122f93913edc6855b2697d126574e839\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:28:13-60
uses-permission#android.permission.FOREGROUND_SERVICE_LOCATION
ADDED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:13:5-86
	android:name
		ADDED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:13:22-83
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:14:5-81
	android:name
		ADDED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:14:22-78
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:15:5-81
	android:name
		ADDED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:15:22-78
receiver#org.altbeacon.beacon.startup.StartupBroadcastReceiver
ADDED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:18:9-26:20
	android:exported
		ADDED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:20:13-37
	android:name
		ADDED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:19:13-81
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED+action:name:android.intent.action.BOOT_COMPLETED
ADDED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:21:13-25:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:22:17-79
	android:name
		ADDED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:22:25-76
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:23:17-87
	android:name
		ADDED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:23:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:24:17-90
	android:name
		ADDED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:24:25-87
service#org.altbeacon.beacon.service.BeaconService
ADDED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:28:9-34:38
	android:enabled
		ADDED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:30:13-35
	android:label
		ADDED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:34:13-35
	android:exported
		ADDED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:31:13-37
	android:isolatedProcess
		ADDED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:33:13-44
	android:foregroundServiceType
		ADDED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:32:13-53
	android:name
		ADDED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:29:13-70
service#org.altbeacon.beacon.BeaconIntentProcessor
ADDED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:35:9-38:40
	android:enabled
		ADDED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:38:13-37
	android:name
		ADDED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:36:13-70
service#org.altbeacon.beacon.service.ScanJob
ADDED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:39:9-49:19
	android:exported
		ADDED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:41:13-37
	android:permission
		ADDED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:42:13-69
	android:name
		ADDED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:40:13-64
meta-data#immediateScanJobId
ADDED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:43:13-45:45
	android:value
		ADDED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:45:17-42
	android:name
		ADDED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:44:17-50
meta-data#periodicScanJobId
ADDED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:46:13-48:45
	android:value
		ADDED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:48:17-42
	android:name
		ADDED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:47:17-49
service#org.altbeacon.bluetooth.BluetoothTestJob
ADDED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:50:9-57:19
	android:exported
		ADDED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:52:13-37
	android:permission
		ADDED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:53:13-69
	android:name
		ADDED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:51:13-68
meta-data#jobId
ADDED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:54:13-56:46
	android:value
		ADDED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:56:17-43
	android:name
		ADDED from [org.altbeacon:android-beacon-library:2.20] C:\Users\<USER>\.gradle\caches\8.9\transforms\7220633d3da0e643121cf943ad5f521b\transformed\jetified-android-beacon-library-2.20\AndroidManifest.xml:55:17-37
uses-feature#android.hardware.camera.front
ADDED from [com.github.bruferrari:CameraKit-Android:0.10.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1f0df7c8b3a52f309b3391a1ea5030d2\transformed\jetified-CameraKit-Android-0.10.1\AndroidManifest.xml:16:5-18:36
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e090038d7f4ca21339112e8dc687487e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:28:5-30:36
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e090038d7f4ca21339112e8dc687487e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:28:5-30:36
	android:required
		ADDED from [com.github.bruferrari:CameraKit-Android:0.10.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1f0df7c8b3a52f309b3391a1ea5030d2\transformed\jetified-CameraKit-Android-0.10.1\AndroidManifest.xml:18:9-33
	android:name
		ADDED from [com.github.bruferrari:CameraKit-Android:0.10.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1f0df7c8b3a52f309b3391a1ea5030d2\transformed\jetified-CameraKit-Android-0.10.1\AndroidManifest.xml:17:9-53
uses-feature#android.hardware.microphone
ADDED from [com.github.bruferrari:CameraKit-Android:0.10.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1f0df7c8b3a52f309b3391a1ea5030d2\transformed\jetified-CameraKit-Android-0.10.1\AndroidManifest.xml:19:5-21:36
	android:required
		ADDED from [com.github.bruferrari:CameraKit-Android:0.10.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1f0df7c8b3a52f309b3391a1ea5030d2\transformed\jetified-CameraKit-Android-0.10.1\AndroidManifest.xml:21:9-33
	android:name
		ADDED from [com.github.bruferrari:CameraKit-Android:0.10.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1f0df7c8b3a52f309b3391a1ea5030d2\transformed\jetified-CameraKit-Android-0.10.1\AndroidManifest.xml:20:9-51
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\068cfc6cfd939845ffece9a7aaeffb7e\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\068cfc6cfd939845ffece9a7aaeffb7e\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\068cfc6cfd939845ffece9a7aaeffb7e\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\068cfc6cfd939845ffece9a7aaeffb7e\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\068cfc6cfd939845ffece9a7aaeffb7e\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\068cfc6cfd939845ffece9a7aaeffb7e\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\068cfc6cfd939845ffece9a7aaeffb7e\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\068cfc6cfd939845ffece9a7aaeffb7e\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\068cfc6cfd939845ffece9a7aaeffb7e\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\068cfc6cfd939845ffece9a7aaeffb7e\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:34:13-89
activity#com.google.firebase.auth.internal.GenericIdpActivity
ADDED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aab237e9eab9fdab0322490d74dca402\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:27:9-44:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aab237e9eab9fdab0322490d74dca402\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:29:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aab237e9eab9fdab0322490d74dca402\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:31:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aab237e9eab9fdab0322490d74dca402\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:30:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aab237e9eab9fdab0322490d74dca402\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:32:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aab237e9eab9fdab0322490d74dca402\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:28:13-80
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:genericidp
ADDED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aab237e9eab9fdab0322490d74dca402\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:33:13-43:29
activity#com.google.firebase.auth.internal.RecaptchaActivity
ADDED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aab237e9eab9fdab0322490d74dca402\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:45:9-62:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aab237e9eab9fdab0322490d74dca402\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:47:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aab237e9eab9fdab0322490d74dca402\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:49:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aab237e9eab9fdab0322490d74dca402\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:48:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aab237e9eab9fdab0322490d74dca402\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:50:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aab237e9eab9fdab0322490d74dca402\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:46:13-79
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:recaptcha
ADDED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aab237e9eab9fdab0322490d74dca402\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:51:13-61:29
meta-data#com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar
ADDED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aab237e9eab9fdab0322490d74dca402\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:67:13-69:85
	android:value
		ADDED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aab237e9eab9fdab0322490d74dca402\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:69:17-82
	android:name
		ADDED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aab237e9eab9fdab0322490d74dca402\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:68:17-109
meta-data#com.google.firebase.components:com.google.firebase.dynamiclinks.internal.FirebaseDynamicLinkRegistrar
ADDED from [com.google.firebase:firebase-dynamic-links:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cacaab093d05956aa25d8a6eb172d90c\transformed\jetified-firebase-dynamic-links-21.1.0\AndroidManifest.xml:26:13-28:85
	android:value
		ADDED from [com.google.firebase:firebase-dynamic-links:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cacaab093d05956aa25d8a6eb172d90c\transformed\jetified-firebase-dynamic-links-21.1.0\AndroidManifest.xml:28:17-82
	android:name
		ADDED from [com.google.firebase:firebase-dynamic-links:21.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cacaab093d05956aa25d8a6eb172d90c\transformed\jetified-firebase-dynamic-links-21.1.0\AndroidManifest.xml:27:17-133
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\50f13cf14d327801350c587d0bf46851\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:28:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\b49bdb60bc03ef79ccc823c576b103d3\transformed\jetified-play-services-cloud-messaging-17.0.1\AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\b49bdb60bc03ef79ccc823c576b103d3\transformed\jetified-play-services-cloud-messaging-17.0.1\AndroidManifest.xml:11:5-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\50f13cf14d327801350c587d0bf46851\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:28:22-79
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\50f13cf14d327801350c587d0bf46851\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:31:9-38:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\50f13cf14d327801350c587d0bf46851\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:33:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\50f13cf14d327801350c587d0bf46851\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:34:13-73
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\50f13cf14d327801350c587d0bf46851\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:32:13-78
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\50f13cf14d327801350c587d0bf46851\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:35:13-37:29
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\50f13cf14d327801350c587d0bf46851\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:36:17-81
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\50f13cf14d327801350c587d0bf46851\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:36:25-78
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\50f13cf14d327801350c587d0bf46851\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:44:9-51:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\50f13cf14d327801350c587d0bf46851\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:47:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\50f13cf14d327801350c587d0bf46851\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:46:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\50f13cf14d327801350c587d0bf46851\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:45:13-82
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\50f13cf14d327801350c587d0bf46851\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:55:13-57:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\50f13cf14d327801350c587d0bf46851\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:57:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\50f13cf14d327801350c587d0bf46851\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:56:17-119
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cdfce347bcc0c6410f73de8b2f931e7b\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cdfce347bcc0c6410f73de8b2f931e7b\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cdfce347bcc0c6410f73de8b2f931e7b\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cdfce347bcc0c6410f73de8b2f931e7b\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:19-85
meta-data#com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar
ADDED from [com.google.firebase:firebase-crashlytics:18.3.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\c7c80e3f565c1b8cf2aedc222f8b8d9d\transformed\jetified-firebase-crashlytics-18.3.2\AndroidManifest.xml:17:13-19:85
	android:value
		ADDED from [com.google.firebase:firebase-crashlytics:18.3.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\c7c80e3f565c1b8cf2aedc222f8b8d9d\transformed\jetified-firebase-crashlytics-18.3.2\AndroidManifest.xml:19:17-82
	android:name
		ADDED from [com.google.firebase:firebase-crashlytics:18.3.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\c7c80e3f565c1b8cf2aedc222f8b8d9d\transformed\jetified-firebase-crashlytics-18.3.2\AndroidManifest.xml:18:17-115
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\231488c28dff287f2e7cb7e3e1ee51ab\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7ed85f6081beebf021a7a48aab2066c3\transformed\jetified-play-services-measurement-impl-21.2.0\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7ed85f6081beebf021a7a48aab2066c3\transformed\jetified-play-services-measurement-impl-21.2.0\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\6dfdae5ee7b8086dfdf091db6a066743\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\6dfdae5ee7b8086dfdf091db6a066743\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1b940cc19d8a2309aa6fdcc2f41576d7\transformed\jetified-play-services-measurement-sdk-api-21.2.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1b940cc19d8a2309aa6fdcc2f41576d7\transformed\jetified-play-services-measurement-sdk-api-21.2.0\AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\231488c28dff287f2e7cb7e3e1ee51ab\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:25:22-76
meta-data#com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
ADDED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\231488c28dff287f2e7cb7e3e1ee51ab\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:31:13-33:85
	android:value
		ADDED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\231488c28dff287f2e7cb7e3e1ee51ab\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:33:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\231488c28dff287f2e7cb7e3e1ee51ab\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:32:17-139
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:17.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1d9d7423538685835871cb5aa6ad5398\transformed\jetified-firebase-installations-17.1.0\AndroidManifest.xml:17:13-19:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1d9d7423538685835871cb5aa6ad5398\transformed\jetified-firebase-installations-17.1.0\AndroidManifest.xml:19:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1d9d7423538685835871cb5aa6ad5398\transformed\jetified-firebase-installations-17.1.0\AndroidManifest.xml:18:17-127
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13c74bafdc5d245406b0a8efef9b5ab\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:27:13-29:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13c74bafdc5d245406b0a8efef9b5ab\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:29:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13c74bafdc5d245406b0a8efef9b5ab\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:28:17-115
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\43505c5b2b7b6eb07641cdceee51b9d9\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:25:9-30:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\43505c5b2b7b6eb07641cdceee51b9d9\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:27:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\43505c5b2b7b6eb07641cdceee51b9d9\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:29:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\43505c5b2b7b6eb07641cdceee51b9d9\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:28:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\43505c5b2b7b6eb07641cdceee51b9d9\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:30:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\43505c5b2b7b6eb07641cdceee51b9d9\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:26:13-77
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1a1d4f07b33d8783f810104ef22bf50f\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7ed85f6081beebf021a7a48aab2066c3\transformed\jetified-play-services-measurement-impl-21.2.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7ed85f6081beebf021a7a48aab2066c3\transformed\jetified-play-services-measurement-impl-21.2.0\AndroidManifest.xml:26:5-110
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1a1d4f07b33d8783f810104ef22bf50f\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:26:22-107
receiver#com.google.android.gms.measurement.AppMeasurementReceiver
ADDED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1a1d4f07b33d8783f810104ef22bf50f\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:29:9-33:20
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1a1d4f07b33d8783f810104ef22bf50f\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:31:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1a1d4f07b33d8783f810104ef22bf50f\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1a1d4f07b33d8783f810104ef22bf50f\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:30:13-85
service#com.google.android.gms.measurement.AppMeasurementService
ADDED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1a1d4f07b33d8783f810104ef22bf50f\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:35:9-38:40
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1a1d4f07b33d8783f810104ef22bf50f\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1a1d4f07b33d8783f810104ef22bf50f\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:38:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1a1d4f07b33d8783f810104ef22bf50f\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:36:13-84
service#com.google.android.gms.measurement.AppMeasurementJobService
ADDED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1a1d4f07b33d8783f810104ef22bf50f\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:39:9-43:72
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1a1d4f07b33d8783f810104ef22bf50f\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:41:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1a1d4f07b33d8783f810104ef22bf50f\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:42:13-37
	android:permission
		ADDED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1a1d4f07b33d8783f810104ef22bf50f\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:43:13-69
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1a1d4f07b33d8783f810104ef22bf50f\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:40:13-87
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b138217803023ee017e5a16beaa9ca30\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b138217803023ee017e5a16beaa9ca30\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b138217803023ee017e5a16beaa9ca30\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0c501d704c37b63fce0fcc21c8a605d7\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0c501d704c37b63fce0fcc21c8a605d7\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0c501d704c37b63fce0fcc21c8a605d7\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0c501d704c37b63fce0fcc21c8a605d7\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0c501d704c37b63fce0fcc21c8a605d7\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0c501d704c37b63fce0fcc21c8a605d7\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
uses-feature#android.hardware.camera.flash
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e090038d7f4ca21339112e8dc687487e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:36:5-38:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e090038d7f4ca21339112e8dc687487e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:38:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e090038d7f4ca21339112e8dc687487e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:37:9-53
uses-feature#android.hardware.screen.landscape
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e090038d7f4ca21339112e8dc687487e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:39:5-41:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e090038d7f4ca21339112e8dc687487e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:41:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e090038d7f4ca21339112e8dc687487e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:40:9-57
uses-feature#android.hardware.wifi
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e090038d7f4ca21339112e8dc687487e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:42:5-44:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e090038d7f4ca21339112e8dc687487e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:44:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e090038d7f4ca21339112e8dc687487e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:43:9-45
activity#com.journeyapps.barcodescanner.CaptureActivity
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e090038d7f4ca21339112e8dc687487e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:47:9-53:63
	android:screenOrientation
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e090038d7f4ca21339112e8dc687487e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:50:13-56
	android:clearTaskOnLaunch
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e090038d7f4ca21339112e8dc687487e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:49:13-45
	android:stateNotNeeded
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e090038d7f4ca21339112e8dc687487e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:51:13-42
	android:windowSoftInputMode
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e090038d7f4ca21339112e8dc687487e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:53:13-60
	android:theme
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e090038d7f4ca21339112e8dc687487e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:52:13-54
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e090038d7f4ca21339112e8dc687487e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:48:13-74
queries
ADDED from [com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\bc76bf08a15cc9c299066e5371206aa1\transformed\jetified-billing-6.0.1\AndroidManifest.xml:12:5-16:15
intent#action:name:com.android.vending.billing.InAppBillingService.BIND
ADDED from [com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\bc76bf08a15cc9c299066e5371206aa1\transformed\jetified-billing-6.0.1\AndroidManifest.xml:13:9-15:18
action#com.android.vending.billing.InAppBillingService.BIND
ADDED from [com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\bc76bf08a15cc9c299066e5371206aa1\transformed\jetified-billing-6.0.1\AndroidManifest.xml:14:13-91
	android:name
		ADDED from [com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\bc76bf08a15cc9c299066e5371206aa1\transformed\jetified-billing-6.0.1\AndroidManifest.xml:14:21-88
meta-data#com.google.android.play.billingclient.version
ADDED from [com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\bc76bf08a15cc9c299066e5371206aa1\transformed\jetified-billing-6.0.1\AndroidManifest.xml:19:9-21:37
	android:value
		ADDED from [com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\bc76bf08a15cc9c299066e5371206aa1\transformed\jetified-billing-6.0.1\AndroidManifest.xml:21:13-34
	android:name
		ADDED from [com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\bc76bf08a15cc9c299066e5371206aa1\transformed\jetified-billing-6.0.1\AndroidManifest.xml:20:13-73
activity#com.android.billingclient.api.ProxyBillingActivity
ADDED from [com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\bc76bf08a15cc9c299066e5371206aa1\transformed\jetified-billing-6.0.1\AndroidManifest.xml:23:9-27:75
	android:exported
		ADDED from [com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\bc76bf08a15cc9c299066e5371206aa1\transformed\jetified-billing-6.0.1\AndroidManifest.xml:26:13-37
	android:configChanges
		ADDED from [com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\bc76bf08a15cc9c299066e5371206aa1\transformed\jetified-billing-6.0.1\AndroidManifest.xml:25:13-96
	android:theme
		ADDED from [com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\bc76bf08a15cc9c299066e5371206aa1\transformed\jetified-billing-6.0.1\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\bc76bf08a15cc9c299066e5371206aa1\transformed\jetified-billing-6.0.1\AndroidManifest.xml:24:13-78
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\dacfb878dcf3ada73c635c44ab3d3981\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\a5092a4592b4dfb55a0ebcf35f9d7ea4\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\a5092a4592b4dfb55a0ebcf35f9d7ea4\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\dacfb878dcf3ada73c635c44ab3d3981\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\dacfb878dcf3ada73c635c44ab3d3981\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\dacfb878dcf3ada73c635c44ab3d3981\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\dacfb878dcf3ada73c635c44ab3d3981\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\dacfb878dcf3ada73c635c44ab3d3981\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\a5092a4592b4dfb55a0ebcf35f9d7ea4\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\a5092a4592b4dfb55a0ebcf35f9d7ea4\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\a5092a4592b4dfb55a0ebcf35f9d7ea4\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\a5092a4592b4dfb55a0ebcf35f9d7ea4\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\a5092a4592b4dfb55a0ebcf35f9d7ea4\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\a5092a4592b4dfb55a0ebcf35f9d7ea4\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\a5092a4592b4dfb55a0ebcf35f9d7ea4\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a0c43417dbd8eb111e7690e9d88b867f\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\2b0313006e36c3f090e112081263bb70\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:26:9-34:20
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\2b0313006e36c3f090e112081263bb70\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:26:9-34:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\53e667bfd73b817246c3e7be86a6326a\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\53e667bfd73b817246c3e7be86a6326a\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a0c43417dbd8eb111e7690e9d88b867f\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a0c43417dbd8eb111e7690e9d88b867f\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a0c43417dbd8eb111e7690e9d88b867f\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a0c43417dbd8eb111e7690e9d88b867f\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a0c43417dbd8eb111e7690e9d88b867f\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a0c43417dbd8eb111e7690e9d88b867f\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a0c43417dbd8eb111e7690e9d88b867f\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fdc9cfd33c5a21669dc8cdc95e0cd352\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fdc9cfd33c5a21669dc8cdc95e0cd352\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fdc9cfd33c5a21669dc8cdc95e0cd352\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
permission#com.flashbid.luv.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fdc9cfd33c5a21669dc8cdc95e0cd352\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fdc9cfd33c5a21669dc8cdc95e0cd352\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fdc9cfd33c5a21669dc8cdc95e0cd352\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fdc9cfd33c5a21669dc8cdc95e0cd352\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fdc9cfd33c5a21669dc8cdc95e0cd352\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
uses-permission#com.flashbid.luv.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fdc9cfd33c5a21669dc8cdc95e0cd352\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fdc9cfd33c5a21669dc8cdc95e0cd352\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\2b0313006e36c3f090e112081263bb70\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\2b0313006e36c3f090e112081263bb70\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\2b0313006e36c3f090e112081263bb70\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
