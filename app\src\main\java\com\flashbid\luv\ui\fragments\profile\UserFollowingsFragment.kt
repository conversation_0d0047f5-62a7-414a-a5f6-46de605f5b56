package com.flashbid.luv.ui.fragments.profile

import android.os.Bundle
import android.view.View
import android.view.inputmethod.EditorInfo
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import com.flashbid.luv.R
import com.flashbid.luv.adapter.UsersListAdapter
import com.flashbid.luv.databinding.FragmentUserFollowingsBinding
import com.flashbid.luv.extensions.hide
import com.flashbid.luv.extensions.setOnClickWithDebounce
import com.flashbid.luv.extensions.setVerticalLayout
import com.flashbid.luv.extensions.show
import com.flashbid.luv.extensions.snackBar
import com.flashbid.luv.extensions.viewBinding
import com.flashbid.luv.models.remote.UserDetails
import com.flashbid.luv.network.Status
import com.flashbid.luv.viewmodels.UserViewModel
import org.koin.androidx.viewmodel.ext.android.viewModel

class UserFollowingsFragment : Fragment(R.layout.fragment_user_followings) {

    private val binding by viewBinding(FragmentUserFollowingsBinding::bind)
    private val viewModel: UserViewModel by viewModel()
    private val list: ArrayList<UserDetails> = ArrayList()
    private val followAdapter by lazy {
        UsersListAdapter(
            list,
            true,
            this::onUserClick,
            this::onFollowClick
        )
    }

    private fun onUserClick(item: UserDetails) {
        val action =
            UserFollowingsFragmentDirections.actionUserFollowingsFragmentToUserProfileFragment(
                item.id ?: 0
            )
        findNavController().navigate(action)
    }

    private fun onFollowClick(item: UserDetails, isFollowed: Int) {
        if (isFollowed == 0) followUser(item.id ?: 0)
        if (isFollowed == 1) unfollowUser(item.id ?: 0)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.imageView.setOnClickWithDebounce {
            findNavController().popBackStack()
        }

        binding.rcvUsers.apply {
            setVerticalLayout()
            adapter = followAdapter
        }

        getFollows("")

        binding.edtSearch.imeOptions = EditorInfo.IME_ACTION_SEARCH
        binding.edtSearch.setOnEditorActionListener { _, actionId, _ ->
            if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                val query = binding.edtSearch.text.toString()
                if (query.isNotEmpty()) searchUser(query)
                else getFollows(query)
                return@setOnEditorActionListener true
            }
            false
        }

    }

    private fun searchUser(query: String) {
        viewModel.searchUser(query).observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(it.message ?: getString(R.string.something_went_wrong, getString(R.string.try_again)))
                Status.LOADING -> {}
                Status.SUCCESS -> {
                    list.clear()
                    list.addAll(it.data?.list ?: ArrayList())
                    followAdapter.refresh()
                    if (followAdapter.itemCount > 0) {
                        binding.rcvUsers.show()
                        binding.llNoItems.hide()
                    } else {
                        binding.rcvUsers.hide()
                        binding.llNoItems.show()
                    }
                }
            }
        }
    }

    private fun getFollows(query: String = "") {
        viewModel.getFollowings(query).observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(it.message ?: getString(R.string.something_went_wrong, getString(R.string.try_again)))
                Status.LOADING -> {}
                Status.SUCCESS -> {
                    list.clear()
                    list.addAll(it.data?.list ?: ArrayList())
                    followAdapter.refresh()
                }
            }
        }
    }

    private fun followUser(id: Int) {
        viewModel.followUser(id).observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(it.message ?: getString(R.string.something_went_wrong, getString(R.string.try_again)))
                Status.LOADING -> {}
                Status.SUCCESS -> {
                    getFollows()
                }
            }
        }
    }

    private fun unfollowUser(id: Int) {
        viewModel.unfollowUser(id).observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(it.message ?: getString(R.string.something_went_wrong, getString(R.string.try_again)))
                Status.LOADING -> {}
                Status.SUCCESS -> {
                    getFollows()
                }
            }
        }
    }


}