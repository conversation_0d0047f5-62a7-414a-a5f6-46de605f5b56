package com.flashbid.luv.ui.fragments.quest

import android.annotation.SuppressLint
import android.app.AlertDialog
import android.content.ContentValues.TAG
import android.content.Intent
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.net.Uri
import android.os.Bundle
import android.os.CountDownTimer
import android.util.DisplayMetrics
import android.util.Log
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.widget.FrameLayout
import androidx.core.net.toUri
import androidx.core.widget.doAfterTextChanged
import androidx.fragment.app.Fragment
import androidx.viewbinding.ViewBinding
import com.flashbid.luv.R
import com.flashbid.luv.adapter.CustomBeaconBrandAdapter
import com.flashbid.luv.adapter.CustomBrandAdapter
import com.flashbid.luv.adapter.CustomBrandNameAdapter
import com.flashbid.luv.data.local.AppPreferences
import com.flashbid.luv.databinding.BottomSheetBrandNameBinding
import com.flashbid.luv.databinding.BottomSheetCustomBrandBinding
import com.flashbid.luv.databinding.DialogDailyRewardBinding
import com.flashbid.luv.databinding.FragmentQuestBinding
import com.flashbid.luv.extensions.hideSoftKeyboard
import com.flashbid.luv.extensions.setOnClickWithDebounce
import com.flashbid.luv.extensions.setVerticalLayout
import com.flashbid.luv.extensions.snackBar
import com.flashbid.luv.extensions.viewBinding
import com.flashbid.luv.models.remote.CustomBrandNameResponse
import com.flashbid.luv.models.remote.CustomBrandResponse
import com.flashbid.luv.network.Status
import com.flashbid.luv.util.Constants
import com.flashbid.luv.util.DeviceUtils.hideSoftInput
import com.flashbid.luv.viewmodels.GiftsViewModel
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.firebase.dynamiclinks.DynamicLink
import com.google.firebase.dynamiclinks.FirebaseDynamicLinks
import com.google.firebase.dynamiclinks.ktx.androidParameters
import com.google.firebase.dynamiclinks.ktx.iosParameters
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel


class QuestFragment : Fragment(R.layout.fragment_quest) {

    private val binding by viewBinding(FragmentQuestBinding::bind)
    private val customBrandAdapter by lazy { CustomBrandAdapter(customBrandList, this::onBrandSelect) }
    private val customBeaconBrandAdapter by lazy { CustomBeaconBrandAdapter(customBeasonrandList, this::onBeaconBrandSelect) }
    private val customBrandNameAdapter by lazy { CustomBrandNameAdapter(customBrandNameList, this::onBrandNameSelect) }

    private val viewModel: GiftsViewModel by viewModel()
    private val pref by inject<AppPreferences>()
    private var timer: CountDownTimer? = null
    //private val sheet: BottomSheetDialog by lazy { BottomSheetDialog(requireContext()) }
    private var sheet: BottomSheetDialog? = null

    private val customBrandList: ArrayList<CustomBrandResponse.CustomBrandDetails> = ArrayList()
    private val customBeasonrandList: ArrayList<CustomBrandResponse.CustomBrandDetails> = ArrayList()
    private val customBrandNameList: ArrayList<CustomBrandNameResponse.CustomBrandNames> = ArrayList()

    private fun onBrandSelect(item: CustomBrandResponse.CustomBrandDetails) {
        sheet?.dismiss()

        //showBrandName(item)
    }
    private fun onBeaconBrandSelect(item: CustomBrandResponse.CustomBrandDetails) {
        sheet?.dismiss()
//        val action =
//            StartLuvBattleFragmentDirections.actionStartLuvBattleFragmentToSelectOpponentFragment(
//                item
//            )
//        findNavController().navigate(action)
    }

    private fun onBrandNameSelect(item: CustomBrandNameResponse.CustomBrandNames) {
        sheet?.dismiss()
//        val action =
//            StartLuvBattleFragmentDirections.actionStartLuvBattleFragmentToSelectOpponentFragment(
//                item
//            )
//        findNavController().navigate(action)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        if (pref.referral.isNullOrEmpty()) getReferralCode()
        else initReferButton()

        binding.btnCrate.setOnClickWithDebounce {
            //Constants.CRATE_TYPE.postValue(""+ HistoryMapping.ACTION.QUEST_CRATE)
            //findNavController().navigate(R.id.scanFragment)
            showCustomBeacon(true)
        }

        binding.btnEarnDrop.setOnClickWithDebounce {
            //Constants.CRATE_TYPE.postValue(""+ HistoryMapping.ACTION.QUEST_CRATE)
            //findNavController().navigate(R.id.scanFragment)
            showCustomBeacon(false)
        }

    }

    private fun showCustomBeacon(boolean: Boolean) {
        /*val view = BottomSheetCustomBrandBinding.inflate(layoutInflater)

        view.edtSearch.doAfterTextChanged {
            if (boolean) {
                customBrandAdapter.filter(it.toString())
            } else {
                customBeaconBrandAdapter.filter(it.toString())
            }
        }

        view.imageView6.setOnClickWithDebounce {
            sheet.dismiss()
        }
        view.rcvUsers.apply {
            setVerticalLayout()
            if (boolean) {
                adapter = customBrandAdapter
            } else {
                adapter = customBeaconBrandAdapter
            }
        }
        if (boolean) {
            view.textView36.text =  getString(R.string.there_luv_inside_package)
            getCustomBrands()
            customBrandAdapter.filter("")
        } else {
            view.textView36.text =  getString(R.string.luv_drop_with_every_visit)
            getCustomBeaconBrands()
            customBeaconBrandAdapter.filter("")
        }

        sheet.setCancelable(true)
        sheet.setContentView(view.root)
        sheet.show()*/



        val viewBinding = BottomSheetCustomBrandBinding.inflate(layoutInflater)
        setupBottomSheet(viewBinding) { sheet, behavior ->

            val displayMetrics = DisplayMetrics()
            activity!!.windowManager.defaultDisplay.getMetrics(displayMetrics)
            val screenHeight = displayMetrics.heightPixels

            this.sheet = sheet

            val bottomSheet = sheet.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)
            bottomSheet?.layoutParams?.height = ViewGroup.LayoutParams.MATCH_PARENT
            bottomSheet?.requestLayout()


            behavior.peekHeight = (screenHeight * 0.9).toInt() // Adjust this value as needed
            behavior.maxHeight = (screenHeight * 0.9).toInt() // Adjust this value as needed

            behavior.state = BottomSheetBehavior.STATE_EXPANDED

            viewBinding.edtSearch.doAfterTextChanged {
                if (boolean) {
                    customBrandAdapter.filter(it.toString())
                } else {
                    customBeaconBrandAdapter.filter(it.toString())
                }
            }

            viewBinding.edtSearch.setOnEditorActionListener { v, actionId, event ->
                if (actionId == EditorInfo.IME_ACTION_DONE) {
                    // Hide the keyboard
                    hideSoftInput(binding.root)
                    true
                } else {
                    false
                }
            }

            viewBinding.imageView6.setOnClickWithDebounce {
                sheet.dismiss()
            }
            viewBinding.rcvUsers.apply {
                setVerticalLayout()
                if (boolean) {
                    adapter = customBrandAdapter
                } else {
                    adapter = customBeaconBrandAdapter
                }
            }
            if (boolean) {
                viewBinding.textView36.text =  getString(R.string.there_luv_inside_package)
                getCustomBrands()
                customBrandAdapter.filter("")
            } else {
                viewBinding.textView36.text =  getString(R.string.luv_drop_with_every_visit)
                getCustomBeaconBrands()
                customBeaconBrandAdapter.filter("")
            }
        }
    }

    private fun showBrandName(item: CustomBrandResponse.CustomBrandDetails) {

        val viewBinding = BottomSheetBrandNameBinding.inflate(layoutInflater)

        setupBottomSheet(viewBinding) { sheet, behavior ->

            val displayMetrics = DisplayMetrics()
            activity!!.windowManager.defaultDisplay.getMetrics(displayMetrics)
            val screenHeight = displayMetrics.heightPixels

            this.sheet = sheet

            val bottomSheet = sheet.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)
            bottomSheet?.layoutParams?.height = ViewGroup.LayoutParams.MATCH_PARENT
            bottomSheet?.requestLayout()


            behavior.peekHeight = (screenHeight * 0.9).toInt() // Adjust this value as needed
            behavior.maxHeight = (screenHeight * 0.9).toInt() // Adjust this value as needed

            behavior.state = BottomSheetBehavior.STATE_EXPANDED

            viewBinding.textView36.text = getString(R.string.luv_crates, item.username)

            viewBinding.imageView6.setOnClickWithDebounce {
                sheet.dismiss()
            }
            viewBinding.rcvUsers.apply {
                setVerticalLayout()
                adapter = customBrandNameAdapter
            }
            getBrandName(item.id)
        }
    }


    private fun setupBottomSheet(
        layoutBinding: ViewBinding,
        onShowBehavior: (BottomSheetDialog, BottomSheetBehavior<FrameLayout>) -> Unit
    ) {
        val sheet = BottomSheetDialog(requireContext()).apply {
            setCancelable(true)
            setContentView(layoutBinding.root)
            setOnShowListener {
                val bottomSheet =
                    findViewById<FrameLayout>(com.google.android.material.R.id.design_bottom_sheet)
                onShowBehavior(this, BottomSheetBehavior.from(bottomSheet!!))
            }
        }
        sheet.show()
    }

    private fun getCustomBrands() {
        viewModel.getCustomBrands().observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(
                    it.message ?: getString(
                        R.string.something_went_wrong, getString(R.string.try_again)
                    )
                )

                Status.LOADING -> {}
                Status.SUCCESS -> {
                    customBrandList.clear()
                    customBrandList.addAll(it.data?.message ?: ArrayList())
                    customBrandAdapter.refresh()
                }
            }
        }
    }

    private fun getBrandName(filter: Int) {
        viewModel.getCustomBrandName(filter).observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(
                    it.message ?: getString(
                        R.string.something_went_wrong, getString(R.string.try_again)
                    )
                )

                Status.LOADING -> {}
                Status.SUCCESS -> {
                    customBrandNameList.clear()
                    customBrandNameList.addAll(it.data?.list ?: ArrayList())
                    customBrandNameAdapter.refresh()

                }
            }
        }
    }

    private fun getCustomBeaconBrands() {
        viewModel.getCustomBeaconBrands().observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(
                    it.message ?: getString(
                        R.string.something_went_wrong, getString(R.string.try_again)
                    )
                )

                Status.LOADING -> {}
                Status.SUCCESS -> {
                    customBeasonrandList.clear()
                    customBeasonrandList.addAll(it.data?.message ?: ArrayList())
                    customBeaconBrandAdapter.refresh()
                }
            }
        }
    }

    private fun generateSharingLink(
        deepLink: Uri,
        previewImageLink: Uri,
        getShareableLink: (String) -> Unit = {}
    ) {
        FirebaseDynamicLinks.getInstance().createDynamicLink().run {
            link = deepLink
            domainUriPrefix = Constants.DYNAMIC_LINK_PREFIX
            setSocialMetaTagParameters(
                DynamicLink.SocialMetaTagParameters.Builder()
                    .setImageUrl(previewImageLink)
                    .build()
            )
            iosParameters("com.manaknight.Luv-App") {
                build()
            }
            androidParameters("com.manaknight.luv") {
                build()
            }
            buildShortDynamicLink()
        }.also {
            it.addOnSuccessListener { dynamicLink ->
                getShareableLink.invoke(dynamicLink.shortLink.toString())
            }
            it.addOnFailureListener { error ->
                snackBar("${error.message}")
            }
        }
    }

    private fun shareDeepLink(deepLink: String) {
        val intent = Intent(Intent.ACTION_SEND)
        intent.type = "text/plain"
        intent.putExtra(
            Intent.EXTRA_SUBJECT,
            getString(R.string.luv_referral)
        )
        intent.putExtra(
            Intent.EXTRA_TEXT,
            getString(R.string.checkout_luv_app, deepLink)
        )
        if (isAdded) {
            requireContext().startActivity(Intent.createChooser(intent, "Share.."))
        }
    }

    private fun getRewardTime() {
        viewModel.getQuestTime().observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> {
                    snackBar(it.message ?: getString(R.string.something_went_wrong, getString(R.string.try_again)))
                }

                Status.LOADING -> {}
                Status.SUCCESS -> {
                    val data = it.data?.message
                    if (data?.seconds != null) {
                        convertDateToRemainingTime(data.seconds.toInt())
                        binding.linearLayout3.setOnClickWithDebounce { }
                    } else {
                        binding.tvTime.text = getString(R.string.send_luv_get_luv)
                        binding.linearLayout3.setOnClickWithDebounce { showDailyRewardDialog() }
                    }
                }
            }
        }
    }

    private fun getReferralCode() {
        viewModel.getReferralCode().observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> {
                    snackBar(it.message ?: getString(R.string.something_went_wrong, getString(R.string.try_again)))
                }

                Status.LOADING -> {}
                Status.SUCCESS -> {
                    val code = it.data?.message?.code
                    if (code != null) {
                        pref.referral = code
                        initReferButton()
                    }
                }
            }
        }
    }

    private fun initReferButton() {
        binding.linearLayout2.setOnClickWithDebounce {
            generateSharingLink(
                deepLink = "${Constants.DYNAMIC_LINK_PREFIX}/referral?code=${pref.referral}".toUri(),
                previewImageLink = "".toUri()
            ) { generatedLink ->
                // Use this generated Link to share via Intent
                shareDeepLink(generatedLink)
                Log.d(TAG, "checkDynamicLink: $generatedLink")
            }
        }
    }

    private fun showDailyRewardDialog() {
        val builder = AlertDialog.Builder(requireContext())
        val binding = DialogDailyRewardBinding.inflate(layoutInflater)
        val dialog = builder.create()

        dialog.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))

        binding.imageView8.setOnClickWithDebounce {
            dialog.dismiss()
        }
        binding.btnOpen.setOnClickWithDebounce {
            openDailyReward()
            dialog.dismiss()
        }

        dialog.setView(binding.root)
        dialog.setCancelable(false)
        dialog.show()
    }

    private fun openDailyReward() {
        viewModel.collectDailyReward().observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(it.message ?: getString(R.string.something_went_wrong, getString(R.string.try_again)))
                Status.LOADING -> {}
                Status.SUCCESS -> {
                    snackBar(it.data?.message ?: getString(R.string.collected))
                    getRewardTime()
                }
            }
        }
    }

    @SuppressLint("SimpleDateFormat")
    private fun convertDateToRemainingTime(date: Int) {
        val hours = date / 3600
        val minutes = (date % 3600) / 60
        val seconds = date % 60
        val time = String.format("%02d:%02d:%02d", hours, minutes, seconds)
        binding.tvTime.text = time
        startCountdown(time)
    }

    private fun startCountdown(time: String) {
        val millis = parseTime(time)
        timer = object : CountDownTimer(millis, 1000) {
            override fun onTick(millisUntilFinished: Long) {
                val formattedTime = formatMillis(millisUntilFinished)
                binding.tvTime.text = formattedTime
            }

            override fun onFinish() {
                // countdown timer finished
                getRewardTime()
            }
        }.start()
    }

    private fun parseTime(time: String): Long {
        val parts = time.split(":")
        val hours = parts[0].toLong()
        val minutes = parts[1].toLong()
        val seconds = parts[2].toLong()
        return (hours * 3600 + minutes * 60 + seconds) * 1000
    }

    private fun formatMillis(millis: Long): String {
        val hours = millis / 3600000
        val minutes = (millis % 3600000) / 60000
        val seconds = (millis % 60000) / 1000
        return String.format("%02d:%02d:%02d", hours, minutes, seconds)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        timer?.cancel()
    }

    override fun onResume() {
        super.onResume()
        getRewardTime()
    }

}