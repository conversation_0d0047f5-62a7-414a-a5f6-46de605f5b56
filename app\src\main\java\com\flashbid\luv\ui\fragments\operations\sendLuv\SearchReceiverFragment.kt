package com.flashbid.luv.ui.fragments.operations.sendLuv

import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.flashbid.luv.R
import com.flashbid.luv.adapter.FragmentPagerAdapter
import com.flashbid.luv.data.local.AppPreferences
import com.flashbid.luv.databinding.FragmentSearchReceiverBinding
import com.flashbid.luv.extensions.setOnClickWithDebounce
import com.flashbid.luv.extensions.viewBinding
import com.flashbid.luv.models.remote.UserDetails
import com.flashbid.luv.ui.fragments.follow.FollowersFragment
import com.flashbid.luv.ui.fragments.follow.FollowingsFragment
import com.google.android.material.tabs.TabLayout
import org.koin.android.ext.android.inject

class SearchReceiverFragment : Fragment(R.layout.fragment_search_receiver) {

    private val binding by viewBinding(FragmentSearchReceiverBinding::bind)
    private val pref by inject<AppPreferences>()
    private val args by navArgs<SearchReceiverFragmentArgs>()

    private fun onUserSelect(item: UserDetails) {
        val action =
            SearchReceiverFragmentDirections.actionSearchReceiverFragmentToSelectReceiverFragment(
                args.gift, item
            )
        findNavController().navigate(action)
    }

    override fun onResume() {
        super.onResume()
        if (args.openNearby) {
            binding.tabLayout.selectTab(binding.tabLayout.getTabAt(2))
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.imageView.setOnClickWithDebounce {
            findNavController().popBackStack()
        }

        binding.tvBalance.text = pref.balanceLuv.toString()

        binding.viewPager.apply {
            isUserInputEnabled = false
            adapter = FragmentPagerAdapter(childFragmentManager,
                lifecycle,
                listOf(FollowingsFragment.newInstance { onUserSelect(it) },
                    FollowersFragment.newInstance { onUserSelect(it) },
                    NearbyUsersFragment.newInstance { onUserSelect(it) }))

        }

        initializeTabs(
            arrayOf(
                getString(R.string.following),
                getString(R.string.followers),
                getString(R.string.nearby)
            )
        )
    }

    private fun initializeTabs(tabs: Array<String>) {
        for (tabName in tabs) {
            val tab = binding.tabLayout.newTab().setText(tabName)
            binding.tabLayout.addTab(tab)
        }

        binding.tabLayout.setTabTextColors(
            resources.getColor(R.color.black), resources.getColor(R.color.white)
        )

        binding.tabLayout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab) {
                binding.viewPager.currentItem = tab.position

            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {
            }

            override fun onTabReselected(tab: TabLayout.Tab?) {
            }
        })

//        if (args.openNearby) {
//            binding.tabLayout.selectTab(binding.tabLayout.getTabAt(2))
//        }
    }

}
