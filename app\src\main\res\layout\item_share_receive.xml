<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:visibility="gone"
    android:background="@drawable/bg_round_corners"
    android:paddingHorizontal="@dimen/_10sdp"
    android:paddingVertical="@dimen/_10sdp">

    <ImageView
        android:id="@+id/imageView4"
        android:layout_width="@dimen/_40sdp"
        android:layout_height="@dimen/_40sdp"
        android:src="@drawable/iv_gray_cup"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvUsername"
        style="@style/small"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_10sdp"
        android:ellipsize="end"
        android:maxLines="3"
        android:text="@string/shared_live_stream"
        android:textColor="@color/gray"
        android:textSize="@dimen/_10sdp"
        app:layout_constraintBottom_toBottomOf="@+id/imageView4"
        app:layout_constraintEnd_toStartOf="@+id/btnJoin"
        app:layout_constraintStart_toEndOf="@+id/imageView4"
        app:layout_constraintTop_toTopOf="@+id/imageView4" />

    <TextView
        android:id="@+id/btnJoin"
        style="@style/small"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_circle_red"
        android:gravity="center"
        android:paddingHorizontal="@dimen/_20sdp"
        android:paddingVertical="@dimen/_8sdp"
        android:text="@string/join"
        android:textColor="@color/white"
        app:layout_constraintBottom_toBottomOf="@+id/tvUsername"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvUsername" />

</androidx.constraintlayout.widget.ConstraintLayout>