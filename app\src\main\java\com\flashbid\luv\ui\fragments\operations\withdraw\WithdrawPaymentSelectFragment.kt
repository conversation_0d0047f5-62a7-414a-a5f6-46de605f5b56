package com.flashbid.luv.ui.fragments.operations.withdraw

import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import com.flashbid.luv.R
import com.flashbid.luv.data.local.AppPreferences
import com.flashbid.luv.databinding.FragmentWithdrawPaymentSelectBinding
import com.flashbid.luv.extensions.*
import com.flashbid.luv.models.remote.Country
import com.flashbid.luv.models.remote.Currency
import com.flashbid.luv.models.remote.PayoutMethod
import com.flashbid.luv.network.Status
import com.flashbid.luv.util.setArrayAdapter
import com.flashbid.luv.viewmodels.TransactionViewModel
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel

class WithdrawPaymentSelectFragment : Fragment(R.layout.fragment_withdraw_payment_select) {

    private val binding by viewBinding(FragmentWithdrawPaymentSelectBinding::bind)
    private val viewModel: TransactionViewModel by viewModel()
    private val pref by inject<AppPreferences>()
    private val methodList: ArrayList<PayoutMethod> = ArrayList()
    private val currencyList: ArrayList<Currency> = ArrayList()
    private val countryList: ArrayList<Country> = ArrayList()
    private var method: Int = -1
    private var currency: String = ""
    private var country: String = ""

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.tvBalance.text = pref.balanceDiamond.toString()

        binding.imageView.setOnClickWithDebounce {
            findNavController().popBackStack()
        }

        getPayoutData()

        binding.btnPasteText.setOnClickWithDebounce {
            binding.edtPaypalLink.setText(getCopiedText())
        }

        binding.btnNext.setOnClickWithDebounce {
            if (pref.balanceDiamond <= 0) {
                binding.tvError.setTextState(getString(R.string.no_balance), TextState.ERROR)
            } else if (method == -1) {
                binding.tvError.setTextState(getString(R.string.fill_all_details), TextState.ERROR)
            } else if (currency == "") {
                binding.tvError.setTextState(getString(R.string.fill_all_details), TextState.ERROR)
            } else if (country == "") {
                binding.tvError.setTextState(getString(R.string.fill_all_details), TextState.ERROR)
            } else if (!binding.edtPaypalLink.textToString()
                    .isValidUrl() && binding.edtPaypalLink.textToString().contains("paypal")
            ) {
                binding.tvError.setTextState(getString(R.string.invalid_paypal), TextState.ERROR)
            } else {
                val direction =
                    WithdrawPaymentSelectFragmentDirections.actionWithdrawPaymentSelectFragmentToWithdrawAmountSelectFragment(
                        country,
                        currency,
                        method,
                        binding.edtPaypalLink.textToString()
                    )
                findNavController().navigate(direction)
            }
        }
    }


    private fun setMethodList(list: ArrayList<PayoutMethod>) {
        methodList.apply {
            clear()
            addAll(list)
        }
        binding.edtMethod.setArrayAdapter(methodList.map { it.name } as ArrayList<String>)
        binding.edtMethod.setOnItemClickListener { _, _, position, _ ->
            method = methodList[position].id
            binding.tvError.hide()
        }
    }

    private fun setCurrencyList(list: ArrayList<Currency>) {
        currencyList.apply {
            clear()
            addAll(list)
        }
        binding.edtCurrency.setArrayAdapter(currencyList.map { it.name } as ArrayList<String>)
        binding.edtCurrency.setOnItemClickListener { _, _, position, _ ->
            currency = currencyList[position].name
            binding.tvError.hide()
        }
    }

    private fun setCountryList(list: ArrayList<Country>) {
        countryList.apply {
            clear()
            addAll(list)
        }
        binding.edtCountry.setArrayAdapter(countryList.map { it.name } as ArrayList<String>)
        binding.edtCountry.setOnItemClickListener { _, _, position, _ ->
            country = countryList[position].name
            binding.tvError.hide()
        }
    }

    private fun getPayoutData() {
        viewModel.getPayoutData().observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(it.message ?: getString(R.string.something_went_wrong, getString(R.string.try_again)))
                Status.LOADING -> {}
                Status.SUCCESS -> {
                    val data = it.data?.message
                    setMethodList(data?.payout_methods ?: ArrayList())
                    setCountryList(data?.countries ?: ArrayList())
                    setCurrencyList(data?.currencies ?: ArrayList())
                }
            }
        }
    }

}