package com.flashbid.luv.repositories

import androidx.lifecycle.LiveData
import androidx.lifecycle.liveData
import com.flashbid.luv.models.battle.TopGiftersResponse
import com.flashbid.luv.models.remote.AlertsResponse
import com.flashbid.luv.models.remote.BattleDetailsResponse
import com.flashbid.luv.models.remote.BattleRematchRequest
import com.flashbid.luv.models.remote.BattleResponseRequest
import com.flashbid.luv.models.remote.BattleResultResponse
import com.flashbid.luv.models.remote.CounterRequest
import com.flashbid.luv.models.remote.MessageResponse
import com.flashbid.luv.models.remote.SendGiftRequest
import com.flashbid.luv.models.remote.ShareInviteRequest
import com.flashbid.luv.models.remote.UpdateVideoStatusRequest
import com.flashbid.luv.network.RemoteDataSource
import com.flashbid.luv.network.Resource
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow

class BattleRepository(private val remoteDataSource: RemoteDataSource) {

    fun endBattle(channelId: String): Flow<MessageResponse?> = flow {
        val response = remoteDataSource.endBattle(channelId).data
        emit(response)
    }

    fun battleRematch(request: BattleRematchRequest): Flow<MessageResponse?> = flow {
         val response = remoteDataSource.battleRematch(request).data
        emit(response)
    }

    fun battleResult(channel: String): Flow<BattleResultResponse?> = flow {
        val response = remoteDataSource.battleResult(channel).data
        emit(response)
    }

    fun battleRequest(request: BattleResponseRequest): LiveData<Resource<MessageResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.battleRequest(request)
            emit(response)
        }

    fun getBattleDetails(channel: String): Flow<BattleDetailsResponse?> = flow {
        val response = remoteDataSource.getBattleDetails(channel).data
        emit(response)
    }

    fun counter(request: CounterRequest): LiveData<Resource<MessageResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.counter(request)
            emit(response)
        }

    fun joinLive(channel: String, inviter: String): Flow<BattleDetailsResponse?> = flow {
        val response = remoteDataSource.joinLive(channel, inviter).data
        emit(response)
    }

    fun shareInvite(request: ShareInviteRequest): LiveData<Resource<MessageResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.shareInvite(request)
            emit(response)
        }

    fun getTopGifts(channelId: String, userID: Int): Flow<TopGiftersResponse?> = flow {
        val response = remoteDataSource.getTopGifts(channelId, userID).data
        emit(response)
    }

    fun getAlerts(): LiveData<Resource<AlertsResponse>> = liveData(Dispatchers.IO) {
        emit(Resource.loading(null))
        val response = remoteDataSource.getAlerts()
        emit(response)
    }
    fun updateVideoStatus(id: Int,
                          request: UpdateVideoStatusRequest
    ): LiveData<Resource<MessageResponse>> = liveData(Dispatchers.IO) {
        emit(Resource.loading(null))
        val response = remoteDataSource.updateVideoStatus(id,
            request
        )
        emit(response)
    }

    fun battleResponse(request: BattleResponseRequest): LiveData<Resource<MessageResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.battleResponse(request)
            emit(response)
        }

    fun leaveBattle(): Flow<MessageResponse?> = flow {
        val response = remoteDataSource.leaveBattle().data
        emit(response)
    }

    fun leaveBattleAudience(channel: String, inviter: String): Flow<BattleDetailsResponse?> = flow {
        val response = remoteDataSource.leaveBattleAudience(channel, inviter).data
        emit(response)
    }

    fun sendGift(channel: SendGiftRequest): Flow<MessageResponse?> = flow {
        val response = remoteDataSource.sendGift(channel).data
        emit(response)
    }

}