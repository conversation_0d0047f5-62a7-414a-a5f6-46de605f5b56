package com.flashbid.luv.ui.fragments.battle

import android.app.Dialog
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.ViewGroup
import androidx.fragment.app.DialogFragment
import com.flashbid.luv.R
import com.flashbid.luv.databinding.PopupGiftReceivedBinding
import com.flashbid.luv.extensions.hide
import com.flashbid.luv.extensions.show
import com.flashbid.luv.models.battle.UnifiedStreamMessage
import com.flashbid.luv.models.remote.ItemGift
import com.flashbid.luv.util.loadImageFromUrl
import com.flashbid.luv.viewmodels.BattleViewModel

class GiftPopupDialogFragment : DialogFragment() {

    private lateinit var itemGift: UnifiedStreamMessage.GiftMessage
    private lateinit var giftItem: ItemGift
    private lateinit var onCounterClick: () -> Unit
    private lateinit var userRole:  BattleViewModel.UserRole
    private var isCounterShow = false

    private val handler = Handler(Looper.getMainLooper())
    private val dismissRunnable = Runnable {
         dismiss()
    }

    companion object {
        fun newInstance(
            giftMessage: UnifiedStreamMessage.GiftMessage,
            giftItem: ItemGift,
            userRole: BattleViewModel.UserRole,
            isCounterShown: Boolean,
            function: () -> Unit
        ): GiftPopupDialogFragment {
            return GiftPopupDialogFragment().apply {
                this.itemGift = giftMessage
                this.giftItem = giftItem
                this.userRole = userRole
                this.isCounterShow = isCounterShown
                this.onCounterClick = function
            }
        }
    }

    private var _binding: PopupGiftReceivedBinding? = null
    private val binding get() = _binding!!

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        _binding = PopupGiftReceivedBinding.inflate(layoutInflater, null, false)

        val dialog = Dialog(requireContext())
        dialog.setContentView(binding.root)

        // Set the dialog window properties
        dialog.window?.apply {
            setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            setLayout(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT)
            setCancelable(true)
        }

        // Bind the gift data to the views
        binding.apply {

            giftValue.text = itemGift.giftMessage?.value.toString()
            giftIcon.loadImageFromUrl(itemGift.giftMessage?.photo)
            giftInfo.text = "${itemGift.giftMessage?.senderName} ${getString(R.string.sent)}\n${itemGift.giftMessage?.name}!"
            counterButton.setOnClickListener {
                onCounterClick.invoke()
                dialog.dismiss()
            }

            if (userRole == BattleViewModel.UserRole.AUDIENCE
                && isCounterShow) {
                counterButton.show()
            } else {
                counterButton.hide()
            }
        }
        handler.postDelayed(dismissRunnable, 5000L)

        return dialog
    }

    override fun onDestroyView() {
        super.onDestroyView()
        handler.removeCallbacks(dismissRunnable)
        _binding = null
    }

}