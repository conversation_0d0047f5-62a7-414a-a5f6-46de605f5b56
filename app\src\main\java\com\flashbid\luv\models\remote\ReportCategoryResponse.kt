package com.flashbid.luv.models.remote

data class ReportCategoryResponse(
    val error: <PERSON><PERSON>an,
    val message: List<Message>,
    val pagintion: Pagintion
) {
    data class Message(
        val category_name: String,
        val create_at: String,
        val id: Int,
        val update_at: String
    )

    data class Pagintion(
        val limit: Int,
        val page: Int,
        val total_count: Int
    )
}