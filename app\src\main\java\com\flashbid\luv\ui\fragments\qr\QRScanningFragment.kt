package com.flashbid.luv.ui.fragments.qr

import android.annotation.SuppressLint
import android.app.Activity
import android.app.AlertDialog
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.BitmapFactory
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.text.Html
import android.view.SurfaceHolder
import android.view.View
import android.widget.Toast
import androidx.activity.result.ActivityResult
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.flashbid.luv.R
import com.flashbid.luv.data.local.AppPreferences
import com.flashbid.luv.databinding.DialogOpenCrateBinding
import com.flashbid.luv.databinding.DialogOpenCrateKeyBinding
import com.flashbid.luv.databinding.DialogUnlockChestBinding
import com.flashbid.luv.databinding.FragmentQRScanningBinding
import com.flashbid.luv.extensions.*
import com.flashbid.luv.network.Status
import com.flashbid.luv.util.Constants
import com.flashbid.luv.util.DeviceUtils.hideSoftInput
import com.flashbid.luv.util.FileUtils.requestGalleryPermissions
import com.flashbid.luv.util.HistoryMapping
import com.flashbid.luv.util.RequestCodes
import com.flashbid.luv.viewmodels.CodesViewModel
import com.flashbid.luv.viewmodels.GiftsViewModel
import com.google.android.gms.vision.CameraSource
import com.google.android.gms.vision.Detector
import com.google.android.gms.vision.Frame
import com.google.android.gms.vision.barcode.Barcode
import com.google.android.gms.vision.barcode.BarcodeDetector
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.io.IOException

class QRScanningFragment : Fragment(R.layout.fragment_q_r_scanning) {

    private val binding by viewBinding(FragmentQRScanningBinding::bind)
    private val pref by inject<AppPreferences>()
    private var cameraSource: CameraSource? = null
    private var barcodeDetector: BarcodeDetector? = null
    private var scannedValue = ""
    private var scanning = false
    private lateinit var surfaceHolder: SurfaceHolder
    private val codesViewModel: CodesViewModel by viewModel()
    private val giftViewModel: GiftsViewModel by viewModel()
    private var crateType = ""+HistoryMapping.ACTION.CRATE

    private val imageResultLauncher: ActivityResultLauncher<Intent> =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult ->
            if (result.resultCode == Activity.RESULT_OK && result.data != null) {
                val selectedImage = result.data!!.data
                if (selectedImage != null) {
                    val stream = requireActivity().contentResolver.openInputStream(selectedImage)
                    val bitmap = BitmapFactory.decodeStream(stream)
                    stream?.close()
                    val imageDetector = BarcodeDetector.Builder(requireContext()).build()
                    val frame = Frame.Builder().setBitmap(bitmap).build()
                    val barcodes = imageDetector.detect(frame)
                    if (barcodes.size() > 0) {
                        val qrCode = barcodes.valueAt(0)
                        val qrCodeValue = qrCode.displayValue

                        if (qrCodeValue.contains("login|")) {
                            redirectToNetworkLogin(qrCodeValue)
                        } else {
                            getDetails(qrCodeValue)
                        }

                    } else snackBar("invalid/no qr code found")
                }
            }
        }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        requestGalleryPermissions()

        binding.photoLib.setOnClickWithDebounce {
            cameraSource?.stop()
            val intent = Intent(Intent.ACTION_PICK)
            intent.type = "image/*"
            imageResultLauncher.launch(intent)
        }

        if (ContextCompat.checkSelfPermission(requireContext(), android.Manifest.permission.CAMERA)
            != PackageManager.PERMISSION_GRANTED
        ) askForCameraPermission()
        else setupControls()

        Constants.CRATE_TYPE.observe(viewLifecycleOwner) {

            if (pref.isLoggedIn)
                crateType = Constants.CRATE_TYPE.value.toString()
        }
    }

    private fun setupControls() {
        barcodeDetector =
            BarcodeDetector.Builder(requireContext()).setBarcodeFormats(Barcode.ALL_FORMATS).build()

        cameraSource = barcodeDetector?.let {
            CameraSource.Builder(requireContext(), it)
                .setRequestedPreviewSize(1920, 1080)
                .setAutoFocusEnabled(true)
                .build()
        }

        binding.cameraSurfaceView.holder.addCallback(object : SurfaceHolder.Callback {
            @SuppressLint("MissingPermission")
            override fun surfaceCreated(holder: SurfaceHolder) {
                try {
                    //Start preview after 1s delay
                    surfaceHolder = holder
                    cameraSource?.start(holder)
                } catch (e: IOException) {
                    e.printStackTrace()
                }
            }

            @SuppressLint("MissingPermission")
            override fun surfaceChanged(
                holder: SurfaceHolder,
                format: Int,
                width: Int,
                height: Int
            ) {
                try {
                    cameraSource?.start(holder)
                    surfaceHolder = holder
                } catch (e: IOException) {
                    e.printStackTrace()
                }
            }

            override fun surfaceDestroyed(holder: SurfaceHolder) {
                surfaceHolder = holder
                cameraSource?.stop()
            }
        })


        barcodeDetector?.setProcessor(object : Detector.Processor<Barcode> {
            override fun release() {
                Toast.makeText(context, "Scanner has been closed", Toast.LENGTH_SHORT)
                    .show()
            }

            override fun receiveDetections(detections: Detector.Detections<Barcode>) {
                lifecycleScope.launch(Dispatchers.Main) {
                    val barcodes = detections.detectedItems
                    if (barcodes.size() >= 1) {
                        scannedValue = barcodes.valueAt(0).rawValue
                        //Don't forg        et to add this line printing value or finishing activity must run on main thread
                        if (scannedValue.isNotEmpty() && !scanning) {
                            scanning = true
                            if (scannedValue.contains("login|")) {
                                redirectToNetworkLogin(scannedValue)
                            } else {
                                getDetails(scannedValue)
                            }
                        }
                    }
                }
            }
        })
    }

    private fun redirectToNetworkLogin(scannedValue: String) {

        val action = QRScanningFragmentDirections.actionScanFragmentToAuthorizeFragment(scannedValue)
        findNavController().navigate(action)
    }

    @SuppressLint("MissingPermission")
    private fun getDetails(scannedValue: String) {
        println("scannedValue is  "+ scannedValue)
        codesViewModel.scanCode(scannedValue).observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> {
                    scanning = false
                    snackBar(
                        it.message ?: getString(
                            R.string.something_went_wrong,
                            getString(R.string.try_again)
                        )
                    )
                }

                Status.LOADING -> {}
                Status.SUCCESS -> {
                    val item = it.data?.data
                    println("item is " + item)
                    if (item != null) {
                        when (it.data.qr_type) {
                            "user" -> {
                                val action =
                                    QRScanningFragmentDirections.actionScanFragmentToUserProfileFragment(
                                        item.id!!
                                    )
                                findNavController().navigate(action)
                            }

                            "chest" -> {
                                showKeyCrate(
                                    item.sender_username ?: "no name",
                                    scannedValue,
                                    item.need_key,
                                    true
                                )
                            }

                            "key_chest" -> {
                                showKeyCrate(
                                    item.sender_username ?: "no name",
                                    scannedValue,
                                    item.need_key,
                                    true
                                )
                            }

                            "key_crate" -> {
                                showKeyCrate(
                                    item.sender_username ?: "no name",
                                    scannedValue,
                                    item.need_key,
                                    false
                                )
                            }

                            "crate" -> {
                                //showNoKeyCrate(item.company_name, item.amount, scannedValue)
                                unlockCrate(scannedValue)
                            }

                            "userWeb" -> {
                                val action =
                                    QRScanningFragmentDirections.actionScanFragmentToUserProfileFragment(
                                        item.id!!
                                    )
                                findNavController().navigate(action)
                            }

                            "auth" -> {
                                showKeyCrate(
                                    item.sender_username ?: "no name",
                                    scannedValue,
                                    item.need_key,
                                    true
                                )
                            }
                        }
                    } else {
                        scanning = false
                        snackBar(getString(R.string.qr_not_found))
                    }
                }
            }
        }
    }

    private fun showNewDialog(senderName: String?, companyName:String?, amount:Int?) {
        val builder = AlertDialog.Builder(requireContext())
        val binding = DialogUnlockChestBinding.inflate(layoutInflater)
        val newDialog = builder.create()

        // Set the dialog view and properties
        binding.textView29.text = senderName + " " + getString(R.string.thanks_you_for_your_support)

        // Format the thank you message
        val thankYouText = getString(R.string.thanks_you_for_your_support, senderName ?: "Someone")
        binding.textView29.text = Html.fromHtml(thankYouText, Html.FROM_HTML_MODE_LEGACY)



// Set text for the sponsored message using the `companyName` and `amount`
        val sponsoredText = getString(R.string.sponsored_by, companyName ?: "Unknown") // Fallback to "Unknown" if null
        binding.textView30.text = Html.fromHtml(sponsoredText, Html.FROM_HTML_MODE_LEGACY)

        // Set text for the sponsored message using the `companyName` and `amount`
        val amountText = amount.toString() + " " + "LUV" // Fallback to "Unknown" if null
        binding.tvAmount.text = amountText

        newDialog.setView(binding.root)
        newDialog.setCancelable(true)

        // Set button listeners (if needed)
        binding.btnOpen.setOnClickWithDebounce {
            newDialog.dismiss()
        }

        newDialog.show()
    }

    private fun unlockChest(key: String, code: String, needKey: Int?, senderName: String, dialog: AlertDialog ) {
        giftViewModel.unlockChest(code, key, needKey ?: 0).observe(viewLifecycleOwner) {

            var companyName:String? =""
            var amount:Int?=0
            when (it.status) {
                Status.ERROR -> {
                    scanning = true
                    snackBar(
                        it.message ?: getString(
                            R.string.something_went_wrong,
                            getString(R.string.try_again)
                        )
                    )
                }

                Status.LOADING -> {}
                Status.SUCCESS -> {
                    if(senderName != null && senderName.length > 0 ) {
                        dialog.dismiss()




                         companyName = it.data?.data?.company_name
                         amount = it.data?.data?.amount

                        println("Company Name: $companyName")
                        println("Amount: $amount")
                        println("this is the dialog  "  + it)
                        val text = senderName + " " + getString(R.string.thanks_you_for_your_support)
//                        snackBar(senderName ?: getString(R.string.done))
                    } else {
                        snackBar(it.data?.message ?: it.message ?: getString(R.string.done))
                    }
                    // Create a new dialog for chest unlocking success
                    showNewDialog(senderName,companyName,amount)

                    dialog.dismiss()
                    findNavController().popBackStack()
                }
            }
        }
    }

    private fun unlockKeyCrate(key: String, code: String, needKey: Int?, senderName: String, dialog: AlertDialog ) {

        giftViewModel.unlockKeyCrate(code, key).observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> {
                    scanning = true
                    snackBar(

                        it.message ?: getString(
                            R.string.something_went_wrong,
                            getString(R.string.try_again)
                        )
                    )
                }

                Status.LOADING -> {}
                Status.SUCCESS -> {

                    if(senderName != null && senderName.length > 0 ) {

                        dialog.dismiss()
                        val text = it.data?.data?.company_name + " " + getString(R.string.thanks_you_for_your_support)
                        val amount = "" + it.data?.data?.amount + " " + getString(R.string.luv)
                        //snackBar(text ?: getString(R.string.done))
                        showResultKeyCrate(text, amount, false)
                    } else {
                        snackBar(it.data?.message ?: it.message ?: getString(R.string.done))
                    }
                    //dialog.dismiss()
                    //findNavController().popBackStack()
                }
            }
        }
    }

    private fun unlockCrate(code: String) {

        var isBonus = 0
        isBonus = if (crateType == ""+HistoryMapping.ACTION.QUEST_CRATE) {
            1
        } else {
            0
        }
        giftViewModel.unlockCrate(code, crateType, isBonus).observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR ->
                {
                    scanning = true
                    snackBar(
                    it.message ?: getString(
                        R.string.something_went_wrong,
                        getString(R.string.try_again)
                    )
                    )
                }


                Status.LOADING -> {}
                Status.SUCCESS -> {
                    //snackBar(getString(R.string.awesome))
                    //findNavController().popBackStack()

                    val text = it.data?.message?.company_name + " " + getString(R.string.has_left_you_no_key)
                    val amount = "" + it.data?.message?.amount + " " + getString(R.string.luv)
                    //snackBar(text ?: getString(R.string.done))
                    showNoKeyCrate(it.data?.message?.company_name, it.data?.message?.amount, scannedValue)
                }
            }
        }
    }

    private fun showNoKeyCrate(senderName: String?, amount: Int?, scannedValue: String) {
        val builder = AlertDialog.Builder(requireContext())
        val view = DialogOpenCrateBinding.inflate(layoutInflater)
        val dialog = builder.create()

        dialog.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))

        view.textView29.text =
            getString(R.string.has_left_you_no_key, senderName)

        view.tvAmount.text = buildString {
            append(amount.toString())
            append(" LUV")
        }
        view.tvAmount.show()

        view.imageView8.setOnClickWithDebounce {
            scanning = false
            dialog.dismiss()
        }

        view.btnOpen.setOnClickWithDebounce {
            //unlockCrate(scannedValue)
            scanning = false
            dialog.dismiss()
            findNavController().popBackStack()
        }


        dialog.setView(view.root)
        dialog.setCancelable(false)
        dialog.show()
    }

    private fun showResultKeyCrate(senderName: String, scannedValue: String, isChest: Boolean) {
        val builder = AlertDialog.Builder(requireContext())
        val binding = DialogOpenCrateKeyBinding.inflate(layoutInflater)
        val dialog = builder.create()

        dialog.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))

        binding.imageView8.setOnClickWithDebounce {
            scanning = false
            dialog.dismiss()
        }

        binding.textView29.text = senderName
        binding.priceText.text = scannedValue
        binding.priceText.show()
        binding.edtKey.hide()
        binding.btnOpen.text = getString(R.string.awesome)

        binding.btnOpen.setOnClickWithDebounce {
            scanning = false
            dialog.dismiss()
            findNavController().popBackStack()
        }
        dialog.setView(binding.root)
        dialog.setCancelable(false)
        dialog.show()
    }

//    private fun showKeyCrate(senderName: String, scannedValue: String, needKey: Int?, isChest: Boolean) {
//        val builder = AlertDialog.Builder(requireContext())
//        val binding = DialogOpenCrateKeyBinding.inflate(layoutInflater)
//        val dialog = builder.create()
//
//        dialog.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
//
//        binding.imageView8.setOnClickWithDebounce {
//            scanning = false
//            dialog.dismiss()
//        }
//
//        if (needKey == 0) {
//            println("this is working")
//            binding.textView29.text =
//                getString(R.string.has_sent, senderName)
//            binding.edtKey.hide()
//        } else {
//            binding.textView29.text =
//                getString(R.string.has_left_you_a_luv_crate_nenter_key_to_open, senderName)
//            binding.edtKey.show()
//        }
//
//        binding.btnOpen.setOnClickWithDebounce {
//            if (needKey == 0 || binding.edtKey.textToString() != "") {
//                println("here 1")
//
//                if(isChest) {
//                    println("here 2")
//                    scanning = false
//                    hideSoftInput(binding.root)
//                    unlockChest(binding.edtKey.textToString(), scannedValue, needKey, senderName, dialog)
//
//                } else {
//                    println("here 3")
//                    scanning = false
//                    hideSoftInput(binding.root)
//                    unlockKeyCrate(binding.edtKey.textToString(), scannedValue, needKey, senderName, dialog)
//                }
//            } else snackBar(getString(R.string.fill_all_details))
//        }
//        dialog.setView(binding.root)
//        dialog.setCancelable(false)
//        dialog.show()
//    }


    private var isDialogShown = false  // Flag to track dialog state

    private fun showKeyCrate(senderName: String, scannedValue: String, needKey: Int?, isChest: Boolean) {
        // Prevent opening the dialog again if it's already shown
        if (isDialogShown) {
            return
        }

        val builder = AlertDialog.Builder(requireContext())
        val binding = DialogOpenCrateKeyBinding.inflate(layoutInflater)
        val dialog = builder.create()

        // Set dialog background
        dialog.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))

        // Track that the dialog is shown
        isDialogShown = true

        // Dismiss the dialog and reset flag when the user clicks on the image
        binding.imageView8.setOnClickWithDebounce {
            scanning = false
            dialog.dismiss()
            isDialogShown = false  // Reset the flag when dialog is dismissed
        }

        if (needKey == 0) {
            println("this is working")
            binding.textView29.text =
                getString(R.string.has_sent, senderName)
            binding.edtKey.hide()
        } else {
            binding.textView29.text =
                getString(R.string.has_left_you_a_luv_crate_nenter_key_to_open, senderName)
            binding.edtKey.show()
        }

        binding.btnOpen.setOnClickWithDebounce {
            if (needKey == 0 || binding.edtKey.textToString() != "") {
                println("here 1")

                if (isChest) {
                    println("here 2")
                    scanning = false
                    hideSoftInput(binding.root)
                    unlockChest(binding.edtKey.textToString(), scannedValue, needKey, senderName, dialog)

                } else {
                    println("here 3")
                    scanning = false
                    hideSoftInput(binding.root)
                    unlockKeyCrate(binding.edtKey.textToString(), scannedValue, needKey, senderName, dialog)
                }
            } else snackBar(getString(R.string.fill_all_details))
        }

        dialog.setView(binding.root)
        dialog.setCancelable(false)
        dialog.show()
    }


    override fun onResume() {
        super.onResume()
        scanning = false
        if (barcodeDetector == null || cameraSource == null) setupControls()
    }

    private fun askForCameraPermission() {
        ActivityCompat.requestPermissions(
            requireActivity(),
            arrayOf(android.Manifest.permission.CAMERA),
            RequestCodes.CAMERA_PERMISSION
        )
    }

    @Deprecated("Deprecated in Java")
    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == RequestCodes.CAMERA_PERMISSION && grantResults.isNotEmpty()) {
            if (grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                setupControls()
            } else {
                Toast.makeText(context, "Permission Denied", Toast.LENGTH_SHORT).show()
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        cameraSource?.stop()
    }

}