<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/redgradstart"
    tools:context=".ui.fragments.onboarding.SplashFragment">

<!--    <com.google.android.material.card.MaterialCardView-->
<!--        android:layout_width="@dimen/_100sdp"-->
<!--        android:layout_height="@dimen/_100sdp"-->
<!--        app:cardCornerRadius="@dimen/_50sdp"-->
<!--        android:alpha="0.6"-->
<!--        app:cardElevation="0dp"-->
<!--        app:strokeWidth="0dp"-->
<!--        app:layout_constraintBottom_toBottomOf="parent"-->
<!--        app:layout_constraintEnd_toEndOf="parent"-->
<!--        app:layout_constraintHorizontal_bias="0.498"-->
<!--        app:layout_constraintStart_toStartOf="parent"-->
<!--        app:layout_constraintTop_toTopOf="parent"-->
<!--        app:layout_constraintVertical_bias="0.244"/>-->

    <ImageView
        android:id="@+id/imageView10"
        android:layout_width="@dimen/_130sdp"
        android:layout_height="@dimen/_130sdp"
        android:src="@drawable/app_icon"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.498"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.244" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="LUV \nNetwork"
        android:textSize="@dimen/_30sdp"
        android:textColor="@color/white"
        android:layout_marginTop="@dimen/_30sdp"
        android:gravity="center"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="@+id/imageView10"
        app:layout_constraintStart_toStartOf="@+id/imageView10"
        app:layout_constraintTop_toBottomOf="@+id/imageView10" />

</androidx.constraintlayout.widget.ConstraintLayout>