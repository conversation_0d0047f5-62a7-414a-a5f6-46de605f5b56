<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.fragments.auth.AuthSelectFragment">

    <TextView
        android:id="@+id/textView"
        style="@style/h1"
        android:layout_marginTop="@dimen/_30sdp"
        android:text="@string/sign_in_to_start"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/textView2"
        style="@style/subtitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_20sdp"
        android:layout_marginTop="@dimen/_60sdp"
        android:background="@drawable/bg_button_gray"
        android:gravity="center"
        android:paddingVertical="@dimen/_10sdp"
        android:paddingStart="@dimen/_10sdp"
        android:paddingEnd="@dimen/_30sdp"
        android:text="@string/google"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textView"
        app:drawableStartCompat="@drawable/google_color" />

    <TextView
        android:id="@+id/tvApple"
        style="@style/subtitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_20sdp"
        android:background="@drawable/bg_button_gray"
        android:gravity="center"
        android:paddingVertical="@dimen/_10sdp"
        android:paddingStart="@dimen/_10sdp"
        android:paddingEnd="@dimen/_25sdp"
        android:text="@string/apple"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textView2"
        app:drawableStartCompat="@drawable/apple" />

    <TextView
        android:id="@+id/textView3"
        style="@style/subtitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_20sdp"
        android:background="@drawable/bg_button_gray"
        android:gravity="center"
        android:transitionGroup="true"
        android:transitionName="@string/login_transition"
        android:paddingVertical="@dimen/_10sdp"
        android:paddingStart="@dimen/_10sdp"
        android:paddingEnd="@dimen/_30sdp"
        android:text="@string/email"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvApple"
        app:drawableStartCompat="@drawable/sms" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/_10sdp"
        android:gravity="center"
        android:text="@string/don_t_have_an_account"
        android:textColor="@color/gray"
        android:textSize="14sp"
        app:layout_constraintBottom_toTopOf="@+id/btnRegister"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent" />

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btnRegister"
        style="@style/button"
        android:layout_margin="@dimen/_50sdp"
        android:text="@string/sign_up"
        android:transitionGroup="true"
        android:transitionName="@string/signup_transition"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>