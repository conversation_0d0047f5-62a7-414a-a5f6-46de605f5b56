package com.flashbid.luv.util

import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Point
import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import android.util.AttributeSet
import android.view.*
import android.widget.FrameLayout
import android.widget.LinearLayout
import android.widget.RadioButton
import androidx.appcompat.widget.AppCompatRadioButton
import androidx.core.content.ContextCompat
import androidx.core.widget.TextViewCompat
import com.flashbid.luv.R
import kotlin.math.abs
import kotlin.math.max
import kotlin.math.min

class TabsRadioGroup @JvmOverloads constructor(
    context: Context,
    val attrs: AttributeSet? = null,
    val defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    companion object {
        const val RADIO_GROUP_ANIMATION_DURATION = 100L
    }

    private val radioButtons = ArrayList<RadioButton>()

    var selectedButtonPosition = 0
        private set

    var isDragEnabled = true

    private var elementTitle = ""

    private var isDragAction = false
    private var flingToggleType = FlingToggleType.NONE

    private var prevPoint = Point(0, 0)

    private var minMarginStart = 0
    private var maxMarginStart = 0

    private var buttonWidth = 0
    var buttonHeight = DeviceUtils.convertDpToPx(context, 41f)
    var selectorHeight = DeviceUtils.convertDpToPx(context, 45f)

    var typographyStyle = R.style.text

    private val gestureDetector: GestureDetector

    private val maxVelocityToSwitchButton = 3000

    private var isInteractionLocked = false
    private var wasActionDownBeforeLock = false

    private var innerRadioGroup: LinearLayout
    private var radioButtonSelector: View

    @Suppress("ABSTRACT_MEMBER_NOT_IMPLEMENTED")
    private val flingListener = object : GestureDetector.SimpleOnGestureListener() {
        override fun onFling(
            e1: MotionEvent?,
            e2: MotionEvent,
            velocityX: Float,
            velocityY: Float
        ): Boolean {
            if (isDragEnabled && isDragAction && abs(velocityX) >= maxVelocityToSwitchButton) {
                if (velocityX > 0) {
                    if (selectedButtonPosition < radioButtons.size - 1) {
                        flingToggleType = FlingToggleType.NEXT
                    }
                } else {
                    if (selectedButtonPosition > 0) {
                        flingToggleType = FlingToggleType.PREVIOUS
                    }
                }
            }
            return true
        }
    }

    var lockRecyclerViewScrollCallback: ((lock: Boolean) -> Unit)? = null
    var onCheckedChangeListener: ((position: Int) -> Unit)? = null

    init {
        View.inflate(context, R.layout.view_animated_tab_layout, this)

        innerRadioGroup = findViewById(R.id.innerRadioGroup)
        radioButtonSelector = findViewById(R.id.radioButtonSelector)

        innerRadioGroup.background = DrawableUtils.createRoundedRectangleBackgroundWithColor(
            context, R.color.bgGray, android.R.color.transparent, 16
        )

        gestureDetector = GestureDetector(context, flingListener)
    }

    fun init(buttonTitles: List<String>, position: Int, elementTitle: String = "") {
        this.elementTitle = elementTitle

        radioButtons.clear()

        innerRadioGroup.removeAllViews()

        buttonTitles.forEach {
            val radioButton = createRadioButton(it)
            radioButtons.add(radioButton)
            innerRadioGroup.addView(radioButton)
        }

        if (position in 0 until radioButtons.size) {
            selectedButtonPosition = position

            radioButtons.forEachIndexed { index, radioButton ->
                radioButton.isChecked = selectedButtonPosition == index
            }
        }

        val layoutParams = radioButtonSelector.layoutParams as LayoutParams

        layoutParams.marginStart = selectedButtonPosition * buttonWidth

        radioButtonSelector.layoutParams = layoutParams
    }

    override fun onInterceptTouchEvent(event: MotionEvent): Boolean {
        val point = Point(event.x.toInt(), event.y.toInt())

        if (!isInteractionLocked) {
            gestureDetector.onTouchEvent(event)
        }

        return when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                if (!isInteractionLocked) {
                    wasActionDownBeforeLock = true
                    lockRecyclerViewScrollCallback?.invoke(true)

                    prevPoint = point
                    flingToggleType = FlingToggleType.NONE
                    isDragAction = selectedButtonPosition == computeTappedTabPosition(prevPoint)
                }
                false
            }

            MotionEvent.ACTION_MOVE -> {
                if (isDragEnabled && !isInteractionLocked && wasActionDownBeforeLock) {
                    if (isDragAction) {
                        val layoutParams = radioButtonSelector.layoutParams as LayoutParams
                        val newMarginStart = layoutParams.marginStart + point.x - prevPoint.x

                        layoutParams.marginStart = when {
                            newMarginStart in minMarginStart..maxMarginStart -> newMarginStart

                            newMarginStart < minMarginStart -> minMarginStart

                            else -> maxMarginStart
                        }

                        radioButtonSelector.layoutParams = layoutParams
                        prevPoint = point
                    }
                }
                false
            }

            MotionEvent.ACTION_UP,
            MotionEvent.ACTION_CANCEL -> {
                if (!isInteractionLocked && wasActionDownBeforeLock) {
                    isInteractionLocked = true
                    wasActionDownBeforeLock = false

                    var position =
                        if (isDragAction) computeHoveredBySelectorTabPosition() else computeTappedTabPosition(
                            point
                        )

                    if (flingToggleType != FlingToggleType.NONE) {
                        position = if (flingToggleType == FlingToggleType.NEXT) {
                            min(position + 1, radioButtons.size - 1)
                        } else {
                            max(position - 1, 0)
                        }
                    }

                    toggle(position)

                    lockRecyclerViewScrollCallback?.invoke(false)
                }
                true
            }

            else -> {
                if (!isInteractionLocked) {
                    lockRecyclerViewScrollCallback?.invoke(false)
                }
                false
            }
        }
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)

        if (radioButtons.isNotEmpty() && buttonWidth == 0) {
            updateSelector()
        }

        setMeasuredDimension(measuredWidth, measuredHeight)
    }

    // need to update selector view size if parent view is constraint layout
    // incorrect calculations in constraint layout after first measuring
    fun updateSelector() {
        buttonWidth = measuredWidth / radioButtons.size

        maxMarginStart = buttonWidth * (radioButtons.size - 1)

        val layoutParams = radioButtonSelector.layoutParams as LayoutParams

        layoutParams.marginStart = selectedButtonPosition * buttonWidth
        layoutParams.width = buttonWidth
        layoutParams.height = buttonHeight

        radioButtonSelector.layoutParams = layoutParams
    }

    fun toggle() {
        if (radioButtons.size != 2) {
            return
        }
        toggle(1 - selectedButtonPosition)
    }

    private fun computeHoveredBySelectorTabPosition(): Int {
        val selectorCenterX = radioButtonSelector.x + radioButtonSelector.width / 2
        return (selectorCenterX.toInt() - (layoutParams as MarginLayoutParams).marginStart) / buttonWidth
    }

    private fun computeTappedTabPosition(point: Point): Int {
        return (point.x - (layoutParams as MarginLayoutParams).marginStart) / buttonWidth
    }

    private fun toggle(position: Int) {
        if (selectedButtonPosition == position && !hasRadioButtonSelectorOffsetChanged()) {
            isInteractionLocked = false
            return
        }

        selectedButtonPosition = position

        val layoutParams = radioButtonSelector.layoutParams as LayoutParams

        val animator = ValueAnimator.ofInt(layoutParams.marginStart, position * buttonWidth)

        var prev = layoutParams.marginStart

        animator.addUpdateListener { animation ->
            layoutParams.marginStart += (animation.animatedValue as Int) - prev
            radioButtonSelector.layoutParams = layoutParams
            prev = animation.animatedValue as Int
        }

        animator.duration = RADIO_GROUP_ANIMATION_DURATION
        animator.start()

        Handler(Looper.getMainLooper()).postDelayed(
            { isInteractionLocked = false }, RADIO_GROUP_ANIMATION_DURATION
        )

        radioButtons.forEachIndexed { index, radioButton ->
            radioButton.isChecked = selectedButtonPosition == index
            radioButton.contentDescription = generateContentDescription(radioButton)
        }

        onCheckedChangeListener?.invoke(selectedButtonPosition)
    }

    private fun hasRadioButtonSelectorOffsetChanged(): Boolean {
        return (radioButtonSelector.layoutParams as MarginLayoutParams).marginStart != selectedButtonPosition * buttonWidth
    }

    private fun createRadioButton(title: String): AppCompatRadioButton {
        val radioButton = AppCompatRadioButton(context)

        radioButton.id = abs(title.hashCode())

        val params =
            LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, buttonHeight, 1f)
        val margin = DeviceUtils.convertDpToPx(context, 5f)
        params.setMargins(margin, margin, margin, margin)
        radioButton.layoutParams = params

        radioButton.gravity = Gravity.CENTER
        TextViewCompat.setTextAppearance(radioButton, typographyStyle)
        radioButton.buttonDrawable = null

        radioButton.maxLines = 1
        radioButton.ellipsize = TextUtils.TruncateAt.END

        radioButton.background = DrawableUtils.createRoundedRectangleBackgroundSelector(
            context, R.color.black, android.R.color.transparent, 16
        )

        radioButton.setTextColor(
            DrawableUtils.createColorStateList(
                android.R.attr.state_checked,
                ContextCompat.getColor(context, R.color.white),
                ContextCompat.getColor(context, R.color.black)
            )
        )

        radioButton.text = title
        radioButton.isChecked = false

        radioButton.contentDescription = generateContentDescription(radioButton)

        return radioButton
    }

    private fun generateContentDescription(button: RadioButton): String {
        return if (!TextUtils.isEmpty(elementTitle)) {
            elementTitle + " Option" + (if (button.isChecked) " Selected: " else ": ") + button.text
        } else {
            "Option" + (if (button.isChecked) " Selected: " else ": ") + button.text
        }
    }

    enum class FlingToggleType {
        NEXT, PREVIOUS, NONE
    }
}