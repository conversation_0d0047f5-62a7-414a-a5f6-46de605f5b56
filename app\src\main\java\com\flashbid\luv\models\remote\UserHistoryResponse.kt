package com.flashbid.luv.models.remote

data class UserHistoryResponse(
    val error: <PERSON><PERSON><PERSON>,
    val message: ArrayList<ItemHistory>
)

data class ItemHistory(
    val action: Int,
    val amount: Int,
    val first_name: String,
    val receiver_user_id: Int,
    val sender_user_id: Int,
    val drop_type: Int,
    val id: Int,
    val last_name: String,
    val currency_amount: String?,
    val photo: String,
    val received_amount: Int,
    val update_at: String,
    val user_id: Int
)
