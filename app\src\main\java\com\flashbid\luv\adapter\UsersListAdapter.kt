package com.flashbid.luv.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.flashbid.luv.R
import com.flashbid.luv.databinding.ItemSearchBinding
import com.flashbid.luv.extensions.*
import com.flashbid.luv.models.remote.UserDetails
import com.flashbid.luv.util.ParamCode
import com.flashbid.luv.util.getColor
import com.flashbid.luv.util.loadImageFromUrl

class UsersListAdapter(
    private val list: MutableList<UserDetails>,
    private val fromProfile: Boolean = false,
    val onClick: (UserDetails) -> Unit,
    val onFollowClick: (UserDetails, Int) -> Unit,
) : RecyclerView.Adapter<UsersListAdapter.ViewHolder>() {

    override fun getItemCount(): Int = list.size

    inner class ViewHolder(val binding: ItemSearchBinding) :
        RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding =
            ItemSearchBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = list[position]

        with(holder.binding) {
            textView12.text = item.first_name + " " + item.last_name
            tvUsername.text = "@" + item.username
            imageView4.loadImageFromUrl(item.photo)
            if (fromProfile) {
                btnFollow.show()
                tvFollow.show()
            } else {
                btnFollow.hide()
                tvFollow.hide()
            }

            if (item.is_followed == ParamCode.USER_FOLLOWED) {
                btnFollow.setUnfollowState()
                tvFollow.text = tvFollow.context.getString(R.string.following)
                tvFollow.setTextColor(tvFollow.getColor(R.color.gray))
            } else {
                btnFollow.setFollowState()
                tvFollow.text = tvFollow.context.getString(R.string.follow)
                tvFollow.setTextColor(tvFollow.getColor(R.color.blue))
            }

            btnFollow.setOnClickWithDebounce {
                onFollowClick(item, item.is_followed ?: 0)
            }
        }

        holder.itemView.setOnClickWithDebounce {
            onClick(item)
        }


    }

    fun refresh() = notifyDataSetChanged()


}