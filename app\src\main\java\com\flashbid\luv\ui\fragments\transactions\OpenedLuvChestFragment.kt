package com.flashbid.luv.ui.fragments.transactions

import android.graphics.Color
import android.os.Bundle
import android.provider.CalendarContract.Colors
import androidx.fragment.app.Fragment
import android.view.View
import androidx.core.content.ContextCompat
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.flashbid.luv.R
import com.flashbid.luv.data.local.AppPreferences
import com.flashbid.luv.databinding.FragmentOpenedLuvChestBinding
import com.flashbid.luv.extensions.convertDate
import com.flashbid.luv.extensions.getTimeInAgo
import com.flashbid.luv.extensions.setOnClickWithDebounce
import com.flashbid.luv.extensions.snackBar
import com.flashbid.luv.extensions.viewBinding
import com.flashbid.luv.models.remote.HistoryDetailResponse
import com.flashbid.luv.network.Status
import com.flashbid.luv.util.HistoryMapping
import com.flashbid.luv.util.getColor
import com.flashbid.luv.util.loadImageFromUrl
import com.flashbid.luv.viewmodels.TransactionViewModel
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel

class OpenedLuvChestFragment : Fragment(R.layout.fragment_opened_luv_chest) {

    private val binding by viewBinding(FragmentOpenedLuvChestBinding::bind)
    private val viewModel by viewModel<TransactionViewModel>()
    private val args by navArgs<OpenedLuvChestFragmentArgs>()
    private val pref by inject<AppPreferences>()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.imageView.setOnClickWithDebounce { findNavController().popBackStack() }

        getHistoryDetail(args.id)
    }

    private fun getHistoryDetail(
        id: String
    ) {
        viewModel.getHistoryDetail(id).observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(
                    it.message ?: getString(
                        R.string.something_went_wrong, getString(R.string.try_again)
                    )
                )

                Status.LOADING -> {}
                Status.SUCCESS -> {
                    setData(it.data)
                }
            }
        }
    }

    private fun setData(data: HistoryDetailResponse?) {
        if (data != null) {

            if (pref.userId == data?.message?.receiver_user_id) {
                binding.tvHearts.text = "+${data.message.amount}"
                binding.textView.text = getString(R.string.luv_chest_received)

                binding.tvFrom.text = ""
            } else {
                binding.tvHearts.text = "-${data.message.amount}"
                binding.tvFrom.text = getString(R.string.to)
                binding.textView.text = getString(R.string.luv_chest_sent)

                val gray = ContextCompat.getColor(requireContext(), R.color.gray)

                binding.tvHearts.setTextColor(gray)
            }



            binding.ivUserImage.loadImageFromUrl(data.message.photo)
            binding.tvName.text = data.message.first_name
            binding.tvDate.text = data.message.update_at.convertDate()
            binding.tvInfo.text = data.message.update_at.getTimeInAgo()
            binding.tvTransaction.text = data.message.id.toString()

            when (data.message.status) {
                HistoryMapping.STATUS.COMPLETED -> binding.tvStatus.text =
                    getString(R.string.completed)

                HistoryMapping.STATUS.PENDING -> binding.tvStatus.text = getString(R.string.pending)
                HistoryMapping.STATUS.VOID -> binding.tvStatus.text = getString(R.string.void_trans)
                HistoryMapping.STATUS.SUSPENDED -> binding.tvStatus.text =
                    getString(R.string.suspended)
            }
        }

    }
}