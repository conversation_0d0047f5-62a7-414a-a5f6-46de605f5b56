package com.flashbid.luv.ui.fragments.qr

import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.flashbid.luv.R
import com.flashbid.luv.data.local.AppPreferences
import com.flashbid.luv.databinding.FragmentQrReceivingBinding
import com.flashbid.luv.extensions.*
import com.flashbid.luv.util.loadImageFromUrl
import com.flashbid.luv.util.shareImage
import org.koin.android.ext.android.inject

class QrReceivingFragment : Fragment(R.layout.fragment_qr_receiving) {

    private val binding by viewBinding(FragmentQrReceivingBinding::bind)
    private val args by navArgs<QrReceivingFragmentArgs>()
    private val pref by inject<AppPreferences>()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.ivUserImage.loadImageFromUrl(pref.photo)

        if (pref.isBrandProfile) binding.tvSponsored.show()
        else  binding.tvSponsored.hide()

        binding.tvSponsored.text = getString(R.string.sponsored_by,pref.userName)

        binding.imageView.setOnClickWithDebounce {
            findNavController().popBackStack()
        }

        binding.tvName.text = getString(R.string.show_me_some_love)

        binding.ivQrGifting.setImageBitmap(generateRoundedQRCode(requireContext(),args.qr,200,20f))

        binding.btnNext.setOnClickWithDebounce {
            shareImage(binding.qrView.createBitmapFromView())
        }

    }

}