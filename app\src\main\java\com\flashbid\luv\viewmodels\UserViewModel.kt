package com.flashbid.luv.viewmodels

import androidx.lifecycle.LiveData
import androidx.lifecycle.ViewModel
import com.flashbid.luv.models.remote.AddFavBrandsRequest
import com.flashbid.luv.models.remote.BalanceResponse
import com.flashbid.luv.models.remote.CustomFollowingResponse
import com.flashbid.luv.models.remote.EmailCodeRequest
import com.flashbid.luv.models.remote.EmailRequest
import com.flashbid.luv.models.remote.FollowRequest
import com.flashbid.luv.models.remote.FollowerResponse
import com.flashbid.luv.models.remote.IndustriesResponse
import com.flashbid.luv.models.remote.LoginAppleRequest
import com.flashbid.luv.models.remote.LoginGoogleRequest
import com.flashbid.luv.models.remote.LoginRequest
import com.flashbid.luv.models.remote.LoginResponse
import com.flashbid.luv.models.remote.MessageResponse
import com.flashbid.luv.models.remote.NameRequest
import com.flashbid.luv.models.remote.ResetRequest
import com.flashbid.luv.models.remote.UpdateProfileRequest
import com.flashbid.luv.models.remote.UpdateVideoStatusRequest
import com.flashbid.luv.models.remote.UploadResponse
import com.flashbid.luv.models.remote.UserDetailsResponse
import com.flashbid.luv.models.remote.UserHistoryResponse
import com.flashbid.luv.models.remote.UserIdRequest
import com.flashbid.luv.network.Resource
import com.flashbid.luv.repositories.UserRepository
import okhttp3.MultipartBody

class UserViewModel(
    private val userRepository: UserRepository
) : ViewModel() {

    fun login(
        email: String, password: String, referralCode: Int?
    ): LiveData<Resource<LoginResponse>> {
        return userRepository.login(LoginRequest(email, password, referral_code = referralCode))
    }

    fun leaveBattle() = userRepository.leaveBattle()

    fun googleLogin(code: String, referralCode: Int?): LiveData<Resource<LoginResponse>> {
        return userRepository.loginGoogle(LoginGoogleRequest(code, referral_code = referralCode))
    }

    fun appleLogin(
        firstName: String, lastName: String, identityToken: String, appleID: String
    ): LiveData<Resource<LoginResponse>> {
        return userRepository.loginApple(
            LoginAppleRequest(
                apple_id = appleID,
                first_name = firstName,
                last_name = lastName,
                identityToken = identityToken
            )
        )
    }

    fun verifyEmail(email: String): LiveData<Resource<MessageResponse>> {
        return userRepository.verifyEmail(EmailRequest(email))
    }

    fun verifyEmailCode(email: String, code: Int): LiveData<Resource<MessageResponse>> {
        return userRepository.verifyEmailCode(EmailCodeRequest(email, code))
    }

    fun register(
        email: String, password: String, referralCode: Int?
    ): LiveData<Resource<LoginResponse>> {
        return userRepository.register(LoginRequest(email, password, referral_code = referralCode))
    }

    fun forgotPassword(email: String): LiveData<Resource<MessageResponse>> {
        return userRepository.forgotPassword(EmailRequest(email))
    }

    fun resetPassword(code: String, password: String): LiveData<Resource<MessageResponse>> {
        return userRepository.resetPassword(ResetRequest(code, password))
    }

    fun updateProfile(first: String, last: String?): LiveData<Resource<MessageResponse>> {
        return userRepository.updateProfile(NameRequest(NameRequest.Payload(first, last)))
    }

//    fun updateVideoStatus(
//        id:Int,
//        request: UpdateVideoStatusRequest
//    ): LiveData<Resource<MessageResponse>> {
//        return userRepository.updateVideoStatus(id,
//            request
//        )
//    }


    fun updatePreference(
        request: UpdateProfileRequest
    ): LiveData<Resource<MessageResponse>> {
        return userRepository.updatePreference(
            request
        )
    }

    fun getFollowers(query: String?): LiveData<Resource<FollowerResponse>> {
        return userRepository.getFollowers(query)
    }

    fun getFollowings(query: String?): LiveData<Resource<FollowerResponse>> {
        return userRepository.getFollowings(query)
    }

    fun searchUser(query: String?): LiveData<Resource<FollowerResponse>> {
        return userRepository.searchUser(query)
    }

    fun followUser(query: Int): LiveData<Resource<MessageResponse>> {
        return userRepository.followUser(FollowRequest(following_user_id = query))
    }

    fun unfollowUser(query: Int): LiveData<Resource<MessageResponse>> {
        return userRepository.unfollowUser(FollowRequest(following_user_id = query))
    }

    fun getUserDetails(): LiveData<Resource<UserDetailsResponse>> {
        return userRepository.getUserDetails()
    }

    fun getUserStats(): LiveData<Resource<UserDetailsResponse>> {
        return userRepository.getUserStats()
    }

    fun getUserHistory(page: Int, limit: Int): LiveData<Resource<UserHistoryResponse>> {
        return userRepository.getUserHistory(page, limit)
    }

    fun getBalance(): LiveData<Resource<BalanceResponse>> {
        return userRepository.getBalance()
    }

    fun deleteAccount(): LiveData<Resource<MessageResponse>> {
        return userRepository.deleteAccount()
    }

    fun getIndustries(): LiveData<Resource<IndustriesResponse>> {
        return userRepository.getIndustries()
    }

    fun uploadPicture(id: MultipartBody.Part): LiveData<Resource<UploadResponse>> {
        return userRepository.uploadPicture(id)
    }

    fun getOtherUserData(id: Int): LiveData<Resource<UserDetailsResponse>> {
        return userRepository.getOtherUserData(UserIdRequest(id))
    }

    fun addFavBrands(list: ArrayList<Int>): LiveData<Resource<MessageResponse>> {
        return userRepository.addFavBrands(AddFavBrandsRequest(list))
    }

    fun rmvFavBrands(list: String): LiveData<Resource<MessageResponse>> {
        return userRepository.rmvFavBrands(list)
    }

    fun getCustomFollowing(
        id: String
    ): LiveData<Resource<CustomFollowingResponse>> {
        return userRepository.getCustomFollowing(id)
    }

    fun getCustomFollower(
        id: String
    ): LiveData<Resource<CustomFollowingResponse>> {
        return userRepository.getCustomFollower(id)
    }

}