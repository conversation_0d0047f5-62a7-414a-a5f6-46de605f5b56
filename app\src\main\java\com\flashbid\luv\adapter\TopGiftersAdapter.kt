package com.flashbid.luv.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.flashbid.luv.R
import com.flashbid.luv.databinding.ItemTopGifterBinding
import com.flashbid.luv.extensions.setGridLayout
import com.flashbid.luv.models.battle.TopGiftersResponse
import com.flashbid.luv.util.loadImageFromUrl


class TopGiftersAdapter(
    private val categories: List<TopGiftersResponse.Message>
) : RecyclerView.Adapter<TopGiftersAdapter.ViewHolder>() {

    inner class ViewHolder(val binding: ItemTopGifterBinding) :
        RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val itemView =
            ItemTopGifterBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(itemView)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val model = categories[position]

        holder.binding.apply {
            textView12.text = model.username
            tvUsername.text = "${model.total_gifts_sent} " + root.context.getString(R.string.gift_sent)


            imageView4.loadImageFromUrl(model.photo, false)
            rcvGifts.setGridLayout(4)
            rcvGifts.adapter = BattleGiftAdapter(model.gifts) {}
        }
    }

    override fun getItemCount(): Int {
        return categories.size
    }

}