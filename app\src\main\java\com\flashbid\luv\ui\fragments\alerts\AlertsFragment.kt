package com.flashbid.luv.ui.fragments.alerts

import android.app.AlertDialog
import android.icu.text.SimpleDateFormat
import android.content.ContentValues.TAG
import android.content.Intent
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.net.Uri
import android.os.Bundle
import android.text.Html
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import android.widget.TimePicker
import android.widget.Toast
import androidx.appcompat.widget.AppCompatButton
import androidx.cardview.widget.CardView
import androidx.core.net.toUri
import androidx.core.view.marginBottom
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.transition.Visibility
import com.flashbid.luv.MainActivity
import com.flashbid.luv.R
import com.flashbid.luv.adapter.AlertsAdapter
import com.flashbid.luv.data.local.AppPreferences
import com.flashbid.luv.databinding.DialogAnnoucementBinding
import com.flashbid.luv.databinding.DialogBeaconDropBinding
import com.flashbid.luv.databinding.DialogCongratsBinding
import com.flashbid.luv.databinding.DialogGiftLuvBinding
import com.flashbid.luv.databinding.DialogGiveawayBinding
import com.flashbid.luv.databinding.FragmentAlertsBinding
import com.flashbid.luv.extensions.TextState
import com.flashbid.luv.extensions.hide
import com.flashbid.luv.extensions.setOnClickWithDebounce
import com.flashbid.luv.extensions.setVerticalLayout
import com.flashbid.luv.extensions.show
import com.flashbid.luv.extensions.snackBar
import com.flashbid.luv.extensions.viewBinding
import com.flashbid.luv.models.remote.AlertModel
import com.flashbid.luv.models.remote.UpdateVideoStatusRequest
import com.flashbid.luv.network.Status
import com.flashbid.luv.util.Constants
import com.flashbid.luv.util.loadImageFromUrl
import com.flashbid.luv.viewmodels.AlertsViewModel
import com.flashbid.luv.viewmodels.UserViewModel
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.firebase.dynamiclinks.DynamicLink
import com.google.firebase.dynamiclinks.FirebaseDynamicLinks
import com.google.firebase.dynamiclinks.ktx.androidParameters
import com.google.firebase.dynamiclinks.ktx.iosParameters
import org.koin.android.ext.android.inject
//import org.koin.androidx.viewmodel.compat.ScopeCompat.viewModel
import java.time.LocalDate
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.time.OffsetDateTime
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit

class AlertsFragment : Fragment(R.layout.fragment_alerts) {

    private val viewModel: AlertsViewModel by viewModel()
    private val binding by viewBinding(FragmentAlertsBinding::bind)
    private val newAlerts: ArrayList<AlertModel> = ArrayList()
    private val readAlerts: ArrayList<AlertModel> = ArrayList()
    private val newAlertsAdapter by lazy { AlertsAdapter(this::onAlertClick) }
    private val readAlertsAdapter by lazy { AlertsAdapter(this::onAlertClick) }
    private val pref by inject<AppPreferences>()
    private val userViewModel: UserViewModel by viewModels()

    private fun onAlertClick(item: AlertModel) {
        println("item is clicked ==> "+ item.action + item.alert_type)
//         item is clicked ==> 15  15

        Log.d(TAG, "onAlertClick: $item")
        if (item.alert_type != null &&  item.alert_type == 0) { // giveaway
             showGiveawayDialog(0, null, url = null)
        } else if (item.alert_type != null &&  item.alert_type == 1) { // winner giveaway
            showGiveawayDialog(1, item, url = null)
        } else if (item.alert_type == 3) { // Lottery Ticket
            showGiveawayDialog(3, item, "https://brands.theluvnetwork.com/user/lottery")
        }
        else if (item.alert_type == 19) { // Lottery Ticket Winner
            showGiveawayDialog(19, item, "https://brands.theluvnetwork.com/user/lottery")
        }
        else if (item.alert_type == 21) { // Lottery Ticket Winner
            showGiftDialog(
                item.name,
                item.transaction_id,
                item.sender_id,
                item.gift_name,
                item.gift_message ?: "",
                false, item,
                item.alert_type
            )
        } else if (item.alert_type == 20) {
            showGiveawayDialog(20, item, "https://brands.theluvnetwork.com/user/lottery")

        }
        else if (item.action == 4) { // beacon luv drop
            showBeaconRewardDialog(4, item)
        } else if (item.action == 10) { // sponsored luv drop
            showBeaconRewardDialog(10, item)
        } else if (item.action == 11) { // admin luv drop
            showBeaconRewardDialog(11, item)
        }
        else if (item.action == 13) { // Referral Event
            showBeaconRewardDialog(13, item)
        }
        else if (item.action == 15) { // Referral Event
            showThankYouVideoBottomSheet(item)
        }
        else if (item.alert_type != null &&  item.alert_type == 7) { // Annoucemnet
            if(item.message.contains(getString(R.string.system_luv_drop))) {
                showBeaconRewardDialog(7, item)
            } else {
                showAnnouncemntDialog(item)
            }
        }

        else if (item.alert_type != null &&  item.alert_type == 8) {  // Gift Message

            var canRecord = false
            println("is thank you ==> alert type is 8 showGiftMessageDialog()"+ " is_thanks_video: "+ item.is_thanks_video )
            if (item.is_thanks_video == 1) {
                canRecord = true
                if (item.story_sent == 1) {
                    canRecord = false
                }
            }
            val name = if (item.gift_message.isNotBlank()) getString(R.string.luv_shout_out) else item.gift_name


            showGiftMessageDialog(
                item.name,
                item.transaction_id,
                item.sender_id,
                name,
                item.gift_image ?: "",
                item.gift_message,
                canRecord
            )
        }

        else if (item.alert_type != null &&  item.alert_type == 10) {  // Gift Message

            var canRecord = false
            println("is thank you ==> alert type is 8 showGiftMessageDialog()"+ " is_thanks_video: "+ item.is_thanks_video )
            if (item.is_thanks_video == 1) {
                canRecord = true
                if (item.story_sent == 1) {
                    canRecord = false
                }
            }

            showGiftMessageDialog(
                item.name,
                item.transaction_id,
                item.sender_id,
                item.gift_name,
                item.gift_image ?: "",
                item.gift_message,
                canRecord
            )
        }

        else if (item.message.contains("LUV Received")) {

            var canRecord = false
            if (item.is_thanks_video == 1) {
                canRecord = true
                if (item.story_sent == 1) {
                    canRecord = false
                }
            }
            showGiftDialog(
                item.name,
                item.transaction_id,
                item.sender_id,
                item.gift_name,
                item.gift_message ?: "",
                canRecord, item,
                item.alert_type
            )
        } else if (item.gift_name == null && item.gift_image == null && item.name == null)
            showInvitedDialog(item.message)
        else {
            var canRecord = false
            if (item.is_thanks_video == 1) {
                canRecord = true
                if (item.story_sent == 1) {
                    canRecord = false
                }
            }
            println("is thank you ==> last alert type showGiftDialog()"+ item.is_thanks_video + item.story_sent)

            showGiftDialog(
                item.name,
                item.transaction_id,
                item.sender_id,
                item.gift_name ,
                item.gift_image ?: "",
                canRecord, item)
        }
    }

    private fun showThankYouVideoBottomSheet(item: AlertModel?) {
        val bottomSheetDialog = BottomSheetDialog(requireContext())
        val view = LayoutInflater.from(requireContext()).inflate(R.layout.sheet_thanks_received, null)

        val titleTextView = view.findViewById<TextView>(R.id.btnEdit)
        val subtitleTextView = view.findViewById<TextView>(R.id.textView43)
        val playButton = view.findViewById<CardView>(R.id.btnDiscard)
        val closeButton = view.findViewById<ImageView>(R.id.imageView6)



        val onlyYou = getString(R.string.only_you_can_see_it_njust_once)
        val anyone= getString(R.string.anyone_can_view_nfor_24h)
        println("selected video item"+ item)
        if (item?.gift_name == "single"){
            subtitleTextView.text = onlyYou
        }
        else {
            subtitleTextView.text = anyone
        }
        val now = OffsetDateTime.now(ZoneOffset.UTC) // Get current time in UTC
        val today = LocalDate.now().toString()
        var formattedDate = item?.update_at ?: ""
//        if (formattedDate.isNotEmpty()){
//            formattedDate = formattedDate.substring(0, 10)
//        }

//        if(item?.is_video_viewed == "1" || formattedDate >= today){
//            playButton.visibility = View.GONE
//        }


        val formatter = DateTimeFormatter.ISO_OFFSET_DATE_TIME // Use the built-in formatter for ISO 8601 with offset
        try {
            val createdAt = OffsetDateTime.parse(formattedDate, formatter)
            val timeDifference = ChronoUnit.HOURS.between(createdAt, now)

            if (item?.is_video_viewed == "1" || timeDifference >= 24) {
                playButton?.visibility = View.GONE
            } else {
                playButton?.visibility = View.VISIBLE
            }
        } catch (e: Exception) {
            println("Error parsing created_at: ${e.message}")
            playButton?.visibility = View.VISIBLE // Or your preferred default
        }


        playButton.setOnClickListener {

            if (item?.gift_name == "single"){
                            handleUpdateVideoStatus(item?.id ?: 0,)
            }




            bottomSheetDialog.dismiss()
            if (item != null) {
                val nonNullItem = item.copy(
                    name = item.name ?: "",
                    company_name = item.company_name ?: "",
                    alert_type= item.alert_type ?: 0,
                    gift_name = item.gift_name ?: "",
                    transaction_id = item.transaction_id ?: "",
                    sender_id = item.sender_id ?: "",
                    gift_image = item.gift_image ?: "",
                   create_at= item.created_at ?: "",
                 id= item.id ?: 0,
                image= "",
                is_read= item.is_read ?: 0,
                message= "",
                 body= "",

                 action= item.action ?: 0,
               amount= item.amount ?: 0,
                update_at= "",
                user_id= item.user_id ?: 0,
                is_thanks_video= 0,
               story_sent= 0,
                 drop_amount= 0,
                gift_message= ""
                )
                val bundle = Bundle().apply {
                    putParcelable("alertModel", nonNullItem)
                }
                findNavController().navigate(R.id.action_AlertsFragment_to_videoPlayerWithDetailsFragment, bundle)
            } else {
                Log.e("AlertsFragment", "Error: AlertModel item is null, cannot navigate.")
                Toast.makeText(requireContext(), "Error loading video.", Toast.LENGTH_SHORT).show()
            }
        }

        closeButton.setOnClickListener {
            bottomSheetDialog.dismiss()
        }

        bottomSheetDialog.setContentView(view)
        bottomSheetDialog.show()
    }

//    /v4/api/records/history/32103


//    {
//        "id": 32103,
//        "is_video_viewed": "1"
//    }


    private fun handleUpdateVideoStatus(id: Int) {
        val is_video_viewed = 1
        val request = UpdateVideoStatusRequest(is_video_viewed = is_video_viewed)
        viewModel.updateVideoStatus(id, request )
            .observe(viewLifecycleOwner) {
                when (it.status) {
                    Status.ERROR -> snackBar(
                        it.message ?: getString(
                            R.string.something_went_wrong,
                            ""
                        )
                    )
                    Status.LOADING -> {}
                    Status.SUCCESS -> {
                    }
                }
            }
    }



    private fun showAnnouncemntDialog(alertModel: AlertModel?) {

        val builder = AlertDialog.Builder(requireContext())

        val binding = DialogAnnoucementBinding.inflate(layoutInflater)
        val dialog = builder.create()
        dialog.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        binding.tvAmount.text = alertModel?.message ?: ""
        binding.textView29.text = alertModel?.body ?: ""

        binding.imageView8.setOnClickWithDebounce {
            dialog.dismiss()
        }

        dialog.setView(binding.root)
        dialog.setCancelable(false)
        dialog.show()
    }

    private fun showGiveawayDialog(type: Int, alertModel: AlertModel?, url: String?) {
        val builder = AlertDialog.Builder(requireContext())
        val binding = DialogGiveawayBinding.inflate(layoutInflater)
        val dialog = builder.create()

        dialog.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        binding.textView30.visibility = View.GONE
        if (type == 3) {
            binding.tvAmount.text = getString(R.string.ticket_for_one)
            binding.textView29.text = getString(R.string.ticket_for_one_description)
            if(alertModel != null) {

                binding.textView30.visibility = View.VISIBLE
                binding.textView30.text = alertModel.company_name
            }
        }

        if (type == 19) {
            binding.tvAmount.text = getString(R.string.we_have_winner)
            binding.textView29.text = getString(R.string.congratulations_giveaway_winner)
            if(alertModel != null) {

                binding.textView30.visibility = View.VISIBLE
                binding.textView30.text = alertModel.name
            }
        }
        else if (type == 20) {

            binding.tvAmount.text = getString(R.string.we_have_winner)
            binding.textView29.text = getString(R.string.congratulations_giveaway_winner)
            if(alertModel != null) {

                binding.textView30.visibility = View.VISIBLE
                binding.textView30.text = alertModel.body.replaceFirstChar { it.uppercaseChar() }

            }


            binding.ivGift.setImageResource(R.drawable.annbg)


        }
        binding.imageView8.setOnClickWithDebounce {
            dialog.dismiss()
        }

        binding.btnOpen.setOnClickWithDebounce {

            if(url != null && alertModel?.lottery_id == null) {
                openInExternalBrowser(url)
            } else {
                openInExternalBrowser("https://luvapp.manaknightdigital.com/giveaway/${alertModel?.lottery_id}")
//                openInExternalBrowser("https://brands.theluvnetwork.com/giveaway")
            }


            dialog.dismiss()
        }

        dialog.setView(binding.root)
        dialog.setCancelable(false)
        dialog.show()
    }

    private fun openInExternalBrowser(url: String) {
        val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
        startActivity(intent)
    }

    private fun showBeaconRewardDialog(type: Int, alertModel: AlertModel?) {
        val builder = AlertDialog.Builder(requireContext())
        val binding = DialogBeaconDropBinding.inflate(layoutInflater)
        val dialog = builder.create()

        println("the type"  + type)

        dialog.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))

        if (type == 4) {

        } else if (type == 10) {
            binding.tvBrand.text = getString(R.string.sponsored_drop)
            if (alertModel != null) {

                binding.textView29.text = Html.fromHtml(
                    getString(R.string.favorite_brand) + " <br><b>" + alertModel.name + "</b>",
                    Html.FROM_HTML_MODE_LEGACY
                )
//                binding.textView29.text = getString(R.string.favorite_brand) + " \n" + alertModel.name
                binding.tvAmount.text =  "" + alertModel.amount + " " + getString(R.string.luv)
            }
        } else if (type == 7) {
            binding.tvBrand.text = getString(R.string.system_luv_drop)
            if (alertModel != null) {
                binding.tvBrand.text = alertModel.message
                binding.textView29.text = alertModel.body //getString(R.string.comming_with_luv_from) + "\n" + getString(R.string.the_luv_network)
                binding.tvAmount.text =  "" + alertModel.drop_amount + " " + getString(R.string.luv) //"" + alertModel.drop_amount + " " + getString(R.string.luv)
            }
        } else if (type == 11) {
            binding.tvBrand.text = getString(R.string.system_luv_drop)
            if (alertModel != null) {
                binding.textView29.text = getString(R.string.comming_with_luv_from) + "\n" + getString(R.string.the_luv_network)
                binding.tvAmount.text =  "" + alertModel.amount + " " + getString(R.string.luv)
            }
        } else if (type == 13) {
            binding.tvBrand.text = getString(R.string.congratulations)
            binding.tvAmount.visibility = View.GONE
            if (alertModel != null) {
                binding.textView29.text = alertModel.message

                val image = alertModel.gift_image ?: ""
                if (image.length > 0) {
                    binding.ivGift.loadImageFromUrl(image, false)
                } else {
                    binding.ivGift.setImageResource(R.drawable.quest5)
                }

            }
        }



        binding.imageView8.setOnClickWithDebounce {
            dialog.dismiss()
        }
        binding.btnOpen.setOnClickWithDebounce {
            dialog.dismiss()
        }

        dialog.setView(binding.root)
        dialog.setCancelable(false)
        dialog.show()
    }

    private fun showInvitedDialog(item: String) {
        val builder = AlertDialog.Builder(requireContext())
        val binding = DialogCongratsBinding.inflate(layoutInflater)
        val dialog = builder.create()

        val words = item.split(" ")
        val firstWord = words.getOrNull(0)

        dialog.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))

        binding.textView29.text =
            getString(R.string.you_get_20_luv_when_you_spread_luv_by_inviting_others, firstWord)

        binding.imageView8.setOnClickWithDebounce {
            dialog.dismiss()
        }

        binding.btnOpen.setOnClickWithDebounce {
            dialog.dismiss()
        }

        binding.tvInviteMore.setOnClickWithDebounce {
            dialog.dismiss()
            findNavController().navigate(R.id.questFragment)
        }

        dialog.setView(binding.root)
        dialog.setCancelable(false)
        dialog.show()
    }

    private fun showGiftDialog(
        senderName: String?,
        transId: String?,
        senderId: String?,
        giftName: String?,
        giftImage: String,
        canRecord: Boolean,
        item: AlertModel,
        alertType: Int? = null
    ) {
        val builder = AlertDialog.Builder(requireContext())
        val binding = DialogGiftLuvBinding.inflate(layoutInflater)
        val dialog = builder.create()

        dialog.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))

        if (alertType ==21 ) {

            binding.tvAmount.text = getString(R.string.top_gifter)
            binding.tvAmount.show()
//            binding.textView30.hide()
            binding.btnOpen.hide()
            binding.textView29.text = getString(R.string.you_have_been_selected)


            val params = binding.textView29.layoutParams as ViewGroup.MarginLayoutParams
            params.bottomMargin = 25
            binding.textView29.layoutParams = params

            binding.textView30.text = getString(R.string.any_user_who_sends_you_the_specified_gift)

            binding.ivGift.loadImageFromUrl(giftImage, false)
            binding.ivBlank.show()
//            binding.tvInviteMore.show()

        }

        else if (giftName == null || giftName == "") {


                binding.tvAmount.text = getString(R.string.congratulations)
                binding.tvAmount.show()
                binding.textView30.hide()
                binding.textView29.text =
                    item.message

                binding.ivGift.loadImageFromUrl(giftImage, false)
                binding.ivBlank.show()
                binding.tvInviteMore.hide()





        } else {
            binding.tvAmount.text = giftName
            binding.tvAmount.show()
            binding.textView30.hide()

            binding.textView29.text =
                getString(R.string.with_luv_from, senderName)

            binding.ivGift.loadImageFromUrl(giftImage, false)
            binding.ivBlank.hide()

        }


        binding.imageView8.setOnClickWithDebounce {
            dialog.dismiss()
        }

        binding.btnOpen.setOnClickWithDebounce {
            dialog.dismiss()
        }

        binding.tvInviteMore.setOnClickWithDebounce {
            dialog.dismiss()
            //findNavController().navigate(R.id.questFragment)

            generateSharingLink(
                deepLink = "${Constants.DYNAMIC_LINK_PREFIX}/referral?code=${pref.referral}".toUri(),
                previewImageLink = "".toUri()
            ) { generatedLink ->
                // Use this generated Link to share via Intent
                shareDeepLink(generatedLink)
                Log.d(TAG, "checkDynamicLink: $generatedLink")
            }
        }

        if (canRecord) {
            binding.btnOpen.layoutParams.width = ViewGroup.LayoutParams.MATCH_PARENT
            binding.btnOpen.requestLayout() // Request a layout update

            binding.btnSendVideo.show()
            binding.btnSendVideo.setOnClickWithDebounce {
                dialog.dismiss()
                if (senderName.isNullOrEmpty() || senderId.isNullOrEmpty() || transId.isNullOrEmpty()) {
                    snackBar("Sender details are invalid")
                } else {
                    findNavController().navigate(
                        AlertsFragmentDirections.actionAlertFragmentToPreRecordingFragment(
                            senderName,
                            senderId, transId
                        )
                    )
                }
            }
        } else {
            binding.btnOpen.layoutParams.width = ViewGroup.LayoutParams.WRAP_CONTENT
            binding.btnOpen.requestLayout() // Request a layout update
            binding.btnSendVideo.hide()
        }

        dialog.setView(binding.root)
        dialog.setCancelable(false)
        dialog.show()
    }

    private fun showGiftMessageDialog(
        senderName: String?,
        transId: String?,
        senderId: String?,
        giftName: String?,
        giftImage: String,
        giftMessage: String,
        canRecord: Boolean
    ) {
        val builder = AlertDialog.Builder(requireContext())
        val binding = DialogGiftLuvBinding.inflate(layoutInflater)
        val dialog = builder.create()

        dialog.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))

        binding.textView29.text =
            getString(R.string.with_luv_from, senderName)

        binding.tvAmount.text = giftName
        binding.textView30.text = giftMessage
        binding.tvAmount.show()
        binding.ivBlank.show()

        binding.ivGift.loadImageFromUrl(giftImage, false)

        binding.imageView8.setOnClickWithDebounce {
            dialog.dismiss()
        }

        binding.btnOpen.setOnClickWithDebounce {
            dialog.dismiss()
        }


        if (canRecord) {
            binding.btnSendVideo.show()
            binding.btnSendVideo.setOnClickWithDebounce {
                dialog.dismiss()
                if (senderName.isNullOrEmpty() || senderId.isNullOrEmpty() || transId.isNullOrEmpty()) {
                    snackBar("Sender details are invalid")
                } else {
                    findNavController().navigate(
                        AlertsFragmentDirections.actionAlertFragmentToPreRecordingFragment(
                            senderName,
                            senderId, transId
                        )
                    )
                }
            }
        } else binding.btnSendVideo.hide()

//        binding.btnOpen.hide()
        dialog.setView(binding.root)
        dialog.setCancelable(false)
        dialog.show()
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.rcvNewAlerts.apply {
            setVerticalLayout()
            adapter = newAlertsAdapter
        }

        binding.rcvReadAlerts.apply {
            setVerticalLayout()
            adapter = readAlertsAdapter
        }
    }

    private fun shareDeepLink(deepLink: String) {
        val intent = Intent(Intent.ACTION_SEND)
        intent.type = "text/plain"
        intent.putExtra(
            Intent.EXTRA_SUBJECT,
            getString(R.string.luv_referral)
        )
        intent.putExtra(
            Intent.EXTRA_TEXT,
            getString(R.string.checkout_luv_app, deepLink)
        )
        if (isAdded) {
            requireContext().startActivity(Intent.createChooser(intent, "Share.."))
        }
    }

    private fun generateSharingLink(
        deepLink: Uri,
        previewImageLink: Uri,
        getShareableLink: (String) -> Unit = {}
    ) {
        FirebaseDynamicLinks.getInstance().createDynamicLink().run {
            link = deepLink
            domainUriPrefix = Constants.DYNAMIC_LINK_PREFIX
            setSocialMetaTagParameters(
                DynamicLink.SocialMetaTagParameters.Builder()
                    .setImageUrl(previewImageLink)
                    .build()
            )
            iosParameters("com.manaknight.Luv-App") {
                build()
            }
            androidParameters("com.manaknight.luv") {
                build()
            }
            buildShortDynamicLink()
        }.also {
            it.addOnSuccessListener { dynamicLink ->
                getShareableLink.invoke(dynamicLink.shortLink.toString())
            }
            it.addOnFailureListener { error ->
                snackBar("${error.message}")
            }
        }
    }

    override fun onResume() {
        super.onResume()
        getAlerts()
    }

    private fun getAlerts() {
        viewModel.getAlerts().observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> snackBar(it.message ?: getString(R.string.something_went_wrong))
                Status.LOADING -> {}
                Status.SUCCESS -> {
                    setAlertLists(it.data?.message)
                }
            }
        }
    }

    private fun setAlertLists(message: List<AlertModel>?) {
        newAlerts.clear()
        readAlerts.clear()
        newAlerts.addAll(message?.filter { it.is_read == 0 } ?: ArrayList())
        readAlerts.addAll(message?.filter { it.is_read == 1 } ?: ArrayList())
        newAlertsAdapter.refresh(newAlerts)
        readAlertsAdapter.refresh(readAlerts)

        if (newAlerts.isEmpty()) {
            binding.tvTagNew.hide()
            binding.rcvNewAlerts.hide()
        } else {
            binding.tvTagNew.show()
            binding.rcvNewAlerts.show()
        }

        if (newAlerts.isEmpty() && readAlerts.isEmpty()) {
            binding.llNoAlerts.show()
        }
    }

}