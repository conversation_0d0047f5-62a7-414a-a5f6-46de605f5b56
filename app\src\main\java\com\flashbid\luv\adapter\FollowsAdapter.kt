package com.flashbid.luv.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.flashbid.luv.databinding.ItemSearchBinding
import com.flashbid.luv.extensions.hide
import com.flashbid.luv.util.loadImageFromUrl
import com.flashbid.luv.extensions.setOnClickWithDebounce
import com.flashbid.luv.extensions.show
import com.flashbid.luv.models.remote.UserDetails

class FollowsAdapter(
    private val list: List<UserDetails>,
    private val fromProfile: Boolean = false,
    val onClick: (UserDetails) -> Unit,
) : RecyclerView.Adapter<FollowsAdapter.ViewHolder>() {

    private var filteredList = list

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding =
            ItemSearchBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = filteredList[position]

        with(holder.binding) {
            textView12.text = item.first_name + " " + item.last_name
            tvUsername.text = "@" + item.username
            imageView4.loadImageFromUrl(item.photo)
            if (fromProfile) {
                btnFollow.show()
                tvFollow.show()
            } else {
                btnFollow.hide()
                tvFollow.hide()
            }
        }

        holder.itemView.setOnClickWithDebounce {
            onClick(item)
        }


    }

    fun filter(query: String) {
        filteredList = if (query.isEmpty()) {
            list
        } else {
            val lowerCaseQuery = query.lowercase()
            list.filter {
                val fullName = "${it.first_name ?: ""} ${it.last_name ?: ""}".lowercase()
                fullName.contains(lowerCaseQuery)
            }
        }
        refresh()
    }

    override fun getItemCount(): Int = filteredList.size

    inner class ViewHolder(val binding: ItemSearchBinding) :
        RecyclerView.ViewHolder(binding.root)

    fun refresh() = notifyDataSetChanged()


}