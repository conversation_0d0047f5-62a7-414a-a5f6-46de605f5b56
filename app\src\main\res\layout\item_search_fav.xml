<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingHorizontal="@dimen/_20sdp"
    android:paddingVertical="@dimen/_10sdp">

    <ImageView
        android:id="@+id/imageView4"
        android:layout_width="@dimen/_30sdp"
        android:layout_height="@dimen/_30sdp"
        android:src="@drawable/ic_user_placeholder"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.5" />

    <TextView
        android:id="@+id/textView12"
        style="@style/text"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_10sdp"
        android:ellipsize="end"
        android:maxLines="2"
        app:layout_constraintBottom_toTopOf="@+id/tvUsername"
        app:layout_constraintEnd_toStartOf="@+id/appCompatButton"
        app:layout_constraintStart_toEndOf="@+id/imageView4"
        app:layout_constraintTop_toTopOf="@+id/imageView4"
        tools:text="Dianne Russell" />

    <TextView
        android:id="@+id/tvUsername"
        style="@style/small"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/gray"
        app:layout_constraintBottom_toBottomOf="@+id/imageView4"
        app:layout_constraintStart_toStartOf="@+id/textView12"
        app:layout_constraintTop_toBottomOf="@+id/textView12"
        tools:text=" @username" />

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/appCompatButton"
        style="@style/blueButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="2dp"
        android:text="@string/follow"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.5" />

</androidx.constraintlayout.widget.ConstraintLayout>