
<resources>
    <attr name="colorSemired" format="color" />
    <attr name="colorOnSemired" format="color" />
    <attr name="colorSemiredContainer" format="color" />
    <attr name="colorOnSemiredContainer" format="color" />
    <attr name="harmonizeSemired" format="boolean" />
    <attr name="colorGray" format="color" />
    <attr name="colorOnGray" format="color" />
    <attr name="colorGrayContainer" format="color" />
    <attr name="colorOnGrayContainer" format="color" />
    <attr name="harmonizeGray" format="boolean" />
    <attr name="colorSemigray" format="color" />
    <attr name="colorOnSemigray" format="color" />
    <attr name="colorSemigrayContainer" format="color" />
    <attr name="colorOnSemigrayContainer" format="color" />
    <attr name="harmonizeSemigray" format="boolean" />
    <attr name="colorGreen" format="color" />
    <attr name="colorOnGreen" format="color" />
    <attr name="colorGreenContainer" format="color" />
    <attr name="colorOnGreenContainer" format="color" />
    <attr name="harmonizeGreen" format="boolean" />
    <attr name="colorSemigreen" format="color" />
    <attr name="colorOnSemigreen" format="color" />
    <attr name="colorSemigreenContainer" format="color" />
    <attr name="colorOnSemigreenContainer" format="color" />
    <attr name="harmonizeSemigreen" format="boolean" />
    <attr name="colorBlue" format="color" />
    <attr name="colorOnBlue" format="color" />
    <attr name="colorBlueContainer" format="color" />
    <attr name="colorOnBlueContainer" format="color" />
    <attr name="harmonizeBlue" format="boolean" />
    <attr name="colorSemiblue" format="color" />
    <attr name="colorOnSemiblue" format="color" />
    <attr name="colorSemiblueContainer" format="color" />
    <attr name="colorOnSemiblueContainer" format="color" />
    <attr name="harmonizeSemiblue" format="boolean" />
    <attr name="colorRedgradstart" format="color" />
    <attr name="colorOnRedgradstart" format="color" />
    <attr name="colorRedgradstartContainer" format="color" />
    <attr name="colorOnRedgradstartContainer" format="color" />
    <attr name="harmonizeRedgradstart" format="boolean" />
    <attr name="colorRedgradend" format="color" />
    <attr name="colorOnRedgradend" format="color" />
    <attr name="colorRedgradendContainer" format="color" />
    <attr name="colorOnRedgradendContainer" format="color" />
    <attr name="harmonizeRedgradend" format="boolean" />

    <declare-styleable name="TabsRadioGroup">
        <attr name="selectorColor" format="color"/>
        <attr name="backgroundColor" format="color"/>
    </declare-styleable>

</resources>
