<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    app:strokeColor="#f5f5f8"
    app:cardBackgroundColor="#EBEBF0"
    app:cardCornerRadius="@dimen/_50sdp"
    app:cardElevation="0dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/llBackground"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        android:paddingVertical="@dimen/_6sdp"
        android:paddingHorizontal="@dimen/_12sdp">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/ivImage"
            android:layout_width="@dimen/_30sdp"
            android:layout_height="@dimen/_30sdp"
            android:scaleType="centerCrop"
            android:src="@drawable/heart_two"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/gray"
            android:textSize="12sp"
            app:layout_constraintStart_toEndOf="@+id/ivImage"
            app:layout_constraintStart_toStartOf="@+id/ivHeart"
            app:layout_constraintTop_toBottomOf="@+id/ivHeart"
            tools:text="gift name" />

        <ImageView
            android:id="@+id/ivHeart"
            android:layout_width="@dimen/_16sdp"
            android:layout_height="@dimen/_16sdp"
            android:layout_marginStart="@dimen/_6sdp"
            android:src="@drawable/heart_filled"
            app:layout_constraintStart_toEndOf="@+id/ivImage"
            app:layout_constraintTop_toTopOf="@+id/ivImage" />

        <TextView
            android:id="@+id/tvLuv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            app:layout_constraintBottom_toBottomOf="@+id/ivHeart"
            app:layout_constraintStart_toEndOf="@+id/ivHeart"
            app:layout_constraintTop_toTopOf="@+id/ivHeart"
            tools:text="100" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>